#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <pthread.h>
#include <netdb.h>
#include <stdarg.h>
#include <stdbool.h>
#include <jpeglib.h>
#include <jerror.h>
#include <setjmp.h>
#include <time.h>
#include <arpa/inet.h>
#include "mosquitto.h"
#include "source/cJSON.h"
#include "source/mqtt.h"
#include "source/utils.h"
#include "source/file.h"
#include "source/activation.h"
// 全局变量定义
int activated_status = 0;
struct tcp_client *g_tcp_client = NULL;

#define MAX_ROUND_STR 32  // 增加缓冲区大小以适应更大的数字

// himixPub专用日志文件

static FILE *himix_pub_log_file = NULL;

// himixPub专用日志函数
void init_himix_pub_log() {
    // 确保日志目录存在
    char mkdir_cmd[256];
    snprintf(mkdir_cmd, sizeof(mkdir_cmd), "mkdir -p %s", LOG_DIR);
    system(mkdir_cmd);
    
    himix_pub_log_file = fopen(HIMIX_PUB_LOG_FILE, "a");
    if (himix_pub_log_file == NULL) {
        fprintf(stderr, "无法打开himixPub日志文件: %s\n", HIMIX_PUB_LOG_FILE);
        himix_pub_log_file = stdout; // 回退到标准输出
    }
    
    // 设置行缓冲
    if (himix_pub_log_file != stdout) {
        setvbuf(himix_pub_log_file, NULL, _IOLBF, 0);
    }
}

void close_himix_pub_log() {
    if (himix_pub_log_file && himix_pub_log_file != stdout) {
        fclose(himix_pub_log_file);
        himix_pub_log_file = NULL;
    }
}

void himix_print_with_time(const char* format, ...) {
    time_t now;
    struct tm *timeinfo;
    char time_buffer[32];
    
    // 获取当前时间
    time(&now);
    timeinfo = localtime(&now);
    
    // 格式化时间字符串 [YYYY-MM-DD HH:MM:SS]
    strftime(time_buffer, sizeof(time_buffer), "[%Y-%m-%d %H:%M:%S] ", timeinfo);
    
    // 写入时间戳到日志文件
    fprintf(himix_pub_log_file, "%s", time_buffer);
    
    // 处理可变参数
    va_list args;
    va_start(args, format);
    vfprintf(himix_pub_log_file, format, args);
    va_end(args);
    
    // 刷新输出缓冲区
    fflush(himix_pub_log_file);
}



// 发布形变数据
void* publish_displacement(void* arg)
{
    struct Ip* host_port = (struct Ip*)arg;
    struct mosquitto *mosq;
    if(mosquitto_lib_init()){
        himix_print_with_time("Init lib error!\n");
        exit(-1);
    }
    char name[50] = "pubData_";
    strcat(name, DEVICE_ID);
    mosq = mosquitto_new(name, true, NULL);
    if(mosq == NULL){
        himix_print_with_time("New pub_test error!\n");
        mosquitto_lib_cleanup();
        exit(-1);
    }
    // DNS解析和MQTT连接重试逻辑
    char ip_str[INET_ADDRSTRLEN];
    int retry_delay = 10;
    while (1) {
        struct hostent *he = gethostbyname(host_port->host);
        if (he == NULL) {
            himix_print_with_time("DNS解析失败: %s，%d秒后重试\n", host_port->host, retry_delay);
            sleep(retry_delay);
            continue;
        }
        inet_ntop(AF_INET, he->h_addr, ip_str, sizeof(ip_str));
        mosquitto_connect_callback_set(mosq, sub_connect_callback);
        mosquitto_publish_callback_set(mosq, publish_callback);
        int rc = mosquitto_connect(mosq, ip_str, host_port->port, 60);
        if(rc != MOSQ_ERR_SUCCESS){
            himix_print_with_time("连接MQTT服务器失败: %s:%d, 错误码: %d，%d秒后重试\n", ip_str, host_port->port, rc, retry_delay);
            sleep(retry_delay);
            continue;
        }
        rc = mosquitto_loop_start(mosq);
        if(rc != MOSQ_ERR_SUCCESS){
            himix_print_with_time("启动mosquitto网络循环失败: %d，%d秒后重试\n", rc, retry_delay);
            mosquitto_disconnect(mosq);
            sleep(retry_delay);
            continue;
        }
        break; // 连接成功，跳出重试循环
    }

    // 其余的发布逻辑保持不变
    char* change_data;
    while(1)
    {
        change_data = read_file(CHANGE_DATA_FILE); 
        if(change_data == NULL) {
            sleep(10);
            continue;
        }
        
        size_t input_length = strlen(change_data);
        size_t base64_size;
        char *change_base64 = base64_encode((const unsigned char *)change_data, input_length, &base64_size);
        
        if(change_base64 != NULL) {
            himix_print_with_time("变形数据发布：%s,%d\n", host_port->host, host_port->port);
            publish_msg(arg, mosq, CHANGE_TYPE, change_base64, base64_size, NULL, 0, NULL);
            free(change_base64);
        }
        
        free(change_data);
        sleep(60);  //发布间隔60秒
    }

    // 清理
    mosquitto_loop_stop(mosq, true);
    mosquitto_disconnect(mosq);
    mosquitto_destroy(mosq);
    mosquitto_lib_cleanup();

    return NULL;
}

// libjpeg压缩错误处理结构
struct jpeg_error_mgr_custom {
    struct jpeg_error_mgr pub;
    jmp_buf setjmp_buffer;
};

// libjpeg错误处理函数
static void jpeg_error_exit(j_common_ptr cinfo) {
    struct jpeg_error_mgr_custom* myerr = (struct jpeg_error_mgr_custom*) cinfo->err;
    longjmp(myerr->setjmp_buffer, 1);
}

// 添加连接回调函数
void connect_callback(struct mosquitto *mosq, void *obj, int rc)
{
    if(rc == 0){
        himix_print_with_time("成功连接到MQTT服务器\n");
    } else {
        himix_print_with_time("连接MQTT服务器失败，返回码: %d\n", rc);
    }
}

// 使用libjpeg压缩图片函数
char* compress_image_to_base64(const char* image_path, size_t* output_size, int quality) {
    FILE *infile, *outfile;
    struct jpeg_decompress_struct cinfo_decomp;
    struct jpeg_compress_struct cinfo_comp;
    struct jpeg_error_mgr_custom jerr_decomp, jerr_comp;
    JSAMPROW row_pointer[1];
    unsigned char *buffer = NULL;
    unsigned long buffer_size = 0;
    char temp_compressed[512];
    char *base64_result = NULL;
     snprintf(temp_compressed, sizeof(temp_compressed), "/tmp/compressed_%d_%ld.jpg", getpid(), time(NULL));
    
    // 打开输入文件
    if ((infile = fopen(image_path, "rb")) == NULL) {
        himix_print_with_time("无法打开输入图片文件: %s\n", image_path);
        return NULL;
    }
    
    // 初始化解压缩对象
    cinfo_decomp.err = jpeg_std_error(&jerr_decomp.pub);
    jerr_decomp.pub.error_exit = jpeg_error_exit;
    
    if (setjmp(jerr_decomp.setjmp_buffer)) {
        jpeg_destroy_decompress(&cinfo_decomp);
        fclose(infile);
        himix_print_with_time("JPEG解压缩错误\n");
        return NULL;
    }
    
    jpeg_create_decompress(&cinfo_decomp);
    jpeg_stdio_src(&cinfo_decomp, infile);
    jpeg_read_header(&cinfo_decomp, TRUE);
    
    // 转换为灰度图像
    cinfo_decomp.out_color_space = JCS_GRAYSCALE;
    jpeg_start_decompress(&cinfo_decomp);
    
    // 分配行缓冲区
    int row_stride = cinfo_decomp.output_width * cinfo_decomp.output_components;
    JSAMPARRAY image_buffer = (*cinfo_decomp.mem->alloc_sarray)
        ((j_common_ptr) &cinfo_decomp, JPOOL_IMAGE, row_stride, 1);
    
    // 打开输出文件
    if ((outfile = fopen(temp_compressed, "wb")) == NULL) {
        jpeg_finish_decompress(&cinfo_decomp);
        jpeg_destroy_decompress(&cinfo_decomp);
        fclose(infile);
        himix_print_with_time("无法创建临时输出文件: %s\n", temp_compressed);
        return NULL;
    }
    
    // 初始化压缩对象
    cinfo_comp.err = jpeg_std_error(&jerr_comp.pub);
    jerr_comp.pub.error_exit = jpeg_error_exit;
    
    if (setjmp(jerr_comp.setjmp_buffer)) {
        jpeg_destroy_compress(&cinfo_comp);
        jpeg_finish_decompress(&cinfo_decomp);
        jpeg_destroy_decompress(&cinfo_decomp);
        fclose(infile);
        fclose(outfile);
        unlink(temp_compressed);
        himix_print_with_time("JPEG压缩错误\n");
        return NULL;
    }
    
    jpeg_create_compress(&cinfo_comp);
    jpeg_stdio_dest(&cinfo_comp, outfile);
    
    // 设置压缩参数
    cinfo_comp.image_width = cinfo_decomp.output_width;
    cinfo_comp.image_height = cinfo_decomp.output_height;
    cinfo_comp.input_components = 1; // 灰度图像
    cinfo_comp.in_color_space = JCS_GRAYSCALE;
    
    jpeg_set_defaults(&cinfo_comp);
    jpeg_set_quality(&cinfo_comp, quality, TRUE);
    jpeg_start_compress(&cinfo_comp, TRUE);
    
    // 逐行处理图像数据
    while (cinfo_decomp.output_scanline < cinfo_decomp.output_height) {
        jpeg_read_scanlines(&cinfo_decomp, image_buffer, 1);
        row_pointer[0] = image_buffer[0];
        jpeg_write_scanlines(&cinfo_comp, row_pointer, 1);
    }
    
    // 完成压缩和解压缩
    jpeg_finish_compress(&cinfo_comp);
    jpeg_destroy_compress(&cinfo_comp);
    jpeg_finish_decompress(&cinfo_decomp);
    jpeg_destroy_decompress(&cinfo_decomp);
    
    fclose(infile);
    fclose(outfile);
    
    // 检查压缩后的文件是否存在并转换为base64
    if (access(temp_compressed, F_OK) == 0) {
        base64_result = image_to_base64(temp_compressed, output_size);
        unlink(temp_compressed); // 删除临时文件
        if (base64_result) {
            himix_print_with_time("libjpeg压缩成功，压缩后Base64大小: %zu KB\n", *output_size/1024);
        }
    } else {
        himix_print_with_time("压缩后的临时文件不存在\n");
    }
    
    return base64_result;
}


// 发布base64编码后的图像 - 统一使用mosquitto库
void* publish_image(void* arg)
{
    char image_name[256], *image_base64, *imageinfo_base64;
    struct Ip* host_port = (struct Ip*)arg;
    unsigned int device_hash = 0;
    for (int i = 0; DEVICE_ID[i] != '\0'; i++) {
        device_hash = device_hash * 31 + DEVICE_ID[i];
    }
    int send_minute = device_hash % 60;
    himix_print_with_time("设备 %s 将在每小时的第 %d 分钟发送图片\n", DEVICE_ID, send_minute);
    if(mosquitto_lib_init()){
        himix_print_with_time("Init lib error!\n");
        exit(-1);
    }
    char name[128] = "pubImage_";
    snprintf(name, sizeof(name), "pubImage_%s_%s_%d", DEVICE_ID, host_port->host, host_port->port);
    struct mosquitto *mosq = mosquitto_new(name, true, NULL);
    if(mosq == NULL){
        himix_print_with_time("New pub_image error!\n");
        mosquitto_lib_cleanup();
        exit(-1);
    }
    // DNS解析和MQTT连接重试逻辑
    char ip_str[INET_ADDRSTRLEN];
    int retry_delay = 10;
    while (1) {
        struct hostent *he = gethostbyname(host_port->host);
        if (he == NULL) {
            himix_print_with_time("DNS解析失败: %s，%d秒后重试\n", host_port->host, retry_delay);
            sleep(retry_delay);
            continue;
        }
        inet_ntop(AF_INET, he->h_addr, ip_str, sizeof(ip_str));
        mosquitto_connect_callback_set(mosq, sub_connect_callback);
        mosquitto_publish_callback_set(mosq, publish_callback);
        int rc = mosquitto_connect(mosq, ip_str, host_port->port, 60);
        if(rc != MOSQ_ERR_SUCCESS){
            himix_print_with_time("连接MQTT服务器失败: %s:%d, 错误码: %d，%d秒后重试\n", ip_str, host_port->port, rc, retry_delay);
            sleep(retry_delay);
            continue;
        }
        rc = mosquitto_loop_start(mosq);
        if(rc != MOSQ_ERR_SUCCESS){
            himix_print_with_time("启动mosquitto网络循环失败: %d，%d秒后重试\n", rc, retry_delay);
            mosquitto_disconnect(mosq);
            sleep(retry_delay);
            continue;
        }
        himix_print_with_time("成功连接到MQTT服务器: %s:%d\n", ip_str, host_port->port);
        break; // 连接成功，跳出重试循环
    }

    while(1)
    {
        time_t current_time;
        struct tm *timeinfo;

        // 获取当前时间
        time(&current_time);
        timeinfo = localtime(&current_time);

        // 每小时在设备特定的分钟发布图像
        if(timeinfo->tm_min == send_minute)
        {
            size_t image_base64_size, imageinfo_base64_size;
            char* last_row = read_last_row(IMAGE_LIST_FILE);
            if (last_row == NULL) {
                himix_print_with_time("读取图像列表失败\n");
                sleep(10);
                continue;
            }
            
            sscanf(last_row, "%*d%*[ ]%s", image_name);
            // 安全地拼接图片路径
            char image_path[MAX_SIZE];
            int path_len = snprintf(image_path, sizeof(image_path), "%s%s", IMAGE_FOLDER, image_name);
            if (path_len >= sizeof(image_path)) {
                himix_print_with_time("错误: 图片路径过长\n");
                free(last_row);
                sleep(10);
                continue;
            }
            himix_print_with_time("处理图片: %s\n", image_path);
            
            // 先尝试压缩，失败则使用原始方法
            // image_base64 = compress_image_to_base64(image_path, &image_base64_size, 70);
            // if (image_base64 == NULL) {
            //     himix_print_with_time("图片压缩失败，使用原始转换\n");
            //     image_base64 = image_to_base64(image_path, &image_base64_size);
            // }
            image_base64 = image_to_base64(image_path, &image_base64_size);
            if (image_base64 == NULL) {
                himix_print_with_time("图像转换base64失败\n");
                free(last_row);
                sleep(10);
                continue;
            }
            
            // 将字符串数据编码为Base64
            size_t input_length = strlen(last_row);
            imageinfo_base64 = base64_encode((const unsigned char *)last_row, input_length, &imageinfo_base64_size);
            
            if(image_base64_size > 1024*16) {
                himix_print_with_time("图片较大 (%zu KB)，开始分块发送\n", image_base64_size/1024);
                size_t once_size = 1024*16;
                char round[MAX_ROUND_STR];  // 使用更大的缓冲区
                char *once_data = malloc(once_size + 1);
                if (!once_data) {
                    himix_print_with_time("内存分配失败\n");
                    free(image_base64);
                    free(imageinfo_base64);
                    free(last_row);
                    sleep(10);
                    continue;
                }
                
                size_t total_chunks = (image_base64_size + once_size - 1) / once_size;  // 向上取整
                himix_print_with_time("分为 %zu 块发送\n", total_chunks);
                
                for(int i = 1; i <= total_chunks; i++) {
                    memset(once_data, 0, once_size + 1);
                    size_t current_chunk_size = (i == total_chunks) ? 
                        (image_base64_size - once_size * (i-1)) : once_size;
                    
                    strncpy(once_data, image_base64 + once_size*(i-1), current_chunk_size);
                    once_data[current_chunk_size] = '\0';
                    snprintf(round, sizeof(round), "%d/%zu", i, total_chunks);
                    
                    himix_print_with_time("发送第 %d/%zu 块，大小: %zu bytes\n", i, total_chunks, current_chunk_size);
                    // 使用mosquitto库的publish_msg函数
                    publish_msg_(arg, mosq, IMAGE_TYPE, once_data, current_chunk_size, 
                               imageinfo_base64, imageinfo_base64_size, round);
                                                   // 在发送每个块后添加短暂延迟，例如100毫秒
                    usleep(100000); // 100000 微秒 = 100 毫秒
                }
                free(once_data);
            } else {
                himix_print_with_time("图片太小适中 (%zu KB)，取消发送\n", image_base64_size/1024);

            }
            
            // 清理内存
            free(image_base64);
            free(imageinfo_base64);
            free(last_row);
            
            himix_print_with_time("图片发送完成，等待下一小时\n");
            // 等待到下一小时，避免在同一小时内重复发送
            sleep(3600); // 等待1小时
        }else{
            // 不是发送时间，等待1分钟再检查
            sleep(60);
        }
    }

    // 清理资源
    mosquitto_loop_stop(mosq, false);
    mosquitto_disconnect(mosq);
    mosquitto_destroy(mosq);
    mosquitto_lib_cleanup();

    return NULL;
}

int main(int argc, char *argv[])
{
    // 初始化文件路径
    init_file_paths();

    // 初始化himixPub专用日志
    init_himix_pub_log();

    if (argc < 2) {
        himix_print_with_time("错误: 缺少设备ID参数\n");
        close_himix_pub_log();
        return 1;
    }

    DEVICE_ID = argv[1];
    strcat(UP_TOPIC, DEVICE_ID);
    
    // 检查设备激活状态
    int activation_status = check_activation_status();
    himix_print_with_time("activation_status: %d\n", activation_status);
    switch(activation_status) {
         
        case STATUS_IN_TRIAL:
            himix_print_with_time("设备在试用期内，剩余天数：%d\n", get_remaining_trial_days());
            break;
        case STATUS_ACTIVATED:
            himix_print_with_time("设备已激活，有效期至：%s", get_expiry_date_string());
            break;
        case STATUS_PERMANENT:
            himix_print_with_time("设备已永久激活\n");
            break;
        case STATUS_NEED_ACTIVATE:
            himix_print_with_time("设备需要激活，请联系供应商获取激活码\n");
            close_himix_pub_log();
            return -1;
        case STATUS_NEED_RENEWAL:
            himix_print_with_time("设备激活已过期，请续期\n");
            close_himix_pub_log();
            return -1;
    }
    struct Ip* host_port = get_host_port(HOST_PORT_FILE);
    himix_print_with_time("host_port: %s\n", host_port->host);
    pthread_t displacement_thread[ip_count], image_thread[ip_count];
    for(int i=0; i<ip_count; i++)
    {
        int ret1,ret2;
        // 创建发布形变数据的线程
        himix_print_with_time("创建发布形变数据的线程\n");
        ret1 = pthread_create(&displacement_thread[i], NULL, publish_displacement, &host_port[i]);
        // 创建发布图像的线程
        ret2 = pthread_create(&image_thread[i], NULL, publish_image, &host_port[i]);
        if (ret1 || ret2) {
        // if (ret2) {
            himix_print_with_time("Error creating thread!\n");
            close_himix_pub_log();
            return -1;
        }
    }

    // 等待线程结束
    for (int i = 0; i < ip_count; i++) {
        pthread_join(displacement_thread[i], NULL);
        pthread_join(image_thread[i], NULL);
    }
    close_himix_pub_log(); // 关闭himixPub日志文件
    return 0;
}
