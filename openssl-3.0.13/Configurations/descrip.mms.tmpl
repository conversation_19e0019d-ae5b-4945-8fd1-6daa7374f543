## descrip.mms to build OpenSSL on OpenVMS
##
## {- join("\n## ", @autowarntext) -}
{-
  use File::Spec::Functions qw/:DEFAULT abs2rel rel2abs/;
  use File::Basename;
  use OpenSSL::Util;

  (our $osslprefix_q = platform->osslprefix()) =~ s/\$/\\\$/;

  our $sover_dirname = platform->shlib_version_as_filename();
  our $osslver = sprintf "%02d", split(/\./, $config{version});

  our $sourcedir = $config{sourcedir};
  our $builddir = $config{builddir};
  sub make_unix_path {
      # Split the native path
      (my $vol, my $dirs, my $file) = File::Spec->splitpath($_[0]);
      my @dirs = File::Spec->splitdir($dirs);

      # Reassemble it as a Unix path
      $vol =~ s|:$||;
      return File::Spec::Unix->catpath(
          '', File::Spec::Unix->catdir('', $vol ? $vol : (), @dirs), $file);
  }
  sub sourcefile {
      catfile($sourcedir, @_);
  }
  sub buildfile {
      catfile($builddir, @_);
  }
  sub sourcedir {
      catdir($sourcedir, @_);
  }
  sub builddir {
      catdir($builddir, @_);
  }
  sub tree {
      (my $x = shift) =~ s|\]$|...]|;
      $x
  }

  # Because we need to make two computations of these data,
  # we store them in arrays for reuse
  our @libs =
      map { platform->staticname($_) }
      @{$unified_info{libraries}};
  our @shlibs =
      map { platform->sharedname($_) // () }
      @{$unified_info{libraries}};
  our @install_libs =
      map { platform->staticname($_) }
      grep { !$unified_info{attributes}->{libraries}->{$_}->{noinst} }
      @{$unified_info{libraries}};
  our @install_shlibs =
      map { platform->sharedname($_) // () }
      grep { !$unified_info{attributes}->{libraries}->{$_}->{noinst} }
      @{$unified_info{libraries}};
  our @install_engines =
      grep { !$unified_info{attributes}->{modules}->{$_}->{noinst}
             && $unified_info{attributes}->{modules}->{$_}->{engine} }
      @{$unified_info{modules}};
  our @install_modules =
      grep { !$unified_info{attributes}->{modules}->{$_}->{noinst}
             && !$unified_info{attributes}->{modules}->{$_}->{engine}
             && !$unified_info{attributes}->{modules}->{$_}->{fips} }
      @{$unified_info{modules}};
  our @install_fipsmodules =
      grep { !$unified_info{attributes}->{modules}->{$_}->{noinst}
             && $unified_info{attributes}->{modules}->{$_}->{fips} }
      @{$unified_info{modules}};
  our @install_programs =
      grep { !$unified_info{attributes}->{programs}->{$_}->{noinst} }
      @{$unified_info{programs}};
  our @install_bin_scripts =
      grep { !$unified_info{attributes}->{scripts}->{$_}->{noinst}
             && !$unified_info{attributes}->{scripts}->{$_}->{misc} }
      @{$unified_info{scripts}};
  our @install_misc_scripts =
      grep { !$unified_info{attributes}->{scripts}->{$_}->{noinst}
             && $unified_info{attributes}->{scripts}->{$_}->{misc} }
      @{$unified_info{scripts}};

  # Configured flags

  our @cnf_asflags = ($target{asflags} || (), @{$config{asflags}});
  our @cnf_defines = (@{$target{defines}}, @{$config{defines}});
  our @cnf_includes = (@{$target{includes}}, @{$config{includes}});
  our @cnf_cppflags = ($target{cppflags} || (), @{$config{cppflags}});
  our @cnf_cflags = ($target{cflags} || (), @{$config{cflags}});
  our @cnf_cxxflags = ($target{cxxflags} || (), @{$config{cxxflags}});
  our @cnf_ldflags = ($target{lflags} || (), @{$config{lflags}});
  our @cnf_ex_libs = (map{ ",$_" } @{$target{ex_libs}}, @{$config{ex_libs}});

  # Variables starting with $lib_ are used to build library object files
  # and shared libraries.
  # Variables starting with $dso_ are used to build DSOs and their object files.
  # Variables starting with $bin_ are used to build programs and their object
  # files.

  # The following array is special and is treated separately from the rest of
  # the lib_ variables.
  our @lib_cppincludes = (@{$target{lib_includes}}, @{$target{shared_includes}},
                          @{$config{lib_includes}}, @{$config{shared_includes}},
                          @cnf_includes);

  our $lib_cppdefines =
      join(',', @{$target{lib_defines}}, @{$target{shared_defines}},
                @{$config{lib_defines}}, @{$config{shared_defines}},
                @cnf_defines,
                'OPENSSLDIR="""$(OPENSSLDIR_C)"""',
                'ENGINESDIR="""$(ENGINESDIR_C)"""',
                'MODULESDIR="""$(MODULESDIR_C)"""'
                )
      . '$(DEFINES)'
      . "'extradefines'";
  our $lib_asflags =
      join(' ', $target{lib_asflags} || (), @{$config{lib_asflags}},
                @cnf_asflags, '$(ASFLAGS)');
  our $lib_cppflags =
      join('', $target{lib_cppflags} || (), $target{shared_cppflags} || (),
               @{$config{lib_cppflags}}, @{$config{shared_cppflag}},
               @cnf_cppflags, '/DEFINE=('.$lib_cppdefines.')', '$(CPPFLAGS)');
  my @lib_cflags = ( $target{lib_cflags} // () );
  my @lib_cflags_no_inst = ( $target{no_inst_lib_cflags} // @lib_cflags );
  my @lib_cflags_cont = ( $target{shared_cflag} || (),
                          @{$config{lib_cflags}}, @{$config{shared_cflag}},
                          @cnf_cflags, '$(CFLAGS)');
  our $lib_cflags = join('', @lib_cflags, @lib_cflags_cont );
  our $lib_cflags_no_inst = join('', @lib_cflags_no_inst, @lib_cflags_cont );
  our $lib_ldflags =
      join('', $target{lib_lflags} || (), $target{shared_ldflag} || (),
               @{$config{lib_lflags}}, @{$config{shared_ldflag}},
               @cnf_ldflags, '$(LDFLAGS)');
  our $lib_ex_libs = join('', @cnf_ex_libs, '$(EX_LIBS)');

  # The following array is special and is treated separately from the rest of
  # the dso_ variables.
  our @dso_cppincludes = (@{$target{dso_includes}}, @{$target{module_includes}},
                          @{$config{dso_includes}}, @{$config{module_includes}},
                          @cnf_includes);

  our $dso_cppdefines =
      join(',', @{$target{dso_defines}}, @{$target{module_defines}},
                @{$config{dso_defines}}, @{$config{module_defines}},
                @cnf_defines,
                )
      . '$(DEFINES)'
      . "'extradefines'";
  our $dso_asflags =
      join(' ', $target{dso_asflags} || (), $target{module_asflags} || (),
                @{$config{dso_asflags}}, @{$config{module_asflags}},
                @cnf_asflags, '$(ASFLAGS)');
  our $dso_cppflags =
      join('', $target{dso_cppflags} || (), $target{module_cppflags} || (),
               @{$config{dso_cppflags}}, @{$config{module_cppflag}},
               @cnf_cppflags,
               '/DEFINE=('.$dso_cppdefines.')',
               '$(CPPFLAGS)');
  my @dso_cflags = ( $target{dso_cflags} // () );
  my @dso_cflags_no_inst = ( $target{no_inst_dso_cflags} // @dso_cflags );
  my @dso_cflags_cont = ( $target{module_cflag} || (),
                          @{$config{dso_cflags}}, @{$config{module_cflag}},
                          @cnf_cflags, '$(CFLAGS)');
  our $dso_cflags = join('', @dso_cflags, @dso_cflags_cont );
  our $dso_cflags_no_inst = join('', @dso_cflags_no_inst, @dso_cflags_cont );
  our $dso_ldflags =
      join('', $target{dso_lflags} || (), $target{module_ldflag} || (),
               @{$config{dso_lflags}}, @{$config{module_ldflag}},
               @cnf_ldflags, '$(LDFLAGS)');
  our $dso_ex_libs = join('', @cnf_ex_libs, '$(EX_LIBS)');

  # The following array is special and is treated separately from the rest of
  # the bin_ variables.
  our @bin_cppincludes = (@{$target{bin_includes}},
                          @{$config{bin_includes}},
                          @cnf_includes);

  our $bin_cppdefines =
      join(',', @{$target{bin_defines}},
                @{$config{bin_defines}},
                @cnf_defines,
                )
      . '$(DEFINES)'
      . "'extradefines'";
  our $bin_asflags =
      join(' ', $target{bin_asflags} || (),
                @{$config{bin_asflags}},
                @cnf_asflags, '$(ASFLAGS)');
  our $bin_cppflags =
      join('', $target{bin_cppflags} || (),
               @{$config{bin_cppflags}},
               @cnf_cppflags,
               '/DEFINE=('.$bin_cppdefines.')',
               '$(CPPFLAGS)');
  my @bin_cflags = ( $target{bin_cflags} // () );
  my @bin_cflags_no_inst = ( $target{no_inst_bin_cflags} // @bin_cflags );
  my @bin_cflags_cont = ( @{$config{bin_cflags}},
                          @cnf_cflags, '$(CFLAGS)');
  our $bin_cflags = join('', @bin_cflags, @bin_cflags_cont );
  our $bin_cflags_no_inst = join('', @bin_cflags_no_inst, @bin_cflags_cont );
  our $bin_ldflags =
      join('', $target{bin_lflags} || (),
               @{$config{bin_lflags}},
               @cnf_ldflags, '$(LDFLAGS)');
  our $bin_ex_libs = join('', @cnf_ex_libs, '$(EX_LIBS)');

  # This is a horrible hack, but is needed because recursive inclusion of files
  # in different directories does not work well with VMS C.  We try to help by
  # specifying extra relative directories.  They must always be in Unix format,
  # relative to the directory where the .c file is located.  The logic is that
  # any inclusion, merged with one of these relative directories, will find the
  # requested inclusion file.
  foreach (grep /\[\.crypto\.async\.arch\].*\.o$/, keys %{$unified_info{sources}}) {
      my $obj = platform->obj($_);
      push @{$unified_info{includes_extra}->{$obj}}, qw(../);
  }
  foreach (grep /\[\.crypto\.ec\.curve448\].*?\.o$/, keys %{$unified_info{sources}}) {
      my $obj = platform->obj($_);
      push @{$unified_info{includes_extra}->{$obj}}, qw(./arch_32 ./arch64);
  }
  foreach (grep /\[\.crypto\.ec\.curve448.arch_(?:32|64)\].*?\.o$/, keys %{$unified_info{sources}}) {
      my $obj = platform->obj($_);
      push @{$unified_info{includes_extra}->{$obj}}, qw(../);
  }
  foreach (grep /\[\.ssl\.(?:record|statem)\].*?\.o$/, keys %{$unified_info{sources}}) {
      my $obj = platform->obj($_);
      # Most of the files in [.ssl.record] and [.ssl.statem] include
      # "../ssl_local.h", which includes things like "record/record.h".
      # Adding "../" as an inclusion directory helps making this sort of header
      # from these directories.
      push @{$unified_info{includes_extra}->{$obj}}, qw(../);

  }
  foreach (grep /\[\.test\].*?\.o$/, keys %{$unified_info{sources}}) {
      my $obj = platform->obj($_);
      push @{$unified_info{includes_extra}->{$obj}}, qw(../ssl ./helpers);
  }
  foreach (grep /\[\.test\.helpers\].*?\.o$/, keys %{$unified_info{sources}}) {
      my $obj = platform->obj($_);
      push @{$unified_info{includes_extra}->{$obj}}, qw(../../ssl);
  }

  # This makes sure things get built in the order they need
  # to. You're welcome.
  sub dependmagic {
      my $target = shift;

      return "$target : build_generated\n\t\pipe \$(MMS) \$(MMSQUALIFIERS) depend && \$(MMS) \$(MMSQUALIFIERS) _$target\n_$target";
  }
  "";
-}
PLATFORM={- $config{target} -}
OPTIONS={- $config{options} -}
CONFIGURE_ARGS=({- join(", ",quotify_l(@{$config{perlargv}})) -})
SRCDIR={- $config{sourcedir} -}
BLDDIR={- $config{builddir} -}
FIPSKEY={- $config{FIPSKEY} -}

# Allow both V and VERBOSE to indicate verbosity.  This only applies
# to testing.
VERBOSE=$(V)
VERBOSE_FAILURE=$(VF)

VERSION={- "$config{full_version}" -}
VERSION_NUMBER={- "$config{version}" -}
MAJOR={- $config{major} -}
MINOR={- $config{minor} -}
SHLIB_VERSION_NUMBER={- $config{shlib_version} -}
SHLIB_TARGET={- $target{shared_target} -}

LIBS={- join(", ", map { "-\n\t".$_.".OLB" } @libs) -}
SHLIBS={- join(", ", map { "-\n\t".$_.".EXE" } @shlibs) -}
MODULES={- join(", ", map { "-\n\t".$_.".EXE" }
                      # Drop all modules that are dependencies, they will
                      # be processed through their dependents
                      grep { my $x = $_;
                             !grep { grep { $_ eq $x } @$_ }
                                   values %{$unified_info{depends}} }
                      @{$unified_info{modules}}) -}
FIPSMODULE={- # We do some extra checking here, as there should be only one
              use File::Basename;
              our @fipsmodules =
                  grep { !$unified_info{attributes}->{modules}->{$_}->{noinst}
                         && $unified_info{attributes}->{modules}->{$_}->{fips} }
                  @{$unified_info{modules}};
              die "More that one FIPS module" if scalar @fipsmodules > 1;
              join(" ", map { platform->dso($_) } @fipsmodules) -}
FIPSMODULENAME={- die "More that one FIPS module" if scalar @fipsmodules > 1;
                  join(", ", map { basename(platform->dso($_)) } @fipsmodules) -}
PROGRAMS={- join(", ", map { "-\n\t".$_.".EXE" } @{$unified_info{programs}}) -}
SCRIPTS={- join(", ", map { "-\n\t".$_ } @{$unified_info{scripts}}) -}
{- output_off() if $disabled{makedepend}; "" -}
DEPS={- our @deps = map { platform->isobj($_) ? platform->dep($_) : $_ }
                    grep { $unified_info{sources}->{$_}->[0] =~ /\.c$/ }
                    keys %{$unified_info{sources}};
        join(", ", map { "-\n\t".$_ } @deps); -}
{- output_on() if $disabled{makedepend}; "" -}
GENERATED_MANDATORY={- join(", ",
                            map { "-\n\t".$_ } @{$unified_info{depends}->{""}} ) -}
GENERATED_PODS={- # common0.tmpl provides @generated
                  join(", ",
                       map { my $x = $_;
                             (
                               grep { 
                                      $unified_info{attributes}->{depends}
                                      ->{$x}->{$_}->{pod} // 0
                                    }
                                   keys %{$unified_info{attributes}->{depends}->{$x}}
                             ) ? "-\n\t".$x : ();
                           }
                           @generated) -}
GENERATED={- # common0.tmpl provides @generated
             join(", ", map { platform->convertext($_) } @generated) -}

INSTALL_LIBS={- join(", ", map { "-\n\t".$_.".OLB" } @install_libs) -}
INSTALL_SHLIBS={- join(", ", map { "-\n\t".$_.".EXE" } @install_shlibs) -}
INSTALL_ENGINES={- join(", ", map { "-\n\t".$_.".EXE" } @install_engines) -}
INSTALL_MODULES={- join(", ", map { "-\n\t".$_.".EXE" } @install_modules) -}
INSTALL_FIPSMODULE={- join(", ", map { "-\n\t".$_.".EXE" } @install_fipsmodules) -}
INSTALL_FIPSMODULECONF=[.providers]fipsmodule.cnf
INSTALL_PROGRAMS={- join(", ", map { "-\n\t".$_.".EXE" } @install_programs) -}
BIN_SCRIPTS={- join(", ", @install_bin_scripts) -}
MISC_SCRIPTS={- join(", ", @install_misc_scripts) -}
HTMLDOCS1={- join(", ", map { "-\n\t".$_ } @{$unified_info{htmldocs}->{man1}}) -}
HTMLDOCS3={- join(", ", map { "-\n\t".$_ } @{$unified_info{htmldocs}->{man3}}) -}
HTMLDOCS5={- join(", ", map { "-\n\t".$_ } @{$unified_info{htmldocs}->{man5}}) -}
HTMLDOCS7={- join(", ", map { "-\n\t".$_ } @{$unified_info{htmldocs}->{man7}}) -}

APPS_OPENSSL="{- use File::Spec::Functions;
                 catfile("apps","openssl") -}"

# DESTDIR is for package builders so that they can configure for, say,
# SYS$COMMON:[OPENSSL] and yet have everything installed in STAGING:[USER].
# In that case, configure with --prefix=SYS$COMMON:[OPENSSL] and then run
# MMS with /MACROS=(DESTDIR=STAGING:[USER]).  The result will end up in
# STAGING:[USER.OPENSSL].
# Normally it is left empty.
DESTDIR=

# Do not edit this manually. Use Configure --prefix=DIR to change this!
INSTALLTOP={- our $installtop =
                  catdir($config{prefix}) || "SYS\$COMMON:[OPENSSL]";
              $installtop -}
SYSTARTUP={- catdir($installtop, '[.SYS$STARTUP]'); -}
# This is the standard central area to store certificates, private keys...
OPENSSLDIR={- catdir($config{openssldir}) or
              $config{prefix} ? catdir($config{prefix},"COMMON")
                              : "SYS\$COMMON:[OPENSSL-COMMON]" -}
# The same, but for C
OPENSSLDIR_C={- platform->osslprefix() -}DATAROOT:[000000]
# Where installed ENGINE modules reside, for C
ENGINESDIR_C={- platform->osslprefix() -}ENGINES{- $sover_dirname.$target{pointer_size} -}:
# Where modules reside, for C
MODULESDIR_C={- platform->osslprefix() -}MODULES{- $target{pointer_size} -}:

##### User defined commands and flags ################################

CC={- $config{CC} -}
CPP={- $config{CPP} -}
DEFINES={- our $defines = join('', map { ",$_" } @{$config{CPPDEFINES}}) -}
#INCLUDES={- our $includes = join(',', @{$config{CPPINCLUDES}}) -}
CPPFLAGS={- our $cppflags = join('', @{$config{CPPFLAGS}}) -}
CFLAGS={- join('', @{$config{CFLAGS}}) -}
LDFLAGS={- join('', @{$config{LFLAGS}}) -}
EX_LIBS={- join('', map { ",$_" } @{$config{LDLIBS}}) -}

PERL={- $config{PERL} -}

AS={- $config{AS} -}
ASFLAGS={- join(' ', @{$config{ASFLAGS}}) -}

##### Special command flags ##########################################

ASOUTFLAG={- $target{asoutflag} -}$(OSSL_EMPTY)

PERLASM_SCHEME={- $target{perlasm_scheme} -}

# CPPFLAGS_Q is used for one thing only: to build up buildinf.h
CPPFLAGS_Q={- (my $c = $lib_cppflags.$cppflags) =~ s|"|""|g;
              (my $d = $lib_cppdefines) =~ s|"|""|g;
              my $i = join(',', @lib_cppincludes || (), '$(INCLUDES)');
              my $x = $c;
              $x .= "/INCLUDE=($i)" if $i;
              $x .= "/DEFINE=($d)" if $d;
              $x; -}

# .FIRST and .LAST are special targets with MMS and MMK.
NODEBUG=@
.FIRST :
        {- join( "\n        \$(NODEBUG) ", @{ $target{setup_commands} // [] },
                                           '!' ) -}
        $(NODEBUG) sourcetop = F$PARSE("$(SRCDIR)","[]A.;",,,"SYNTAX_ONLY,NO_CONCEAL") - ".][000000" - "[000000." - "][" - "]A.;" + ".]"
        $(NODEBUG) DEFINE ossl_sourceroot 'sourcetop'
        $(NODEBUG) !
        $(NODEBUG) staging_dir = "$(DESTDIR)"
        $(NODEBUG) staging_instdir = ""
        $(NODEBUG) staging_datadir = ""
        $(NODEBUG) IF staging_dir .NES. "" THEN -
                staging_instdir = F$PARSE("A.;",staging_dir,"[]",,"SYNTAX_ONLY")
        $(NODEBUG) IF staging_instdir - "]A.;" .NES. staging_instdir THEN -
                staging_instdir = staging_instdir - "]A.;" + ".OPENSSL-INSTALL]"
        $(NODEBUG) IF staging_instdir - "A.;" .NES. staging_instdir THEN -
                staging_instdir = staging_instdir - "A.;" + "[OPENSSL-INSTALL]"
        $(NODEBUG) IF staging_dir .NES. "" THEN -
                staging_datadir = F$PARSE("A.;",staging_dir,"[]",,"SYNTAX_ONLY")
        $(NODEBUG) IF staging_datadir - "]A.;" .NES. staging_datadir THEN -
                staging_datadir = staging_datadir - "]A.;" + ".OPENSSL-COMMON]"
        $(NODEBUG) IF staging_datadir - "A.;" .NES. staging_datadir THEN -
                staging_datadir = staging_datadir - "A.;" + "[OPENSSL-COMMON]"
        $(NODEBUG) !
        $(NODEBUG) ! Installation logical names
        $(NODEBUG) !
        $(NODEBUG) ! This also creates a few DCL variables that are used for
        $(NODEBUG) ! the "install_msg" target.
        $(NODEBUG) !
        $(NODEBUG) installroot = F$PARSE(staging_instdir,"$(INSTALLTOP)","[]A.;",,"SYNTAX_ONLY,NO_CONCEAL") - ".][000000" - "[000000." - "][" - "]A.;"
        $(NODEBUG) installtop = installroot + ".]"
        $(NODEBUG) dataroot = F$PARSE(staging_datadir,"$(OPENSSLDIR)","[]A.;",,"SYNTAX_ONLY,NO_CONCEAL") - ".][000000" - "[000000." - "][" - "]A.;"
        $(NODEBUG) datatop = dataroot + ".]"
        $(NODEBUG) DEFINE ossl_installroot 'installtop'
        $(NODEBUG) DEFINE ossl_dataroot 'datatop'
        $(NODEBUG) !
        $(NODEBUG) ! Override disturbing system logicals.  We can't deassign
        $(NODEBUG) ! them, so we create it instead.  This is an unfortunate
        $(NODEBUG) ! necessity.
        $(NODEBUG) !
        $(NODEBUG) openssl_inc1 = F$PARSE("[.include.openssl]","A.;",,,"syntax_only") - "A.;"
        $(NODEBUG) openssl_inc2 = F$PARSE("sourcetop:[include.openssl]","A.;",,,"SYNTAX_ONLY") - "A.;"
        $(NODEBUG) DEFINE openssl 'openssl_inc1','openssl_inc2'
        $(NODEBUG) !
        $(NODEBUG) ! Figure out the architecture
        $(NODEBUG) !
        $(NODEBUG) arch = f$edit( f$getsyi( "arch_name"), "upcase")
        $(NODEBUG) !
        $(NODEBUG) ! Set up logical names for the libraries, so LINK and
        $(NODEBUG) ! running programs can use them.
        $(NODEBUG) !
        $(NODEBUG) {- join("\n\t\$(NODEBUG) ", map { "DEFINE ".uc($_)." 'F\$ENV(\"DEFAULT\")'".uc($_)."\$(SHLIB_EXT)" } @shlibs) || "!" -}

.LAST :
        $(NODEBUG) {- join("\n\t\$(NODEBUG) ", map { "DEASSIGN ".uc($_) } @shlibs) || "!" -}
        $(NODEBUG) DEASSIGN openssl
        $(NODEBUG) DEASSIGN ossl_dataroot
        $(NODEBUG) DEASSIGN ossl_installroot
        $(NODEBUG) DEASSIGN ossl_sourceroot
.DEFAULT :
        @ ! MMS cannot handle no actions...

# The main targets ###################################################

{- dependmagic('build_sw'); -} : build_libs_nodep, build_modules_nodep, build_programs_nodep
{- dependmagic('build_libs'); -} : build_libs_nodep
{- dependmagic('build_modules'); -} : build_modules_nodep
{- dependmagic('build_programs'); -} : build_programs_nodep

build_generated_pods : $(GENERATED_PODS)
build_docs : build_html_docs
build_html_docs : $(HTMLDOCS1) $(HTMLDOCS3) $(HTMLDOCS5) $(HTMLDOCS7)

build_generated : $(GENERATED_MANDATORY)
build_libs_nodep : $(LIBS), $(SHLIBS)
build_modules_nodep : $(MODULES)
build_programs_nodep : $(PROGRAMS), $(SCRIPTS)

# Kept around for backward compatibility
build_apps build_tests : build_programs

# Convenience target to prebuild all generated files, not just the mandatory
# ones
build_all_generated : $(GENERATED_MANDATORY) $(GENERATED) build_docs
	@ ! {- output_off() if $disabled{makedepend}; "" -}
	@ WRITE SYS$OUTPUT "Warning: consider configuring with no-makedepend, because if"
	@ WRITE SYS$OUTPUT "         target system doesn't have $(PERL),"
	@ WRITE SYS$OUTPUT "         then make will fail..."
	@ ! {- output_on() if $disabled{makedepend}; "" -}

all : build_sw build_docs

test : tests
{- dependmagic('tests'); -} : build_programs_nodep, build_modules_nodep
	$(MMS) $(MMSQUALIFIERS) run_tests
run_tests :
        @ ! {- output_off() if $disabled{tests}; "" -}
        DEFINE SRCTOP "$(SRCDIR)"
        DEFINE BLDTOP "$(BLDDIR)"
        DEFINE FIPSKEY "$(FIPSKEY)"
        IF "$(VERBOSE)" .NES. "" THEN DEFINE VERBOSE "$(VERBOSE)"
        $(PERL) {- sourcefile("test", "run_tests.pl") -} $(TESTS)
        DEASSIGN BLDTOP
        DEASSIGN SRCTOP
        DEASSIGN FIPSKEY
        @ ! {- if ($disabled{tests}) { output_on(); } else { output_off(); } "" -}
        @ WRITE SYS$OUTPUT "Tests are not supported with your chosen Configure options"
        @ ! {- output_on() if !$disabled{tests}; "" -}

list-tests :
        @ ! {- output_off() if $disabled{tests}; "" -}
        @ DEFINE SRCTOP "$(SRCDIR)"
        @ $(PERL) {- sourcefile("test", "run_tests.pl") -} list
        @ DEASSIGN SRCTOP
        @ ! {- if ($disabled{tests}) { output_on(); } else { output_off(); } "" -}
        @ WRITE SYS$OUTPUT "Tests are not supported with your chosen Configure options"
        @ ! {- output_on() if !$disabled{tests}; "" -}

install : install_sw install_ssldirs install_docs {- $disabled{fips} ? "" : "install_fips" -} install_msg

install_msg :
        @ WRITE SYS$OUTPUT ""
        @ WRITE SYS$OUTPUT "######################################################################"
        @ WRITE SYS$OUTPUT ""
        @ IF "$(DESTDIR)" .EQS. "" THEN -
             @{- sourcefile("VMS", "msg_install.com") -} "$(SYSTARTUP)" "{- $osslver -}"
        @ IF "$(DESTDIR)" .NES. "" THEN -
             @{- sourcefile("VMS", "msg_staging.com") -} -
             "''installroot']" "''dataroot']" "$(INSTALLTOP)" "$(OPENSSLDIR)" -
             "$(SYSTARTUP)" "{- $osslver -}"

check_install :
        spawn/nolog @ossl_installroot:[SYSTEST]openssl_ivp{- $osslver -}.com

uninstall : uninstall_docs uninstall_sw {- $disabled{fips} ? "" : "uninstall_fips" -}

# Because VMS wants the generation number (or *) to delete files, we can't
# use $(LIBS), $(PROGRAMS), $(GENERATED) and $(MODULES) directly.
libclean :
        {- join("\n\t", map { "- DELETE $_.OLB;*" } @libs) || "@ !" -}
        {- join("\n\t", map { "- DELETE $_.EXE;*,$_.MAP;*" } @shlibs) || "@ !" -}

clean : libclean
        {- join("\n\t", map { "- DELETE $_;*" } @{$unified_info{htmldocs}->{man1}}) || "@ !" -}
        {- join("\n\t", map { "- DELETE $_;*" } @{$unified_info{htmldocs}->{man3}}) || "@ !" -}
        {- join("\n\t", map { "- DELETE $_;*" } @{$unified_info{htmldocs}->{man5}}) || "@ !" -}
        {- join("\n\t", map { "- DELETE $_;*" } @{$unified_info{htmldocs}->{man7}}) || "@ !" -}
        {- join("\n\t", map { "- DELETE $_.EXE;*,$_.OPT;*" } @{$unified_info{programs}}) || "@ !" -}
        {- join("\n\t", map { "- DELETE $_.EXE;*,$_.OPT;*" } @{$unified_info{modules}}) || "@ !" -}
        {- join("\n\t", map { "- DELETE $_;*" } @{$unified_info{scripts}}) || "@ !" -}
        {- join("\n\t", map { "- DELETE $_;*" } @{$unified_info{depends}->{""}}) || "@ !" -}
        {- join("\n\t", map { "- DELETE $_;*" } @generated) || "@ !" -}
        - DELETE [...]*.MAP;*
        - DELETE [...]*.D;*
        - DELETE [...]*.OBJ;*,*.LIS;*
        - DELETE []CXX$DEMANGLER_DB.;*
        - DELETE [.VMS]openssl_startup.com;*
        - DELETE [.VMS]openssl_shutdown.com;*
        - DELETE []vmsconfig.pm;*

distclean : clean
        - DELETE [.include.openssl]configuration.h;*
        - DELETE configdata.pm;*
        - DELETE descrip.mms;*

depend : descrip.mms
	@ ! {- output_off() if $disabled{makedepend}; "" -}
	@ $(PERL) {- sourcefile("util", "add-depends.pl") -} "{- $config{makedep_scheme} -}"
	@ ! {- output_on() if $disabled{makedepend}; "" -}

# Install helper targets #############################################

install_sw : install_dev install_engines install_modules -
             install_runtime install_startup install_ivp

uninstall_sw : uninstall_dev uninstall_modules uninstall_engines -
               uninstall_runtime uninstall_startup uninstall_ivp

install_docs : install_html_docs

uninstall_docs : uninstall_html_docs

{- output_off() if $disabled{fips}; "" -}
install_fips : build_sw $(INSTALL_FIPSMODULECONF)
	@ WRITE SYS$OUTPUT "*** Installing FIPS module"
	- CREATE/DIR ossl_installroot:[MODULES{- $target{pointer_size} -}.'arch']
	- CREATE/DIR/PROT=(S:RWED,O:RWE,G:RE,W:RE) OSSL_DATAROOT:[000000]
	COPY/PROT=W:RE $(INSTALL_FIPSMODULES) -
                ossl_installroot:[MODULES{- $target{pointer_size} -}.'arch']$(FIPSMODULENAME)
	@ WRITE SYS$OUTPUT "*** Installing FIPS module configuration"
	COPY/PROT=W:RE $(INSTALL_FIPSMODULECONF) OSSL_DATAROOT:[000000]

uninstall_fips :
	@ WRITE SYS$OUTPUT "*** Uninstalling FIPS module configuration"
	DELETE OSSL_DATAROOT:[000000]fipsmodule.cnf;*
	@ WRITE SYS$OUTPUT "*** Uninstalling FIPS module"
	DELETE ossl_installroot:[MODULES{- $target{pointer_size} -}.'arch']$(FIPSMODULENAME);*
{- output_on() if $disabled{fips}; "" -}

install_ssldirs : check_INSTALLTOP
        - CREATE/DIR/PROT=(S:RWED,O:RWE,G:RE,W:RE) OSSL_DATAROOT:[000000]
        IF F$SEARCH("OSSL_DATAROOT:[000000]CERTS.DIR;1") .EQS. "" THEN -
                CREATE/DIR/PROT=(S:RWED,O:RWE,G:RE,W:RE) OSSL_DATAROOT:[CERTS]
        IF F$SEARCH("OSSL_DATAROOT:[000000]PRIVATE.DIR;1") .EQS. "" THEN -
                CREATE/DIR/PROT=(S:RWED,O:RWE,G,W) OSSL_DATAROOT:[PRIVATE]
        IF F$SEARCH("OSSL_DATAROOT:[000000]MISC.DIR;1") .EQS. "" THEN -
                CREATE/DIR/PROT=(S:RWED,O:RWE,G,W) OSSL_DATAROOT:[MISC]
        COPY/PROT=W:RE $(MISC_SCRIPTS) OSSL_DATAROOT:[MISC]
        @ ! Install configuration file
        COPY/PROT=W:R {- sourcefile("apps", "openssl-vms.cnf") -} -
                ossl_dataroot:[000000]openssl.cnf-dist
        IF F$SEARCH("OSSL_DATAROOT:[000000]openssl.cnf") .EQS. "" THEN -
                COPY/PROT=W:R {- sourcefile("apps", "openssl-vms.cnf") -} -
                        ossl_dataroot:[000000]openssl.cnf
        @ ! Install CTLOG configuration file
        COPY/PROT=W:R {- sourcefile("apps", "ct_log_list.cnf") -} -
                ossl_dataroot:[000000]ct_log_list.cnf-dist
        IF F$SEARCH("OSSL_DATAROOT:[000000]ct_log_list.cnf") .EQS. "" THEN -
                COPY/PROT=W:R {- sourcefile("apps", "ct_log_list.cnf") -} -
                        ossl_dataroot:[000000]ct_log_list.cnf

install_dev : check_INSTALLTOP install_runtime_libs
        @ WRITE SYS$OUTPUT "*** Installing development files"
        @ ! Install header files
        - CREATE/DIR ossl_installroot:[include.openssl]
        COPY/PROT=W:R ossl_sourceroot:[include.openssl]*.h -
                ossl_installroot:[include.openssl]
        COPY/PROT=W:R [.include.openssl]*.h ossl_installroot:[include.openssl]
        @ ! Install static (development) libraries
        - CREATE/DIR ossl_installroot:[LIB.'arch']
        {- join("\n        ",
                map { "COPY/PROT=W:R $_.OLB ossl_installroot:[LIB.'arch']" }
                @install_libs) -}

install_engines : check_INSTALLTOP install_runtime_libs build_modules
        @ {- output_off() unless scalar @install_engines; "" -} !
        @ WRITE SYS$OUTPUT "*** Installing engines"
        - CREATE/DIR ossl_installroot:[ENGINES{- $sover_dirname.$target{pointer_size} -}.'arch']
        {- join("\n        ",
                map { "COPY/PROT=W:RE $_.EXE ossl_installroot:[ENGINES$sover_dirname$target{pointer_size}.'arch']" }
                @install_engines) -}
        @ {- output_on() unless scalar @install_engines; "" -} !

install_modules : check_INSTALLTOP install_runtime_libs build_modules
        @ {- output_off() unless scalar @install_modules; "" -} !
        @ WRITE SYS$OUTPUT "*** Installing modules"
        - CREATE/DIR ossl_installroot:[MODULES{- $target{pointer_size} -}.'arch']
        {- join("\n        ",
                map { "COPY/PROT=W:RE $_.EXE ossl_installroot:[MODULES$target{pointer_size}.'arch']" }
                @install_modules) -}
        @ {- output_on() unless scalar @install_modules; "" -} !

install_runtime : install_programs

install_runtime_libs : check_INSTALLTOP build_libs
        @ {- output_off() if $disabled{shared}; "" -} !
        @ WRITE SYS$OUTPUT "*** Installing shareable images"
        @ ! Install shared (runtime) libraries
        - CREATE/DIR ossl_installroot:[LIB.'arch']
        {- join("\n        ",
                map { "COPY/PROT=W:R $_.EXE ossl_installroot:[LIB.'arch']" }
                @install_shlibs) -}
        @ {- output_on() if $disabled{shared}; "" -} !

install_programs : check_INSTALLTOP install_runtime_libs build_programs
        @ {- output_off() if $disabled{apps}; "" -} !
        @ ! Install the main program
        - CREATE/DIR ossl_installroot:[EXE.'arch']
        COPY/PROT=W:RE [.APPS]openssl.EXE -
                ossl_installroot:[EXE.'arch']openssl{- $osslver -}.EXE
        @ ! Install scripts
        COPY/PROT=W:RE $(BIN_SCRIPTS) ossl_installroot:[EXE]
        @ ! {- output_on() if $disabled{apps}; "" -}

install_startup : [.VMS]openssl_startup.com [.VMS]openssl_shutdown.com -
                 [.VMS]openssl_utils.com, check_INSTALLTOP
        - CREATE/DIR ossl_installroot:[SYS$STARTUP]
        COPY/PROT=W:RE [.VMS]openssl_startup.com -
                ossl_installroot:[SYS$STARTUP]openssl_startup{- $osslver -}.com
        COPY/PROT=W:RE [.VMS]openssl_shutdown.com -
                ossl_installroot:[SYS$STARTUP]openssl_shutdown{- $osslver -}.com
        COPY/PROT=W:RE [.VMS]openssl_utils.com -
                ossl_installroot:[SYS$STARTUP]openssl_utils{- $osslver -}.com

install_ivp : [.VMS]openssl_ivp.com check_INSTALLTOP
        - CREATE/DIR ossl_installroot:[SYSTEST]
        COPY/PROT=W:RE [.VMS]openssl_ivp.com -
                ossl_installroot:[SYSTEST]openssl_ivp{- $osslver -}.com

[.VMS]openssl_startup.com : vmsconfig.pm {- sourcefile("VMS", "openssl_startup.com.in") -}
        - CREATE/DIR [.VMS]
        $(PERL) "-I." "-Mvmsconfig" {- sourcefile("util", "dofile.pl") -} -
                {- sourcefile("VMS", "openssl_startup.com.in") -} -
                > [.VMS]openssl_startup.com

[.VMS]openssl_utils.com : vmsconfig.pm {- sourcefile("VMS", "openssl_utils.com.in") -}
        - CREATE/DIR [.VMS]
        $(PERL) "-I." "-Mvmsconfig" {- sourcefile("util", "dofile.pl") -} -
                {- sourcefile("VMS", "openssl_utils.com.in") -} -
                > [.VMS]openssl_utils.com

[.VMS]openssl_shutdown.com : vmsconfig.pm {- sourcefile("VMS", "openssl_shutdown.com.in") -}
        - CREATE/DIR [.VMS]
        $(PERL) "-I." "-Mvmsconfig" {- sourcefile("util", "dofile.pl") -} -
                {- sourcefile("VMS", "openssl_shutdown.com.in") -} -
                > [.VMS]openssl_shutdown.com

[.VMS]openssl_ivp.com : vmsconfig.pm {- sourcefile("VMS", "openssl_ivp.com.in") -}
        - CREATE/DIR [.VMS]
        $(PERL) "-I." "-Mvmsconfig" {- sourcefile("util", "dofile.pl") -} -
                {- sourcefile("VMS", "openssl_ivp.com.in") -} -
                > [.VMS]openssl_ivp.com

vmsconfig.pm : configdata.pm
        OPEN/WRITE/SHARE=READ CONFIG []vmsconfig.pm
        WRITE CONFIG "package vmsconfig;"
        WRITE CONFIG "use strict; use warnings;"
        WRITE CONFIG "use Exporter;"
        WRITE CONFIG "our @ISA = qw(Exporter);"
        WRITE CONFIG "our @EXPORT = qw(%config %target %withargs %unified_info %disabled);"
        WRITE CONFIG "our %config = ("
        WRITE CONFIG "  target => '","{- $config{target} -}","',"
        WRITE CONFIG "  version => '","{- $config{version} -}","',"
        WRITE CONFIG "  shlib_version => '","{- $config{shlib_version} -}","',"
        WRITE CONFIG "  shlib_major => '","{- $config{shlib_major} -}","',"
        WRITE CONFIG "  shlib_minor => '","{- $config{shlib_minor} -}","',"
        WRITE CONFIG "  INSTALLTOP => '$(INSTALLTOP)',"
        WRITE CONFIG "  OPENSSLDIR => '$(OPENSSLDIR)',"
        WRITE CONFIG ");"
        WRITE CONFIG "our %target = ("
        WRITE CONFIG "  pointer_size => '","{- $target{pointer_size} -}","',"
        WRITE CONFIG ");"
        WRITE CONFIG "our %disabled = ("
        WRITE CONFIG "  shared => '","{- $disabled{shared} -}","',"
        WRITE CONFIG ");"
        WRITE CONFIG "our %withargs = ();"
        WRITE CONFIG "our %unified_info = ();"
        WRITE CONFIG "1;"
        CLOSE CONFIG

install_html_docs : check_INSTALLTOP build_html_docs
        @ WRITE SYS$OUTPUT "*** Installing HTML docs"
        - CREATE/DIR ossl_installroot:[HTML.MAN1]
        - CREATE/DIR ossl_installroot:[HTML.MAN3]
        - CREATE/DIR ossl_installroot:[HTML.MAN5]
        - CREATE/DIR ossl_installroot:[HTML.MAN7]
        {- join("\n        ",
                ( map { "COPY/PROT=W:RE $_ ossl_installroot:[HTML.MAN1]" }
                  @{$unified_info{htmldocs}->{man1}} ),
                ( map { "COPY/PROT=W:RE $_ ossl_installroot:[HTML.MAN3]" }
                  @{$unified_info{htmldocs}->{man3}} ),
                ( map { "COPY/PROT=W:RE $_ ossl_installroot:[HTML.MAN5]" }
                  @{$unified_info{htmldocs}->{man5}} ),
                ( map { "COPY/PROT=W:RE $_ ossl_installroot:[HTML.MAN7]" }
                  @{$unified_info{htmldocs}->{man7}} )) -}

check_INSTALLTOP :
        @ IF "$(INSTALLTOP)" .EQS. "" THEN -
                WRITE SYS$ERROR "INSTALLTOP should not be empty"
        @ IF "$(INSTALLTOP)" .EQS. "" THEN -
                EXIT %x10000002

# Developer targets ##################################################

debug_logicals :
        SH LOGICAL/PROC openssl,internal,ossl_installroot,ossl_dataroot

# Building targets ###################################################

descrip.mms : configdata.pm {- join(" ", @{$config{build_file_templates}}) -}
	perl configdata.pm
        @ WRITE SYS$OUTPUT "*************************************************"
        @ WRITE SYS$OUTPUT "***                                           ***"
        @ WRITE SYS$OUTPUT "***   Please run the same mms command again   ***"
        @ WRITE SYS$OUTPUT "***                                           ***"
        @ WRITE SYS$OUTPUT "*************************************************"
        @ PIPE ( EXIT %X10000000 )

configdata.pm : $(SRCDIR)Configure $(SRCDIR)config.com {- join(" ", @{$config{build_infos}}, @{$config{conf_files}}) -}
        perl configdata.pm -r
        @ WRITE SYS$OUTPUT "*************************************************"
        @ WRITE SYS$OUTPUT "***                                           ***"
        @ WRITE SYS$OUTPUT "***   Please run the same mms command again   ***"
        @ WRITE SYS$OUTPUT "***                                           ***"
        @ WRITE SYS$OUTPUT "*************************************************"
        @ PIPE ( EXIT %X10000000 )

reconfigure reconf :
	perl configdata.pm -r

{-
  use File::Basename;
  use File::Spec::Functions qw/abs2rel rel2abs catfile catdir/;
  use File::Spec::Unix;

  # Helper function to convert dependencies in platform agnostic form to
  # dependencies in platform form.
  sub compute_platform_depends {
      map { my $x = $_;

            grep { $x eq $_ } @{$unified_info{programs}} and platform->bin($x)
            or grep { $x eq $_ } @{$unified_info{modules}} and platform->dso($x)
            or grep { $x eq $_ } @{$unified_info{libraries}} and platform->lib($x)
            or platform->convertext($x); } @_;
  }

  # Helper function to figure out dependencies on libraries
  # It takes a list of library names and outputs a list of dependencies
  sub compute_lib_depends {
      # Depending on shared libraries:
      # On Windows POSIX layers, we depend on {libname}.dll.a
      # On Unix platforms, we depend on {shlibname}.so
      return map {
          { lib   => platform->sharedlib($_) // platform->staticlib($_),
            attrs => $unified_info{attributes}->{libraries}->{$_} }
      } @_;
  }

  # Helper function to deal with inclusion directory specs.
  # We're dealing with two issues:
  # 1. A lot of include directory specs take up a lot of command line real
  #    estate, and the DCL command line is very limited (2KiB).
  # 2. For optimal usage, include directory paths must be in Unix form,
  #    that's the only way the C compiler can merge multiple include paths
  #    in a sane way (we can stop worrying about 1.h including foo/2.h
  #    including ../3.h).
  #
  # To resolve 1, we need to create a file with include directory pragmas,
  # and let the compiler use it with /FIRST_INCLUDE=.
  # To resolve 2, we convert all include directory specs we get to Unix,
  # with available File::Spec functions.
  #
  # We use CRC-24 from https://tools.ietf.org/html/rfc4880#section-6,
  # reimplemented in Perl to get a workable and constant file name for each
  # combination of include directory specs.  It is assumed that the order of
  # these directories don't matter.
  #
  # This function takes as input a list of include directories
  # This function returns a list two things:
  # 1. The file name to use with /FIRST_INCLUDE=
  # 2. Text to insert into descrip.mms (may be the empty string)
  sub crc24 {
      my $input = shift;

      my $init_value = 0x00B704CE;
      my $poly_value = 0x01864CFB;

      my $crc = $init_value;

      foreach my $x (unpack ('C*', $input)) {
          $crc ^= $x << 16;

          for (my $i; $i < 8; $i++) {
              $crc <<= 1;
              if ($crc & 0x01000000) {
                  $crc ^= $poly_value;
              }
          }
      }
      $crc &= 0xFFFFFF;

      return $crc;
  }
  my %includefile_cache;
  sub make_includefile {
      my %dirs = map {
          my $udir = make_unix_path(rel2abs($_));

          $udir => 1;
      } @_;
      my @dirs = sort keys %dirs;
      my $filename = sprintf 'incdirs_%x.h', crc24(join(',', @dirs));

      if ($includefile_cache{$filename}) {
          return ($filename, "");
      }

      my $scripture = <<"EOF";
$filename :
	open/write inc_output $filename
EOF
      foreach (@dirs) {
          $scripture .= <<"EOF";
	write inc_output "#pragma include_directory ""$_"""
EOF
      }
      $scripture .= <<"EOF";
	close inc_output
EOF
      $includefile_cache{$filename} = $scripture;

      return ($filename, $scripture);
  }

  # On VMS, (some) header file directories include the files
  # __DECC_INCLUDE_EPILOGUE.H and __DECC_INCLUDE_PROLOGUE.H.
  # When header files are generated, and the build directory
  # isn't the same as the source directory, these files must
  # be copied alongside the generated header file, or their
  # effect will be lost.
  # We use the same include file cache as make_includefile
  # to check if the scripture to copy these files has already
  # been generated.
  sub make_decc_include_files {
      my $outd = shift;
      my $ind = shift;

      # If the build directory and the source directory are the
      # same, there's no need to copy the prologue and epilogue
      # files.
      return ('') if $outd eq $ind;

      my $outprologue = catfile($outd, '__DECC_INCLUDE_PROLOGUE.H');
      my $outepilogue = catfile($outd, '__DECC_INCLUDE_EPILOGUE.H');
      my $inprologue = catfile($ind, '__DECC_INCLUDE_PROLOGUE.H');
      my $inepilogue = catfile($ind, '__DECC_INCLUDE_EPILOGUE.H');
      my @filenames = ();
      my $scripture = '';

      if ($includefile_cache{$outprologue}) {
          push @filenames, $outprologue;
      } elsif (-f $inprologue) {
          my $local_scripture .= <<"EOF";
$outprologue : $inprologue
	COPY $inprologue $outprologue
EOF
          $includefile_cache{$outprologue} = $local_scripture;

          push @filenames, $outprologue;
          $scripture .= $local_scripture;
      }
      if ($includefile_cache{$outepilogue}) {
          push @filenames, $outepilogue;
      } elsif (-f $inepilogue) {
          my $local_scripture .= <<"EOF";
$outepilogue : $inepilogue
	COPY $inepilogue $outepilogue
EOF
          $includefile_cache{$outepilogue} = $local_scripture;

          push @filenames, $outepilogue;
          $scripture .= $local_scripture;
      }

      return (@filenames, $scripture);
  }

  sub generatetarget {
      my %args = @_;
      my $deps = join(" ", compute_platform_depends(@{$args{deps}}));
      return <<"EOF";
$args{target} : $deps
EOF
  }

  sub generatesrc {
      my %args = @_;
      my $gen0 = $args{generator}->[0];
      my $gen_args = join('', map { " $_" }
                              @{$args{generator}}[1..$#{$args{generator}}]);
      my $gen_incs = join("", map { ' "-I'.$_.'"' } @{$args{generator_incs}});
      my $deps = join(", -\n\t\t",
                      compute_platform_depends(@{$args{generator_deps}},
                                               @{$args{deps}}));

      if ($args{src} =~ /\.html$/) {
          #
          # HTML generator
          #
          my $title = basename($args{src}, ".html");
          my $pod = $gen0;
          my $mkpod2html = sourcefile('util', 'mkpod2html.pl');
          my $srcdoc = sourcedir('doc');
          return <<"EOF";
$args{src} : $pod
	\$(PERL) $mkpod2html -i $pod -o \$\@ -t "$title" -r "$srcdoc"
EOF
      } elsif ($args{src} =~ /\.(\d)$/) {
          #
          # Man-page generator, on VMS we simply ignore man-pages
          #
          return "";
      } elsif (platform->isdef($args{src})) {
          #
          # Linker script-ish generator
          #
          my $target = platform->def($args{src});
          my $mkdef = sourcefile('util', 'mkdef.pl');
          my $ord_ver = $args{intent} eq 'lib' ? ' --version $(VERSION_NUMBER)' : '';
          my $ord_name =
              $args{generator}->[1] || basename($args{product}, '.EXE');
          my $case_insensitive =
              $target{$args{intent}.'_cflags'} =~ m|/NAMES=[^/]*AS_IS|i
              ? '' : ' --case-insensitive';
          return <<"EOF";
$target : $gen0 $deps $mkdef
	\$(PERL) $mkdef$ord_ver --type $args{intent} --ordinals $gen0 --name $ord_name "--OS" "VMS"$case_insensitive > $target
EOF
      } elsif (platform->isasm($args{src})
               || platform->iscppasm($args{src})) {
          #
          # Assembler generator
          #
          my $cppflags =
              { shlib => "$lib_cflags $lib_cppflags",
                lib => "$lib_cflags $lib_cppflags",
                dso => "$dso_cflags $dso_cppflags",
                bin => "$bin_cflags $bin_cppflags" } -> {$args{intent}};
          my $defs = join("", map { ",".$_ } @{$args{defs}});
          my $target = platform->isasm($args{src})
                       ? platform->asm($args{src})
                       : $args{src};

          my $generator;
          if ($gen0 =~ /\.pl$/) {
              $generator = '$(PERL)'.$gen_incs.' '.$gen0.$gen_args
                  .' '.$cppflags;
          } elsif ($gen0 =~ /\.S$/) {
              $generator = undef;
          } else {
              die "Generator type for $src unknown: $gen0.$gen_args\n";
          }

          if (defined($generator)) {
              return <<"EOF";
$target : $gen0 $deps
        \@ extradefines = "$defs"
	$generator \$\@
        \@ DELETE/SYMBOL/LOCAL extradefines
EOF
          }
          return <<"EOF";
$target : $gen0 $deps
        \@ extradefines = "$defs"
        PIPE \$(CPP) $cppflags $gen0 | -
        \$(PERL) "-ne" "/^#(\\s*line)?\\s*[0-9]+\\s+""/ or print" > \$\@
        \@ DELETE/SYMBOL/LOCAL extradefines
EOF
      } elsif ($gen0 =~ m|^.*\.in$|) {
          #
          # "dofile" generator (file.in -> file)
          #
          my $dofile = abs2rel(rel2abs(catfile($config{sourcedir},
                                               "util", "dofile.pl")),
                               rel2abs($config{builddir}));
          my @perlmodules = ( 'configdata.pm',
                              grep { $_ =~ m|\.pm$| } @{$args{deps}} );
          my %perlmoduleincs = map { '"-I'.dirname($_).'"' => 1 } @perlmodules;
          my @decc_include_data
              = make_decc_include_files(dirname($args{src}), dirname($gen0));
          my $decc_include_scripture = pop @decc_include_data;
          $deps = join(' ', $deps, @decc_include_data,
                            compute_platform_depends(@perlmodules));
          @perlmodules = map { '"-M'.basename($_, '.pm').'"' } @perlmodules;
          my $perlmodules = join(' ', '', sort keys %perlmoduleincs, @perlmodules);

          return <<"EOF";
$args{src} : $gen0 $deps
	\$(PERL)$perlmodules $dofile "-o$target{build_file}" $gen0$gen_args > \$\@
$decc_include_scripture
EOF
      } elsif (grep { $_ eq $gen0 } @{$unified_info{programs}}) {
          #
          # Generic generator using OpenSSL programs
          #

          # Redo $gen0, to ensure that we have the proper extension
          $gen0 = platform->bin($gen0);
          return <<"EOF";
$args{src} : $gen0 $deps
	PIPE MCR $gen0$gen_args > \$@
EOF
      } else {
          #
          # Generic generator using Perl
          #
          return <<"EOF";
$args{src} : $gen0 $deps
	\$(PERL)$gen_incs $gen0$gen_args > \$\@
EOF
      }
  }

  sub src2obj {
      my $asmext = platform->asmext();
      my %args = @_;
      my @srcs =
          map { my $x = $_;
                (platform->isasm($x) && grep { $x eq $_ } @generated)
                ? platform->asm($x) : $x }
          ( @{$args{srcs}} );
      my $obj = platform->obj($args{obj});
      my $dep = platform->dep($args{obj});
      my $deps = join(", -\n\t\t", @srcs, @{$args{deps}});

      # Because VMS C isn't very good at combining a /INCLUDE path with
      # #includes having a relative directory (like '#include "../foo.h"),
      # the best choice is to move to the first source file's intended
      # directory before compiling, and make sure to write the object file
      # in the correct position (important when the object tree is other
      # than the source tree).
      my $forward = dirname($args{srcs}->[0]);
      my $backward = abs2rel(rel2abs("."), rel2abs($forward));
      my $objd = abs2rel(rel2abs(dirname($obj)), rel2abs($forward));
      my $objn = basename($obj);
      my $depd = abs2rel(rel2abs(dirname($dep)), rel2abs($forward));
      my $depn = basename($dep);
      my $srcs =
          join(", ", map { abs2rel(rel2abs($_), rel2abs($forward)) } @srcs);
      my $incextra = join(',', map { "\"$_\"" }
                               @{$unified_info{includes_extra}->{$obj}});
      $incextra = "/INCLUDE=($incextra)" if $incextra;

      my $cflags;
      if ($args{attrs}->{noinst}) {
          $cflags .= { shlib => $lib_cflags_no_inst,
                       lib => $lib_cflags_no_inst,
                       dso => $dso_cflags_no_inst,
                       bin => $bin_cflags_no_inst } -> {$args{intent}};
      } else {
          $cflags .= { shlib => $lib_cflags,
                       lib => $lib_cflags,
                       dso => $dso_cflags,
                       bin => $bin_cflags } -> {$args{intent}};
      }
      $cflags .= { shlib => $lib_cppflags,
		   lib => $lib_cppflags,
		   dso => $dso_cppflags,
		   bin => $bin_cppflags } -> {$args{intent}};
      $cflags .= $incextra;
      my $defs = join("", map { ",".$_ } @{$args{defs}});
      my $asflags = { shlib => $lib_asflags,
		      lib => $lib_asflags,
		      dso => $dso_asflags,
		      bin => $bin_asflags } -> {$args{intent}};

      if ($srcs[0] =~ /\Q${asmext}\E$/) {
          return <<"EOF";
$obj : $deps
        SET DEFAULT $forward
        \$(AS) $asflags \$(ASOUTFLAG)${objd}${objn} $srcs
        SET DEFAULT $backward
        - PURGE $obj
EOF
      } elsif ($srcs[0] =~ /.S$/) {
         return <<"EOF";
$obj : $deps
        SET DEFAULT $forward
        \@ $incs_on
        \@ extradefines = "$defs"
        PIPE \$(CPP) ${cflags} $srcs | -
             \$(PERL) -ne "/^#(\\s*line)?\\s*[0-9]+\\s+""/ or print" -
             > ${objd}${objn}-asm
        \@ DELETE/SYMBOL/LOCAL extradefines
        \@ $incs_off
        SET DEFAULT $backward
        \$(AS) $asflags \$(ASOUTFLAG)$obj $obj-asm
        - PURGE $obj
EOF
      }

      my ($incdir_filename, $incdir_scripture) =
          make_includefile(@{ { shlib => [ @lib_cppincludes ],
                                lib => [ @lib_cppincludes ],
                                dso => [ @dso_cppincludes ],
                                bin => [ @bin_cppincludes ] } -> {$args{intent}} },
                           @{$args{incs}});
      $deps .= ", -\n\t\t$incdir_filename";
      $cflags =
          $target{cflag_incfirst}
          . '"'.make_unix_path(rel2abs($incdir_filename)).'"'
          . $cflags;

      my $depbuild = $disabled{makedepend} ? ""
          : " /MMS=(FILE=${depd}${depn},TARGET=$obj)";

      return <<"EOF";
$obj : $deps
        SET DEFAULT $forward
        \@ $incs_on
        \@ extradefines = "$defs"
        \$(CC) ${cflags}${depbuild} /OBJECT=${objd}${objn} /REPOSITORY=$backward $srcs
        \@ DELETE/SYMBOL/LOCAL extradefines
        \@ $incs_off
        SET DEFAULT $backward
        - PURGE $obj
$incdir_scripture
EOF
  }
  sub obj2shlib {
      my %args = @_;
      my $shlibname = platform->sharedname($args{lib});
      my $shlib = platform->sharedlib($args{lib});
      my @objs = map { platform->convertext($_) }
                 grep { platform->isobj($_) }
                 @{$args{objs}};
      my @defs = map { platform->convertext($_) }
                 grep { platform->isdef($_) }
                 @{$args{objs}};
      my @deps = compute_lib_depends(@{$args{deps}});
      die "More than one symbol vector" if scalar @defs > 1;
      my $deps = join(", -\n\t\t", @objs, @defs, map { $_->{lib} } @deps);
      my $shlib_target = $disabled{shared} ? "" : $target{shared_target};
      my $translatesyms_pl = abs2rel(rel2abs(catfile($config{sourcedir},
                                                     "VMS", "translatesyms.pl")),
                                     rel2abs($config{builddir}));
      # The "[]" hack is because in .OPT files, each line inherits the
      # previous line's file spec as default, so if no directory spec
      # is present in the current line and the previous line has one that
      # doesn't apply, you're in for a surprise.
      my $write_opt1 =
          join(",-\"\n\t", map { my $x = $_ =~ /\[/ ? $_ : "[]".$_;
                                 "WRITE OPT_FILE \"$x" } @objs).
          "\"";
      my $write_opt2 =
          join("\n\t", map { my $x = $_->{lib} =~ /\[/
                                 ? $_->{lib} : "[]".$_->{lib};
                             $x =~ s|(\.EXE)|$1/SHARE|;
                             $x =~ s|(\.OLB)|$1/LIB|;
                             "WRITE OPT_FILE \"$x\"" } @deps)
          || "\@ !";
      return <<"EOF"
$shlib : $deps
        \$(PERL) $translatesyms_pl \$(BLDDIR)CXX\$DEMANGLER_DB. < $defs[0] > $defs[0]-translated
        OPEN/WRITE/SHARE=READ OPT_FILE $shlibname-components.OPT
        $write_opt1
        $write_opt2
        CLOSE OPT_FILE
        LINK ${lib_ldflags}/SHARE=\$\@ $defs[0]-translated/OPT,-
                $shlibname-components.OPT/OPT \$(LIB_EX_LIBS)
        DELETE $defs[0]-translated;*,$shlibname-components.OPT;*
        PURGE $shlibname.EXE,$shlibname.MAP
EOF
        . ($config{target} =~ m|alpha| ? "" : <<"EOF"
        SET IMAGE/FLAGS=(NOCALL_DEBUG) \$\@
EOF
        );
  }
  sub obj2dso {
      my %args = @_;
      my $dsoname = platform->dsoname($args{module});
      my $dso = platform->dso($args{module});
      my @objs = map { platform->convertext($_) }
                 grep { platform->isobj($_) }
                 @{$args{objs}};
      my @defs = map { platform->convertext($_) }
                 grep { platform->isdef($_) }
                 @{$args{objs}};
      my @deps = compute_lib_depends(@{$args{deps}});
      my $deps = join(", -\n\t\t", @objs, @defs, map { $_->{lib} } @deps);
      die "More than one symbol vector" if scalar @defs > 1;
      my $shlib_target = $disabled{shared} ? "" : $target{shared_target};
      # The "[]" hack is because in .OPT files, each line inherits the
      # previous line's file spec as default, so if no directory spec
      # is present in the current line and the previous line has one that
      # doesn't apply, you're in for a surprise.
      my $write_opt1 =
          join(",-\"\n\t", map { my $x = $_ =~ /\[/ ? $_ : "[]".$_;
                                 "WRITE OPT_FILE \"$x" } @objs).
          "\"";
      my $write_opt2 =
          join("\n\t", map { my $x = $_->{lib} =~ /\[/
                                 ? $_->{lib} : "[]".$_->{lib};
                             $x =~ s|(\.EXE)|$1/SHARE|;
                             $x =~ s|(\.OLB)|$1/LIB|;
                             "WRITE OPT_FILE \"$x\"" } @deps)
          || "\@ !";
      return <<"EOF"
$dso : $deps
        OPEN/WRITE/SHARE=READ OPT_FILE $dsoname-components.OPT
        $write_opt1
        $write_opt2
        CLOSE OPT_FILE
        LINK ${dso_ldflags}/SHARE=\$\@ $defs[0]/OPT,-
		$dsoname-components.OPT/OPT \$(DSO_EX_LIBS)
        - PURGE $dsoname.EXE,$dsoname.OPT,$dsoname.MAP
EOF
        . ($config{target} =~ m|alpha| ? "" : <<"EOF"
        SET IMAGE/FLAGS=(NOCALL_DEBUG) \$\@
EOF
        );
  }
  sub obj2lib {
      my %args = @_;
      my $lib = platform->staticlib($args{lib});
      my @objs = map { platform->convertext($_) }
                 grep { platform->isobj($_) }
                 @{$args{objs}};
      my $objs = join(", -\n\t\t", @objs);
      my $fill_lib = join("\n\t", (map { "LIBRARY/REPLACE $lib $_" } @objs));
      return <<"EOF";
$lib : $objs
        LIBRARY/CREATE/OBJECT $lib
        $fill_lib
        - PURGE $lib
EOF
  }
  sub obj2bin {
      my %args = @_;
      my $bin = platform->bin($args{bin});
      my $binname = platform->binname($args{bin});
      my @objs = map { platform->convertext($_) }
                 grep { platform->isobj($_) }
                 @{$args{objs}};
      my $objs = join(",", @objs);
      my @deps = compute_lib_depends(@{$args{deps}});
      my $deps = join(", -\n\t\t", @objs, map { $_->{lib} } @deps);

      my $olb_count = scalar grep(m|\.OLB$|, map { $_->{lib} } @deps);
      my $analyse_objs = "@ !";
      if ($olb_count > 0) {
          my $analyse_quals =
              $config{target} =~ m|alpha| ? "/GSD" : "/SECTIONS=SYMTAB";
          $analyse_objs = "- pipe ANALYSE/OBJECT$analyse_quals $objs | SEARCH SYS\$INPUT \"\"\"main\"\"\" ; nomain = \$severity .NE. 1"
      }
      # The "[]" hack is because in .OPT files, each line inherits the
      # previous line's file spec as default, so if no directory spec
      # is present in the current line and the previous line has one that
      # doesn't apply, you're in for a surprise.
      my $write_opt1 =
          join(",-\"\n\t", map { my $x = $_ =~ /\[/ ? $_ : "[]".$_;
                                 "\@ WRITE OPT_FILE \"$x" } @objs).
          "\"";
      my $write_opt2 =
          join("\n\t", "WRITE OPT_FILE \"CASE_SENSITIVE=YES\"",
                       map { my @lines = ();
                             use Data::Dumper;
                             my $x = $_->{lib} =~ /\[/
                                 ? $_->{lib} : "[]".$_->{lib};
                             if ($x =~ m|\.EXE$|) {
                                 push @lines, "\@ WRITE OPT_FILE \"$x/SHARE\"";
                             } elsif ($x =~ m|\.OLB$|) {
                                 # Special hack to include the MAIN object
                                 # module explicitly.  This will only be done
                                 # if there isn't a 'main' in the program's
                                 # object modules already.
                                 my $main = $_->{attrs}->{has_main}
                                     ? '/INCLUDE=main' : '';
                                 push @lines,
                                     "\@ IF nomain THEN WRITE OPT_FILE \"$x/LIB$main\"",
                                     "\@ IF .NOT. nomain THEN WRITE OPT_FILE \"$x/LIB\""
                             }
                             @lines
                           } @deps)
          || "\@ !";
      # The linking commands looks a bit complex, but it's for good reason.
      # When you link, say, foo.obj, bar.obj and libsomething.exe/share, and
      # bar.obj happens to have a symbol that also exists in libsomething.exe,
      # the linker will warn about it, loudly, and will then choose to pick
      # the first copy encountered (the one in bar.obj in this example).
      # On Unix and on Windows, the corresponding maneuver goes through
      # silently with the same effect.
      # With some test programs, made for checking the internals of OpenSSL,
      # we do this kind of linking deliberately, picking a few specific object
      # files from within [.crypto] or [.ssl] so we can reach symbols that are
      # otherwise unreachable (since the shareable images only exports the
      # symbols listed in [.util]*.num), and then with the shared libraries
      # themselves.  So we need to silence the warning about multiply defined
      # symbols, to mimic the way linking work on Unix and Windows, and so
      # the build isn't interrupted (MMS stops when warnings are signaled,
      # by default), and so someone building doesn't have to worry where it
      # isn't necessary.  If there are other warnings, however, we show them
      # and let it break the build.
      return <<"EOF"
$bin : $deps
        $analyse_objs
        @ OPEN/WRITE/SHARE=READ OPT_FILE $binname.OPT
        $write_opt1
        $write_opt2
        @ CLOSE OPT_FILE
        TYPE $binname.OPT ! For debugging
        - pipe SPAWN/WAIT/NOLOG/OUT=$binname.LINKLOG -
                    LINK ${bin_ldflags}/EXEC=\$\@ $binname.OPT/OPT \$(BIN_EX_LIBS) ; -
               link_status = \$status ; link_severity = link_status .AND. 7
        @ search_severity = 1
        -@ IF link_severity .EQ. 0 THEN -
                pipe SEARCH $binname.LINKLOG "%","-"/MATCH=AND | -
                     SPAWN/WAIT/NOLOG/OUT=NLA0: -
                          SEARCH SYS\$INPUT: "-W-MULDEF,"/MATCH=NOR ; -
                     search_severity = \$severity
        @ ! search_severity is 3 when the last search didn't find any matching
        @ ! string: %SEARCH-I-NOMATCHES, no strings matched
        @ ! If that was the result, we pretend linking got through without
        @ ! fault or warning.
        @ IF search_severity .EQ. 3 THEN link_severity = 1
        @ ! At this point, if link_severity shows that there was a fault
        @ ! or warning, make sure to restore the linking status.
        -@ IF .NOT. link_severity THEN TYPE $binname.LINKLOG
        -@ DELETE $binname.LINKLOG;*
        @ IF .NOT. link_severity THEN SPAWN/WAIT/NOLOG EXIT 'link_status'
        - PURGE $bin,$binname.OPT
EOF
      . ($config{target} =~ m|alpha| ? "" : <<"EOF"
        SET IMAGE/FLAGS=(NOCALL_DEBUG) \$\@
EOF
        );
  }
  sub in2script {
      my %args = @_;
      my $script = $args{script};
      return "" if grep { $_ eq $script } @{$args{sources}}; # No overwrite!
      my $sources = join(" ", @{$args{sources}});
      my $dofile = abs2rel(rel2abs(catfile($config{sourcedir},
                                           "util", "dofile.pl")),
                           rel2abs($config{builddir}));
      return <<"EOF";
$script : $sources configdata.pm
        \$(PERL) "-I\$(BLDDIR)" "-Mconfigdata" $dofile -
	    "-o$target{build_file}" $sources > $script
        SET FILE/PROT=(S:RWED,O:RWED,G:RE,W:RE) $script
        PURGE $script
EOF
  }
  ""    # Important!  This becomes part of the template result.
-}
