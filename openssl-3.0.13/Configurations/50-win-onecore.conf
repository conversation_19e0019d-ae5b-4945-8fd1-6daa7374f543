## -*- mode: perl; -*-
# Windows OneCore targets.
#
# OneCore is new API stability "contract" that transcends Desktop, IoT and
# Mobile[?] Windows editions. It's a set up "umbrella" libraries that
# export subset of Win32 API that are common to all Windows 10 devices.
#
# OneCore Configuration temporarily dedicated for console applications
# due to disabled event logging, which is incompatible with one core.
# Error messages are provided via standard error only.
# TODO: extend error handling to use ETW based eventing
# (Or rework whole error messaging)

my $UWP_info = {};
sub UWP_info {
    unless (%$UWP_info) {
        my $SDKver = `powershell -Command  \"& {\$(Get-Item \\\"hklm:\\SOFTWARE\\WOW6432Node\\Microsoft\\Microsoft SDKs\\Windows\\\").GetValue(\\\"CurrentVersion\\\")}\"`;
        $SDKver =~ s|\R$||;
        my @SDKver_split = split(/\./, $SDKver);
        # SDK version older than 10.0.17763 don't support our ASM builds
        if ($SDKver_split[0] < 10
            || ($SDKver_split[0] == 10
                && $SDKver_split[1] == 0
                && $SDKver_split[2] < 17763)) {
            $UWP_info->{disable} = [ 'asm' ];
        } else {
            $UWP_info->{disable} = [ ];
        }
    }
    return $UWP_info;
}

my %targets = (
    "VC-WIN32-ONECORE" => {
        inherit_from    => [ "VC-WIN32" ],
        # /NODEFAULTLIB:kernel32.lib is needed, because MSVCRT.LIB has
        # hidden reference to kernel32.lib, but we don't actually want
        # it in "onecore" build.
        # /APPCONTAINER is needed for Universal Windows Platform compat
        lflags          => add("/NODEFAULTLIB:kernel32.lib /APPCONTAINER"),
        defines         => add("OPENSSL_SYS_WIN_CORE"),
        ex_libs         => "onecore.lib",
    },
    "VC-WIN64A-ONECORE" => {
        inherit_from    => [ "VC-WIN64A" ],
        lflags          => add("/NODEFAULTLIB:kernel32.lib /APPCONTAINER"),
        defines         => add("OPENSSL_SYS_WIN_CORE"),
        ex_libs         => "onecore.lib",
    },

    # Windows on ARM targets. ARM compilers are additional components in
    # VS2017, i.e. they are not installed by default. And when installed,
    # there are no "ARM Tool Command Prompt"s on Start menu, you have
    # to locate vcvarsall.bat and act accordingly. VC-WIN32-ARM has
    # received limited testing with evp_test.exe on Windows 10 IoT Core,
    # but not VC-WIN64-ARM, no hardware... In other words they are not
    # actually supported...
    #
    # Another thing to keep in mind [in cross-compilation scenario such
    # as this one] is that target's file system has nothing to do with
    # compilation system's one. This means that you're are likely to use
    # --prefix and --openssldir with target-specific values. 'nmake install'
    # step is effectively meaningless in cross-compilation case, though
    # it might be useful to 'nmake install DESTDIR=S:\ome\where' where you
    # can point Visual Studio to when compiling custom application code.

    "VC-WIN32-ARM" => {
        inherit_from    => [ "VC-noCE-common" ],
        defines         => add("_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE",
                               "OPENSSL_SYS_WIN_CORE"),
        bn_ops          => "BN_LLONG RC4_CHAR",
        lflags          => add("/NODEFAULTLIB:kernel32.lib /APPCONTAINER"),
        ex_libs         => "onecore.lib",
        multilib        => "-arm",
    },
    "VC-WIN64-ARM" => {
        inherit_from    => [ "VC-noCE-common" ],
        defines         => add("_ARM_WINAPI_PARTITION_DESKTOP_SDK_AVAILABLE",
                               "OPENSSL_SYS_WIN_CORE"),
        bn_ops          => "SIXTY_FOUR_BIT RC4_CHAR",
        lflags          => add("/NODEFAULTLIB:kernel32.lib /APPCONTAINER"),
        ex_libs         => "onecore.lib",
        multilib        => "-arm64",
    },

    # Universal Windows Platform (UWP) App Support

    # TODO
    #
    # The 'disable' attribute should have 'uplink'.
    # however, these are checked in some 'inherit_from', which is processed
    # very early, before the 'disable' attributes are seen.
    # This is a problem that needs to be resolved in Configure first.
    #
    # But if you want to build library with Windows 10 Version 1809 SDK or
    # earlier, the 'disable' attribute should also have 'asm'.

    "VC-WIN32-UWP" => {
        inherit_from    => [ "VC-WIN32-ONECORE" ],
        lflags          => add("/APPCONTAINER"),
        defines         => add("WINAPI_FAMILY=WINAPI_FAMILY_APP",
                               "_WIN32_WINNT=0x0A00"),
        dso_scheme      => "",
        disable         => sub { [ 'ui-console', 'stdio', 'async', 'uplink',
                                   @{ UWP_info()->{disable} } ] },
        ex_libs         => "WindowsApp.lib",
    },
     "VC-WIN64A-UWP" => {
        inherit_from    => [ "VC-WIN64A-ONECORE" ],
        lflags          => add("/APPCONTAINER"),
        defines         => add("WINAPI_FAMILY=WINAPI_FAMILY_APP",
                               "_WIN32_WINNT=0x0A00"),
        dso_scheme      => "",
        disable         => sub { [ 'ui-console', 'stdio', 'async', 'uplink',
                                   @{ UWP_info()->{disable} } ] },
        ex_libs         => "WindowsApp.lib",
    },
    "VC-WIN32-ARM-UWP" => {
        inherit_from    => [ "VC-WIN32-ARM" ],
        lflags          => add("/APPCONTAINER"),
        defines         => add("WINAPI_FAMILY=WINAPI_FAMILY_APP",
                               "_WIN32_WINNT=0x0A00"),
        dso_scheme      => "",
        disable         => sub { [ 'ui-console', 'stdio', 'async', 'uplink',
                                   @{ UWP_info()->{disable} } ] },
        ex_libs         => "WindowsApp.lib",
    },
     "VC-WIN64-ARM-UWP" => {
        inherit_from    => [ "VC-WIN64-ARM" ],
        lflags          => add("/APPCONTAINER"),
        defines         => add("WINAPI_FAMILY=WINAPI_FAMILY_APP",
                               "_WIN32_WINNT=0x0A00"),
        dso_scheme      => "",
        disable         => sub { [ 'ui-console', 'stdio', 'async', 'uplink',
                                   @{ UWP_info()->{disable} } ] },
        ex_libs         => "WindowsApp.lib",
    },
);
