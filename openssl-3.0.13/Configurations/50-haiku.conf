my %targets = (
    "haiku-common" => {
        template         => 1,
        CC               => "cc",
        CFLAGS           => add_before(picker(default => "-Wall",
                                              debug   => "-g -O0",
                                              release => "-O2")),
        cflags           => add_before("-DL_ENDIAN -include \$(SRCDIR)/os-dep/haiku.h",
                                       threads("-D_REENTRANT")),
        AR              => "ar",
        ARFLAGS         => "qc",
        HASHBANGPERL    => "/bin/env perl",
        sys_id           => "HAIKU",
        ex_libs          => "-lnetwork",
        perlasm_scheme   => "elf",
        thread_scheme    => "pthreads",
        dso_scheme       => "dlfcn",
        shared_target    => "gnu-shared",
        shared_cflag     => "-fPIC",
        shared_ldflag    => "-shared",
        perl_platform    => 'Unix',
    },
    "haiku-x86" => {
        inherit_from     => [ "haiku-common" ],
        CFLAGS           => add(picker(release => "-fomit-frame-pointer")),
        bn_ops           => "BN_LLONG",
        asm_arch         => 'x86',
        perlasm_scheme   => 'elf',
    },
    "haiku-x86_64" => {
        inherit_from     => [ "haiku-common" ],
        cflags           => add("-m64"),
        bn_ops           => "SIXTY_FOUR_BIT_LONG",
    },
);
