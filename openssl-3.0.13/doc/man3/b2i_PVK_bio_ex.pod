=pod

=head1 NAME

b2i_PVK_bio, b2i_PVK_bio_ex, i2b_PVK_bio, i2b_PVK_bio_ex - Decode and encode
functions for reading and writing MSBLOB format private keys

=head1 SYNOPSIS

 #include <openssl/pem.h>

 EVP_PKEY *b2i_PVK_bio(BIO *in, pem_password_cb *cb, void *u);
 EVP_PKEY *b2i_PVK_bio_ex(BIO *in, pem_password_cb *cb, void *u,
                          OSSL_LIB_CTX *libctx, const char *propq);
 int i2b_PVK_bio(BIO *out, const EVP_PKEY *pk, int enclevel,
                 pem_password_cb *cb, void *u);
 int i2b_PVK_bio_ex(BIO *out, const EVP_PKEY *pk, int enclevel,
                    pem_password_cb *cb, void *u,
                    OSSL_LIB_CTX *libctx, const char *propq);

=head1 DESCRIPTION

b2i_PVK_bio_ex() decodes a private key of MSBLOB format read from a B<BIO>. It
attempts to automatically determine the key type. If the key is encrypted then
I<cb> is called with the user data I<u> in order to obtain a password to decrypt
the key. The supplied library context I<libctx> and property query
string I<propq> are used in any decrypt operation.

b2i_PVK_bio() does the same as b2i_PVK_bio_ex() except that the default
library context and property query string are used.

i2b_PVK_bio_ex() encodes I<pk> using MSBLOB format. If I<enclevel> is 1 then
a password obtained via I<pem_password_cb> is used to encrypt the private key.
If I<enclevel> is 0 then no encryption is applied. The user data in I<u> is
passed to the password callback. The supplied library context I<libctx> and
property query string I<propq> are used in any decrypt operation.

i2b_PVK_bio() does the same as i2b_PVK_bio_ex() except that the default
library context and property query string are used.

=head1 RETURN VALUES

The b2i_PVK_bio() and b2i_PVK_bio_ex() functions return a valid B<EVP_KEY>
structure or B<NULL> if an error occurs. The error code can be obtained by calling
L<ERR_get_error(3)>.

i2b_PVK_bio() and i2b_PVK_bio_ex() return the number of bytes successfully
encoded or a negative value if an error occurs. The error code can be obtained
by calling L<ERR_get_error(3)>.

=head1 SEE ALSO

L<crypto(7)>,
L<d2i_PKCS8PrivateKey_bio(3)>

=head1 HISTORY

b2i_PVK_bio_ex() and i2b_PVK_bio_ex() were added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
