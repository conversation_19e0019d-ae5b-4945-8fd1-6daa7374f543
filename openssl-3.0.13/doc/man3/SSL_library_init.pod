=pod

=head1 NAME

SSL_library_init, OpenSSL_add_ssl_algorithms
- initialize SSL library by registering algorithms

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 int SSL_library_init(void);

 int OpenSSL_add_ssl_algorithms(void);

=head1 DESCRIPTION

SSL_library_init() registers the available SSL/TLS ciphers and digests.

OpenSSL_add_ssl_algorithms() is a synonym for SSL_library_init() and is
implemented as a macro.

=head1 NOTES

SSL_library_init() must be called before any other action takes place.
SSL_library_init() is not reentrant.

=head1 WARNINGS

SSL_library_init() adds ciphers and digests used directly and indirectly by
SSL/TLS.

=head1 RETURN VALUES

SSL_library_init() always returns "1", so it is safe to discard the return
value.

=head1 SEE ALSO

L<ssl(7)>,
L<RAND_add(3)>

=head1 HISTORY

The SSL_library_init() and OpenSSL_add_ssl_algorithms() functions were
deprecated in OpenSSL 1.1.0 by OPENSSL_init_ssl().

=head1 COPYRIGHT

Copyright 2000-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
