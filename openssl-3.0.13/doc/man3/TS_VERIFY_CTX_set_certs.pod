=pod

=head1 NAME

TS_VERIFY_CTX_set_certs, TS_VERIFY_CTS_set_certs
- set certificates for TS response verification

=head1 SYNOPSIS

 #include <openssl/ts.h>

 STACK_OF(X509) *TS_VERIFY_CTX_set_certs(TS_VERIFY_CTX *ctx,
                                         STACK_OF(X509) *certs);
 STACK_OF(X509) *TS_VERIFY_CTS_set_certs(TS_VERIFY_CTX *ctx,
                                         STACK_OF(X509) *certs);

=head1 DESCRIPTION

The Time-Stamp Protocol (TSP) is defined by RFC 3161. TSP is a protocol used to
provide long term proof of the existence of a certain datum before a particular
time. TSP defines a Time Stamping Authority (TSA) and an entity who shall make
requests to the TSA. Usually the TSA is denoted as the server side and the
requesting entity is denoted as the client.

In TSP, when a server is sending a response to a client, the server normally
needs to sign the response data - the TimeStampToken (TST) - with its private
key. Then the client shall verify the received TST by the server's certificate
chain.

TS_VERIFY_CTX_set_certs() is used to set the server's certificate chain when
verifying a TST. B<ctx> is the verification context created in advance and
B<certs> is a stack of B<X509> certificates.

TS_VERIFY_CTS_set_certs() is a misspelled version of TS_VERIFY_CTX_set_certs()
which takes the same parameters and returns the same result.

=head1 RETURN VALUES

TS_VERIFY_CTX_set_certs() returns the stack of B<X509> certificates the user
passes in via parameter B<certs>.

=head1 SEE ALSO

L<OSSL_ESS_check_signing_certs(3)>

=head1 HISTORY

The spelling of TS_VERIFY_CTX_set_certs() was corrected in OpenSSL 3.0.0.
The misspelled version TS_VERIFY_CTS_set_certs() has been retained for
compatibility reasons, but it is deprecated in OpenSSL 3.0.0.

=head1 COPYRIGHT

Copyright 2019-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
