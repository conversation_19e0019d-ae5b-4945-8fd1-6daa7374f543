=pod

=head1 NAME

SSL_set_verify_result - override result of peer certificate verification

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 void SSL_set_verify_result(SSL *ssl, long verify_result);

=head1 DESCRIPTION

SSL_set_verify_result() sets B<verify_result> of the object B<ssl> to be the
result of the verification of the X509 certificate presented by the peer,
if any.

=head1 NOTES

SSL_set_verify_result() overrides the verification result. It only changes
the verification result of the B<ssl> object. It does not become part of the
established session, so if the session is to be reused later, the original
value will reappear.

The valid codes for B<verify_result> are documented in L<openssl-verify(1)>.

=head1 RETURN VALUES

SSL_set_verify_result() does not provide a return value.

=head1 SEE ALSO

L<ssl(7)>, L<SSL_get_verify_result(3)>,
L<SSL_get_peer_certificate(3)>,
L<openssl-verify(1)>

=head1 COPYRIGHT

Copyright 2000-2016 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
