=pod

=head1 NAME

X509_add_cert,
X509_add_certs -
X509 certificate list addition functions

=head1 SYNOPSIS

 #include <openssl/x509.h>

 int X509_add_cert(STACK_OF(X509) *sk, X509 *cert, int flags);
 int X509_add_certs(STACK_OF(X509) *sk, STACK_OF(X509) *certs, int flags);

=head1 DESCRIPTION

X509_add_cert() adds a certificate I<cert> to the given list I<sk>.

X509_add_certs() adds a list of certificate I<certs> to the given list I<sk>.
The I<certs> argument may be NULL, which implies no effect.
It does not modify the list I<certs> but
in case the B<X509_ADD_FLAG_UP_REF> flag (described below) is set
the reference counters of those of its members added to I<sk> are increased.

Both these functions have a I<flags> parameter,
which is used to control details of the operation.

The value B<X509_ADD_FLAG_DEFAULT>, which equals 0, means no special semantics.

If B<X509_ADD_FLAG_UP_REF> is set then
the reference counts of those certificates added successfully are increased.

If B<X509_ADD_FLAG_PREPEND> is set then the certificates are prepended to I<sk>.
By default they are appended to I<sk>.
In both cases the original order of the added certificates is preserved.

If B<X509_ADD_FLAG_NO_DUP> is set then certificates already contained in I<sk>,
which is determined using L<X509_cmp(3)>, are ignored.

If B<X509_ADD_FLAG_NO_SS> is set then certificates that are marked self-signed,
which is determined using L<X509_self_signed(3)>, are ignored.

=head1 RETURN VALUES

Both functions return 1 for success and 0 for failure.

=head1 NOTES

If X509_add_certs() is used with the flags B<X509_ADD_FLAG_NO_DUP> or
B<X509_ADD_FLAG_NO_SS> it is advisable to use also B<X509_ADD_FLAG_UP_REF>
because otherwise likely not for all members of the I<certs> list
the ownership is transferred to the list of certificates I<sk>.

Care should also be taken in case the I<certs> argument equals I<sk>.

=head1 SEE ALSO

L<X509_cmp(3)>
L<X509_self_signed(3)>

=head1 HISTORY

The functions X509_add_cert() and X509_add_certs()
were added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2019-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
