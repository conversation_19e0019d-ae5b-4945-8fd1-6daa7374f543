=pod

=head1 NAME

SSL_state_string, SSL_state_string_long - get textual description of state of an SSL object

=head1 SYNOPSIS

 #include <openssl/ssl.h>

 const char *SSL_state_string(const SSL *ssl);
 const char *SSL_state_string_long(const SSL *ssl);

=head1 DESCRIPTION

SSL_state_string() returns an abbreviated string indicating the current state
of the SSL object B<ssl>. The returned NUL-terminated string contains 6 or fewer characters.

SSL_state_string_long() returns a descriptive string indicating the current state of
the SSL object B<ssl>.

=head1 NOTES

During its use, an SSL objects passes several states. The state is internally
maintained. Querying the state information is not very informative before
or when a connection has been established. It however can be of significant
interest during the handshake.

When using nonblocking sockets, the function call performing the handshake
may return with SSL_ERROR_WANT_READ or SSL_ERROR_WANT_WRITE condition,
so that SSL_state_string[_long]() may be called.

For both blocking or nonblocking sockets, the details state information
can be used within the info_callback function set with the
SSL_set_info_callback() call.

=head1 RETURN VALUES

Detailed description of possible states to be included later.

=head1 SEE ALSO

L<ssl(7)>, L<SSL_CTX_set_info_callback(3)>

=head1 COPYRIGHT

Copyright 2001-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
