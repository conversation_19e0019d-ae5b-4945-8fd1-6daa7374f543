=pod

=begin comment

Any deprecated keypair/params d2i or i2d functions are collected on this page.

=end comment

=head1 NAME

d2i_DSAPrivateKey,
d2i_DSAPrivate<PERSON>ey_bio,
d2i_DSAPrivateKey_fp,
d2i_DSAPublicKey,
d2i_DSA_PUBKEY,
d2i_DSA_PUBKEY_bio,
d2i_DSA_PUBKEY_fp,
d2i_DSAparams,
d2i_RSAPrivateKey,
d2i_RSAPrivateKey_bio,
d2i_RSAPrivateKey_fp,
d2i_RSAPublicKey,
d2i_RSAPublicKey_bio,
d2i_RSAPublicKey_fp,
d2i_RSA_PUBKEY,
d2i_RSA_PUBKEY_bio,
d2i_RSA_PUBKEY_fp,
d2i_DHparams,
d2i_DHparams_bio,
d2i_DHparams_fp,
d2i_ECParameters,
d2i_ECPrivateKey,
d2i_ECPrivate<PERSON><PERSON>_bio,
d2i_ECPrivateKey_fp,
d2i_EC_PUBKEY,
d2i_EC_PUBKEY_bio,
d2i_EC_PUBKEY_fp,
i2d_RSAPrivateKey,
i2d_RSAPrivateKey_bio,
i2d_RSAPrivateKey_fp,
i2d_RSAPublicKey,
i2d_RSAPublicKey_bio,
i2d_RSAPublicKey_fp,
i2d_RSA_PUBKEY,
i2d_RSA_PUBKEY_bio,
i2d_RSA_PUBKEY_fp,
i2d_DHparams,
i2d_DHparams_bio,
i2d_DHparams_fp,
i2d_DSAPrivateKey,
i2d_DSAPrivateKey_bio,
i2d_DSAPrivateKey_fp,
i2d_DSAPublicKey,
i2d_DSA_PUBKEY,
i2d_DSA_PUBKEY_bio,
i2d_DSA_PUBKEY_fp,
i2d_DSAparams,
i2d_ECParameters,
i2d_ECPrivateKey,
i2d_ECPrivateKey_bio,
i2d_ECPrivateKey_fp,
i2d_EC_PUBKEY,
i2d_EC_PUBKEY_bio,
i2d_EC_PUBKEY_fp
- DEPRECATED

=head1 SYNOPSIS

=for openssl generic

The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining B<OPENSSL_API_COMPAT> with a suitable version value,
see L<openssl_user_macros(7)>:

 TYPE *d2i_TYPEPrivateKey(TYPE **a, const unsigned char **ppin, long length);
 TYPE *d2i_TYPEPrivateKey_bio(BIO *bp, TYPE **a);
 TYPE *d2i_TYPEPrivateKey_fp(FILE *fp, TYPE **a);
 TYPE *d2i_TYPEPublicKey(TYPE **a, const unsigned char **ppin, long length);
 TYPE *d2i_TYPEPublicKey_bio(BIO *bp, TYPE **a);
 TYPE *d2i_TYPEPublicKey_fp(FILE *fp, TYPE **a);
 TYPE *d2i_TYPEparams(TYPE **a, const unsigned char **ppin, long length);
 TYPE *d2i_TYPEparams_bio(BIO *bp, TYPE **a);
 TYPE *d2i_TYPEparams_fp(FILE *fp, TYPE **a);
 TYPE *d2i_TYPE_PUBKEY(TYPE **a, const unsigned char **ppin, long length);
 TYPE *d2i_TYPE_PUBKEY_bio(BIO *bp, TYPE **a);
 TYPE *d2i_TYPE_PUBKEY_fp(FILE *fp, TYPE **a);

 int i2d_TYPEPrivateKey(const TYPE *a, unsigned char **ppout);
 int i2d_TYPEPrivateKey(TYPE *a, unsigned char **ppout);
 int i2d_TYPEPrivateKey_fp(FILE *fp, const TYPE *a);
 int i2d_TYPEPrivateKey_fp(FILE *fp, TYPE *a);
 int i2d_TYPEPrivateKey_bio(BIO *bp, const TYPE *a);
 int i2d_TYPEPrivateKey_bio(BIO *bp, TYPE *a);
 int i2d_TYPEPublicKey(const TYPE *a, unsigned char **ppout);
 int i2d_TYPEPublicKey(TYPE *a, unsigned char **ppout);
 int i2d_TYPEPublicKey_fp(FILE *fp, const TYPE *a);
 int i2d_TYPEPublicKey_fp(FILE *fp, TYPE *a);
 int i2d_TYPEPublicKey_bio(BIO *bp, const TYPE *a);
 int i2d_TYPEPublicKey_bio(BIO *bp, TYPE *a);
 int i2d_TYPEparams(const TYPE *a, unsigned char **ppout);
 int i2d_TYPEparams(TYPE *a, unsigned char **ppout);
 int i2d_TYPEparams_fp(FILE *fp, const TYPE *a);
 int i2d_TYPEparams_fp(FILE *fp, TYPE *a);
 int i2d_TYPEparams_bio(BIO *bp, const TYPE *a);
 int i2d_TYPEparams_bio(BIO *bp, TYPE *a);
 int i2d_TYPE_PUBKEY(const TYPE *a, unsigned char **ppout);
 int i2d_TYPE_PUBKEY(TYPE *a, unsigned char **ppout);
 int i2d_TYPE_PUBKEY_fp(FILE *fp, const TYPE *a);
 int i2d_TYPE_PUBKEY_fp(FILE *fp, TYPE *a);
 int i2d_TYPE_PUBKEY_bio(BIO *bp, const TYPE *a);
 int i2d_TYPE_PUBKEY_bio(BIO *bp, TYPE *a);

=head1 DESCRIPTION

All functions described here are deprecated.  Please use L<OSSL_DECODER(3)>
instead of the B<d2i> functions and L<OSSL_ENCODER(3)> instead of the B<i2d>
functions.  See L</Migration> below.

In the description here, B<I<TYPE>> is used a placeholder for any of the
OpenSSL datatypes, such as B<RSA>.
The function parameters I<ppin> and I<ppout> are generally either both named
I<pp> in the headers, or I<in> and I<out>.

All the functions here behave the way that's described in L<d2i_X509(3)>.

Please note that not all functions in the synopsis are available for all key
types.  For example, there are no d2i_RSAparams() or i2d_RSAparams(),
because the PKCS#1 B<RSA> structure doesn't include any key parameters.

B<d2i_I<TYPE>PrivateKey>() and derivates thereof decode DER encoded
B<I<TYPE>> private key data organized in a type specific structure.

B<d2i_I<TYPE>PublicKey>() and derivates thereof decode DER encoded
B<I<TYPE>> public key data organized in a type specific structure.

B<d2i_I<TYPE>params>() and derivates thereof decode DER encoded B<I<TYPE>>
key parameters organized in a type specific structure.

B<d2i_I<TYPE>_PUBKEY>() and derivates thereof decode DER encoded B<I<TYPE>>
public key data organized in a B<SubjectPublicKeyInfo> structure.

B<i2d_I<TYPE>PrivateKey>() and derivates thereof encode the private key
B<I<TYPE>> data into a type specific DER encoded structure.

B<i2d_I<TYPE>PublicKey>() and derivates thereof encode the public key
B<I<TYPE>> data into a type specific DER encoded structure.

B<i2d_I<TYPE>params>() and derivates thereof encode the B<I<TYPE>> key
parameters data into a type specific DER encoded structure.

B<i2d_I<TYPE>_PUBKEY>() and derivates thereof encode the public key
B<I<TYPE>> data into a DER encoded B<SubjectPublicKeyInfo> structure.

For example, d2i_RSAPrivateKey() and d2i_RSAPublicKey() expects the
structure defined by PKCS#1.
Similarly, i2d_RSAPrivateKey() and  i2d_RSAPublicKey() produce DER encoded
string organized according to PKCS#1.

=head2 Migration

Migration from the diverse B<I<TYPE>>s requires using corresponding new
OpenSSL types.  For all B<I<TYPE>>s described here, the corresponding new
type is B<EVP_PKEY>.  The rest of this section assumes that this has been
done, exactly how to do that is described elsewhere.

There are two migration paths:

=over 4

=item *

Replace
b<d2i_I<TYPE>PrivateKey()> with L<d2i_PrivateKey(3)>,
b<d2i_I<TYPE>PublicKey()> with L<d2i_PublicKey(3)>,
b<d2i_I<TYPE>params()> with L<d2i_KeyParams(3)>,
b<d2i_I<TYPE>_PUBKEY()> with L<d2i_PUBKEY(3)>,
b<i2d_I<TYPE>PrivateKey()> with L<i2d_PrivateKey(3)>,
b<i2d_I<TYPE>PublicKey()> with L<i2d_PublicKey(3)>,
b<i2d_I<TYPE>params()> with L<i2d_KeyParams(3)>,
b<i2d_I<TYPE>_PUBKEY()> with L<i2d_PUBKEY(3)>.
A caveat is that L<i2d_PrivateKey(3)> may output a DER encoded PKCS#8
outermost structure instead of the type specific structure, and that
L<d2i_PrivateKey(3)> recognises and unpacks a PKCS#8 structures.

=item *

Use L<OSSL_DECODER(3)> and L<OSSL_ENCODER(3)>.  How to migrate is described
below.  All those descriptions assume that the key to be encoded is in the
variable I<pkey>.

=back

=head3 Migrating B<i2d> functions to B<OSSL_ENCODER>

The exact L<OSSL_ENCODER(3)> output is driven by arguments rather than by
function names.  The sample code to get DER encoded output in a type
specific structure is uniform, the only things that vary are the selection
of what part of the B<EVP_PKEY> should be output, and the structure.  The
B<i2d> functions names can therefore be translated into two variables,
I<selection> and I<structure> as follows:

=over 4

=item B<i2d_I<TYPE>PrivateKey>() translates into:

 int selection = EVP_PKEY_KEYPAIR;
 const char *structure = "type-specific";

=item B<i2d_I<TYPE>PublicKey>() translates into:

 int selection = EVP_PKEY_PUBLIC_KEY;
 const char *structure = "type-specific";

=item B<i2d_I<TYPE>params>() translates into:

 int selection = EVP_PKEY_PARAMETERS;
 const char *structure = "type-specific";

=item B<i2d_I<TYPE>_PUBKEY>() translates into:

 int selection = EVP_PKEY_PUBLIC_KEY;
 const char *structure = "SubjectPublicKeyInfo";

=back

The following sample code does the rest of the work:

 unsigned char *p = buffer;     /* |buffer| is supplied by the caller */
 size_t len = buffer_size;      /* assumed be the size of |buffer| */
 OSSL_ENCODER_CTX *ctx =
     OSSL_ENCODER_CTX_new_for_pkey(pkey, selection, "DER", structure,
                                   NULL, NULL);
 if (ctx == NULL) {
     /* fatal error handling */
 }
 if (OSSL_ENCODER_CTX_get_num_encoders(ctx) == 0) {
     OSSL_ENCODER_CTX_free(ctx);
     /* non-fatal error handling */
 }
 if (!OSSL_ENCODER_to_data(ctx, &p, &len)) {
     OSSL_ENCODER_CTX_free(ctx);
     /* error handling */
 }
 OSSL_ENCODER_CTX_free(ctx);

=for comment TODO: a similar section on OSSL_DECODER is to be added

=head1 NOTES

The letters B<i> and B<d> in B<i2d_I<TYPE>>() stand for
"internal" (that is, an internal C structure) and "DER" respectively.
So B<i2d_I<TYPE>>() converts from internal to DER.

The functions can also understand B<BER> forms.

The actual TYPE structure passed to B<i2d_I<TYPE>>() must be a valid
populated B<I<TYPE>> structure -- it B<cannot> simply be fed with an
empty structure such as that returned by TYPE_new().

The encoded data is in binary form and may contain embedded zeros.
Therefore, any FILE pointers or BIOs should be opened in binary mode.
Functions such as strlen() will B<not> return the correct length
of the encoded structure.

The ways that I<*ppin> and I<*ppout> are incremented after the operation
can trap the unwary. See the B<WARNINGS> section in L<d2i_X509(3)> for some
common errors.
The reason for this-auto increment behaviour is to reflect a typical
usage of ASN1 functions: after one structure is encoded or decoded
another will be processed after it.

The following points about the data types might be useful:

=over 4

=item B<DSA_PUBKEY>

Represents a DSA public key using a B<SubjectPublicKeyInfo> structure.

=item B<DSAPublicKey>, B<DSAPrivateKey>

Use a non-standard OpenSSL format and should be avoided; use B<DSA_PUBKEY>,
L<PEM_write_PrivateKey(3)>, or similar instead.

=back

=head1 RETURN VALUES

B<d2i_I<TYPE>>(), B<d2i_I<TYPE>_bio>() and B<d2i_I<TYPE>_fp>() return a valid
B<I<TYPE>> structure or NULL if an error occurs.  If the "reuse" capability has
been used with a valid structure being passed in via I<a>, then the object is
freed in the event of error and I<*a> is set to NULL.

B<i2d_I<TYPE>>() returns the number of bytes successfully encoded or a negative
value if an error occurs.

B<i2d_I<TYPE>_bio>() and B<i2d_I<TYPE>_fp>() return 1 for success and 0 if an
error occurs.

=head1 SEE ALSO

L<OSSL_ENCODER(3)>, L<OSSL_DECODER(3)>,
L<d2i_PrivateKey(3)>, L<d2i_PublicKey(3)>, L<d2i_KeyParams(3)>,
L<d2i_PUBKEY(3)>,
L<i2d_PrivateKey(3)>, L<i2d_PublicKey(3)>, L<i2d_KeyParams(3)>,
L<i2d_PUBKEY(3)>

=head1 COPYRIGHT

Copyright 2020-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
