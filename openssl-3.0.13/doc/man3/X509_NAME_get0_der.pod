=pod

=head1 NAME

X509_NAME_get0_der - get X509_NAME DER encoding

=head1 SYNOPSIS

 #include <openssl/x509.h>

 int X509_NAME_get0_der(const X509_NAME *nm, const unsigned char **pder,
                        size_t *pderlen);


=head1 DESCRIPTION

The function X509_NAME_get0_der() returns an internal pointer to the
encoding of an B<X509_NAME> structure in B<*pder> and consisting of
B<*pderlen> bytes. It is useful for applications that wish to examine
the encoding of an B<X509_NAME> structure without copying it.

=head1 RETURN VALUES

The function X509_NAME_get0_der() returns 1 for success and 0 if an error
occurred.

=head1 SEE ALSO

L<d2i_X509(3)>

=head1 COPYRIGHT

Copyright 2002-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
