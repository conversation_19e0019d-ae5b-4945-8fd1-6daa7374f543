=pod

=head1 NAME

i2s_ASN1_IA5STRING,
s2i_ASN1_IA5STRING,
i2s_ASN1_INTEGER,
s2i_ASN1_INTEGER,
i2s_ASN1_OCTET_STRING,
s2i_ASN1_OCTET_STRING,
i2s_ASN1_ENUMERATED,
i2s_ASN1_ENUMERATED_TABLE,
i2s_ASN1_UTF8STRING,
s2i_ASN1_UTF8STRING
- convert objects from/to ASN.1/string representation

=head1 SYNOPSIS

 #include <openssl/x509v3.h>

 char *i2s_ASN1_IA5STRING(X509V3_EXT_METHOD *method, ASN1_IA5STRING *ia5);
 ASN1_IA5STRING *s2i_ASN1_IA5STRING(X509V3_EXT_METHOD *method,
                                   X509V3_CTX *ctx, const char *str);
 char *i2s_ASN1_INTEGER(X509V3_EXT_METHOD *method, const ASN1_INTEGER *a);
 ASN1_INTEGER *s2i_ASN1_INTEGER(X509V3_EXT_METHOD *method, const char *value);
 char *i2s_ASN1_OCTET_STRING(X509V3_EXT_METHOD *method,
                            const ASN1_OCTET_STRING *oct);
 ASN1_OCTET_STRING *s2i_ASN1_OCTET_STRING(X509V3_EXT_METHOD *method,
                                         X509V3_CTX *ctx, const char *str);
 char *i2s_ASN1_ENUMERATED(X509V3_EXT_METHOD *method, const ASN1_ENUMERATED *a);
 char *i2s_ASN1_ENUMERATED_TABLE(X509V3_EXT_METHOD *method,
                                const ASN1_ENUMERATED *e);

 char *i2s_ASN1_UTF8STRING(X509V3_EXT_METHOD *method,
                           ASN1_UTF8STRING *utf8);
 ASN1_UTF8STRING *s2i_ASN1_UTF8STRING(X509V3_EXT_METHOD *method,
                                      X509V3_CTX *ctx, const char *str);

=head1 DESCRIPTION

These functions convert OpenSSL objects to and from their ASN.1/string
representation. This function is used for B<X509v3> extensions.

=head1 NOTES

The letters B<i> and B<s> in B<i2s> and B<s2i> stand for
"internal" (that is, an internal C structure) and string respectively.
So B<i2s_ASN1_IA5STRING>() converts from internal to string.

It is the caller's responsibility to free the returned string.
In the B<i2s_ASN1_IA5STRING>() function the string is copied and
the ownership of the original string remains with the caller.

=head1 RETURN VALUES

B<i2s_ASN1_IA5STRING>() returns the pointer to a IA5 string
or NULL if an error occurs.

B<s2i_ASN1_IA5STRING>() return a valid
B<ASN1_IA5STRING> structure or NULL if an error occurs.

B<i2s_ASN1_INTEGER>() return a valid
string or NULL if an error occurs.

B<s2i_ASN1_INTEGER>() returns the pointer to a B<ASN1_INTEGER>
structure or NULL if an error occurs.

B<i2s_ASN1_OCTET_STRING>() returns the pointer to a OCTET_STRING string
or NULL if an error occurs.

B<s2i_ASN1_OCTET_STRING>() return a valid
B<ASN1_OCTET_STRING> structure or NULL if an error occurs.

B<i2s_ASN1_ENUMERATED>() return a valid
string or NULL if an error occurs.

B<s2i_ASN1_ENUMERATED>() returns the pointer to a B<ASN1_ENUMERATED>
structure or NULL if an error occurs.

B<s2i_ASN1_UTF8STRING>() return a valid
B<ASN1_UTF8STRING> structure or NULL if an error occurs.

B<i2s_ASN1_UTF8STRING>() returns the pointer to a UTF-8 string
or NULL if an error occurs.

=head1 HISTORY

i2s_ASN1_UTF8STRING() and s2i_ASN1_UTF8STRING() were made public in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2020-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
