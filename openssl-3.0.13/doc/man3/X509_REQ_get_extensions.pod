=pod

=head1 NAME

X509_REQ_get_extensions,
X509_REQ_add_extensions, X509_REQ_add_extensions_nid
- handle X.509 extension attributes of a CSR

=head1 SYNOPSIS

 #include <openssl/x509.h>

 STACK_OF(X509_EXTENSION) *X509_REQ_get_extensions(X509_REQ *req);
 int X509_REQ_add_extensions(X509_REQ *req, const STACK_OF(X509_EXTENSION) *exts);
 int X509_REQ_add_extensions_nid(X509_REQ *req,
                                 const STACK_OF(X509_EXTENSION) *exts, int nid);

=head1 DESCRIPTION

X509_REQ_get_extensions() returns the first list of X.509 extensions
found in the attributes of I<req>.
The returned list is empty if there are no such extensions in I<req>.
The caller is responsible for freeing the list obtained.

X509_REQ_add_extensions() adds to I<req> a list of X.509 extensions I<exts>,
which must not be NULL, using the default B<NID_ext_req>.
This function must not be called more than once on the same I<req>.

X509_REQ_add_extensions_nid() is like X509_REQ_add_extensions()
except that I<nid> is used to identify the extensions attribute.
This function must not be called more than once with the same I<req> and I<nid>.

=head1 RETURN VALUES

X509_REQ_get_extensions() returns a pointer to B<STACK_OF(X509_EXTENSION)>
or NULL on error.

X509_REQ_add_extensions() and X509_REQ_add_extensions_nid()
return 1 on success, 0 on error.

=head1 COPYRIGHT

Copyright 2022-2024 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
