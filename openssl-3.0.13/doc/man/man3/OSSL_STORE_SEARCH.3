.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_STORE_SEARCH 3ossl"
.TH OSSL_STORE_SEARCH 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_STORE_SEARCH,
OSSL_STORE_SEARCH_by_name,
OSSL_STORE_SEARCH_by_issuer_serial,
OSSL_STORE_SEARCH_by_key_fingerprint,
OSSL_STORE_SEARCH_by_alias,
OSSL_STORE_SEARCH_free,
OSSL_STORE_SEARCH_get_type,
OSSL_STORE_SEARCH_get0_name,
OSSL_STORE_SEARCH_get0_serial,
OSSL_STORE_SEARCH_get0_bytes,
OSSL_STORE_SEARCH_get0_string,
OSSL_STORE_SEARCH_get0_digest
\&\- Type and functions to create OSSL_STORE search criteria
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/store.h>
\&
\& typedef struct ossl_store_search_st OSSL_STORE_SEARCH;
\&
\& OSSL_STORE_SEARCH *OSSL_STORE_SEARCH_by_name(X509_NAME *name);
\& OSSL_STORE_SEARCH *OSSL_STORE_SEARCH_by_issuer_serial(X509_NAME *name,
\&                                                       const ASN1_INTEGER
\&                                                       *serial);
\& OSSL_STORE_SEARCH *OSSL_STORE_SEARCH_by_key_fingerprint(const EVP_MD *digest,
\&                                                         const unsigned char
\&                                                         *bytes, int len);
\& OSSL_STORE_SEARCH *OSSL_STORE_SEARCH_by_alias(const char *alias);
\&
\& void OSSL_STORE_SEARCH_free(OSSL_STORE_SEARCH *search);
\&
\& int OSSL_STORE_SEARCH_get_type(const OSSL_STORE_SEARCH *criterion);
\& X509_NAME *OSSL_STORE_SEARCH_get0_name(OSSL_STORE_SEARCH *criterion);
\& const ASN1_INTEGER *OSSL_STORE_SEARCH_get0_serial(const OSSL_STORE_SEARCH
\&                                                   *criterion);
\& const unsigned char *OSSL_STORE_SEARCH_get0_bytes(const OSSL_STORE_SEARCH
\&                                                   *criterion, size_t *length);
\& const char *OSSL_STORE_SEARCH_get0_string(const OSSL_STORE_SEARCH *criterion);
\& const EVP_MD *OSSL_STORE_SEARCH_get0_digest(const OSSL_STORE_SEARCH
\&                                             *criterion);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions are used to specify search criteria to help search for specific
objects through other names than just the URI that's given to \fBOSSL_STORE_open()\fR.
For example, this can be useful for an application that has received a URI
and then wants to add on search criteria in a uniform and supported manner.
.SS Types
.IX Subsection "Types"
\&\fBOSSL_STORE_SEARCH\fR is an opaque type that holds the constructed search
criterion, and that can be given to an OSSL_STORE context with
\&\fBOSSL_STORE_find()\fR.
.PP
The calling application owns the allocation of an \fBOSSL_STORE_SEARCH\fR at all
times, and should therefore be careful not to deallocate it before
\&\fBOSSL_STORE_close()\fR has been called for the OSSL_STORE context it was given
to.
.SS "Application Functions"
.IX Subsection "Application Functions"
\&\fBOSSL_STORE_SEARCH_by_name()\fR,
\&\fBOSSL_STORE_SEARCH_by_issuer_serial()\fR,
\&\fBOSSL_STORE_SEARCH_by_key_fingerprint()\fR,
and \fBOSSL_STORE_SEARCH_by_alias()\fR
are used to create an \fBOSSL_STORE_SEARCH\fR from a subject name, an issuer name
and serial number pair, a key fingerprint, and an alias (for example a friendly
name).
The parameters that are provided are not copied, only referred to in a
criterion, so they must have at least the same life time as the created
\&\fBOSSL_STORE_SEARCH\fR.
.PP
\&\fBOSSL_STORE_SEARCH_free()\fR is used to free the \fBOSSL_STORE_SEARCH\fR.
.SS "Loader Functions"
.IX Subsection "Loader Functions"
\&\fBOSSL_STORE_SEARCH_get_type()\fR returns the criterion type for the given
\&\fBOSSL_STORE_SEARCH\fR.
.PP
\&\fBOSSL_STORE_SEARCH_get0_name()\fR, \fBOSSL_STORE_SEARCH_get0_serial()\fR,
\&\fBOSSL_STORE_SEARCH_get0_bytes()\fR, \fBOSSL_STORE_SEARCH_get0_string()\fR,
and \fBOSSL_STORE_SEARCH_get0_digest()\fR
are used to retrieve different data from a \fBOSSL_STORE_SEARCH\fR, as
available for each type.
For more information, see "SUPPORTED CRITERION TYPES" below.
.SH "SUPPORTED CRITERION TYPES"
.IX Header "SUPPORTED CRITERION TYPES"
Currently supported criterion types are:
.IP OSSL_STORE_SEARCH_BY_NAME 4
.IX Item "OSSL_STORE_SEARCH_BY_NAME"
This criterion supports a search by exact match of subject name.
The subject name itself is a \fBX509_NAME\fR pointer.
A criterion of this type is created with \fBOSSL_STORE_SEARCH_by_name()\fR,
and the actual subject name is retrieved with \fBOSSL_STORE_SEARCH_get0_name()\fR.
.IP OSSL_STORE_SEARCH_BY_ISSUER_SERIAL 4
.IX Item "OSSL_STORE_SEARCH_BY_ISSUER_SERIAL"
This criterion supports a search by exact match of both issuer name and serial
number.
The issuer name itself is a \fBX509_NAME\fR pointer, and the serial number is
a \fBASN1_INTEGER\fR pointer.
A criterion of this type is created with \fBOSSL_STORE_SEARCH_by_issuer_serial()\fR
and the actual issuer name and serial number are retrieved with
\&\fBOSSL_STORE_SEARCH_get0_name()\fR and \fBOSSL_STORE_SEARCH_get0_serial()\fR.
.IP OSSL_STORE_SEARCH_BY_KEY_FINGERPRINT 4
.IX Item "OSSL_STORE_SEARCH_BY_KEY_FINGERPRINT"
This criterion supports a search by exact match of key fingerprint.
The key fingerprint in itself is a string of bytes and its length, as
well as the algorithm that was used to compute the fingerprint.
The digest may be left unspecified (NULL), and in that case, the
loader has to decide on a default digest and compare fingerprints
accordingly.
A criterion of this type is created with \fBOSSL_STORE_SEARCH_by_key_fingerprint()\fR
and the actual fingerprint and its length can be retrieved with
\&\fBOSSL_STORE_SEARCH_get0_bytes()\fR.
The digest can be retrieved with \fBOSSL_STORE_SEARCH_get0_digest()\fR.
.IP OSSL_STORE_SEARCH_BY_ALIAS 4
.IX Item "OSSL_STORE_SEARCH_BY_ALIAS"
This criterion supports a search by match of an alias of some kind.
The alias in itself is a simple C string.
A criterion of this type is created with \fBOSSL_STORE_SEARCH_by_alias()\fR
and the actual alias is retrieved with \fBOSSL_STORE_SEARCH_get0_string()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_STORE_SEARCH_by_name()\fR,
\&\fBOSSL_STORE_SEARCH_by_issuer_serial()\fR,
\&\fBOSSL_STORE_SEARCH_by_key_fingerprint()\fR,
and \fBOSSL_STORE_SEARCH_by_alias()\fR
return a \fBOSSL_STORE_SEARCH\fR pointer on success, or NULL on failure.
.PP
\&\fBOSSL_STORE_SEARCH_get_type()\fR returns the criterion type of the given
\&\fBOSSL_STORE_SEARCH\fR.
There is no error value.
.PP
\&\fBOSSL_STORE_SEARCH_get0_name()\fR returns a \fBX509_NAME\fR pointer on success,
or NULL when the given \fBOSSL_STORE_SEARCH\fR was of a different type.
.PP
\&\fBOSSL_STORE_SEARCH_get0_serial()\fR returns a \fBASN1_INTEGER\fR pointer on success,
or NULL when the given \fBOSSL_STORE_SEARCH\fR was of a different type.
.PP
\&\fBOSSL_STORE_SEARCH_get0_bytes()\fR returns a \fBconst unsigned char\fR pointer and
sets \fI*length\fR to the strings length on success, or NULL when the given
\&\fBOSSL_STORE_SEARCH\fR was of a different type.
.PP
\&\fBOSSL_STORE_SEARCH_get0_string()\fR returns a \fBconst char\fR pointer on success,
or NULL when the given \fBOSSL_STORE_SEARCH\fR was of a different type.
.PP
\&\fBOSSL_STORE_SEARCH_get0_digest()\fR returns a \fBconst EVP_MD\fR pointer.
NULL is a valid value and means that the store loader default will
be used when applicable.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBossl_store\fR\|(7), \fBOSSL_STORE_supports_search\fR\|(3), \fBOSSL_STORE_find\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBOSSL_STORE_SEARCH\fR,
\&\fBOSSL_STORE_SEARCH_by_name()\fR,
\&\fBOSSL_STORE_SEARCH_by_issuer_serial()\fR,
\&\fBOSSL_STORE_SEARCH_by_key_fingerprint()\fR,
\&\fBOSSL_STORE_SEARCH_by_alias()\fR,
\&\fBOSSL_STORE_SEARCH_free()\fR,
\&\fBOSSL_STORE_SEARCH_get_type()\fR,
\&\fBOSSL_STORE_SEARCH_get0_name()\fR,
\&\fBOSSL_STORE_SEARCH_get0_serial()\fR,
\&\fBOSSL_STORE_SEARCH_get0_bytes()\fR,
and \fBOSSL_STORE_SEARCH_get0_string()\fR
were added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
