.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_NEW_CMS 3ossl"
.TH BIO_NEW_CMS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_new_CMS \- CMS streaming filter BIO
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& BIO *BIO_new_CMS(BIO *out, CMS_ContentInfo *cms);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_new_CMS()\fR returns a streaming filter BIO chain based on \fBcms\fR. The output
of the filter is written to \fBout\fR. Any data written to the chain is
automatically translated to a BER format CMS structure of the appropriate type.
.SH NOTES
.IX Header "NOTES"
The chain returned by this function behaves like a standard filter BIO. It
supports non blocking I/O. Content is processed and streamed on the fly and not
all held in memory at once: so it is possible to encode very large structures.
After all content has been written through the chain \fBBIO_flush()\fR must be called
to finalise the structure.
.PP
The \fBCMS_STREAM\fR flag must be included in the corresponding \fBflags\fR
parameter of the \fBcms\fR creation function.
.PP
If an application wishes to write additional data to \fBout\fR BIOs should be
removed from the chain using \fBBIO_pop()\fR and freed with \fBBIO_free()\fR until \fBout\fR
is reached. If no additional data needs to be written \fBBIO_free_all()\fR can be
called to free up the whole chain.
.PP
Any content written through the filter is used verbatim: no canonical
translation is performed.
.PP
It is possible to chain multiple BIOs to, for example, create a triple wrapped
signed, enveloped, signed structure. In this case it is the applications
responsibility to set the inner content type of any outer CMS_ContentInfo
structures.
.PP
Large numbers of small writes through the chain should be avoided as this will
produce an output consisting of lots of OCTET STRING structures. Prepending
a \fBBIO_f_buffer()\fR buffering BIO will prevent this.
.SH BUGS
.IX Header "BUGS"
There is currently no corresponding inverse BIO: i.e. one which can decode
a CMS structure on the fly.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_new_CMS()\fR returns a BIO chain when successful or NULL if an error
occurred. The error can be obtained from \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBCMS_sign\fR\|(3),
\&\fBCMS_encrypt\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBBIO_new_CMS()\fR function was added in OpenSSL 1.0.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2008\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
