.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RAND_EGD 3ossl"
.TH RAND_EGD 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RAND_egd, RAND_egd_bytes, RAND_query_egd_bytes \- query entropy gathering daemon
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rand.h>
\&
\& int RAND_egd_bytes(const char *path, int num);
\& int RAND_egd(const char *path);
\&
\& int RAND_query_egd_bytes(const char *path, unsigned char *buf, int num);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
On older platforms without a good source of randomness such as \f(CW\*(C`/dev/urandom\*(C'\fR,
it is possible to query an Entropy Gathering Daemon (EGD) over a local
socket to obtain randomness and seed the OpenSSL RNG.
The protocol used is defined by the EGDs available at
<http://egd.sourceforge.net/> or <http://prngd.sourceforge.net>.
.PP
\&\fBRAND_egd_bytes()\fR requests \fBnum\fR bytes of randomness from an EGD at the
specified socket \fBpath\fR, and passes the data it receives into \fBRAND_add()\fR.
\&\fBRAND_egd()\fR is equivalent to \fBRAND_egd_bytes()\fR with \fBnum\fR set to 255.
.PP
\&\fBRAND_query_egd_bytes()\fR requests \fBnum\fR bytes of randomness from an EGD at
the specified socket \fBpath\fR, where \fBnum\fR must be less than 256.
If \fBbuf\fR is \fBNULL\fR, it is equivalent to \fBRAND_egd_bytes()\fR.
If \fBbuf\fR is not \fBNULL\fR, then the data is copied to the buffer and
\&\fBRAND_add()\fR is not called.
.PP
OpenSSL can be configured at build time to try to use the EGD for seeding
automatically.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRAND_egd()\fR and \fBRAND_egd_bytes()\fR return the number of bytes read from the
daemon on success, or \-1 if the connection failed or the daemon did not
return enough data to fully seed the PRNG.
.PP
\&\fBRAND_query_egd_bytes()\fR returns the number of bytes read from the daemon on
success, or \-1 if the connection failed.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBRAND_add\fR\|(3),
\&\fBRAND_bytes\fR\|(3),
\&\fBRAND\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
