.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CMS_ENVELOPEDDATA_CREATE 3ossl"
.TH CMS_ENVELOPEDDATA_CREATE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CMS_EnvelopedData_create_ex, CMS_EnvelopedData_create,
CMS_AuthEnvelopedData_create, CMS_AuthEnvelopedData_create_ex
\&\- Create CMS envelope
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& CMS_ContentInfo *
\& CMS_EnvelopedData_create_ex(const EVP_CIPHER *cipher, OSSL_LIB_CTX *libctx,
\&                             const char *propq);
\& CMS_ContentInfo *CMS_EnvelopedData_create(const EVP_CIPHER *cipher);
\&
\& CMS_ContentInfo *
\& CMS_AuthEnvelopedData_create_ex(const EVP_CIPHER *cipher, OSSL_LIB_CTX *libctx,
\&                                 const char *propq);
\& CMS_ContentInfo *CMS_AuthEnvelopedData_create(const EVP_CIPHER *cipher);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBCMS_EnvelopedData_create_ex()\fR creates a \fBCMS_ContentInfo\fR structure
with a type \fBNID_pkcs7_enveloped\fR. \fIcipher\fR is the symmetric cipher to use.
The library context \fIlibctx\fR and the property query \fIpropq\fR are used when
retrieving algorithms from providers.
.PP
\&\fBCMS_AuthEnvelopedData_create_ex()\fR creates a \fBCMS_ContentInfo\fR
structure with a type \fBNID_id_smime_ct_authEnvelopedData\fR. \fBcipher\fR is the
symmetric AEAD cipher to use. Currently only AES variants with GCM mode are
supported. The library context \fIlibctx\fR and the property query \fIpropq\fR are
used when retrieving algorithms from providers.
.PP
The algorithm passed in the \fIcipher\fR parameter must support ASN1 encoding of
its parameters.
.PP
The recipients can be added later using \fBCMS_add1_recipient_cert\fR\|(3) or
\&\fBCMS_add0_recipient_key\fR\|(3).
.PP
The \fBCMS_ContentInfo\fR structure needs to be finalized using \fBCMS_final\fR\|(3)
and then freed using \fBCMS_ContentInfo_free\fR\|(3).
.PP
\&\fBCMS_EnvelopedData_create()\fR and  CMS_AuthEnvelopedData_create are similar to
\&\fBCMS_EnvelopedData_create_ex()\fR and
\&\fBCMS_AuthEnvelopedData_create_ex()\fR but use default values of NULL for
the library context \fIlibctx\fR and the property query \fIpropq\fR.
.SH NOTES
.IX Header "NOTES"
Although \fBCMS_EnvelopedData_create()\fR and \fBCMS_AuthEnvelopedData_create()\fR allocate
a new \fBCMS_ContentInfo\fR structure, they are not usually used in applications.
The wrappers \fBCMS_encrypt\fR\|(3) and \fBCMS_decrypt\fR\|(3) are often used instead.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
If the allocation fails, \fBCMS_EnvelopedData_create()\fR and
\&\fBCMS_AuthEnvelopedData_create()\fR return NULL and set an error code that can be
obtained by \fBERR_get_error\fR\|(3). Otherwise they return a pointer to the newly
allocated structure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBCMS_encrypt\fR\|(3), \fBCMS_decrypt\fR\|(3), \fBCMS_final\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBCMS_EnvelopedData_create_ex()\fR method was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
