.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_S_BIO 3ossl"
.TH BIO_S_BIO 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_s_bio, BIO_make_bio_pair, BIO_destroy_bio_pair, BIO_shutdown_wr,
BIO_set_write_buf_size, BIO_get_write_buf_size, BIO_new_bio_pair,
BIO_get_write_guarantee, BIO_ctrl_get_write_guarantee, BIO_get_read_request,
BIO_ctrl_get_read_request, BIO_ctrl_reset_read_request \- BIO pair BIO
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& const BIO_METHOD *BIO_s_bio(void);
\&
\& int BIO_make_bio_pair(BIO *b1, BIO *b2);
\& int BIO_destroy_bio_pair(BIO *b);
\& int BIO_shutdown_wr(BIO *b);
\&
\& int BIO_set_write_buf_size(BIO *b, long size);
\& size_t BIO_get_write_buf_size(BIO *b, long size);
\&
\& int BIO_new_bio_pair(BIO **bio1, size_t writebuf1, BIO **bio2, size_t writebuf2);
\&
\& int BIO_get_write_guarantee(BIO *b);
\& size_t BIO_ctrl_get_write_guarantee(BIO *b);
\& int BIO_get_read_request(BIO *b);
\& size_t BIO_ctrl_get_read_request(BIO *b);
\& int BIO_ctrl_reset_read_request(BIO *b);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_s_bio()\fR returns the method for a BIO pair. A BIO pair is a pair of source/sink
BIOs where data written to either half of the pair is buffered and can be read from
the other half. Both halves must usually by handled by the same application thread
since no locking is done on the internal data structures.
.PP
Since BIO chains typically end in a source/sink BIO it is possible to make this
one half of a BIO pair and have all the data processed by the chain under application
control.
.PP
One typical use of BIO pairs is to place TLS/SSL I/O under application control, this
can be used when the application wishes to use a non standard transport for
TLS/SSL or the normal socket routines are inappropriate.
.PP
Calls to \fBBIO_read_ex()\fR will read data from the buffer or request a retry if no
data is available.
.PP
Calls to \fBBIO_write_ex()\fR will place data in the buffer or request a retry if the
buffer is full.
.PP
The standard calls \fBBIO_ctrl_pending()\fR and \fBBIO_ctrl_wpending()\fR can be used to
determine the amount of pending data in the read or write buffer.
.PP
\&\fBBIO_reset()\fR clears any data in the write buffer.
.PP
\&\fBBIO_make_bio_pair()\fR joins two separate BIOs into a connected pair.
.PP
\&\fBBIO_destroy_pair()\fR destroys the association between two connected BIOs. Freeing
up any half of the pair will automatically destroy the association.
.PP
\&\fBBIO_shutdown_wr()\fR is used to close down a BIO \fBb\fR. After this call no further
writes on BIO \fBb\fR are allowed (they will return an error). Reads on the other
half of the pair will return any pending data or EOF when all pending data has
been read.
.PP
\&\fBBIO_set_write_buf_size()\fR sets the write buffer size of BIO \fBb\fR to \fBsize\fR.
If the size is not initialized a default value is used. This is currently
17K, sufficient for a maximum size TLS record.
.PP
\&\fBBIO_get_write_buf_size()\fR returns the size of the write buffer.
.PP
\&\fBBIO_new_bio_pair()\fR combines the calls to \fBBIO_new()\fR, \fBBIO_make_bio_pair()\fR and
\&\fBBIO_set_write_buf_size()\fR to create a connected pair of BIOs \fBbio1\fR, \fBbio2\fR
with write buffer sizes \fBwritebuf1\fR and \fBwritebuf2\fR. If either size is
zero then the default size is used.  \fBBIO_new_bio_pair()\fR does not check whether
\&\fBbio1\fR or \fBbio2\fR do point to some other BIO, the values are overwritten,
\&\fBBIO_free()\fR is not called.
.PP
\&\fBBIO_get_write_guarantee()\fR and \fBBIO_ctrl_get_write_guarantee()\fR return the maximum
length of data that can be currently written to the BIO. Writes larger than this
value will return a value from \fBBIO_write_ex()\fR less than the amount requested or
if the buffer is full request a retry. \fBBIO_ctrl_get_write_guarantee()\fR is a
function whereas \fBBIO_get_write_guarantee()\fR is a macro.
.PP
\&\fBBIO_get_read_request()\fR and \fBBIO_ctrl_get_read_request()\fR return the
amount of data requested, or the buffer size if it is less, if the
last read attempt at the other half of the BIO pair failed due to an
empty buffer.  This can be used to determine how much data should be
written to the BIO so the next read will succeed: this is most useful
in TLS/SSL applications where the amount of data read is usually
meaningful rather than just a buffer size. After a successful read
this call will return zero.  It also will return zero once new data
has been written satisfying the read request or part of it.
Note that \fBBIO_get_read_request()\fR never returns an amount larger
than that returned by \fBBIO_get_write_guarantee()\fR.
.PP
\&\fBBIO_ctrl_reset_read_request()\fR can also be used to reset the value returned by
\&\fBBIO_get_read_request()\fR to zero.
.SH NOTES
.IX Header "NOTES"
Both halves of a BIO pair should be freed. That is even if one half is implicit
freed due to a \fBBIO_free_all()\fR or \fBSSL_free()\fR call the other half needs to be freed.
.PP
When used in bidirectional applications (such as TLS/SSL) care should be taken to
flush any data in the write buffer. This can be done by calling \fBBIO_pending()\fR
on the other half of the pair and, if any data is pending, reading it and sending
it to the underlying transport. This must be done before any normal processing
(such as calling \fBselect()\fR ) due to a request and \fBBIO_should_read()\fR being true.
.PP
To see why this is important consider a case where a request is sent using
\&\fBBIO_write_ex()\fR and a response read with \fBBIO_read_ex()\fR, this can occur during an
TLS/SSL handshake for example. \fBBIO_write_ex()\fR will succeed and place data in the
write buffer. \fBBIO_read_ex()\fR will initially fail and \fBBIO_should_read()\fR will be
true. If the application then waits for data to be available on the underlying
transport before flushing the write buffer it will never succeed because the
request was never sent!
.PP
\&\fBBIO_eof()\fR is true if no data is in the peer BIO and the peer BIO has been
shutdown.
.PP
\&\fBBIO_make_bio_pair()\fR, \fBBIO_destroy_bio_pair()\fR, \fBBIO_shutdown_wr()\fR,
\&\fBBIO_set_write_buf_size()\fR, \fBBIO_get_write_buf_size()\fR,
\&\fBBIO_get_write_guarantee()\fR, and \fBBIO_get_read_request()\fR are implemented
as macros.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_new_bio_pair()\fR returns 1 on success, with the new BIOs available in
\&\fBbio1\fR and \fBbio2\fR, or 0 on failure, with NULL pointers stored into the
locations for \fBbio1\fR and \fBbio2\fR. Check the error stack for more information.
.PP
[XXXXX: More return values need to be added here]
.SH EXAMPLES
.IX Header "EXAMPLES"
The BIO pair can be used to have full control over the network access of an
application. The application can call \fBselect()\fR on the socket as required
without having to go through the SSL-interface.
.PP
.Vb 1
\& BIO *internal_bio, *network_bio;
\&
\& ...
\& BIO_new_bio_pair(&internal_bio, 0, &network_bio, 0);
\& SSL_set_bio(ssl, internal_bio, internal_bio);
\& SSL_operations(); /* e.g. SSL_read and SSL_write */
\& ...
\&
\& application |   TLS\-engine
\&    |        |
\&    +\-\-\-\-\-\-\-\-\-\-> SSL_operations()
\&             |     /\e    ||
\&             |     ||    \e/
\&             |   BIO\-pair (internal_bio)
\&             |   BIO\-pair (network_bio)
\&             |     ||     /\e
\&             |     \e/     ||
\&    +\-\-\-\-\-\-\-\-\-\-\-< BIO_operations()
\&    |        |
\&    |        |
\&   socket
\&
\&  ...
\&  SSL_free(ssl);                /* implicitly frees internal_bio */
\&  BIO_free(network_bio);
\&  ...
.Ve
.PP
As the BIO pair will only buffer the data and never directly access the
connection, it behaves nonblocking and will return as soon as the write
buffer is full or the read buffer is drained. Then the application has to
flush the write buffer and/or fill the read buffer.
.PP
Use the \fBBIO_ctrl_pending()\fR, to find out whether data is buffered in the BIO
and must be transferred to the network. Use \fBBIO_ctrl_get_read_request()\fR to
find out, how many bytes must be written into the buffer before the
\&\fBSSL_operation()\fR can successfully be continued.
.SH WARNINGS
.IX Header "WARNINGS"
As the data is buffered, \fBSSL_operation()\fR may return with an ERROR_SSL_WANT_READ
condition, but there is still data in the write buffer. An application must
not rely on the error value of \fBSSL_operation()\fR but must assure that the
write buffer is always flushed first. Otherwise a deadlock may occur as
the peer might be waiting for the data before being able to continue.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSSL_set_bio\fR\|(3), \fBssl\fR\|(7), \fBbio\fR\|(7),
\&\fBBIO_should_retry\fR\|(3), \fBBIO_read_ex\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
