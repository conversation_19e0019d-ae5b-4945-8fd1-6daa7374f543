.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_MAC 3ossl"
.TH EVP_MAC 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_MAC, EVP_MAC_fetch, EVP_MAC_up_ref, EVP_MAC_free, EVP_MAC_is_a,
EVP_MAC_get0_name, EVP_MAC_names_do_all, EVP_MAC_get0_description,
EVP_MAC_get0_provider, EVP_MAC_get_params, EVP_MAC_gettable_params,
EVP_MAC_CTX, EVP_MAC_CTX_new, EVP_MAC_CTX_free, EVP_MAC_CTX_dup,
EVP_MAC_CTX_get0_mac, EVP_MAC_CTX_get_params, EVP_MAC_CTX_set_params,
EVP_MAC_CTX_get_mac_size, EVP_MAC_CTX_get_block_size, EVP_Q_mac,
EVP_MAC_init, EVP_MAC_update, EVP_MAC_final, EVP_MAC_finalXOF,
EVP_MAC_gettable_ctx_params, EVP_MAC_settable_ctx_params,
EVP_MAC_CTX_gettable_params, EVP_MAC_CTX_settable_params,
EVP_MAC_do_all_provided \- EVP MAC routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& typedef struct evp_mac_st EVP_MAC;
\& typedef struct evp_mac_ctx_st EVP_MAC_CTX;
\&
\& EVP_MAC *EVP_MAC_fetch(OSSL_LIB_CTX *libctx, const char *algorithm,
\&                        const char *properties);
\& int EVP_MAC_up_ref(EVP_MAC *mac);
\& void EVP_MAC_free(EVP_MAC *mac);
\& int EVP_MAC_is_a(const EVP_MAC *mac, const char *name);
\& const char *EVP_MAC_get0_name(const EVP_MAC *mac);
\& int EVP_MAC_names_do_all(const EVP_MAC *mac,
\&                          void (*fn)(const char *name, void *data),
\&                          void *data);
\& const char *EVP_MAC_get0_description(const EVP_MAC *mac);
\& const OSSL_PROVIDER *EVP_MAC_get0_provider(const EVP_MAC *mac);
\& int EVP_MAC_get_params(EVP_MAC *mac, OSSL_PARAM params[]);
\&
\& EVP_MAC_CTX *EVP_MAC_CTX_new(EVP_MAC *mac);
\& void EVP_MAC_CTX_free(EVP_MAC_CTX *ctx);
\& EVP_MAC_CTX *EVP_MAC_CTX_dup(const EVP_MAC_CTX *src);
\& EVP_MAC *EVP_MAC_CTX_get0_mac(EVP_MAC_CTX *ctx);
\& int EVP_MAC_CTX_get_params(EVP_MAC_CTX *ctx, OSSL_PARAM params[]);
\& int EVP_MAC_CTX_set_params(EVP_MAC_CTX *ctx, const OSSL_PARAM params[]);
\&
\& size_t EVP_MAC_CTX_get_mac_size(EVP_MAC_CTX *ctx);
\& size_t EVP_MAC_CTX_get_block_size(EVP_MAC_CTX *ctx);
\& unsigned char *EVP_Q_mac(OSSL_LIB_CTX *libctx, const char *name, const char *propq,
\&                          const char *subalg, const OSSL_PARAM *params,
\&                          const void *key, size_t keylen,
\&                          const unsigned char *data, size_t datalen,
\&                          unsigned char *out, size_t outsize, size_t *outlen);
\& int EVP_MAC_init(EVP_MAC_CTX *ctx, const unsigned char *key, size_t keylen,
\&                  const OSSL_PARAM params[]);
\& int EVP_MAC_update(EVP_MAC_CTX *ctx, const unsigned char *data, size_t datalen);
\& int EVP_MAC_final(EVP_MAC_CTX *ctx,
\&                   unsigned char *out, size_t *outl, size_t outsize);
\& int EVP_MAC_finalXOF(EVP_MAC_CTX *ctx, unsigned char *out, size_t outsize);
\&
\& const OSSL_PARAM *EVP_MAC_gettable_params(const EVP_MAC *mac);
\& const OSSL_PARAM *EVP_MAC_gettable_ctx_params(const EVP_MAC *mac);
\& const OSSL_PARAM *EVP_MAC_settable_ctx_params(const EVP_MAC *mac);
\& const OSSL_PARAM *EVP_MAC_CTX_gettable_params(EVP_MAC_CTX *ctx);
\& const OSSL_PARAM *EVP_MAC_CTX_settable_params(EVP_MAC_CTX *ctx);
\&
\& void EVP_MAC_do_all_provided(OSSL_LIB_CTX *libctx,
\&                              void (*fn)(EVP_MAC *mac, void *arg),
\&                              void *arg);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These types and functions help the application to calculate MACs of
different types and with different underlying algorithms if there are
any.
.PP
MACs are a bit complex insofar that some of them use other algorithms
for actual computation.  HMAC uses a digest, and CMAC uses a cipher.
Therefore, there are sometimes two contexts to keep track of, one for
the MAC algorithm itself and one for the underlying computation
algorithm if there is one.
.PP
To make things less ambiguous, this manual talks about a "context" or
"MAC context", which is to denote the MAC level context, and about a
"underlying context", or "computation context", which is to denote the
context for the underlying computation algorithm if there is one.
.SS Types
.IX Subsection "Types"
\&\fBEVP_MAC\fR is a type that holds the implementation of a MAC.
.PP
\&\fBEVP_MAC_CTX\fR is a context type that holds internal MAC information
as well as a reference to a computation context, for those MACs that
rely on an underlying computation algorithm.
.SS "Algorithm implementation fetching"
.IX Subsection "Algorithm implementation fetching"
\&\fBEVP_MAC_fetch()\fR fetches an implementation of a MAC \fIalgorithm\fR, given
a library context \fIlibctx\fR and a set of \fIproperties\fR.
See "ALGORITHM FETCHING" in \fBcrypto\fR\|(7) for further information.
.PP
See "Message Authentication Code (MAC)" in \fBOSSL_PROVIDER\-default\fR\|(7) for the list
of algorithms supported by the default provider.
.PP
The returned value must eventually be freed with
\&\fBEVP_MAC_free\fR\|(3).
.PP
\&\fBEVP_MAC_up_ref()\fR increments the reference count of an already fetched
MAC.
.PP
\&\fBEVP_MAC_free()\fR frees a fetched algorithm.
NULL is a valid parameter, for which this function is a no-op.
.SS "Context manipulation functions"
.IX Subsection "Context manipulation functions"
\&\fBEVP_MAC_CTX_new()\fR creates a new context for the MAC type \fImac\fR.
The created context can then be used with most other functions
described here.
.PP
\&\fBEVP_MAC_CTX_free()\fR frees the contents of the context, including an
underlying context if there is one, as well as the context itself.
NULL is a valid parameter, for which this function is a no-op.
.PP
\&\fBEVP_MAC_CTX_dup()\fR duplicates the \fIsrc\fR context and returns a newly allocated
context.
.PP
\&\fBEVP_MAC_CTX_get0_mac()\fR returns the \fBEVP_MAC\fR associated with the context
\&\fIctx\fR.
.SS "Computing functions"
.IX Subsection "Computing functions"
\&\fBEVP_Q_mac()\fR computes the message authentication code
of \fIdata\fR with length \fIdatalen\fR
using the MAC algorithm \fIname\fR and the key \fIkey\fR with length \fIkeylen\fR.
The MAC algorithm is fetched using any given \fIlibctx\fR and property query
string \fIpropq\fR. It takes parameters \fIsubalg\fR and further \fIparams\fR,
both of which may be NULL if not needed.
If \fIout\fR is not NULL, it places the result in the memory pointed at by \fIout\fR,
but only if \fIoutsize\fR is sufficient (otherwise no computation is made).
If \fIout\fR is NULL, it allocates and uses a buffer of suitable length,
which will be returned on success and must be freed by the caller.
In either case, also on error,
it assigns the number of bytes written to \fI*outlen\fR unless \fIoutlen\fR is NULL.
.PP
\&\fBEVP_MAC_init()\fR sets up the underlying context \fIctx\fR with information given
via the \fIkey\fR and \fIparams\fR arguments.  The MAC \fIkey\fR has a length of
\&\fIkeylen\fR and the parameters in \fIparams\fR are processed before setting
the key.  If \fIkey\fR is NULL, the key must be set via \fIparams\fR either
as part of this call or separately using \fBEVP_MAC_CTX_set_params()\fR.
Providing non-NULL \fIparams\fR to this function is equivalent to calling
\&\fBEVP_MAC_CTX_set_params()\fR with those \fIparams\fR for the same \fIctx\fR beforehand.
Note: There are additional requirements for some MAC algorithms during
re-initalization (i.e. calling \fBEVP_MAC_init()\fR on an EVP_MAC after \fBEVP_MAC_final()\fR
has been called on the same object).  See the NOTES section below.
.PP
\&\fBEVP_MAC_init()\fR should be called before \fBEVP_MAC_update()\fR and \fBEVP_MAC_final()\fR.
.PP
\&\fBEVP_MAC_update()\fR adds \fIdatalen\fR bytes from \fIdata\fR to the MAC input.
.PP
\&\fBEVP_MAC_final()\fR does the final computation and stores the result in
the memory pointed at by \fIout\fR of size \fIoutsize\fR, and sets the number
of bytes written in \fI*outl\fR at.
If \fIout\fR is NULL or \fIoutsize\fR is too small, then no computation
is made.
To figure out what the output length will be and allocate space for it
dynamically, simply call with \fIout\fR being NULL and \fIoutl\fR
pointing at a valid location, then allocate space and make a second
call with \fIout\fR pointing at the allocated space.
.PP
\&\fBEVP_MAC_finalXOF()\fR does the final computation for an XOF based MAC and stores
the result in the memory pointed at by \fIout\fR of size \fIoutsize\fR.
.PP
\&\fBEVP_MAC_get_params()\fR retrieves details about the implementation
\&\fImac\fR.
The set of parameters given with \fIparams\fR determine exactly what
parameters should be retrieved.
Note that a parameter that is unknown in the underlying context is
simply ignored.
.PP
\&\fBEVP_MAC_CTX_get_params()\fR retrieves chosen parameters, given the
context \fIctx\fR and its underlying context.
The set of parameters given with \fIparams\fR determine exactly what
parameters should be retrieved.
Note that a parameter that is unknown in the underlying context is
simply ignored.
.PP
\&\fBEVP_MAC_CTX_set_params()\fR passes chosen parameters to the underlying
context, given a context \fIctx\fR.
The set of parameters given with \fIparams\fR determine exactly what
parameters are passed down.
If \fIparams\fR are NULL, the underlying context should do nothing and return 1.
Note that a parameter that is unknown in the underlying context is
simply ignored.
Also, what happens when a needed parameter isn't passed down is
defined by the implementation.
.PP
\&\fBEVP_MAC_gettable_params()\fR returns an \fBOSSL_PARAM\fR\|(3) array that describes
the retrievable and settable parameters.  \fBEVP_MAC_gettable_params()\fR
returns parameters that can be used with \fBEVP_MAC_get_params()\fR.
.PP
\&\fBEVP_MAC_gettable_ctx_params()\fR and \fBEVP_MAC_CTX_gettable_params()\fR
return constant \fBOSSL_PARAM\fR\|(3) arrays that describe the retrievable
parameters that can be used with \fBEVP_MAC_CTX_get_params()\fR.
\&\fBEVP_MAC_gettable_ctx_params()\fR returns the parameters that can be retrieved
from the algorithm, whereas \fBEVP_MAC_CTX_gettable_params()\fR returns
the parameters that can be retrieved in the context's current state.
.PP
\&\fBEVP_MAC_settable_ctx_params()\fR and \fBEVP_MAC_CTX_settable_params()\fR return
constant \fBOSSL_PARAM\fR\|(3) arrays that describe the settable parameters that
can be used with \fBEVP_MAC_CTX_set_params()\fR.  \fBEVP_MAC_settable_ctx_params()\fR
returns the parameters that can be retrieved from the algorithm,
whereas \fBEVP_MAC_CTX_settable_params()\fR returns the parameters that can
be retrieved in the context's current state.
.SS "Information functions"
.IX Subsection "Information functions"
\&\fBEVP_MAC_CTX_get_mac_size()\fR returns the MAC output size for the given context.
.PP
\&\fBEVP_MAC_CTX_get_block_size()\fR returns the MAC block size for the given context.
Not all MAC algorithms support this.
.PP
\&\fBEVP_MAC_is_a()\fR checks if the given \fImac\fR is an implementation of an
algorithm that's identifiable with \fIname\fR.
.PP
\&\fBEVP_MAC_get0_provider()\fR returns the provider that holds the implementation
of the given \fImac\fR.
.PP
\&\fBEVP_MAC_do_all_provided()\fR traverses all MAC implemented by all activated
providers in the given library context \fIlibctx\fR, and for each of the
implementations, calls the given function \fIfn\fR with the implementation method
and the given \fIarg\fR as argument.
.PP
\&\fBEVP_MAC_get0_name()\fR return the name of the given MAC.  For fetched MACs
with multiple names, only one of them is returned; it's
recommended to use \fBEVP_MAC_names_do_all()\fR instead.
.PP
\&\fBEVP_MAC_names_do_all()\fR traverses all names for \fImac\fR, and calls
\&\fIfn\fR with each name and \fIdata\fR.
.PP
\&\fBEVP_MAC_get0_description()\fR returns a description of the \fImac\fR, meant
for display and human consumption.  The description is at the discretion
of the mac implementation.
.SH PARAMETERS
.IX Header "PARAMETERS"
Parameters are identified by name as strings, and have an expected
data type and maximum size.
OpenSSL has a set of macros for parameter names it expects to see in
its own MAC implementations.
Here, we show all three, the OpenSSL macro for the parameter name, the
name in string form, and a type description.
.PP
The standard parameter names are:
.IP """key"" (\fBOSSL_MAC_PARAM_KEY\fR) <octet string>" 4
.IX Item """key"" (OSSL_MAC_PARAM_KEY) <octet string>"
Its value is the MAC key as an array of bytes.
.Sp
For MACs that use an underlying computation algorithm, the algorithm
must be set first, see parameter names "algorithm" below.
.IP """iv"" (\fBOSSL_MAC_PARAM_IV\fR) <octet string>" 4
.IX Item """iv"" (OSSL_MAC_PARAM_IV) <octet string>"
Some MAC implementations (GMAC) require an IV, this parameter sets the IV.
.IP """custom"" (\fBOSSL_MAC_PARAM_CUSTOM\fR) <octet string>" 4
.IX Item """custom"" (OSSL_MAC_PARAM_CUSTOM) <octet string>"
Some MAC implementations (KMAC, BLAKE2) accept a Customization String,
this parameter sets the Customization String. The default value is the
empty string.
.IP """salt"" (\fBOSSL_MAC_PARAM_SALT\fR) <octet string>" 4
.IX Item """salt"" (OSSL_MAC_PARAM_SALT) <octet string>"
This option is used by BLAKE2 MAC.
.IP """xof"" (\fBOSSL_MAC_PARAM_XOF\fR) <integer>" 4
.IX Item """xof"" (OSSL_MAC_PARAM_XOF) <integer>"
It's a simple flag, the value 0 or 1 are expected.
.Sp
This option is used by KMAC.
.IP """digest-noinit"" (\fBOSSL_MAC_PARAM_DIGEST_NOINIT\fR) <integer>" 4
.IX Item """digest-noinit"" (OSSL_MAC_PARAM_DIGEST_NOINIT) <integer>"
A simple flag to set the MAC digest to not initialise the
implementation specific data. The value 0 or 1 is expected.
.Sp
This option is used by HMAC.
.IP """digest-oneshot"" (\fBOSSL_MAC_PARAM_DIGEST_ONESHOT\fR) <integer>" 4
.IX Item """digest-oneshot"" (OSSL_MAC_PARAM_DIGEST_ONESHOT) <integer>"
A simple flag to set the MAC digest to be a oneshot operation.
The value 0 or 1 is expected.
.Sp
This option is used by HMAC.
.IP """properties"" (\fBOSSL_MAC_PARAM_PROPERTIES\fR) <UTF8 string>" 4
.IX Item """properties"" (OSSL_MAC_PARAM_PROPERTIES) <UTF8 string>"
.PD 0
.IP """digest"" (\fBOSSL_MAC_PARAM_DIGEST\fR) <UTF8 string>" 4
.IX Item """digest"" (OSSL_MAC_PARAM_DIGEST) <UTF8 string>"
.IP """cipher"" (\fBOSSL_MAC_PARAM_CIPHER\fR) <UTF8 string>" 4
.IX Item """cipher"" (OSSL_MAC_PARAM_CIPHER) <UTF8 string>"
.PD
For MAC implementations that use an underlying computation cipher or
digest, these parameters set what the algorithm should be.
.Sp
The value is always the name of the intended algorithm,
or the properties.
.Sp
Note that not all algorithms may support all digests.
HMAC does not support variable output length digests such as SHAKE128
or SHAKE256.
.IP """size"" (\fBOSSL_MAC_PARAM_SIZE\fR) <unsigned integer>" 4
.IX Item """size"" (OSSL_MAC_PARAM_SIZE) <unsigned integer>"
For MAC implementations that support it, set the output size that
\&\fBEVP_MAC_final()\fR should produce.
The allowed sizes vary between MAC implementations, but must never exceed
what can be given with a \fBsize_t\fR.
.IP """tls-data-size"" (\fBOSSL_MAC_PARAM_TLS_DATA_SIZE\fR) <unsigned integer>" 4
.IX Item """tls-data-size"" (OSSL_MAC_PARAM_TLS_DATA_SIZE) <unsigned integer>"
This parameter is only supported by HMAC. If set then special handling is
activated for calculating the MAC of a received mac-then-encrypt TLS record
where variable length record padding has been used (as in the case of CBC mode
ciphersuites). The value represents the total length of the record that is
having the MAC calculated including the received MAC and the record padding.
.Sp
When used EVP_MAC_update must be called precisely twice. The first time with
the 13 bytes of TLS "header" data, and the second time with the entire record
including the MAC itself and any padding. The entire record length must equal
the value passed in the "tls-data-size" parameter. The length passed in the
\&\fBdatalen\fR parameter to \fBEVP_MAC_update()\fR should be equal to the length of the
record after the MAC and any padding has been removed.
.PP
All these parameters should be used before the calls to any of
\&\fBEVP_MAC_init()\fR, \fBEVP_MAC_update()\fR and \fBEVP_MAC_final()\fR for a full
computation.
Anything else may give undefined results.
.SH NOTES
.IX Header "NOTES"
The MAC life-cycle is described in \fBlife_cycle\-mac\fR\|(7).  In the future,
the transitions described there will be enforced.  When this is done, it will
not be considered a breaking change to the API.
.PP
The usage of the parameter names "custom", "iv" and "salt" correspond to
the names used in the standard where the algorithm was defined.
.PP
Some MAC algorithms store internal state that cannot be extracted during
re-initalization.  For example GMAC cannot extract an \fBIV\fR from the
underlying CIPHER context, and so calling \fBEVP_MAC_init()\fR on an EVP_MAC object
after \fBEVP_MAC_final()\fR has been called cannot reset its cipher state to what it
was when the \fBIV\fR was initially generated.  For such instances, an
\&\fBOSSL_MAC_PARAM_IV\fR parameter must be passed with each call to \fBEVP_MAC_init()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_MAC_fetch()\fR returns a pointer to a newly fetched \fBEVP_MAC\fR, or
NULL if allocation failed.
.PP
\&\fBEVP_MAC_up_ref()\fR returns 1 on success, 0 on error.
.PP
\&\fBEVP_MAC_names_do_all()\fR returns 1 if the callback was called for all names. A
return value of 0 means that the callback was not called for any names.
.PP
\&\fBEVP_MAC_free()\fR returns nothing at all.
.PP
\&\fBEVP_MAC_is_a()\fR returns 1 if the given method can be identified with
the given name, otherwise 0.
.PP
\&\fBEVP_MAC_get0_name()\fR returns a name of the MAC, or NULL on error.
.PP
\&\fBEVP_MAC_get0_provider()\fR returns a pointer to the provider for the MAC, or
NULL on error.
.PP
\&\fBEVP_MAC_CTX_new()\fR and \fBEVP_MAC_CTX_dup()\fR return a pointer to a newly
created EVP_MAC_CTX, or NULL if allocation failed.
.PP
\&\fBEVP_MAC_CTX_free()\fR returns nothing at all.
.PP
\&\fBEVP_MAC_CTX_get_params()\fR and \fBEVP_MAC_CTX_set_params()\fR return 1 on
success, 0 on error.
.PP
\&\fBEVP_Q_mac()\fR returns a pointer to the computed MAC value, or NULL on error.
.PP
\&\fBEVP_MAC_init()\fR, \fBEVP_MAC_update()\fR, \fBEVP_MAC_final()\fR, and \fBEVP_MAC_finalXOF()\fR
return 1 on success, 0 on error.
.PP
\&\fBEVP_MAC_CTX_get_mac_size()\fR returns the expected output size, or 0 if it isn't
set.  If it isn't set, a call to \fBEVP_MAC_init()\fR will set it.
.PP
\&\fBEVP_MAC_CTX_get_block_size()\fR returns the block size, or 0 if it isn't set.
If it isn't set, a call to \fBEVP_MAC_init()\fR will set it.
.PP
\&\fBEVP_MAC_do_all_provided()\fR returns nothing at all.
.SH EXAMPLES
.IX Header "EXAMPLES"
.Vb 5
\&  #include <stdlib.h>
\&  #include <stdio.h>
\&  #include <string.h>
\&  #include <stdarg.h>
\&  #include <unistd.h>
\&
\&  #include <openssl/evp.h>
\&  #include <openssl/err.h>
\&  #include <openssl/params.h>
\&
\&  int main() {
\&      EVP_MAC *mac = EVP_MAC_fetch(NULL, getenv("MY_MAC"), NULL);
\&      const char *cipher = getenv("MY_MAC_CIPHER");
\&      const char *digest = getenv("MY_MAC_DIGEST");
\&      const char *key = getenv("MY_KEY");
\&      EVP_MAC_CTX *ctx = NULL;
\&
\&      unsigned char buf[4096];
\&      size_t read_l;
\&      size_t final_l;
\&
\&      size_t i;
\&
\&      OSSL_PARAM params[3];
\&      size_t params_n = 0;
\&
\&      if (cipher != NULL)
\&          params[params_n++] =
\&              OSSL_PARAM_construct_utf8_string("cipher", (char*)cipher, 0);
\&      if (digest != NULL)
\&          params[params_n++] =
\&              OSSL_PARAM_construct_utf8_string("digest", (char*)digest, 0);
\&      params[params_n] = OSSL_PARAM_construct_end();
\&
\&      if (mac == NULL
\&          || key == NULL
\&          || (ctx = EVP_MAC_CTX_new(mac)) == NULL
\&          || !EVP_MAC_init(ctx, (const unsigned char *)key, strlen(key),
\&                           params))
\&          goto err;
\&
\&      while ( (read_l = read(STDIN_FILENO, buf, sizeof(buf))) > 0) {
\&          if (!EVP_MAC_update(ctx, buf, read_l))
\&              goto err;
\&      }
\&
\&      if (!EVP_MAC_final(ctx, buf, &final_l, sizeof(buf)))
\&          goto err;
\&
\&      printf("Result: ");
\&      for (i = 0; i < final_l; i++)
\&          printf("%02X", buf[i]);
\&      printf("\en");
\&
\&      EVP_MAC_CTX_free(ctx);
\&      EVP_MAC_free(mac);
\&      exit(0);
\&
\&   err:
\&      EVP_MAC_CTX_free(ctx);
\&      EVP_MAC_free(mac);
\&      fprintf(stderr, "Something went wrong\en");
\&      ERR_print_errors_fp(stderr);
\&      exit (1);
\&  }
.Ve
.PP
A run of this program, called with correct environment variables, can
look like this:
.PP
.Vb 3
\&  $ MY_MAC=cmac MY_KEY=secret0123456789 MY_MAC_CIPHER=aes\-128\-cbc \e
\&    LD_LIBRARY_PATH=. ./foo < foo.c
\&  Result: C5C06683CD9DDEF904D754505C560A4E
.Ve
.PP
(in this example, that program was stored in \fIfoo.c\fR and compiled to
\&\fI./foo\fR)
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBproperty\fR\|(7)
\&\fBOSSL_PARAM\fR\|(3),
\&\fBEVP_MAC\-BLAKE2\fR\|(7),
\&\fBEVP_MAC\-CMAC\fR\|(7),
\&\fBEVP_MAC\-GMAC\fR\|(7),
\&\fBEVP_MAC\-HMAC\fR\|(7),
\&\fBEVP_MAC\-KMAC\fR\|(7),
\&\fBEVP_MAC\-Siphash\fR\|(7),
\&\fBEVP_MAC\-Poly1305\fR\|(7),
\&\fBprovider\-mac\fR\|(7),
\&\fBlife_cycle\-mac\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018\-2024 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
