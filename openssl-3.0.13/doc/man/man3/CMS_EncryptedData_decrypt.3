.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CMS_ENCRYPTEDDATA_DECRYPT 3ossl"
.TH CMS_ENCRYPTEDDATA_DECRYPT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CMS_EncryptedData_decrypt
\&\- Decrypt CMS EncryptedData
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& int CMS_EncryptedData_decrypt(CMS_ContentInfo *cms,
\&                               const unsigned char *key, size_t keylen,
\&                               BIO *dcont, BIO *out, unsigned int flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBCMS_EncryptedData_decrypt()\fR decrypts a \fIcms\fR EncryptedData object using the
symmetric \fIkey\fR of size \fIkeylen\fR bytes. \fIout\fR is a BIO to write the content
to and \fIflags\fR is an optional set of flags.
\&\fIdcont\fR is used in the rare case where the encrypted content is detached. It
will normally be set to NULL.
.PP
The following flags can be passed in the \fIflags\fR parameter.
.PP
If the \fBCMS_TEXT\fR flag is set MIME headers for type \f(CW\*(C`text/plain\*(C'\fR are deleted
from the content. If the content is not of type \f(CW\*(C`text/plain\*(C'\fR then an error is
returned.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCMS_EncryptedData_decrypt()\fR returns 0 if an error occurred otherwise it
returns 1.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBCMS_EncryptedData_encrypt\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
