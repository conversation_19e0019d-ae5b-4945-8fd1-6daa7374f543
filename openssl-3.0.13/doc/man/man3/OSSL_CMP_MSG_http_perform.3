.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_CMP_MSG_HTTP_PERFORM 3ossl"
.TH OSSL_CMP_MSG_HTTP_PERFORM 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_CMP_MSG_http_perform
\&\- client\-side HTTP(S) transfer of a CMP request\-response pair
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cmp.h>
\&
\& OSSL_CMP_MSG *OSSL_CMP_MSG_http_perform(OSSL_CMP_CTX *ctx,
\&                                         const OSSL_CMP_MSG *req);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOSSL_CMP_MSG_http_perform()\fR sends the given PKIMessage \fIreq\fR
to the CMP server specified in \fIctx\fR via \fBOSSL_CMP_CTX_set1_server\fR\|(3)
and optionally \fBOSSL_CMP_CTX_set_serverPort\fR\|(3), using
any "CMP alias" optionally specified via \fBOSSL_CMP_CTX_set1_serverPath\fR\|(3).
The default port is 80 for HTTP and 443 for HTTPS; the default path is "/".
On success the function returns the server's response PKIMessage.
.PP
The function makes use of any HTTP callback function
set via \fBOSSL_CMP_CTX_set_http_cb\fR\|(3).
It respects any timeout value set via \fBOSSL_CMP_CTX_set_option\fR\|(3)
with an \fBOSSL_CMP_OPT_MSG_TIMEOUT\fR argument.
It also respects any HTTP(S) proxy options set via \fBOSSL_CMP_CTX_set1_proxy\fR\|(3)
and \fBOSSL_CMP_CTX_set1_no_proxy\fR\|(3) and the respective environment variables.
Proxying plain HTTP is supported directly,
while using a proxy for HTTPS connections requires a suitable callback function
such as \fBOSSL_HTTP_proxy_connect\fR\|(3).
.SH NOTES
.IX Header "NOTES"
CMP is defined in RFC 4210.
HTTP transfer for CMP is defined in RFC 6712.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_CMP_MSG_http_perform()\fR returns a CMP message on success, else NULL.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOSSL_CMP_CTX_new\fR\|(3), \fBOSSL_HTTP_proxy_connect\fR\|(3).
.SH HISTORY
.IX Header "HISTORY"
The OpenSSL CMP support was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2007\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
