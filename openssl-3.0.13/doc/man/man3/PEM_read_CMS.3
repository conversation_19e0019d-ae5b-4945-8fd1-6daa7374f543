.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PEM_READ_CMS 3ossl"
.TH PEM_READ_CMS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
DECLARE_PEM_rw,
PEM_read_CMS,
PEM_read_bio_CMS,
PEM_write_CMS,
PEM_write_bio_CMS,
PEM_write_DHxparams,
PEM_write_bio_DHxparams,
PEM_read_ECPKParameters,
PEM_read_bio_ECPKParameters,
PEM_write_ECPKParameters,
PEM_write_bio_ECPKParameters,
PEM_read_ECPrivateKey,
PEM_write_ECPrivateKey,
PEM_write_bio_ECPrivateKey,
PEM_read_EC_PUBKEY,
PEM_read_bio_EC_PUBKEY,
PEM_write_EC_PUBKEY,
PEM_write_bio_EC_PUBKEY,
PEM_read_NETSCAPE_CERT_SEQUENCE,
PEM_read_bio_NETSCAPE_CERT_SEQUENCE,
PEM_write_NETSCAPE_CERT_SEQUENCE,
PEM_write_bio_NETSCAPE_CERT_SEQUENCE,
PEM_read_PKCS8,
PEM_read_bio_PKCS8,
PEM_write_PKCS8,
PEM_write_bio_PKCS8,
PEM_write_PKCS8_PRIV_KEY_INFO,
PEM_read_bio_PKCS8_PRIV_KEY_INFO,
PEM_read_PKCS8_PRIV_KEY_INFO,
PEM_write_bio_PKCS8_PRIV_KEY_INFO,
PEM_read_SSL_SESSION,
PEM_read_bio_SSL_SESSION,
PEM_write_SSL_SESSION,
PEM_write_bio_SSL_SESSION,
PEM_read_X509_PUBKEY,
PEM_read_bio_X509_PUBKEY,
PEM_write_X509_PUBKEY,
PEM_write_bio_X509_PUBKEY
\&\- PEM object encoding routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pem.h>
\&
\& DECLARE_PEM_rw(name, TYPE)
\&
\& TYPE *PEM_read_TYPE(FILE *fp, TYPE **a, pem_password_cb *cb, void *u);
\& TYPE *PEM_read_bio_TYPE(BIO *bp, TYPE **a, pem_password_cb *cb, void *u);
\& int PEM_write_TYPE(FILE *fp, const TYPE *a);
\& int PEM_write_bio_TYPE(BIO *bp, const TYPE *a);
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& #include <openssl/pem.h>
\&
\& int PEM_write_DHxparams(FILE *out, const DH *dh);
\& int PEM_write_bio_DHxparams(BIO *out, const DH *dh);
\& EC_GROUP *PEM_read_ECPKParameters(FILE *fp, EC_GROUP **x, pem_password_cb *cb, void *u);
\& EC_GROUP *PEM_read_bio_ECPKParameters(BIO *bp, EC_GROUP **x, pem_password_cb *cb, void *u);
\& int PEM_write_ECPKParameters(FILE *out, const EC_GROUP *x);
\& int PEM_write_bio_ECPKParameters(BIO *out, const EC_GROUP *x),
\&
\& EC_KEY *PEM_read_EC_PUBKEY(FILE *fp, EC_KEY **x, pem_password_cb *cb, void *u);
\& EC_KEY *PEM_read_bio_EC_PUBKEY(BIO *bp, EC_KEY **x, pem_password_cb *cb, void *u);
\& int PEM_write_EC_PUBKEY(FILE *out, const EC_KEY *x);
\& int PEM_write_bio_EC_PUBKEY(BIO *out, const EC_KEY *x);
\&
\& EC_KEY *PEM_read_ECPrivateKey(FILE *out, EC_KEY **x, pem_password_cb *cb, void *u);
\& EC_KEY *PEM_read_bio_ECPrivateKey(BIO *out, EC_KEY **x, pem_password_cb *cb, void *u);
\& int PEM_write_ECPrivateKey(FILE *out, const EC_KEY *x, const EVP_CIPHER *enc,
\&                            const unsigned char *kstr, int klen,
\&                            pem_password_cb *cb, void *u);
\& int PEM_write_bio_ECPrivateKey(BIO *out, const EC_KEY *x, const EVP_CIPHER *enc,
\&                                const unsigned char *kstr, int klen,
\&                                pem_password_cb *cb, void *u);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated.
Applications should use \fBOSSL_ENCODER_to_bio()\fR and \fBOSSL_DECODER_from_bio()\fR
instead.
.PP
In the description below, \fR\f(BITYPE\fR\fB\fR is used
as a placeholder for any of the OpenSSL datatypes, such as \fBX509\fR.
The macro \fBDECLARE_PEM_rw\fR expands to the set of declarations shown in
the next four lines of the synopsis.
.PP
These routines convert between local instances of ASN1 datatypes and
the PEM encoding.  For more information on the templates, see
\&\fBASN1_ITEM\fR\|(3).  For more information on the lower-level routines used
by the functions here, see \fBPEM_read\fR\|(3).
.PP
\&\fBPEM_read_\fR\f(BITYPE\fR() reads a PEM-encoded object of \fB\fR\f(BITYPE\fR\fB\fR from the file
\&\fIfp\fR and returns it.  The \fIcb\fR and \fIu\fR parameters are as described in
\&\fBpem_password_cb\fR\|(3).
.PP
\&\fBPEM_read_bio_\fR\f(BITYPE\fR() is similar to \fBPEM_read_\fR\f(BITYPE\fR\fB\fR() but reads from
the BIO \fIbp\fR.
.PP
\&\fBPEM_write_\fR\f(BITYPE\fR() writes the PEM encoding of the object \fIa\fR to the file
\&\fIfp\fR.
.PP
\&\fBPEM_write_bio_\fR\f(BITYPE\fR() similarly writes to the BIO \fIbp\fR.
.SH NOTES
.IX Header "NOTES"
These functions make no assumption regarding the pass phrase received from the
password callback.
It will simply be treated as a byte sequence.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPEM_read_\fR\f(BITYPE\fR() and \fBPEM_read_bio_\fR\f(BITYPE\fR\fB\fR() return a pointer to an
allocated object, which should be released by calling \fB\fR\f(BITYPE\fR\fB_free\fR(), or
NULL on error.
.PP
\&\fBPEM_write_\fR\f(BITYPE\fR() and \fBPEM_write_bio_\fR\f(BITYPE\fR\fB\fR() return 1 for success or 0 for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBPEM_read\fR\|(3),
\&\fBpassphrase\-encoding\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The functions \fBPEM_write_DHxparams()\fR, \fBPEM_write_bio_DHxparams()\fR,
\&\fBPEM_read_ECPKParameters()\fR, \fBPEM_read_bio_ECPKParameters()\fR,
\&\fBPEM_write_ECPKParameters()\fR, \fBPEM_write_bio_ECPKParameters()\fR,
\&\fBPEM_read_EC_PUBKEY()\fR, \fBPEM_read_bio_EC_PUBKEY()\fR,
\&\fBPEM_write_EC_PUBKEY()\fR, \fBPEM_write_bio_EC_PUBKEY()\fR,
\&\fBPEM_read_ECPrivateKey()\fR, \fBPEM_read_bio_ECPrivateKey()\fR,
\&\fBPEM_write_ECPrivateKey()\fR and \fBPEM_write_bio_ECPrivateKey()\fR
were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 1998\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
