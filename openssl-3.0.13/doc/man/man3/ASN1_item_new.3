.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ASN1_ITEM_NEW 3ossl"
.TH ASN1_ITEM_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ASN1_item_new_ex, ASN1_item_new
\&\- create new ASN.1 values
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/asn1.h>
\&
\& ASN1_VALUE *ASN1_item_new_ex(const ASN1_ITEM *it, OSSL_LIB_CTX *libctx,
\&                              const char *propq);
\& ASN1_VALUE *ASN1_item_new(const ASN1_ITEM *it);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBASN1_item_new_ex()\fR creates a new \fBASN1_VALUE\fR structure based on the
\&\fBASN1_ITEM\fR template given in the \fIit\fR parameter. If any algorithm fetches are
required during the process then they will use the \fBOSSL_LIB_CTX\fR provided in
the \fIlibctx\fR parameter and the property query string in \fIpropq\fR. See
"ALGORITHM FETCHING" in \fBcrypto\fR\|(7) for more information about algorithm fetching.
.PP
\&\fBASN1_item_new()\fR is the same as \fBASN1_item_new_ex()\fR except that the default
\&\fBOSSL_LIB_CTX\fR is used (i.e. NULL) and with a NULL property query string.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBASN1_item_new_ex()\fR and \fBASN1_item_new()\fR return a pointer to the newly created
\&\fBASN1_VALUE\fR or NULL on error.
.SH HISTORY
.IX Header "HISTORY"
The function \fBASN1_item_new_ex()\fR was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
