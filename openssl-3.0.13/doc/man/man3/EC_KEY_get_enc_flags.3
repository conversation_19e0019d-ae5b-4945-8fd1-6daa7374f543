.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EC_KEY_GET_ENC_FLAGS 3ossl"
.TH EC_KEY_GET_ENC_FLAGS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EC_KEY_get_enc_flags, EC_KEY_set_enc_flags
\&\- Get and set flags for encoding EC_KEY structures
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ec.h>
\&
\& unsigned int EC_KEY_get_enc_flags(const EC_KEY *key);
\& void EC_KEY_set_enc_flags(EC_KEY *eckey, unsigned int flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The format of the external representation of the public key written by
\&\fBi2d_ECPrivateKey()\fR (such as whether it is stored in a compressed form or not) is
described by the point_conversion_form. See \fBEC_GROUP_copy\fR\|(3)
for a description of point_conversion_form.
.PP
When reading a private key encoded without an associated public key (e.g. if
EC_PKEY_NO_PUBKEY has been used \- see below), then \fBd2i_ECPrivateKey()\fR generates
the missing public key automatically. Private keys encoded without parameters
(e.g. if EC_PKEY_NO_PARAMETERS has been used \- see below) cannot be loaded using
\&\fBd2i_ECPrivateKey()\fR.
.PP
The functions \fBEC_KEY_get_enc_flags()\fR and \fBEC_KEY_set_enc_flags()\fR get and set the
value of the encoding flags for the \fBkey\fR. There are two encoding flags
currently defined \- EC_PKEY_NO_PARAMETERS and EC_PKEY_NO_PUBKEY.  These flags
define the behaviour of how the  \fBkey\fR is converted into ASN1 in a call to
\&\fBi2d_ECPrivateKey()\fR. If EC_PKEY_NO_PARAMETERS is set then the public parameters for
the curve are not encoded along with the private key. If EC_PKEY_NO_PUBKEY is
set then the public key is not encoded along with the private key.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEC_KEY_get_enc_flags()\fR returns the value of the current encoding flags for the
EC_KEY.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7), \fBEC_GROUP_new\fR\|(3),
\&\fBEC_GROUP_copy\fR\|(3), \fBEC_POINT_new\fR\|(3),
\&\fBEC_POINT_add\fR\|(3),
\&\fBEC_GFp_simple_method\fR\|(3),
\&\fBd2i_ECPKParameters\fR\|(3),
\&\fBd2i_ECPrivateKey\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
