.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_SELF_TEST_NEW 3ossl"
.TH OSSL_SELF_TEST_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_SELF_TEST_new,
OSSL_SELF_TEST_free,
OSSL_SELF_TEST_onbegin,
OSSL_SELF_TEST_oncorrupt_byte,
OSSL_SELF_TEST_onend \- functionality to trigger a callback during a self test
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/self_test.h>
\&
\& OSSL_SELF_TEST *OSSL_SELF_TEST_new(OSSL_CALLBACK *cb, void *cbarg);
\& void OSSL_SELF_TEST_free(OSSL_SELF_TEST *st);
\&
\& void OSSL_SELF_TEST_onbegin(OSSL_SELF_TEST *st, const char *type,
\&                             const char *desc);
\& int OSSL_SELF_TEST_oncorrupt_byte(OSSL_SELF_TEST *st, unsigned char *bytes);
\& void OSSL_SELF_TEST_onend(OSSL_SELF_TEST *st, int ret);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These methods are intended for use by provider implementers, to display
diagnostic information during self testing.
.PP
\&\fBOSSL_SELF_TEST_new()\fR allocates an opaque \fBOSSL_SELF_TEST\fR object that has a
callback and callback argument associated with it.
.PP
The callback \fIcb\fR may be triggered multiple times by a self test to indicate
different phases.
.PP
\&\fBOSSL_SELF_TEST_free()\fR frees the space allocated by \fBOSSL_SELF_TEST_new()\fR.
.PP
\&\fBOSSL_SELF_TEST_onbegin()\fR may be inserted at the start of a block of self test
code. It can be used for diagnostic purposes.
If this method is called the callback \fIcb\fR will receive the following
\&\fBOSSL_PARAM\fR\|(3) object.
.IP """st-phase"" (\fBOSSL_PROV_PARAM_SELF_TEST_PHASE\fR) <UTF8 string>" 4
.IX Item """st-phase"" (OSSL_PROV_PARAM_SELF_TEST_PHASE) <UTF8 string>"
The value is the string "Start"
.PP
\&\fBOSSL_SELF_TEST_oncorrupt_byte()\fR may be inserted just after the known answer is
calculated, but before the self test compares the result. The first byte in the
passed in array of \fIbytes\fR will be corrupted if the callback returns 0,
otherwise it leaves the array unaltered. It can be used for failure testing.
The \fItype\fR and \fIdesc\fR can be used to identify an individual self test to
target for failure testing.
If this method is called the callback \fIcb\fR will receive the following
\&\fBOSSL_PARAM\fR\|(3) object.
.IP """st-phase"" (\fBOSSL_PROV_PARAM_SELF_TEST_PHASE\fR) <UTF8 string>" 4
.IX Item """st-phase"" (OSSL_PROV_PARAM_SELF_TEST_PHASE) <UTF8 string>"
The value is the string "Corrupt"
.PP
\&\fBOSSL_SELF_TEST_onend()\fR may be inserted at the end of a block of self test code
just before cleanup to indicate if the test passed or failed. It can be used for
diagnostic purposes.
If this method is called the callback \fIcb\fR will receive the following
\&\fBOSSL_PARAM\fR\|(3) object.
.IP """st-phase"" (\fBOSSL_PROV_PARAM_SELF_TEST_PHASE\fR) <UTF8 string>" 4
.IX Item """st-phase"" (OSSL_PROV_PARAM_SELF_TEST_PHASE) <UTF8 string>"
The value of the string is "Pass" if \fIret\fR is non zero, otherwise it has the
value "Fail".
.PP
After the callback \fIcb\fR has been called the values that were set by
\&\fBOSSL_SELF_TEST_onbegin()\fR for \fItype\fR and \fIdesc\fR are set to the value "None".
.PP
If \fBOSSL_SELF_TEST_onbegin()\fR, \fBOSSL_SELF_TEST_oncorrupt_byte()\fR or
\&\fBOSSL_SELF_TEST_onend()\fR is called the following additional \fBOSSL_PARAM\fR\|(3) are
passed to the callback.
.IP """st-type"" (\fBOSSL_PROV_PARAM_SELF_TEST_TYPE\fR) <UTF8 string>" 4
.IX Item """st-type"" (OSSL_PROV_PARAM_SELF_TEST_TYPE) <UTF8 string>"
The value is setup by the \fItype\fR passed to \fBOSSL_SELF_TEST_onbegin()\fR.
This allows the callback to identify the type of test being run.
.IP """st-desc"" (\fBOSSL_PROV_PARAM_SELF_TEST_DESC\fR) <UTF8 string>" 4
.IX Item """st-desc"" (OSSL_PROV_PARAM_SELF_TEST_DESC) <UTF8 string>"
The value is setup by the \fItype\fR passed to \fBOSSL_SELF_TEST_onbegin()\fR.
This allows the callback to identify the sub category of the test being run.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_SELF_TEST_new()\fR returns the allocated \fBOSSL_SELF_TEST\fR object, or NULL if
it fails.
.PP
\&\fBOSSL_SELF_TEST_oncorrupt_byte()\fR returns 1 if corruption occurs, otherwise it
returns 0.
.SH EXAMPLES
.IX Header "EXAMPLES"
A single self test could be set up in the following way:
.PP
.Vb 8
\&    OSSL_SELF_TEST *st = NULL;
\&    OSSL_CALLBACK *cb;
\&    void *cbarg;
\&    int ok = 0;
\&    unsigned char out[EVP_MAX_MD_SIZE];
\&    unsigned int out_len = 0;
\&    EVP_MD_CTX *ctx = EVP_MD_CTX_new();
\&    EVP_MD *md = EVP_MD_fetch(libctx, t\->algorithm, NULL);
\&
\&    /*
\&     * Retrieve the callback \- will be NULL if not set by the application via
\&     * OSSL_SELF_TEST_set_callback().
\&     */
\&    OSSL_SELF_TEST_get_callback(libctx, &cb, &cbarg);
\&
\&    st = OSSL_SELF_TEST_new(cb, cb_arg);
\&
\&    /* Trigger the optional callback */
\&    OSSL_SELF_TEST_onbegin(st, OSSL_SELF_TEST_TYPE_KAT_DIGEST,
\&                           OSSL_SELF_TEST_DESC_MD_SHA2);
\&
\&    if (!EVP_DigestInit_ex(ctx, md, NULL)
\&        || !EVP_DigestUpdate(ctx, pt, pt_len)
\&        || !EVP_DigestFinal(ctx, out, &out_len))
\&        goto err;
\&
\&    /* Optional corruption \- If the application callback returns 0 */
\&    OSSL_SELF_TEST_oncorrupt_byte(st, out);
\&
\&    if (out_len != t\->expected_len
\&        || memcmp(out, t\->expected, out_len) != 0)
\&        goto err;
\&    ok = 1;
\&  err:
\&    OSSL_SELF_TEST_onend(st, ok);
\&    EVP_MD_free(md);
\&    EVP_MD_CTX_free(ctx);
.Ve
.PP
Multiple self test's can be set up in a similar way by repeating the pattern of
\&\fBOSSL_SELF_TEST_onbegin()\fR, \fBOSSL_SELF_TEST_oncorrupt_byte()\fR, \fBOSSL_SELF_TEST_onend()\fR
for each test.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOSSL_SELF_TEST_set_callback\fR\|(3),
\&\fBopenssl\-core.h\fR\|(7),
\&\fBOSSL_PROVIDER\-FIPS\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The functions described here were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
