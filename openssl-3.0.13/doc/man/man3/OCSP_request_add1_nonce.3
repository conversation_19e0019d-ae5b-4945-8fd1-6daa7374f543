.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OCSP_REQUEST_ADD1_NONCE 3ossl"
.TH OCSP_REQUEST_ADD1_NONCE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OCSP_request_add1_nonce, OCSP_basic_add1_nonce, OCSP_check_nonce, OCSP_copy_nonce \- OCSP nonce functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ocsp.h>
\&
\& int OCSP_request_add1_nonce(OCSP_REQUEST *req, unsigned char *val, int len);
\& int OCSP_basic_add1_nonce(OCSP_BASICRESP *resp, unsigned char *val, int len);
\& int OCSP_copy_nonce(OCSP_BASICRESP *resp, OCSP_REQUEST *req);
\& int OCSP_check_nonce(OCSP_REQUEST *req, OCSP_BASICRESP *resp);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOCSP_request_add1_nonce()\fR adds a nonce of value \fBval\fR and length \fBlen\fR to
OCSP request \fBreq\fR. If \fBval\fR is \fBNULL\fR a random nonce is used. If \fBlen\fR
is zero or negative a default length will be used (currently 16 bytes).
.PP
\&\fBOCSP_basic_add1_nonce()\fR is identical to \fBOCSP_request_add1_nonce()\fR except
it adds a nonce to OCSP basic response \fBresp\fR.
.PP
\&\fBOCSP_check_nonce()\fR compares the nonce value in \fBreq\fR and \fBresp\fR.
.PP
\&\fBOCSP_copy_nonce()\fR copies any nonce value present in \fBreq\fR to \fBresp\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOCSP_request_add1_nonce()\fR and \fBOCSP_basic_add1_nonce()\fR return 1 for success
and 0 for failure.
.PP
\&\fBOCSP_copy_nonce()\fR returns 1 if a nonce was successfully copied, 2 if no nonce
was present in \fBreq\fR and 0 if an error occurred.
.PP
\&\fBOCSP_check_nonce()\fR returns the result of the nonce comparison between \fBreq\fR
and \fBresp\fR. The return value indicates the result of the comparison.  If
nonces are present and equal 1 is returned. If the nonces are absent 2 is
returned. If a nonce is present in the response only 3 is returned. If nonces
are present and unequal 0 is returned. If the nonce is present in the request
only then \-1 is returned.
.SH NOTES
.IX Header "NOTES"
For most purposes the nonce value in a request is set to a random value so
the \fBval\fR parameter in \fBOCSP_request_add1_nonce()\fR is usually NULL.
.PP
An OCSP nonce is typically added to an OCSP request to thwart replay attacks
by checking the same nonce value appears in the response.
.PP
Some responders may include a nonce in all responses even if one is not
supplied.
.PP
Some responders cache OCSP responses and do not sign each response for
performance reasons. As a result they do not support nonces.
.PP
The return values of \fBOCSP_check_nonce()\fR can be checked to cover each case.  A
positive return value effectively indicates success: nonces are both present
and match, both absent or present in the response only. A nonzero return
additionally covers the case where the nonce is present in the request only:
this will happen if the responder doesn't support nonces. A zero return value
indicates present and mismatched nonces: this should be treated as an error
condition.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7),
\&\fBOCSP_cert_to_id\fR\|(3),
\&\fBOCSP_REQUEST_new\fR\|(3),
\&\fBOCSP_resp_find_status\fR\|(3),
\&\fBOCSP_response_status\fR\|(3),
\&\fBOCSP_sendreq_new\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
