.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BN_MOD_EXP_MONT 3ossl"
.TH BN_MOD_EXP_MONT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BN_mod_exp_mont, BN_mod_exp_mont_consttime, BN_mod_exp_mont_consttime_x2 \-
Montgomery exponentiation
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bn.h>
\&
\& int BN_mod_exp_mont(BIGNUM *rr, const BIGNUM *a, const BIGNUM *p,
\&                     const BIGNUM *m, BN_CTX *ctx, BN_MONT_CTX *in_mont);
\&
\& int BN_mod_exp_mont_consttime(BIGNUM *rr, const BIGNUM *a, const BIGNUM *p,
\&                               const BIGNUM *m, BN_CTX *ctx,
\&                               BN_MONT_CTX *in_mont);
\&
\& int BN_mod_exp_mont_consttime_x2(BIGNUM *rr1, const BIGNUM *a1,
\&                                  const BIGNUM *p1, const BIGNUM *m1,
\&                                  BN_MONT_CTX *in_mont1, BIGNUM *rr2,
\&                                  const BIGNUM *a2, const BIGNUM *p2,
\&                                  const BIGNUM *m2, BN_MONT_CTX *in_mont2,
\&                                  BN_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBN_mod_exp_mont()\fR computes \fIa\fR to the \fIp\fR\-th power modulo \fIm\fR (\f(CW\*(C`rr=a^p % m\*(C'\fR)
using Montgomery multiplication. \fIin_mont\fR is a Montgomery context and can be
NULL. In the case \fIin_mont\fR is NULL, it will be initialized within the
function, so you can save time on initialization if you provide it in advance.
.PP
\&\fBBN_mod_exp_mont_consttime()\fR computes \fIa\fR to the \fIp\fR\-th power modulo \fIm\fR
(\f(CW\*(C`rr=a^p % m\*(C'\fR) using Montgomery multiplication. It is a variant of
\&\fBBN_mod_exp_mont\fR\|(3) that uses fixed windows and the special precomputation
memory layout to limit data-dependency to a minimum to protect secret exponents.
It is called automatically when \fBBN_mod_exp_mont\fR\|(3) is called with parameters
\&\fIa\fR, \fIp\fR, \fIm\fR, any of which have \fBBN_FLG_CONSTTIME\fR flag.
.PP
\&\fBBN_mod_exp_mont_consttime_x2()\fR computes two independent exponentiations \fIa1\fR to
the \fIp1\fR\-th power modulo \fIm1\fR (\f(CW\*(C`rr1=a1^p1 % m1\*(C'\fR) and \fIa2\fR to the \fIp2\fR\-th
power modulo \fIm2\fR (\f(CW\*(C`rr2=a2^p2 % m2\*(C'\fR) using Montgomery multiplication. For some
fixed and equal modulus sizes \fIm1\fR and \fIm2\fR it uses optimizations that allow
to speedup two exponentiations. In all other cases the function reduces to two
calls of \fBBN_mod_exp_mont_consttime\fR\|(3).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
For all functions 1 is returned for success, 0 on error.
The error codes can be obtained by \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBBN_mod_exp_mont\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
