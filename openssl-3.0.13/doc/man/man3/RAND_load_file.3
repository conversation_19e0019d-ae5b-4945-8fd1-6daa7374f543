.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RAND_LOAD_FILE 3ossl"
.TH RAND_LOAD_FILE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RAND_load_file, RAND_write_file, RAND_file_name \- PRNG seed file
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rand.h>
\&
\& int RAND_load_file(const char *filename, long max_bytes);
\&
\& int RAND_write_file(const char *filename);
\&
\& const char *RAND_file_name(char *buf, size_t num);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBRAND_load_file()\fR reads a number of bytes from file \fBfilename\fR and
adds them to the PRNG. If \fBmax_bytes\fR is nonnegative,
up to \fBmax_bytes\fR are read;
if \fBmax_bytes\fR is \-1, the complete file is read.
Do not load the same file multiple times unless its contents have
been updated by \fBRAND_write_file()\fR between reads.
Also, note that \fBfilename\fR should be adequately protected so that an
attacker cannot replace or examine the contents.
If \fBfilename\fR is not a regular file, then user is considered to be
responsible for any side effects, e.g. non-anticipated blocking or
capture of controlling terminal.
.PP
\&\fBRAND_write_file()\fR writes a number of random bytes (currently 128) to
file \fBfilename\fR which can be used to initialize the PRNG by calling
\&\fBRAND_load_file()\fR in a later session.
.PP
\&\fBRAND_file_name()\fR generates a default path for the random seed
file. \fBbuf\fR points to a buffer of size \fBnum\fR in which to store the
filename.
.PP
On all systems, if the environment variable \fBRANDFILE\fR is set, its
value will be used as the seed filename.
Otherwise, the file is called \f(CW\*(C`.rnd\*(C'\fR, found in platform dependent locations:
.IP "On Windows (in order of preference)" 4
.IX Item "On Windows (in order of preference)"
.Vb 1
\& %HOME%, %USERPROFILE%, %SYSTEMROOT%, C:\e
.Ve
.IP "On VMS" 4
.IX Item "On VMS"
.Vb 1
\& SYS$LOGIN:
.Ve
.IP "On all other systems" 4
.IX Item "On all other systems"
.Vb 1
\& $HOME
.Ve
.PP
If \f(CW$HOME\fR (on non-Windows and non-VMS system) is not set either, or
\&\fBnum\fR is too small for the pathname, an error occurs.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBRAND_load_file()\fR returns the number of bytes read or \-1 on error.
.PP
\&\fBRAND_write_file()\fR returns the number of bytes written, or \-1 if the
bytes written were generated without appropriate seeding.
.PP
\&\fBRAND_file_name()\fR returns a pointer to \fBbuf\fR on success, and NULL on
error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBRAND_add\fR\|(3),
\&\fBRAND_bytes\fR\|(3),
\&\fBRAND\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
