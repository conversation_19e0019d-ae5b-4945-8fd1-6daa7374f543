.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_GET_FIELD_TYPE 3ossl"
.TH EVP_PKEY_GET_FIELD_TYPE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_get_field_type, EVP_PKEY_get_ec_point_conv_form \- get field type
or point conversion form of a key
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_get_field_type(const EVP_PKEY *pkey);
\& int EVP_PKEY_get_ec_point_conv_form(const EVP_PKEY *pkey);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_PKEY_get_field_type()\fR returns the field type NID of the \fIpkey\fR, if
\&\fIpkey\fR's key type supports it. The types currently supported
by the built-in OpenSSL providers are either \fBNID_X9_62_prime_field\fR
for prime curves or \fBNID_X9_62_characteristic_two_field\fR for binary curves;
these values are defined in the \fI<openssl/obj_mac.h>\fR header file.
.PP
\&\fBEVP_PKEY_get_ec_point_conv_form()\fR returns the point conversion format
of the \fIpkey\fR, if \fIpkey\fR's key type supports it.
.SH NOTES
.IX Header "NOTES"
Among the standard OpenSSL key types, this is only supported for EC and
SM2 keys.  Other providers may support this for additional key types.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_get_field_type()\fR returns the field type NID or 0 on error.
.PP
\&\fBEVP_PKEY_get_ec_point_conv_form()\fR returns the point conversion format number
(see \fBEC_GROUP_copy\fR\|(3)) or 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEC_GROUP_copy\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
