.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EC_GROUP_COPY 3ossl"
.TH EC_GROUP_COPY 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EC_GROUP_get0_order, EC_GROUP_order_bits, EC_GROUP_get0_cofactor,
EC_GROUP_copy, EC_GROUP_dup, EC_GROUP_method_of, EC_GROUP_set_generator,
EC_GROUP_get0_generator, EC_GROUP_get_order, EC_GROUP_get_cofactor,
EC_GROUP_set_curve_name, EC_GROUP_get_curve_name, EC_GROUP_set_asn1_flag,
EC_GROUP_get_asn1_flag, EC_GROUP_set_point_conversion_form,
EC_GROUP_get_point_conversion_form, EC_GROUP_get0_seed,
EC_GROUP_get_seed_len, EC_GROUP_set_seed, EC_GROUP_get_degree,
EC_GROUP_check, EC_GROUP_check_named_curve,
EC_GROUP_check_discriminant, EC_GROUP_cmp,
EC_GROUP_get_basis_type, EC_GROUP_get_trinomial_basis,
EC_GROUP_get_pentanomial_basis, EC_GROUP_get0_field,
EC_GROUP_get_field_type
\&\- Functions for manipulating EC_GROUP objects
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ec.h>
\&
\& int EC_GROUP_copy(EC_GROUP *dst, const EC_GROUP *src);
\& EC_GROUP *EC_GROUP_dup(const EC_GROUP *src);
\&
\& int EC_GROUP_set_generator(EC_GROUP *group, const EC_POINT *generator,
\&                            const BIGNUM *order, const BIGNUM *cofactor);
\& const EC_POINT *EC_GROUP_get0_generator(const EC_GROUP *group);
\&
\& int EC_GROUP_get_order(const EC_GROUP *group, BIGNUM *order, BN_CTX *ctx);
\& const BIGNUM *EC_GROUP_get0_order(const EC_GROUP *group);
\& int EC_GROUP_order_bits(const EC_GROUP *group);
\& int EC_GROUP_get_cofactor(const EC_GROUP *group, BIGNUM *cofactor, BN_CTX *ctx);
\& const BIGNUM *EC_GROUP_get0_cofactor(const EC_GROUP *group);
\& const BIGNUM *EC_GROUP_get0_field(const EC_GROUP *group);
\&
\& void EC_GROUP_set_curve_name(EC_GROUP *group, int nid);
\& int EC_GROUP_get_curve_name(const EC_GROUP *group);
\&
\& void EC_GROUP_set_asn1_flag(EC_GROUP *group, int flag);
\& int EC_GROUP_get_asn1_flag(const EC_GROUP *group);
\&
\& void EC_GROUP_set_point_conversion_form(EC_GROUP *group, point_conversion_form_t form);
\& point_conversion_form_t EC_GROUP_get_point_conversion_form(const EC_GROUP *group);
\&
\& unsigned char *EC_GROUP_get0_seed(const EC_GROUP *group);
\& size_t EC_GROUP_get_seed_len(const EC_GROUP *group);
\& size_t EC_GROUP_set_seed(EC_GROUP *group, const unsigned char *, size_t len);
\&
\& int EC_GROUP_get_degree(const EC_GROUP *group);
\&
\& int EC_GROUP_check(const EC_GROUP *group, BN_CTX *ctx);
\& int EC_GROUP_check_named_curve(const EC_GROUP *group, int nist_only,
\&                                BN_CTX *ctx);
\&
\& int EC_GROUP_check_discriminant(const EC_GROUP *group, BN_CTX *ctx);
\&
\& int EC_GROUP_cmp(const EC_GROUP *a, const EC_GROUP *b, BN_CTX *ctx);
\&
\& int EC_GROUP_get_basis_type(const EC_GROUP *group);
\& int EC_GROUP_get_trinomial_basis(const EC_GROUP *group, unsigned int *k);
\& int EC_GROUP_get_pentanomial_basis(const EC_GROUP *group, unsigned int *k1,
\&                                    unsigned int *k2, unsigned int *k3);
\&
\& int EC_GROUP_get_field_type(const EC_GROUP *group);
.Ve
.PP
The following function has been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& const EC_METHOD *EC_GROUP_method_of(const EC_GROUP *group);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEC_GROUP_copy()\fR copies the curve \fBsrc\fR into \fBdst\fR. Both \fBsrc\fR and \fBdst\fR must use the same EC_METHOD.
.PP
\&\fBEC_GROUP_dup()\fR creates a new EC_GROUP object and copies the content from \fBsrc\fR to the newly created
EC_GROUP object.
.PP
\&\fBEC_GROUP_method_of()\fR obtains the EC_METHOD of \fBgroup\fR.
This function was deprecated in OpenSSL 3.0, since EC_METHOD is no longer a public concept.
.PP
\&\fBEC_GROUP_set_generator()\fR sets curve parameters that must be agreed by all participants using the curve. These
parameters include the \fBgenerator\fR, the \fBorder\fR and the \fBcofactor\fR. The \fBgenerator\fR is a well defined point on the
curve chosen for cryptographic operations. Integers used for point multiplications will be between 0 and
n\-1 where n is the \fBorder\fR. The \fBorder\fR multiplied by the \fBcofactor\fR gives the number of points on the curve.
.PP
\&\fBEC_GROUP_get0_generator()\fR returns the generator for the identified \fBgroup\fR.
.PP
\&\fBEC_GROUP_get_order()\fR retrieves the order of \fBgroup\fR and copies its value into
\&\fBorder\fR.  It fails in case \fBgroup\fR is not fully initialized (i.e., its order
is not set or set to zero).
.PP
\&\fBEC_GROUP_get_cofactor()\fR retrieves the cofactor of \fBgroup\fR and copies its value
into \fBcofactor\fR. It fails in case  \fBgroup\fR is not fully initialized or if the
cofactor is not set (or set to zero).
.PP
The functions \fBEC_GROUP_set_curve_name()\fR and \fBEC_GROUP_get_curve_name()\fR, set and get the NID for the curve respectively
(see \fBEC_GROUP_new\fR\|(3)). If a curve does not have a NID associated with it, then EC_GROUP_get_curve_name
will return NID_undef.
.PP
The asn1_flag value is used to determine whether the curve encoding uses
explicit parameters or a named curve using an ASN1 OID: many applications only
support the latter form. If asn1_flag is \fBOPENSSL_EC_NAMED_CURVE\fR then the
named curve form is used and the parameters must have a corresponding
named curve NID set. If asn1_flags is \fBOPENSSL_EC_EXPLICIT_CURVE\fR the
parameters are explicitly encoded. The functions \fBEC_GROUP_get_asn1_flag()\fR and
\&\fBEC_GROUP_set_asn1_flag()\fR get and set the status of the asn1_flag for the curve.
Note: \fBOPENSSL_EC_EXPLICIT_CURVE\fR was added in OpenSSL 1.1.0, for
previous versions of OpenSSL the value 0 must be used instead. Before OpenSSL
1.1.0 the default form was to use explicit parameters (meaning that
applications would have to explicitly set the named curve form) in OpenSSL
1.1.0 and later the named curve form is the default.
.PP
The point_conversion_form for a curve controls how EC_POINT data is encoded as ASN1 as defined in X9.62 (ECDSA).
point_conversion_form_t is an enum defined as follows:
.PP
.Vb 10
\& typedef enum {
\&        /** the point is encoded as z||x, where the octet z specifies
\&         *   which solution of the quadratic equation y is  */
\&        POINT_CONVERSION_COMPRESSED = 2,
\&        /** the point is encoded as z||x||y, where z is the octet 0x04  */
\&        POINT_CONVERSION_UNCOMPRESSED = 4,
\&        /** the point is encoded as z||x||y, where the octet z specifies
\&         *  which solution of the quadratic equation y is  */
\&        POINT_CONVERSION_HYBRID = 6
\& } point_conversion_form_t;
.Ve
.PP
For POINT_CONVERSION_UNCOMPRESSED the point is encoded as an octet signifying the UNCOMPRESSED form has been used followed by
the octets for x, followed by the octets for y.
.PP
For any given x coordinate for a point on a curve it is possible to derive two possible y values. For
POINT_CONVERSION_COMPRESSED the point is encoded as an octet signifying that the COMPRESSED form has been used AND which of
the two possible solutions for y has been used, followed by the octets for x.
.PP
For POINT_CONVERSION_HYBRID the point is encoded as an octet signifying the HYBRID form has been used AND which of the two
possible solutions for y has been used, followed by the octets for x, followed by the octets for y.
.PP
The functions \fBEC_GROUP_set_point_conversion_form()\fR and \fBEC_GROUP_get_point_conversion_form()\fR, set and get the point_conversion_form
for the curve respectively.
.PP
ANSI X9.62 (ECDSA standard) defines a method of generating the curve parameter b from a random number. This provides advantages
in that a parameter obtained in this way is highly unlikely to be susceptible to special purpose attacks, or have any trapdoors in it.
If the seed is present for a curve then the b parameter was generated in a verifiable fashion using that seed. The OpenSSL EC library
does not use this seed value but does enable you to inspect it using \fBEC_GROUP_get0_seed()\fR. This returns a pointer to a memory block
containing the seed that was used. The length of the memory block can be obtained using \fBEC_GROUP_get_seed_len()\fR. A number of the
built-in curves within the library provide seed values that can be obtained. It is also possible to set a custom seed using
\&\fBEC_GROUP_set_seed()\fR and passing a pointer to a memory block, along with the length of the seed. Again, the EC library will not use
this seed value, although it will be preserved in any ASN1 based communications.
.PP
\&\fBEC_GROUP_get_degree()\fR gets the degree of the field.
For Fp fields this will be the number of bits in p.
For F2^m fields this will be the value m.
.PP
\&\fBEC_GROUP_get_field_type()\fR identifies what type of field the EC_GROUP structure supports,
which will be either F2^m or Fp.
.PP
The function \fBEC_GROUP_check_discriminant()\fR calculates the discriminant for the curve and verifies that it is valid.
For a curve defined over Fp the discriminant is given by the formula 4*a^3 + 27*b^2 whilst for F2^m curves the discriminant is
simply b. In either case for the curve to be valid the discriminant must be non zero.
.PP
The function \fBEC_GROUP_check()\fR behaves in the following way:
For the OpenSSL default provider it performs a number of checks on a curve to verify that it is valid. Checks performed include
verifying that the discriminant is non zero; that a generator has been defined; that the generator is on the curve and has
the correct order. For the OpenSSL FIPS provider it uses \fBEC_GROUP_check_named_curve()\fR to conform to SP800\-56Ar3.
.PP
The function \fBEC_GROUP_check_named_curve()\fR determines if the group's domain parameters match one of the built-in curves supported by the library.
The curve name is returned as a \fBNID\fR if it matches. If the group's domain parameters have been modified then no match will be found.
If the curve name of the given group is \fBNID_undef\fR (e.g. it has been created by using explicit parameters with no curve name),
then this method can be used to lookup the name of the curve that matches the group domain parameters. The built-in curves contain
aliases, so that multiple NID's can map to the same domain parameters. For such curves it is unspecified which of the aliases will be
returned if the curve name of the given group is NID_undef.
If \fBnist_only\fR is 1 it will only look for NIST approved curves, otherwise it searches all built-in curves.
This function may be passed a BN_CTX object in the \fBctx\fR parameter.
The \fBctx\fR parameter may be NULL.
.PP
\&\fBEC_GROUP_cmp()\fR compares \fBa\fR and \fBb\fR to determine whether they represent the same curve or not.
.PP
The functions \fBEC_GROUP_get_basis_type()\fR, \fBEC_GROUP_get_trinomial_basis()\fR and \fBEC_GROUP_get_pentanomial_basis()\fR should only be called for curves
defined over an F2^m field. Addition and multiplication operations within an F2^m field are performed using an irreducible polynomial
function f(x). This function is either a trinomial of the form:
.PP
f(x) = x^m + x^k + 1 with m > k >= 1
.PP
or a pentanomial of the form:
.PP
f(x) = x^m + x^k3 + x^k2 + x^k1 + 1 with m > k3 > k2 > k1 >= 1
.PP
The function \fBEC_GROUP_get_basis_type()\fR returns a NID identifying whether a trinomial or pentanomial is in use for the field. The
function \fBEC_GROUP_get_trinomial_basis()\fR must only be called where f(x) is of the trinomial form, and returns the value of \fBk\fR. Similarly
the function \fBEC_GROUP_get_pentanomial_basis()\fR must only be called where f(x) is of the pentanomial form, and returns the values of \fBk1\fR,
\&\fBk2\fR and \fBk3\fR respectively.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The following functions return 1 on success or 0 on error: \fBEC_GROUP_copy()\fR, \fBEC_GROUP_set_generator()\fR, \fBEC_GROUP_check()\fR,
\&\fBEC_GROUP_check_discriminant()\fR, \fBEC_GROUP_get_trinomial_basis()\fR and \fBEC_GROUP_get_pentanomial_basis()\fR.
.PP
\&\fBEC_GROUP_dup()\fR returns a pointer to the duplicated curve, or NULL on error.
.PP
\&\fBEC_GROUP_method_of()\fR returns the EC_METHOD implementation in use for the given curve or NULL on error.
.PP
\&\fBEC_GROUP_get0_generator()\fR returns the generator for the given curve or NULL on error.
.PP
\&\fBEC_GROUP_get_order()\fR returns 0 if the order is not set (or set to zero) for
\&\fBgroup\fR or if copying into \fBorder\fR fails, 1 otherwise.
.PP
\&\fBEC_GROUP_get_cofactor()\fR returns 0 if the cofactor is not set (or is set to zero) for \fBgroup\fR or if copying into \fBcofactor\fR fails, 1 otherwise.
.PP
\&\fBEC_GROUP_get_curve_name()\fR returns the curve name (NID) for \fBgroup\fR or will return NID_undef if no curve name is associated.
.PP
\&\fBEC_GROUP_get_asn1_flag()\fR returns the ASN1 flag for the specified \fBgroup\fR .
.PP
\&\fBEC_GROUP_get_point_conversion_form()\fR returns the point_conversion_form for \fBgroup\fR.
.PP
\&\fBEC_GROUP_get_degree()\fR returns the degree for \fBgroup\fR or 0 if the operation is not supported by the underlying group implementation.
.PP
\&\fBEC_GROUP_get_field_type()\fR returns either \fBNID_X9_62_prime_field\fR for prime curves
or \fBNID_X9_62_characteristic_two_field\fR for binary curves;
these values are defined in the \fI<openssl/obj_mac.h>\fR header file.
.PP
\&\fBEC_GROUP_check_named_curve()\fR returns the nid of the matching named curve, otherwise it returns 0 for no match, or \-1 on error.
.PP
\&\fBEC_GROUP_get0_order()\fR returns an internal pointer to the group order.
\&\fBEC_GROUP_order_bits()\fR returns the number of bits in the group order.
\&\fBEC_GROUP_get0_cofactor()\fR returns an internal pointer to the group cofactor.
\&\fBEC_GROUP_get0_field()\fR returns an internal pointer to the group field. For curves over GF(p), this is the modulus; for curves
over GF(2^m), this is the irreducible polynomial defining the field.
.PP
\&\fBEC_GROUP_get0_seed()\fR returns a pointer to the seed that was used to generate the parameter b, or NULL if the seed is not
specified. \fBEC_GROUP_get_seed_len()\fR returns the length of the seed or 0 if the seed is not specified.
.PP
\&\fBEC_GROUP_set_seed()\fR returns the length of the seed that has been set. If the supplied seed is NULL, or the supplied seed length is
0, the return value will be 1. On error 0 is returned.
.PP
\&\fBEC_GROUP_cmp()\fR returns 0 if the curves are equal, 1 if they are not equal, or \-1 on error.
.PP
\&\fBEC_GROUP_get_basis_type()\fR returns the values NID_X9_62_tpBasis or NID_X9_62_ppBasis (as defined in \fI<openssl/obj_mac.h>\fR) for a
trinomial or pentanomial respectively. Alternatively in the event of an error a 0 is returned.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7), \fBEC_GROUP_new\fR\|(3),
\&\fBEC_POINT_new\fR\|(3), \fBEC_POINT_add\fR\|(3), \fBEC_KEY_new\fR\|(3),
\&\fBEC_GFp_simple_method\fR\|(3), \fBd2i_ECPKParameters\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBEC_GROUP_method_of()\fR was deprecated in OpenSSL 3.0.
\&\fBEC_GROUP_get0_field()\fR, \fBEC_GROUP_check_named_curve()\fR and \fBEC_GROUP_get_field_type()\fR were added in OpenSSL 3.0.
\&\fBEC_GROUP_get0_order()\fR, \fBEC_GROUP_order_bits()\fR and \fBEC_GROUP_get0_cofactor()\fR were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2013\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
