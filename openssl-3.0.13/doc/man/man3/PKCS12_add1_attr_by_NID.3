.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS12_ADD1_ATTR_BY_NID 3ossl"
.TH PKCS12_ADD1_ATTR_BY_NID 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS12_add1_attr_by_NID, PKCS12_add1_attr_by_txt \- Add an attribute to a PKCS#12
safeBag structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pkcs12.h>
\&
\& int PKCS12_add1_attr_by_NID(PKCS12_SAFEBAG *bag, int nid, int type,
\&                             const unsigned char *bytes, int len);
\& int PKCS12_add1_attr_by_txt(PKCS12_SAFEBAG *bag, const char *attrname, int type,
\&                             const unsigned char *bytes, int len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions add a PKCS#12 Attribute to the Attribute Set of the \fBbag\fR.
.PP
\&\fBPKCS12_add1_attr_by_NID()\fR adds an attribute of type \fBnid\fR with a value of ASN1
type \fBtype\fR constructed using \fBlen\fR bytes from \fBbytes\fR.
.PP
\&\fBPKCS12_add1_attr_by_txt()\fR adds an attribute of type \fBattrname\fR with a value of
ASN1 type \fBtype\fR constructed using \fBlen\fR bytes from \fBbytes\fR.
.SH NOTES
.IX Header "NOTES"
These functions do not check whether an existing attribute of the same type is
present. There can be multiple attributes with the same type assigned to a
safeBag.
.PP
Both functions were added in OpenSSL 3.0.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
A return value of 1 indicates success, 0 indicates failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBPKCS12_create\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
