.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_SET_TYPE 3ossl"
.TH EVP_PKEY_SET_TYPE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_set_type, EVP_PKEY_set_type_str, EVP_PKEY_set_type_by_keymgmt
\&\- functions to change the EVP_PKEY type
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_set_type(EVP_PKEY *pkey, int type);
\& int EVP_PKEY_set_type_str(EVP_PKEY *pkey, const char *str, int len);
\& int EVP_PKEY_set_type_by_keymgmt(EVP_PKEY *pkey, EVP_KEYMGMT *keymgmt);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All the functions described here behave the same in so far that they
clear all the previous key data and methods from \fIpkey\fR, and reset it
to be of the type of key given by the different arguments.  If
\&\fIpkey\fR is NULL, these functions will still return the same return
values as if it wasn't.
.PP
\&\fBEVP_PKEY_set_type()\fR initialises \fIpkey\fR to contain an internal legacy
key.  When doing this, it finds a \fBEVP_PKEY_ASN1_METHOD\fR\|(3)
corresponding to \fItype\fR, and associates \fIpkey\fR with the findings.
It is an error if no \fBEVP_PKEY_ASN1_METHOD\fR\|(3) could be found for
\&\fItype\fR.
.PP
\&\fBEVP_PKEY_set_type_str()\fR initialises \fIpkey\fR to contain an internal legacy
key. When doing this, it finds a \fBEVP_PKEY_ASN1_METHOD\fR\|(3)
corresponding to \fIstr\fR that has then length \fIlen\fR, and associates
\&\fIpkey\fR with the findings.
It is an error if no \fBEVP_PKEY_ASN1_METHOD\fR\|(3) could be found for
\&\fItype\fR.
.PP
For both \fBEVP_PKEY_set_type()\fR and \fBEVP_PKEY_set_type_str()\fR, \fIpkey\fR gets
a numeric type, which can be retrieved with \fBEVP_PKEY_get_id\fR\|(3).  This
numeric type is taken from the \fBEVP_PKEY_ASN1_METHOD\fR\|(3) that was
found, and is equal to or closely related to \fItype\fR in the case of
\&\fBEVP_PKEY_set_type()\fR, or related to \fIstr\fR in the case of
\&\fBEVP_PKEY_set_type_str()\fR.
.PP
\&\fBEVP_PKEY_set_type_by_keymgmt()\fR initialises \fIpkey\fR to contain an
internal provider side key.  When doing this, it associates \fIpkey\fR
with \fIkeymgmt\fR.  For keys initialised like this, the numeric type
retrieved with \fBEVP_PKEY_get_id\fR\|(3) will always be \fBEVP_PKEY_NONE\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All functions described here return 1 if successful, or 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_assign\fR\|(3), \fBEVP_PKEY_get_id\fR\|(3), \fBEVP_PKEY_get0_RSA\fR\|(3),
\&\fBEVP_PKEY_copy_parameters\fR\|(3), \fBEVP_PKEY_ASN1_METHOD\fR\|(3),
\&\fBEVP_KEYMGMT\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
