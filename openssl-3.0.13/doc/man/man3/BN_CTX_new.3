.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BN_CTX_NEW 3ossl"
.TH BN_CTX_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BN_CTX_new_ex, BN_CTX_new, BN_CTX_secure_new_ex, BN_CTX_secure_new, BN_CTX_free
\&\- allocate and free BN_CTX structures
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bn.h>
\&
\& BN_CTX *BN_CTX_new_ex(OSSL_LIB_CTX *ctx);
\& BN_CTX *BN_CTX_new(void);
\&
\& BN_CTX *BN_CTX_secure_new_ex(OSSL_LIB_CTX *ctx);
\& BN_CTX *BN_CTX_secure_new(void);
\&
\& void BN_CTX_free(BN_CTX *c);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
A \fBBN_CTX\fR is a structure that holds \fBBIGNUM\fR temporary variables used by
library functions. Since dynamic memory allocation to create \fBBIGNUM\fRs
is rather expensive when used in conjunction with repeated subroutine
calls, the \fBBN_CTX\fR structure is used.
.PP
\&\fBBN_CTX_new_ex()\fR allocates and initializes a \fBBN_CTX\fR structure for the given
library context \fBctx\fR. The <ctx> value may be NULL in which case the default
library context will be used. \fBBN_CTX_new()\fR is the same as \fBBN_CTX_new_ex()\fR except
that the default library context is always used.
.PP
\&\fBBN_CTX_secure_new_ex()\fR allocates and initializes a \fBBN_CTX\fR structure
but uses the secure heap (see \fBCRYPTO_secure_malloc\fR\|(3)) to hold the
\&\fBBIGNUM\fRs for the given library context \fBctx\fR. The <ctx> value may be NULL in
which case the default library context will be used. \fBBN_CTX_secure_new()\fR is the
same as \fBBN_CTX_secure_new_ex()\fR except that the default library context is always
used.
.PP
\&\fBBN_CTX_free()\fR frees the components of the \fBBN_CTX\fR and the structure itself.
Since \fBBN_CTX_start()\fR is required in order to obtain \fBBIGNUM\fRs from the
\&\fBBN_CTX\fR, in most cases \fBBN_CTX_end()\fR must be called before the \fBBN_CTX\fR may
be freed by \fBBN_CTX_free()\fR.  If \fBc\fR is NULL, nothing is done.
.PP
A given \fBBN_CTX\fR must only be used by a single thread of execution.  No
locking is performed, and the internal pool allocator will not properly handle
multiple threads of execution.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBN_CTX_new()\fR and \fBBN_CTX_secure_new()\fR return a pointer to the \fBBN_CTX\fR.
If the allocation fails,
they return \fBNULL\fR and sets an error code that can be obtained by
\&\fBERR_get_error\fR\|(3).
.PP
\&\fBBN_CTX_free()\fR has no return values.
.SH "REMOVED FUNCTIONALITY"
.IX Header "REMOVED FUNCTIONALITY"
.Vb 1
\& void BN_CTX_init(BN_CTX *c);
.Ve
.PP
\&\fBBN_CTX_init()\fR is no longer available as of OpenSSL 1.1.0. Applications should
replace use of BN_CTX_init with BN_CTX_new instead:
.PP
.Vb 6
\& BN_CTX *ctx;
\& ctx = BN_CTX_new();
\& if (!ctx)
\&     /* error */
\& ...
\& BN_CTX_free(ctx);
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBBN_add\fR\|(3),
\&\fBBN_CTX_start\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBBN_CTX_init()\fR was removed in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
