.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DSA_SIGN 3ossl"
.TH DSA_SIGN 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
DSA_sign, DSA_sign_setup, DSA_verify \- DSA signatures
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/dsa.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 2
\& int DSA_sign(int type, const unsigned char *dgst, int len,
\&              unsigned char *sigret, unsigned int *siglen, DSA *dsa);
\&
\& int DSA_sign_setup(DSA *dsa, BN_CTX *ctx, BIGNUM **kinvp, BIGNUM **rp);
\&
\& int DSA_verify(int type, const unsigned char *dgst, int len,
\&                unsigned char *sigbuf, int siglen, DSA *dsa);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated.
Applications should instead use \fBEVP_PKEY_sign_init\fR\|(3), \fBEVP_PKEY_sign\fR\|(3),
\&\fBEVP_PKEY_verify_init\fR\|(3) and \fBEVP_PKEY_verify\fR\|(3).
.PP
\&\fBDSA_sign()\fR computes a digital signature on the \fBlen\fR byte message
digest \fBdgst\fR using the private key \fBdsa\fR and places its ASN.1 DER
encoding at \fBsigret\fR. The length of the signature is places in
*\fBsiglen\fR. \fBsigret\fR must point to DSA_size(\fBdsa\fR) bytes of memory.
.PP
\&\fBDSA_sign_setup()\fR is defined only for backward binary compatibility and
should not be used.
Since OpenSSL 1.1.0 the DSA type is opaque and the output of
\&\fBDSA_sign_setup()\fR cannot be used anyway: calling this function will only
cause overhead, and does not affect the actual signature
(pre\-)computation.
.PP
\&\fBDSA_verify()\fR verifies that the signature \fBsigbuf\fR of size \fBsiglen\fR
matches a given message digest \fBdgst\fR of size \fBlen\fR.
\&\fBdsa\fR is the signer's public key.
.PP
The \fBtype\fR parameter is ignored.
.PP
The random generator must be seeded when \fBDSA_sign()\fR (or \fBDSA_sign_setup()\fR)
is called.
If the automatic seeding or reseeding of the OpenSSL CSPRNG fails due to
external circumstances (see \fBRAND\fR\|(7)), the operation will fail.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBDSA_sign()\fR and \fBDSA_sign_setup()\fR return 1 on success, 0 on error.
\&\fBDSA_verify()\fR returns 1 for a valid signature, 0 for an incorrect
signature and \-1 on error. The error codes can be obtained by
\&\fBERR_get_error\fR\|(3).
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
US Federal Information Processing Standard FIPS186\-4 (Digital Signature
Standard, DSS), ANSI X9.30
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBDSA_new\fR\|(3), \fBERR_get_error\fR\|(3), \fBRAND_bytes\fR\|(3),
\&\fBDSA_do_sign\fR\|(3),
\&\fBRAND\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
All of these functions were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
