.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS12_GEN_MAC 3ossl"
.TH PKCS12_GEN_MAC 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS12_gen_mac, PKCS12_setup_mac, PKCS12_set_mac,
PKCS12_verify_mac \- Functions to create and manipulate a PKCS#12 structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pkcs12.h>
\&
\& int PKCS12_gen_mac(PKCS12 *p12, const char *pass, int passlen,
\&                    unsigned char *mac, unsigned int *maclen);
\& int PKCS12_verify_mac(PKCS12 *p12, const char *pass, int passlen);
\& int PKCS12_set_mac(PKCS12 *p12, const char *pass, int passlen,
\&                    unsigned char *salt, int saltlen, int iter,
\&                    const EVP_MD *md_type);
\& int PKCS12_setup_mac(PKCS12 *p12, int iter, unsigned char *salt,
\&                      int saltlen, const EVP_MD *md_type);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPKCS12_gen_mac()\fR generates an HMAC over the entire PKCS#12 object using the
supplied password along with a set of already configured parameters.
The default key generation mechanism used is PKCS12KDF.
.PP
\&\fBPKCS12_verify_mac()\fR verifies the PKCS#12 object's HMAC using the supplied
password.
.PP
\&\fBPKCS12_setup_mac()\fR sets the MAC part of the PKCS#12 structure with the supplied
parameters.
.PP
\&\fBPKCS12_set_mac()\fR sets the MAC and MAC parameters into the PKCS#12 object.
.PP
\&\fIpass\fR is the passphrase to use in the HMAC. \fIsalt\fR is the salt value to use,
\&\fIiter\fR is the iteration count and \fImd_type\fR is the message digest
function to use.
.SH NOTES
.IX Header "NOTES"
If \fIsalt\fR is NULL then a suitable salt will be generated and used.
.PP
If \fIiter\fR is 1 then an iteration count will be omitted from the PKCS#12
structure.
.PP
\&\fBPKCS12_gen_mac()\fR, \fBPKCS12_verify_mac()\fR and \fBPKCS12_set_mac()\fR make assumptions
regarding the encoding of the given passphrase. See \fBpassphrase\-encoding\fR\|(7)
for more information.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All functions return 1 on success and 0 if an error occurred.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
IETF RFC 7292 (<https://tools.ietf.org/html/rfc7292>)
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBd2i_PKCS12\fR\|(3),
\&\fBEVP_KDF\-PKCS12KDF\fR\|(7),
\&\fBPKCS12_create\fR\|(3),
\&\fBpassphrase\-encoding\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
