.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL_ADD_ALL_ALGORITHMS 3ossl"
.TH OPENSSL_ADD_ALL_ALGORITHMS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OpenSSL_add_all_algorithms, OpenSSL_add_all_ciphers, OpenSSL_add_all_digests, EVP_cleanup \-
add algorithms to internal table
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 1.1.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 3
\& void OpenSSL_add_all_algorithms(void);
\& void OpenSSL_add_all_ciphers(void);
\& void OpenSSL_add_all_digests(void);
\&
\& void EVP_cleanup(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
OpenSSL keeps an internal table of digest algorithms and ciphers. It uses
this table to lookup ciphers via functions such as \fBEVP_get_cipher_byname()\fR.
.PP
\&\fBOpenSSL_add_all_digests()\fR adds all digest algorithms to the table.
.PP
\&\fBOpenSSL_add_all_algorithms()\fR adds all algorithms to the table (digests and
ciphers).
.PP
\&\fBOpenSSL_add_all_ciphers()\fR adds all encryption algorithms to the table including
password based encryption algorithms.
.PP
In versions prior to 1.1.0 \fBEVP_cleanup()\fR removed all ciphers and digests from
the table. It no longer has any effect in OpenSSL 1.1.0.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
None of the functions return a value.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBevp\fR\|(7), \fBEVP_DigestInit\fR\|(3),
\&\fBEVP_EncryptInit\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBOpenSSL_add_all_algorithms()\fR, \fBOpenSSL_add_all_ciphers()\fR,
\&\fBOpenSSL_add_all_digests()\fR, and \fBEVP_cleanup()\fR, functions
were deprecated in OpenSSL 1.1.0 by \fBOPENSSL_init_crypto()\fR and should
not be used.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
