.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_RAND 3ossl"
.TH EVP_RAND 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_RAND, EVP_RAND_fetch, EVP_RAND_free, EVP_RAND_up_ref, EVP_RAND_CTX,
EVP_RAND_CTX_new, EVP_RAND_CTX_free, EVP_RAND_instantiate,
EVP_RAND_uninstantiate, EVP_RAND_generate, EVP_RAND_reseed, EVP_RAND_nonce,
EVP_RAND_enable_locking, EVP_RAND_verify_zeroization, EVP_RAND_get_strength,
EVP_RAND_get_state,
EVP_RAND_get0_provider, EVP_RAND_CTX_get0_rand, EVP_RAND_is_a,
EVP_RAND_get0_name, EVP_RAND_names_do_all,
EVP_RAND_get0_description,
EVP_RAND_CTX_get_params,
EVP_RAND_CTX_set_params, EVP_RAND_do_all_provided, EVP_RAND_get_params,
EVP_RAND_gettable_ctx_params, EVP_RAND_settable_ctx_params,
EVP_RAND_CTX_gettable_params, EVP_RAND_CTX_settable_params,
EVP_RAND_gettable_params, EVP_RAND_STATE_UNINITIALISED, EVP_RAND_STATE_READY,
EVP_RAND_STATE_ERROR \- EVP RAND routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& typedef struct evp_rand_st EVP_RAND;
\& typedef struct evp_rand_ctx_st EVP_RAND_CTX;
\&
\& EVP_RAND *EVP_RAND_fetch(OSSL_LIB_CTX *libctx, const char *algorithm,
\&                        const char *properties);
\& int EVP_RAND_up_ref(EVP_RAND *rand);
\& void EVP_RAND_free(EVP_RAND *rand);
\& EVP_RAND_CTX *EVP_RAND_CTX_new(EVP_RAND *rand, EVP_RAND_CTX *parent);
\& void EVP_RAND_CTX_free(EVP_RAND_CTX *ctx);
\& EVP_RAND *EVP_RAND_CTX_get0_rand(EVP_RAND_CTX *ctx);
\& int EVP_RAND_get_params(EVP_RAND *rand, OSSL_PARAM params[]);
\& int EVP_RAND_CTX_get_params(EVP_RAND_CTX *ctx, OSSL_PARAM params[]);
\& int EVP_RAND_CTX_set_params(EVP_RAND_CTX *ctx, const OSSL_PARAM params[]);
\& const OSSL_PARAM *EVP_RAND_gettable_params(const EVP_RAND *rand);
\& const OSSL_PARAM *EVP_RAND_gettable_ctx_params(const EVP_RAND *rand);
\& const OSSL_PARAM *EVP_RAND_settable_ctx_params(const EVP_RAND *rand);
\& const OSSL_PARAM *EVP_RAND_CTX_gettable_params(EVP_RAND_CTX *ctx);
\& const OSSL_PARAM *EVP_RAND_CTX_settable_params(EVP_RAND_CTX *ctx);
\& const char *EVP_RAND_get0_name(const EVP_RAND *rand);
\& const char *EVP_RAND_get0_description(const EVP_RAND *rand);
\& int EVP_RAND_is_a(const EVP_RAND *rand, const char *name);
\& const OSSL_PROVIDER *EVP_RAND_get0_provider(const EVP_RAND *rand);
\& void EVP_RAND_do_all_provided(OSSL_LIB_CTX *libctx,
\&                               void (*fn)(EVP_RAND *rand, void *arg),
\&                               void *arg);
\& int EVP_RAND_names_do_all(const EVP_RAND *rand,
\&                           void (*fn)(const char *name, void *data),
\&                           void *data);
\&
\& int EVP_RAND_instantiate(EVP_RAND_CTX *ctx, unsigned int strength,
\&                          int prediction_resistance,
\&                          const unsigned char *pstr, size_t pstr_len,
\&                          const OSSL_PARAM params[]);
\& int EVP_RAND_uninstantiate(EVP_RAND_CTX *ctx);
\& int EVP_RAND_generate(EVP_RAND_CTX *ctx, unsigned char *out, size_t outlen,
\&                       unsigned int strength, int prediction_resistance,
\&                       const unsigned char *addin, size_t addin_len);
\& int EVP_RAND_reseed(EVP_RAND_CTX *ctx, int prediction_resistance,
\&                     const unsigned char *ent, size_t ent_len,
\&                     const unsigned char *addin, size_t addin_len);
\& int EVP_RAND_nonce(EVP_RAND_CTX *ctx, unsigned char *out, size_t outlen);
\& int EVP_RAND_enable_locking(EVP_RAND_CTX *ctx);
\& int EVP_RAND_verify_zeroization(EVP_RAND_CTX *ctx);
\& unsigned int EVP_RAND_get_strength(EVP_RAND_CTX *ctx);
\& int EVP_RAND_get_state(EVP_RAND_CTX *ctx);
\&
\& #define EVP_RAND_STATE_UNINITIALISED    0
\& #define EVP_RAND_STATE_READY            1
\& #define EVP_RAND_STATE_ERROR            2
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The EVP RAND routines are a high-level interface to random number generators
both deterministic and not.
If you just want to generate random bytes then you don't need to use
these functions: just call \fBRAND_bytes()\fR or \fBRAND_priv_bytes()\fR.
If you want to do more, these calls should be used instead of the older
RAND and RAND_DRBG functions.
.PP
After creating a \fBEVP_RAND_CTX\fR for the required algorithm using
\&\fBEVP_RAND_CTX_new()\fR, inputs to the algorithm are supplied either by
passing them as part of the \fBEVP_RAND_instantiate()\fR call or using calls to
\&\fBEVP_RAND_CTX_set_params()\fR before calling \fBEVP_RAND_instantiate()\fR.  Finally,
call \fBEVP_RAND_generate()\fR to produce cryptographically secure random bytes.
.SS Types
.IX Subsection "Types"
\&\fBEVP_RAND\fR is a type that holds the implementation of a RAND.
.PP
\&\fBEVP_RAND_CTX\fR is a context type that holds the algorithm inputs.
\&\fBEVP_RAND_CTX\fR structures are reference counted.
.SS "Algorithm implementation fetching"
.IX Subsection "Algorithm implementation fetching"
\&\fBEVP_RAND_fetch()\fR fetches an implementation of a RAND \fIalgorithm\fR, given
a library context \fIlibctx\fR and a set of \fIproperties\fR.
See "ALGORITHM FETCHING" in \fBcrypto\fR\|(7) for further information.
.PP
The returned value must eventually be freed with
\&\fBEVP_RAND_free\fR\|(3).
.PP
\&\fBEVP_RAND_up_ref()\fR increments the reference count of an already fetched
RAND.
.PP
\&\fBEVP_RAND_free()\fR frees a fetched algorithm.
NULL is a valid parameter, for which this function is a no-op.
.SS "Context manipulation functions"
.IX Subsection "Context manipulation functions"
\&\fBEVP_RAND_CTX_new()\fR creates a new context for the RAND implementation \fIrand\fR.
If not NULL, \fIparent\fR specifies the seed source for this implementation.
Not all random number generators need to have a seed source specified.
If a parent is required, a NULL \fIparent\fR will utilise the operating
system entropy sources.
It is recommended to minimise the number of random number generators that
rely on the operating system for their randomness because this is often scarce.
.PP
\&\fBEVP_RAND_CTX_free()\fR frees up the context \fIctx\fR.  If \fIctx\fR is NULL, nothing
is done.
.PP
\&\fBEVP_RAND_CTX_get0_rand()\fR returns the \fBEVP_RAND\fR associated with the context
\&\fIctx\fR.
.SS "Random Number Generator Functions"
.IX Subsection "Random Number Generator Functions"
\&\fBEVP_RAND_instantiate()\fR processes any parameters in \fIparams\fR and
then instantiates the RAND \fIctx\fR with a minimum security strength
of <strength> and personalisation string \fIpstr\fR of length <pstr_len>.
If \fIprediction_resistance\fR is specified, fresh entropy from a live source
will be sought.  This call operates as per NIST SP 800\-90A and SP 800\-90C.
.PP
\&\fBEVP_RAND_uninstantiate()\fR uninstantiates the RAND \fIctx\fR as per
NIST SP 800\-90A and SP 800\-90C.  Subsequent to this call, the RAND cannot
be used to generate bytes.  It can only be freed or instantiated again.
.PP
\&\fBEVP_RAND_generate()\fR produces random bytes from the RAND \fIctx\fR with the
additional input \fIaddin\fR of length \fIaddin_len\fR.  The bytes
produced will meet the security \fIstrength\fR.
If \fIprediction_resistance\fR is specified, fresh entropy from a live source
will be sought.  This call operates as per NIST SP 800\-90A and SP 800\-90C.
.PP
\&\fBEVP_RAND_reseed()\fR reseeds the RAND with new entropy.
Entropy \fIent\fR of length \fIent_len\fR bytes can be supplied as can additional
input \fIaddin\fR of length \fIaddin_len\fR bytes.  In the FIPS provider, both are
treated as additional input as per NIST SP\-800\-90Ar1, Sections 9.1 and 9.2.
Additional seed material is also drawn from the RAND's parent or the
operating system.  If \fIprediction_resistance\fR is specified, fresh entropy
from a live source will be sought.  This call operates as per NIST SP 800\-90A
and SP 800\-90C.
.PP
\&\fBEVP_RAND_nonce()\fR creates a nonce in \fIout\fR of maximum length \fIoutlen\fR
bytes from the RAND \fIctx\fR. The function returns the length of the generated
nonce. If \fIout\fR is NULL, the length is still returned but no generation
takes place. This allows a caller to dynamically allocate a buffer of the
appropriate size.
.PP
\&\fBEVP_RAND_enable_locking()\fR enables locking for the RAND \fIctx\fR and all of
its parents.  After this \fIctx\fR will operate in a thread safe manner, albeit
more slowly. This function is not itself thread safe if called with the same
\&\fIctx\fR from multiple threads. Typically locking should be enabled before a
\&\fIctx\fR is shared across multiple threads.
.PP
\&\fBEVP_RAND_get_params()\fR retrieves details about the implementation
\&\fIrand\fR.
The set of parameters given with \fIparams\fR determine exactly what
parameters should be retrieved.
Note that a parameter that is unknown in the underlying context is
simply ignored.
.PP
\&\fBEVP_RAND_CTX_get_params()\fR retrieves chosen parameters, given the
context \fIctx\fR and its underlying context.
The set of parameters given with \fIparams\fR determine exactly what
parameters should be retrieved.
Note that a parameter that is unknown in the underlying context is
simply ignored.
.PP
\&\fBEVP_RAND_CTX_set_params()\fR passes chosen parameters to the underlying
context, given a context \fIctx\fR.
The set of parameters given with \fIparams\fR determine exactly what
parameters are passed down.
Note that a parameter that is unknown in the underlying context is
simply ignored.
Also, what happens when a needed parameter isn't passed down is
defined by the implementation.
.PP
\&\fBEVP_RAND_gettable_params()\fR returns an \fBOSSL_PARAM\fR\|(3) array that describes
the retrievable and settable parameters.  \fBEVP_RAND_gettable_params()\fR returns
parameters that can be used with \fBEVP_RAND_get_params()\fR.
.PP
\&\fBEVP_RAND_gettable_ctx_params()\fR and \fBEVP_RAND_CTX_gettable_params()\fR return
constant \fBOSSL_PARAM\fR\|(3) arrays that describe the retrievable parameters that
can be used with \fBEVP_RAND_CTX_get_params()\fR.  \fBEVP_RAND_gettable_ctx_params()\fR
returns the parameters that can be retrieved from the algorithm, whereas
\&\fBEVP_RAND_CTX_gettable_params()\fR returns the parameters that can be retrieved
in the context's current state.
.PP
\&\fBEVP_RAND_settable_ctx_params()\fR and \fBEVP_RAND_CTX_settable_params()\fR return
constant \fBOSSL_PARAM\fR\|(3) arrays that describe the settable parameters that
can be used with \fBEVP_RAND_CTX_set_params()\fR.  \fBEVP_RAND_settable_ctx_params()\fR
returns the parameters that can be retrieved from the algorithm, whereas
\&\fBEVP_RAND_CTX_settable_params()\fR returns the parameters that can be retrieved
in the context's current state.
.SS "Information functions"
.IX Subsection "Information functions"
\&\fBEVP_RAND_get_strength()\fR returns the security strength of the RAND \fIctx\fR.
.PP
\&\fBEVP_RAND_get_state()\fR returns the current state of the RAND \fIctx\fR.
States defined by the OpenSSL RNGs are:
.IP \(bu 4
EVP_RAND_STATE_UNINITIALISED: this RNG is currently uninitialised.
The instantiate call will change this to the ready state.
.IP \(bu 4
EVP_RAND_STATE_READY: this RNG is currently ready to generate output.
.IP \(bu 4
EVP_RAND_STATE_ERROR: this RNG is in an error state.
.PP
\&\fBEVP_RAND_is_a()\fR returns 1 if \fIrand\fR is an implementation of an
algorithm that's identifiable with \fIname\fR, otherwise 0.
.PP
\&\fBEVP_RAND_get0_provider()\fR returns the provider that holds the implementation
of the given \fIrand\fR.
.PP
\&\fBEVP_RAND_do_all_provided()\fR traverses all RAND implemented by all activated
providers in the given library context \fIlibctx\fR, and for each of the
implementations, calls the given function \fIfn\fR with the implementation method
and the given \fIarg\fR as argument.
.PP
\&\fBEVP_RAND_get0_name()\fR returns the canonical name of \fIrand\fR.
.PP
\&\fBEVP_RAND_names_do_all()\fR traverses all names for \fIrand\fR, and calls
\&\fIfn\fR with each name and \fIdata\fR.
.PP
\&\fBEVP_RAND_get0_description()\fR returns a description of the rand, meant for
display and human consumption.  The description is at the discretion of
the rand implementation.
.PP
\&\fBEVP_RAND_verify_zeroization()\fR confirms if the internal DRBG state is
currently zeroed.  This is used by the FIPS provider to support the mandatory
self tests.
.SH PARAMETERS
.IX Header "PARAMETERS"
The standard parameter names are:
.IP """state"" (\fBOSSL_RAND_PARAM_STATE\fR) <integer>" 4
.IX Item """state"" (OSSL_RAND_PARAM_STATE) <integer>"
Returns the state of the random number generator.
.IP """strength"" (\fBOSSL_RAND_PARAM_STRENGTH\fR) <unsigned integer>" 4
.IX Item """strength"" (OSSL_RAND_PARAM_STRENGTH) <unsigned integer>"
Returns the bit strength of the random number generator.
.PP
For rands that are also deterministic random bit generators (DRBGs), these
additional parameters are recognised. Not all
parameters are relevant to, or are understood by all DRBG rands:
.IP """reseed_requests"" (\fBOSSL_DRBG_PARAM_RESEED_REQUESTS\fR) <unsigned integer>" 4
.IX Item """reseed_requests"" (OSSL_DRBG_PARAM_RESEED_REQUESTS) <unsigned integer>"
Reads or set the number of generate requests before reseeding the
associated RAND ctx.
.IP """reseed_time_interval"" (\fBOSSL_DRBG_PARAM_RESEED_TIME_INTERVAL\fR) <integer>" 4
.IX Item """reseed_time_interval"" (OSSL_DRBG_PARAM_RESEED_TIME_INTERVAL) <integer>"
Reads or set the number of elapsed seconds before reseeding the
associated RAND ctx.
.IP """max_request"" (\fBOSSL_DRBG_PARAM_RESEED_REQUESTS\fR) <unsigned integer>" 4
.IX Item """max_request"" (OSSL_DRBG_PARAM_RESEED_REQUESTS) <unsigned integer>"
Specifies the maximum number of bytes that can be generated in a single
call to OSSL_FUNC_rand_generate.
.IP """min_entropylen"" (\fBOSSL_DRBG_PARAM_MIN_ENTROPYLEN\fR) <unsigned integer>" 4
.IX Item """min_entropylen"" (OSSL_DRBG_PARAM_MIN_ENTROPYLEN) <unsigned integer>"
.PD 0
.IP """max_entropylen"" (\fBOSSL_DRBG_PARAM_MAX_ENTROPYLEN\fR) <unsigned integer>" 4
.IX Item """max_entropylen"" (OSSL_DRBG_PARAM_MAX_ENTROPYLEN) <unsigned integer>"
.PD
Specify the minimum and maximum number of bytes of random material that
can be used to seed the DRBG.
.IP """min_noncelen"" (\fBOSSL_DRBG_PARAM_MIN_NONCELEN\fR) <unsigned integer>" 4
.IX Item """min_noncelen"" (OSSL_DRBG_PARAM_MIN_NONCELEN) <unsigned integer>"
.PD 0
.IP """max_noncelen"" (\fBOSSL_DRBG_PARAM_MAX_NONCELEN\fR) <unsigned integer>" 4
.IX Item """max_noncelen"" (OSSL_DRBG_PARAM_MAX_NONCELEN) <unsigned integer>"
.PD
Specify the minimum and maximum number of bytes of nonce that can be used to
seed the DRBG.
.IP """max_perslen"" (\fBOSSL_DRBG_PARAM_MAX_PERSLEN\fR) <unsigned integer>" 4
.IX Item """max_perslen"" (OSSL_DRBG_PARAM_MAX_PERSLEN) <unsigned integer>"
.PD 0
.IP """max_adinlen"" (\fBOSSL_DRBG_PARAM_MAX_ADINLEN\fR) <unsigned integer>" 4
.IX Item """max_adinlen"" (OSSL_DRBG_PARAM_MAX_ADINLEN) <unsigned integer>"
.PD
Specify the minimum and maximum number of bytes of personalisation string
that can be used with the DRBG.
.IP """reseed_counter"" (\fBOSSL_DRBG_PARAM_RESEED_COUNTER\fR) <unsigned integer>" 4
.IX Item """reseed_counter"" (OSSL_DRBG_PARAM_RESEED_COUNTER) <unsigned integer>"
Specifies the number of times the DRBG has been seeded or reseeded.
.IP """properties"" (\fBOSSL_RAND_PARAM_PROPERTIES\fR) <UTF8 string>" 4
.IX Item """properties"" (OSSL_RAND_PARAM_PROPERTIES) <UTF8 string>"
.PD 0
.IP """mac"" (\fBOSSL_RAND_PARAM_MAC\fR) <UTF8 string>" 4
.IX Item """mac"" (OSSL_RAND_PARAM_MAC) <UTF8 string>"
.IP """digest"" (\fBOSSL_RAND_PARAM_DIGEST\fR) <UTF8 string>" 4
.IX Item """digest"" (OSSL_RAND_PARAM_DIGEST) <UTF8 string>"
.IP """cipher"" (\fBOSSL_RAND_PARAM_CIPHER\fR) <UTF8 string>" 4
.IX Item """cipher"" (OSSL_RAND_PARAM_CIPHER) <UTF8 string>"
.PD
For RAND implementations that use an underlying computation MAC, digest or
cipher, these parameters set what the algorithm should be.
.Sp
The value is always the name of the intended algorithm,
or the properties in the case of \fBOSSL_RAND_PARAM_PROPERTIES\fR.
.SH NOTES
.IX Header "NOTES"
The use of a nonzero value for the \fIprediction_resistance\fR argument to
\&\fBEVP_RAND_instantiate()\fR, \fBEVP_RAND_generate()\fR or \fBEVP_RAND_reseed()\fR should
be used sparingly.  In the default setup, this will cause all public and
private DRBGs to be reseeded on next use.  Since, by default, public and
private DRBGs are allocated on a per thread basis, this can result in
significant overhead for highly multi-threaded applications.  For normal
use-cases, the default "reseed_requests" and "reseed_time_interval"
thresholds ensure sufficient prediction resistance over time and you
can reduce those values if you think they are too high.  Explicitly
requesting prediction resistance is intended for more special use-cases
like generating long-term secrets.
.PP
An \fBEVP_RAND_CTX\fR needs to have locking enabled if it acts as the parent of
more than one child and the children can be accessed concurrently.  This must
be done by explicitly calling \fBEVP_RAND_enable_locking()\fR.
.PP
The RAND life-cycle is described in \fBlife_cycle\-rand\fR\|(7).  In the future,
the transitions described there will be enforced.  When this is done, it will
not be considered a breaking change to the API.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_RAND_fetch()\fR returns a pointer to a newly fetched \fBEVP_RAND\fR, or
NULL if allocation failed.
.PP
\&\fBEVP_RAND_get0_provider()\fR returns a pointer to the provider for the RAND, or
NULL on error.
.PP
\&\fBEVP_RAND_CTX_get0_rand()\fR returns a pointer to the \fBEVP_RAND\fR associated
with the context.
.PP
\&\fBEVP_RAND_get0_name()\fR returns the name of the random number generation
algorithm.
.PP
\&\fBEVP_RAND_up_ref()\fR returns 1 on success, 0 on error.
.PP
\&\fBEVP_RAND_names_do_all()\fR returns 1 if the callback was called for all names. A
return value of 0 means that the callback was not called for any names.
.PP
\&\fBEVP_RAND_CTX_new()\fR returns either the newly allocated
\&\fBEVP_RAND_CTX\fR structure or NULL if an error occurred.
.PP
\&\fBEVP_RAND_CTX_free()\fR does not return a value.
.PP
\&\fBEVP_RAND_nonce()\fR returns the length of the nonce.
.PP
\&\fBEVP_RAND_get_strength()\fR returns the strength of the random number generator
in bits.
.PP
\&\fBEVP_RAND_gettable_params()\fR, \fBEVP_RAND_gettable_ctx_params()\fR and
\&\fBEVP_RAND_settable_ctx_params()\fR return an array of OSSL_PARAMs.
.PP
\&\fBEVP_RAND_verify_zeroization()\fR returns 1 if the internal DRBG state is
currently zeroed, and 0 if not.
.PP
The remaining functions return 1 for success and 0 or a negative value for
failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBRAND_bytes\fR\|(3),
\&\fBEVP_RAND\-CTR\-DRBG\fR\|(7),
\&\fBEVP_RAND\-HASH\-DRBG\fR\|(7),
\&\fBEVP_RAND\-HMAC\-DRBG\fR\|(7),
\&\fBEVP_RAND\-TEST\-RAND\fR\|(7),
\&\fBprovider\-rand\fR\|(7),
\&\fBlife_cycle\-rand\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
This functionality was added to OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
