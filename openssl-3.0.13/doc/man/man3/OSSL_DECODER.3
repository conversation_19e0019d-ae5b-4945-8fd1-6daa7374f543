.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_DECODER 3ossl"
.TH OSSL_DECODER 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_DECODER,
OSSL_DECODER_fetch,
OSSL_DECODER_up_ref,
OSSL_DECODER_free,
OSSL_DECODER_get0_provider,
OSSL_DECODER_get0_properties,
OSSL_DECODER_is_a,
OSSL_DECODER_get0_name,
OSSL_DECODER_get0_description,
OSSL_DECODER_do_all_provided,
OSSL_DECODER_names_do_all,
OSSL_DECODER_gettable_params,
OSSL_DECODER_get_params
\&\- Decoder method routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/decoder.h>
\&
\& typedef struct ossl_decoder_st OSSL_DECODER;
\&
\& OSSL_DECODER *OSSL_DECODER_fetch(OSSL_LIB_CTX *ctx, const char *name,
\&                                  const char *properties);
\& int OSSL_DECODER_up_ref(OSSL_DECODER *decoder);
\& void OSSL_DECODER_free(OSSL_DECODER *decoder);
\& const OSSL_PROVIDER *OSSL_DECODER_get0_provider(const OSSL_DECODER *decoder);
\& const char *OSSL_DECODER_get0_properties(const OSSL_DECODER *decoder);
\& int OSSL_DECODER_is_a(const OSSL_DECODER *decoder, const char *name);
\& const char *OSSL_DECODER_get0_name(const OSSL_DECODER *decoder);
\& const char *OSSL_DECODER_get0_description(const OSSL_DECODER *decoder);
\& void OSSL_DECODER_do_all_provided(OSSL_LIB_CTX *libctx,
\&                                   void (*fn)(OSSL_DECODER *decoder, void *arg),
\&                                   void *arg);
\& int OSSL_DECODER_names_do_all(const OSSL_DECODER *decoder,
\&                               void (*fn)(const char *name, void *data),
\&                               void *data);
\& const OSSL_PARAM *OSSL_DECODER_gettable_params(OSSL_DECODER *decoder);
\& int OSSL_DECODER_get_params(OSSL_DECODER_CTX *ctx, const OSSL_PARAM params[]);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOSSL_DECODER\fR is a method for decoders, which know how to
decode encoded data into an object of some type that the rest
of OpenSSL knows how to handle.
.PP
\&\fBOSSL_DECODER_fetch()\fR looks for an algorithm within the provider that
has been loaded into the \fBOSSL_LIB_CTX\fR given by \fIctx\fR, having the
name given by \fIname\fR and the properties given by \fIproperties\fR.
The \fIname\fR determines what type of object the fetched decoder
method is expected to be able to decode, and the properties are
used to determine the expected output type.
For known properties and the values they may have, please have a look
in "Names and properties" in \fBprovider\-encoder\fR\|(7).
.PP
\&\fBOSSL_DECODER_up_ref()\fR increments the reference count for the given
\&\fIdecoder\fR.
.PP
\&\fBOSSL_DECODER_free()\fR decrements the reference count for the given
\&\fIdecoder\fR, and when the count reaches zero, frees it.
.PP
\&\fBOSSL_DECODER_get0_provider()\fR returns the provider of the given
\&\fIdecoder\fR.
.PP
\&\fBOSSL_DECODER_get0_properties()\fR returns the property definition associated
with the given \fIdecoder\fR.
.PP
\&\fBOSSL_DECODER_is_a()\fR checks if \fIdecoder\fR is an implementation
of an algorithm that's identifiable with \fIname\fR.
.PP
\&\fBOSSL_DECODER_get0_name()\fR returns the name used to fetch the given \fIdecoder\fR.
.PP
\&\fBOSSL_DECODER_get0_description()\fR returns a description of the \fIdecoder\fR, meant
for display and human consumption.  The description is at the discretion
of the \fIdecoder\fR implementation.
.PP
\&\fBOSSL_DECODER_names_do_all()\fR traverses all names for the given
\&\fIdecoder\fR, and calls \fIfn\fR with each name and \fIdata\fR as arguments.
.PP
\&\fBOSSL_DECODER_do_all_provided()\fR traverses all decoder
implementations by all activated providers in the library context
\&\fIlibctx\fR, and for each of the implementations, calls \fIfn\fR with the
implementation method and \fIarg\fR as arguments.
.PP
\&\fBOSSL_DECODER_gettable_params()\fR returns an \fBOSSL_PARAM\fR\|(3)
array of parameter descriptors.
.PP
\&\fBOSSL_DECODER_get_params()\fR attempts to get parameters specified
with an \fBOSSL_PARAM\fR\|(3) array \fIparams\fR.  Parameters that the
implementation doesn't recognise should be ignored.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_DECODER_fetch()\fR returns a pointer to an OSSL_DECODER object,
or NULL on error.
.PP
\&\fBOSSL_DECODER_up_ref()\fR returns 1 on success, or 0 on error.
.PP
\&\fBOSSL_DECODER_free()\fR doesn't return any value.
.PP
\&\fBOSSL_DECODER_get0_provider()\fR returns a pointer to a provider object, or
NULL on error.
.PP
\&\fBOSSL_DECODER_get0_properties()\fR returns a pointer to a property
definition string, or NULL on error.
.PP
\&\fBOSSL_DECODER_is_a()\fR returns 1 if \fIdecoder\fR was identifiable,
otherwise 0.
.PP
\&\fBOSSL_DECODER_get0_name()\fR returns the algorithm name from the provided
implementation for the given \fIdecoder\fR. Note that the \fIdecoder\fR may have
multiple synonyms associated with it. In this case the first name from the
algorithm definition is returned. Ownership of the returned string is retained
by the \fIdecoder\fR object and should not be freed by the caller.
.PP
\&\fBOSSL_DECODER_get0_description()\fR returns a pointer to a description, or NULL if
there isn't one.
.PP
\&\fBOSSL_DECODER_names_do_all()\fR returns 1 if the callback was called for all
names. A return value of 0 means that the callback was not called for any names.
.SH NOTES
.IX Header "NOTES"
\&\fBOSSL_DECODER_fetch()\fR may be called implicitly by other fetching
functions, using the same library context and properties.
Any other API that uses keys will typically do this.
.SH EXAMPLES
.IX Header "EXAMPLES"
To list all decoders in a provider to a bio_out:
.PP
.Vb 3
\& static void collect_decoders(OSSL_DECODER *decoder, void *stack)
\& {
\&     STACK_OF(OSSL_DECODER) *decoder_stack = stack;
\&
\&     sk_OSSL_DECODER_push(decoder_stack, decoder);
\&     OSSL_DECODER_up_ref(decoder);
\& }
\&
\& void print_name(const char *name, void *vdata)
\& {
\&     BIO *bio = vdata;
\&
\&     BIO_printf(bio, "%s ", name);
\& }
\&
\&
\& STACK_OF(OSSL_DECODER) *decoders;
\& int i;
\&
\& decoders = sk_OSSL_DECODER_new_null();
\&
\& BIO_printf(bio_out, "DECODERs provided by %s:\en", provider);
\& OSSL_DECODER_do_all_provided(NULL, collect_decoders,
\&                              decoders);
\&
\& for (i = 0; i < sk_OSSL_DECODER_num(decoders); i++) {
\&     OSSL_DECODER *decoder = sk_OSSL_DECODER_value(decoders, i);
\&
\&     if (strcmp(OSSL_PROVIDER_get0_name(OSSL_DECODER_get0_provider(decoder)),
\&                provider) != 0)
\&         continue;
\&
\&     if (OSSL_DECODER_names_do_all(decoder, print_name, bio_out))
\&            BIO_printf(bio_out, "\en");
\& }
\& sk_OSSL_DECODER_pop_free(decoders, OSSL_DECODER_free);
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\fR\|(7), \fBOSSL_DECODER_CTX\fR\|(3), \fBOSSL_DECODER_from_bio\fR\|(3),
\&\fBOSSL_DECODER_CTX_new_for_pkey\fR\|(3), \fBOSSL_LIB_CTX\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The functions described here were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
