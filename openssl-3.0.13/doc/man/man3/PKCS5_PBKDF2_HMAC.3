.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS5_PBKDF2_HMAC 3ossl"
.TH PKCS5_PBKDF2_HMAC 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS5_PBKDF2_HMAC, PKCS5_PBKDF2_HMAC_SHA1 \- password based derivation routines with salt and iteration count
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int PKCS5_PBKDF2_HMAC(const char *pass, int passlen,
\&                       const unsigned char *salt, int saltlen, int iter,
\&                       const EVP_MD *digest,
\&                       int keylen, unsigned char *out);
\&
\& int PKCS5_PBKDF2_HMAC_SHA1(const char *pass, int passlen,
\&                            const unsigned char *salt, int saltlen, int iter,
\&                            int keylen, unsigned char *out);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPKCS5_PBKDF2_HMAC()\fR derives a key from a password using a salt and iteration count
as specified in RFC 2898.
.PP
\&\fBpass\fR is the password used in the derivation of length \fBpasslen\fR. \fBpass\fR
is an optional parameter and can be NULL. If \fBpasslen\fR is \-1, then the
function will calculate the length of \fBpass\fR using \fBstrlen()\fR.
.PP
\&\fBsalt\fR is the salt used in the derivation of length \fBsaltlen\fR. If the
\&\fBsalt\fR is NULL, then \fBsaltlen\fR must be 0. The function will not
attempt to calculate the length of the \fBsalt\fR because it is not assumed to
be NULL terminated.
.PP
\&\fBiter\fR is the iteration count and its value should be greater than or
equal to 1. RFC 2898 suggests an iteration count of at least 1000. Any
\&\fBiter\fR value less than 1 is invalid; such values will result in failure
and raise the PROV_R_INVALID_ITERATION_COUNT error.
.PP
\&\fBdigest\fR is the message digest function used in the derivation.
\&\fBPKCS5_PBKDF2_HMAC_SHA1()\fR calls \fBPKCS5_PBKDF2_HMAC()\fR with \fBEVP_sha1()\fR.
.PP
The derived key will be written to \fBout\fR. The size of the \fBout\fR buffer
is specified via \fBkeylen\fR.
.SH NOTES
.IX Header "NOTES"
A typical application of this function is to derive keying material for an
encryption algorithm from a password in the \fBpass\fR, a salt in \fBsalt\fR,
and an iteration count.
.PP
Increasing the \fBiter\fR parameter slows down the algorithm which makes it
harder for an attacker to perform a brute force attack using a large number
of candidate passwords.
.PP
These functions make no assumption regarding the given password.
It will simply be treated as a byte sequence.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPKCS5_PBKDF2_HMAC()\fR and \fBPBKCS5_PBKDF2_HMAC_SHA1()\fR return 1 on success or 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBevp\fR\|(7), \fBRAND_bytes\fR\|(3),
\&\fBEVP_BytesToKey\fR\|(3),
\&\fBpassphrase\-encoding\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2014\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
