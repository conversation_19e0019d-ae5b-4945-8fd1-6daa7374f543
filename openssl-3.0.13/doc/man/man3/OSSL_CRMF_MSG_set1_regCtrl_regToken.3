.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_CRMF_MSG_SET1_REGCTRL_REGTOKEN 3ossl"
.TH OSSL_CRMF_MSG_SET1_REGCTRL_REGTOKEN 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_CRMF_MSG_get0_regCtrl_regToken,
OSSL_CRMF_MSG_set1_regCtrl_regToken,
OSSL_CRMF_MSG_get0_regCtrl_authenticator,
OSSL_CRMF_MSG_set1_regCtrl_authenticator,
OSSL_CRMF_MSG_PKIPublicationInfo_push0_SinglePubInfo,
OSSL_CRMF_MSG_set0_SinglePubInfo,
OSSL_CRMF_MSG_set_PKIPublicationInfo_action,
OSSL_CRMF_MSG_get0_regCtrl_pkiPublicationInfo,
OSSL_CRMF_MSG_set1_regCtrl_pkiPublicationInfo,
OSSL_CRMF_MSG_get0_regCtrl_protocolEncrKey,
OSSL_CRMF_MSG_set1_regCtrl_protocolEncrKey,
OSSL_CRMF_MSG_get0_regCtrl_oldCertID,
OSSL_CRMF_MSG_set1_regCtrl_oldCertID,
OSSL_CRMF_CERTID_gen
\&\- functions getting or setting CRMF Registration Controls
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/crmf.h>
\&
\& ASN1_UTF8STRING
\&    *OSSL_CRMF_MSG_get0_regCtrl_regToken(const OSSL_CRMF_MSG *msg);
\& int OSSL_CRMF_MSG_set1_regCtrl_regToken(OSSL_CRMF_MSG *msg,
\&                                         const ASN1_UTF8STRING *tok);
\& ASN1_UTF8STRING
\&    *OSSL_CRMF_MSG_get0_regCtrl_authenticator(const OSSL_CRMF_MSG *msg);
\& int OSSL_CRMF_MSG_set1_regCtrl_authenticator(OSSL_CRMF_MSG *msg,
\&                                              const ASN1_UTF8STRING *auth);
\& int OSSL_CRMF_MSG_PKIPublicationInfo_push0_SinglePubInfo(
\&                                  OSSL_CRMF_PKIPUBLICATIONINFO *pi,
\&                                  OSSL_CRMF_SINGLEPUBINFO *spi);
\& int OSSL_CRMF_MSG_set0_SinglePubInfo(OSSL_CRMF_SINGLEPUBINFO *spi,
\&                                      int method, GENERAL_NAME *nm);
\& int OSSL_CRMF_MSG_set_PKIPublicationInfo_action(
\&                                  OSSL_CRMF_PKIPUBLICATIONINFO *pi, int action);
\& OSSL_CRMF_PKIPUBLICATIONINFO
\&    *OSSL_CRMF_MSG_get0_regCtrl_pkiPublicationInfo(const OSSL_CRMF_MSG *msg);
\& int OSSL_CRMF_MSG_set1_regCtrl_pkiPublicationInfo(OSSL_CRMF_MSG *msg,
\&                                        const OSSL_CRMF_PKIPUBLICATIONINFO *pi);
\& X509_PUBKEY
\&    *OSSL_CRMF_MSG_get0_regCtrl_protocolEncrKey(const OSSL_CRMF_MSG *msg);
\& int OSSL_CRMF_MSG_set1_regCtrl_protocolEncrKey(OSSL_CRMF_MSG *msg,
\&                                                const X509_PUBKEY *pubkey);
\& OSSL_CRMF_CERTID
\&    *OSSL_CRMF_MSG_get0_regCtrl_oldCertID(const OSSL_CRMF_MSG *msg);
\& int OSSL_CRMF_MSG_set1_regCtrl_oldCertID(OSSL_CRMF_MSG *msg,
\&                                          const OSSL_CRMF_CERTID *cid);
\& OSSL_CRMF_CERTID *OSSL_CRMF_CERTID_gen(const X509_NAME *issuer,
\&                                        const ASN1_INTEGER *serial);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Each of the \fBOSSL_CRMF_MSG_get0_regCtrl_X()\fR functions
returns the respective control X in the given \fImsg\fR, if present.
.PP
\&\fBOSSL_CRMF_MSG_set1_regCtrl_regToken()\fR sets the regToken control in the given
\&\fImsg\fR copying the given \fItok\fR as value. See RFC 4211, section 6.1.
.PP
\&\fBOSSL_CRMF_MSG_set1_regCtrl_authenticator()\fR sets the authenticator control in
the given \fImsg\fR copying the given \fIauth\fR as value. See RFC 4211, section 6.2.
.PP
\&\fBOSSL_CRMF_MSG_PKIPublicationInfo_push0_SinglePubInfo()\fR pushes the given \fIspi\fR
to \fIsi\fR. Consumes the \fIspi\fR pointer.
.PP
\&\fBOSSL_CRMF_MSG_set0_SinglePubInfo()\fR sets in the given SinglePubInfo \fIspi\fR
the \fImethod\fR and publication location, in the form of a GeneralName, \fInm\fR.
The publication location is optional, and therefore \fInm\fR may be NULL.
The function consumes the \fInm\fR pointer if present.
Available methods are:
 # define OSSL_CRMF_PUB_METHOD_DONTCARE 0
 # define OSSL_CRMF_PUB_METHOD_X500     1
 # define OSSL_CRMF_PUB_METHOD_WEB      2
 # define OSSL_CRMF_PUB_METHOD_LDAP     3
.PP
\&\fBOSSL_CRMF_MSG_set_PKIPublicationInfo_action()\fR sets the action in the given \fIpi\fR
using the given \fIaction\fR as value. See RFC 4211, section 6.3.
Available actions are:
 # define OSSL_CRMF_PUB_ACTION_DONTPUBLISH   0
 # define OSSL_CRMF_PUB_ACTION_PLEASEPUBLISH 1
.PP
\&\fBOSSL_CRMF_MSG_set1_regCtrl_pkiPublicationInfo()\fR sets the pkiPublicationInfo
control in the given \fImsg\fR copying the given \fItok\fR as value. See RFC 4211,
section 6.3.
.PP
\&\fBOSSL_CRMF_MSG_set1_regCtrl_protocolEncrKey()\fR sets the protocolEncrKey control in
the given \fImsg\fR copying the given \fIpubkey\fR as value. See RFC 4211 section 6.6.
.PP
\&\fBOSSL_CRMF_MSG_set1_regCtrl_oldCertID()\fR sets the \fBoldCertID\fR regToken control in
the given \fImsg\fR copying the given \fIcid\fR as value. See RFC 4211, section 6.5.
.PP
OSSL_CRMF_CERTID_gen produces an OSSL_CRMF_CERTID_gen structure copying the
given \fIissuer\fR name and \fIserial\fR number.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All OSSL_CRMF_MSG_get0_*() functions
return the respective pointer value or NULL if not present and on error.
.PP
All OSSL_CRMF_MSG_set1_*() functions return 1 on success, 0 on error.
.PP
\&\fBOSSL_CRMF_CERTID_gen()\fR returns a pointer to the resulting structure
or NULL on error.
.SH NOTES
.IX Header "NOTES"
A function \fBOSSL_CRMF_MSG_set1_regCtrl_pkiArchiveOptions()\fR for setting an
Archive Options Control is not yet implemented due to missing features to
create the needed OSSL_CRMF_PKIARCHIVEOPTINS content.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
RFC 4211
.SH HISTORY
.IX Header "HISTORY"
The OpenSSL CRMF support was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2007\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
