.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_DIGESTVERIFYINIT 3ossl"
.TH EVP_DIGESTVERIFYINIT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_DigestVerifyInit_ex, EVP_DigestVerifyInit, EVP_DigestVerifyUpdate,
EVP_DigestVerifyFinal, EVP_DigestVerify \- EVP signature verification functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_DigestVerifyInit_ex(EVP_MD_CTX *ctx, EVP_PKEY_CTX **pctx,
\&                             const char *mdname, OSSL_LIB_CTX *libctx,
\&                             const char *props, EVP_PKEY *pkey,
\&                             const OSSL_PARAM params[]);
\& int EVP_DigestVerifyInit(EVP_MD_CTX *ctx, EVP_PKEY_CTX **pctx,
\&                          const EVP_MD *type, ENGINE *e, EVP_PKEY *pkey);
\& int EVP_DigestVerifyUpdate(EVP_MD_CTX *ctx, const void *d, size_t cnt);
\& int EVP_DigestVerifyFinal(EVP_MD_CTX *ctx, const unsigned char *sig,
\&                           size_t siglen);
\& int EVP_DigestVerify(EVP_MD_CTX *ctx, const unsigned char *sigret,
\&                      size_t siglen, const unsigned char *tbs, size_t tbslen);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The EVP signature routines are a high-level interface to digital signatures.
Input data is digested first before the signature verification takes place.
.PP
\&\fBEVP_DigestVerifyInit_ex()\fR sets up verification context \fBctx\fR to use a
digest with the name \fBmdname\fR and public key \fBpkey\fR. The name of the digest to
be used is passed to the provider of the signature algorithm in use. How that
provider interprets the digest name is provider specific. The provider may
implement that digest directly itself or it may (optionally) choose to fetch it
(which could result in a digest from a different provider being selected). If
the provider supports fetching the digest then it may use the \fBprops\fR argument
for the properties to be used during the fetch. Finally, the passed parameters
\&\fIparams\fR, if not NULL, are set on the context before returning.
.PP
The \fIpkey\fR algorithm is used to fetch a \fBEVP_SIGNATURE\fR method implicitly, to
be used for the actual signing. See "Implicit fetch" in \fBprovider\fR\|(7) for
more information about implicit fetches.
.PP
The OpenSSL default and legacy providers support fetching digests and can fetch
those digests from any available provider. The OpenSSL FIPS provider also
supports fetching digests but will only fetch digests that are themselves
implemented inside the FIPS provider.
.PP
\&\fBctx\fR must be created with \fBEVP_MD_CTX_new()\fR before calling this function. If
\&\fBpctx\fR is not NULL, the EVP_PKEY_CTX of the verification operation will be
written to \fB*pctx\fR: this can be used to set alternative verification options.
Note that any existing value in \fB*pctx\fR is overwritten. The EVP_PKEY_CTX value
returned must not be freed directly by the application if \fBctx\fR is not assigned
an EVP_PKEY_CTX value before being passed to \fBEVP_DigestVerifyInit_ex()\fR
(which means the EVP_PKEY_CTX is created inside
\&\fBEVP_DigestVerifyInit_ex()\fR and it will be freed automatically when the
EVP_MD_CTX is freed). If the EVP_PKEY_CTX to be used is created by
EVP_DigestVerifyInit_ex then it will use the \fBOSSL_LIB_CTX\fR specified
in \fIlibctx\fR and the property query string specified in \fIprops\fR.
.PP
No \fBEVP_PKEY_CTX\fR will be created by \fBEVP_DigestVerifyInit_ex()\fR if the
passed \fBctx\fR has already been assigned one via \fBEVP_MD_CTX_set_pkey_ctx\fR\|(3).
See also \fBSM2\fR\|(7).
.PP
Not all digests can be used for all key types. The following combinations apply.
.IP DSA 4
.IX Item "DSA"
Supports SHA1, SHA224, SHA256, SHA384 and SHA512
.IP ECDSA 4
.IX Item "ECDSA"
Supports SHA1, SHA224, SHA256, SHA384, SHA512 and SM3
.IP "RSA with no padding" 4
.IX Item "RSA with no padding"
Supports no digests (the digest \fBtype\fR must be NULL)
.IP "RSA with X931 padding" 4
.IX Item "RSA with X931 padding"
Supports SHA1, SHA256, SHA384 and SHA512
.IP "All other RSA padding types" 4
.IX Item "All other RSA padding types"
Support SHA1, SHA224, SHA256, SHA384, SHA512, MD5, MD5_SHA1, MD2, MD4, MDC2,
SHA3\-224, SHA3\-256, SHA3\-384, SHA3\-512
.IP "Ed25519 and Ed448" 4
.IX Item "Ed25519 and Ed448"
Support no digests (the digest \fBtype\fR must be NULL)
.IP HMAC 4
.IX Item "HMAC"
Supports any digest
.IP "CMAC, Poly1305 and Siphash" 4
.IX Item "CMAC, Poly1305 and Siphash"
Will ignore any digest provided.
.PP
If RSA-PSS is used and restrictions apply then the digest must match.
.PP
\&\fBEVP_DigestVerifyInit()\fR works in the same way as
\&\fBEVP_DigestVerifyInit_ex()\fR except that the \fBmdname\fR parameter will be
inferred from the supplied digest \fBtype\fR, and \fBprops\fR will be NULL. Where
supplied the ENGINE \fBe\fR will be used for the signature verification and digest
algorithm implementations. \fBe\fR may be NULL.
.PP
\&\fBEVP_DigestVerifyUpdate()\fR hashes \fBcnt\fR bytes of data at \fBd\fR into the
verification context \fBctx\fR. This function can be called several times on the
same \fBctx\fR to include additional data.
.PP
\&\fBEVP_DigestVerifyFinal()\fR verifies the data in \fBctx\fR against the signature in
\&\fBsig\fR of length \fBsiglen\fR.
.PP
\&\fBEVP_DigestVerify()\fR verifies \fBtbslen\fR bytes at \fBtbs\fR against the signature
in \fBsig\fR of length \fBsiglen\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_DigestVerifyInit()\fR and \fBEVP_DigestVerifyUpdate()\fR return 1 for success and 0
for failure.
.PP
\&\fBEVP_DigestVerifyFinal()\fR and \fBEVP_DigestVerify()\fR return 1 for success; any other
value indicates failure.  A return value of zero indicates that the signature
did not verify successfully (that is, \fBtbs\fR did not match the original data or
the signature had an invalid form), while other values indicate a more serious
error (and sometimes also indicate an invalid signature form).
.PP
The error codes can be obtained from \fBERR_get_error\fR\|(3).
.SH NOTES
.IX Header "NOTES"
The \fBEVP\fR interface to digital signatures should almost always be used in
preference to the low-level interfaces. This is because the code then becomes
transparent to the algorithm used and much more flexible.
.PP
\&\fBEVP_DigestVerify()\fR is a one shot operation which verifies a single block of
data in one function. For algorithms that support streaming it is equivalent
to calling \fBEVP_DigestVerifyUpdate()\fR and \fBEVP_DigestVerifyFinal()\fR. For
algorithms which do not support streaming (e.g. PureEdDSA) it is the only way
to verify data.
.PP
In previous versions of OpenSSL there was a link between message digest types
and public key algorithms. This meant that "clone" digests such as \fBEVP_dss1()\fR
needed to be used to sign using SHA1 and DSA. This is no longer necessary and
the use of clone digest is now discouraged.
.PP
For some key types and parameters the random number generator must be seeded.
If the automatic seeding or reseeding of the OpenSSL CSPRNG fails due to
external circumstances (see \fBRAND\fR\|(7)), the operation will fail.
.PP
The call to \fBEVP_DigestVerifyFinal()\fR internally finalizes a copy of the digest
context. This means that \fBEVP_VerifyUpdate()\fR and \fBEVP_VerifyFinal()\fR can
be called later to digest and verify additional data.
.PP
\&\fBEVP_DigestVerifyInit()\fR and \fBEVP_DigestVerifyInit_ex()\fR functions can be called
multiple times on a context and the parameters set by previous calls should be
preserved if the \fIpkey\fR parameter is NULL. The call then just resets the state
of the \fIctx\fR.
.PP
Ignoring failure returns of \fBEVP_DigestVerifyInit()\fR and \fBEVP_DigestVerifyInit_ex()\fR
functions can lead to subsequent undefined behavior when calling
\&\fBEVP_DigestVerifyUpdate()\fR, \fBEVP_DigestVerifyFinal()\fR, or \fBEVP_DigestVerify()\fR.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_DigestSignInit\fR\|(3),
\&\fBEVP_DigestInit\fR\|(3),
\&\fBevp\fR\|(7), \fBHMAC\fR\|(3), \fBMD2\fR\|(3),
\&\fBMD5\fR\|(3), \fBMDC2\fR\|(3), \fBRIPEMD160\fR\|(3),
\&\fBSHA1\fR\|(3), \fBopenssl\-dgst\fR\|(1),
\&\fBRAND\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
\&\fBEVP_DigestVerifyInit()\fR, \fBEVP_DigestVerifyUpdate()\fR and \fBEVP_DigestVerifyFinal()\fR
were added in OpenSSL 1.0.0.
.PP
\&\fBEVP_DigestVerifyInit_ex()\fR was added in OpenSSL 3.0.
.PP
\&\fBEVP_DigestVerifyUpdate()\fR was converted from a macro to a function in OpenSSL
3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
