.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "HMAC 3ossl"
.TH HMAC 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
HMAC,
HMAC_CTX_new,
HMAC_CTX_reset,
HMAC_CTX_free,
HMAC_Init,
HMAC_Init_ex,
HMAC_Update,
HMAC_Final,
HMAC_CTX_copy,
HMAC_CTX_set_flags,
HMAC_CTX_get_md,
HMAC_size
\&\- HMAC message authentication code
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/hmac.h>
\&
\& unsigned char *HMAC(const EVP_MD *evp_md, const void *key, int key_len,
\&                     const unsigned char *data, size_t data_len,
\&                     unsigned char *md, unsigned int *md_len);
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 2
\& HMAC_CTX *HMAC_CTX_new(void);
\& int HMAC_CTX_reset(HMAC_CTX *ctx);
\&
\& int HMAC_Init_ex(HMAC_CTX *ctx, const void *key, int key_len,
\&                  const EVP_MD *md, ENGINE *impl);
\& int HMAC_Update(HMAC_CTX *ctx, const unsigned char *data, size_t len);
\& int HMAC_Final(HMAC_CTX *ctx, unsigned char *md, unsigned int *len);
\&
\& void HMAC_CTX_free(HMAC_CTX *ctx);
\&
\& int HMAC_CTX_copy(HMAC_CTX *dctx, HMAC_CTX *sctx);
\& void HMAC_CTX_set_flags(HMAC_CTX *ctx, unsigned long flags);
\& const EVP_MD *HMAC_CTX_get_md(const HMAC_CTX *ctx);
\&
\& size_t HMAC_size(const HMAC_CTX *e);
.Ve
.PP
The following function has been deprecated since OpenSSL 1.1.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 2
\& int HMAC_Init(HMAC_CTX *ctx, const void *key, int key_len,
\&               const EVP_MD *md);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
HMAC is a MAC (message authentication code), i.e. a keyed hash
function used for message authentication, which is based on a hash
function.
.PP
\&\fBHMAC()\fR computes the message authentication code of the \fIdata_len\fR bytes at
\&\fIdata\fR using the hash function \fIevp_md\fR and the key \fIkey\fR which is
\&\fIkey_len\fR bytes long. The \fIkey\fR may also be NULL with \fIkey_len\fR being 0.
.PP
It places the result in \fImd\fR (which must have space for the output of
the hash function, which is no more than \fBEVP_MAX_MD_SIZE\fR bytes).
If \fImd\fR is NULL, the digest is placed in a static array.  The size of
the output is placed in \fImd_len\fR, unless it is NULL. Note: passing a NULL
value for \fImd\fR to use the static array is not thread safe.
.PP
\&\fIevp_md\fR is a message digest such as \fBEVP_sha1()\fR, \fBEVP_ripemd160()\fR etc.
HMAC does not support variable output length digests such as \fBEVP_shake128()\fR and
\&\fBEVP_shake256()\fR.
.PP
\&\fBHMAC()\fR uses the default \fBOSSL_LIB_CTX\fR.
Use \fBEVP_Q_mac\fR\|(3) instead if a library context is required.
.PP
All of the functions described below are deprecated.
Applications should instead use \fBEVP_MAC_CTX_new\fR\|(3), \fBEVP_MAC_CTX_free\fR\|(3),
\&\fBEVP_MAC_init\fR\|(3), \fBEVP_MAC_update\fR\|(3) and \fBEVP_MAC_final\fR\|(3)
or the 'quick' single-shot MAC function \fBEVP_Q_mac\fR\|(3).
.PP
\&\fBHMAC_CTX_new()\fR creates a new HMAC_CTX in heap memory.
.PP
\&\fBHMAC_CTX_reset()\fR clears an existing \fBHMAC_CTX\fR and associated
resources, making it suitable for new computations as if it was newly
created with \fBHMAC_CTX_new()\fR.
.PP
\&\fBHMAC_CTX_free()\fR erases the key and other data from the \fBHMAC_CTX\fR,
releases any associated resources and finally frees the \fBHMAC_CTX\fR
itself.
.PP
The following functions may be used if the message is not completely
stored in memory:
.PP
\&\fBHMAC_Init_ex()\fR initializes or reuses a \fBHMAC_CTX\fR structure to use the hash
function \fIevp_md\fR and key \fIkey\fR. If both are NULL, or if \fIkey\fR is NULL
and \fIevp_md\fR is the same as the previous call, then the
existing key is
reused. \fIctx\fR must have been created with \fBHMAC_CTX_new()\fR before the first use
of an \fBHMAC_CTX\fR in this function.
.PP
If \fBHMAC_Init_ex()\fR is called with \fIkey\fR NULL and \fIevp_md\fR is not the
same as the previous digest used by \fIctx\fR then an error is returned
because reuse of an existing key with a different digest is not supported.
.PP
\&\fBHMAC_Init()\fR initializes a \fBHMAC_CTX\fR structure to use the hash
function \fIevp_md\fR and the key \fIkey\fR which is \fIkey_len\fR bytes
long.
.PP
\&\fBHMAC_Update()\fR can be called repeatedly with chunks of the message to
be authenticated (\fIlen\fR bytes at \fIdata\fR).
.PP
\&\fBHMAC_Final()\fR places the message authentication code in \fImd\fR, which
must have space for the hash function output.
.PP
\&\fBHMAC_CTX_copy()\fR copies all of the internal state from \fIsctx\fR into \fIdctx\fR.
.PP
\&\fBHMAC_CTX_set_flags()\fR applies the specified flags to the internal EVP_MD_CTXs.
These flags have the same meaning as for \fBEVP_MD_CTX_set_flags\fR\|(3).
.PP
\&\fBHMAC_CTX_get_md()\fR returns the EVP_MD that has previously been set for the
supplied HMAC_CTX.
.PP
\&\fBHMAC_size()\fR returns the length in bytes of the underlying hash function output.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBHMAC()\fR returns a pointer to the message authentication code or NULL if
an error occurred.
.PP
\&\fBHMAC_CTX_new()\fR returns a pointer to a new \fBHMAC_CTX\fR on success or
NULL if an error occurred.
.PP
\&\fBHMAC_CTX_reset()\fR, \fBHMAC_Init_ex()\fR, \fBHMAC_Update()\fR, \fBHMAC_Final()\fR and
\&\fBHMAC_CTX_copy()\fR return 1 for success or 0 if an error occurred.
.PP
\&\fBHMAC_CTX_get_md()\fR return the EVP_MD previously set for the supplied HMAC_CTX or
NULL if no EVP_MD has been set.
.PP
\&\fBHMAC_size()\fR returns the length in bytes of the underlying hash function output
or zero on error.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
RFC 2104
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBSHA1\fR\|(3), \fBEVP_Q_mac\fR\|(3), \fBevp\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
All functions except for \fBHMAC()\fR were deprecated in OpenSSL 3.0.
.PP
\&\fBHMAC_CTX_init()\fR was replaced with \fBHMAC_CTX_reset()\fR in OpenSSL 1.1.0.
.PP
\&\fBHMAC_CTX_cleanup()\fR existed in OpenSSL before version 1.1.0.
.PP
\&\fBHMAC_CTX_new()\fR, \fBHMAC_CTX_free()\fR and \fBHMAC_CTX_get_md()\fR are new in OpenSSL 1.1.0.
.PP
\&\fBHMAC_Init_ex()\fR, \fBHMAC_Update()\fR and \fBHMAC_Final()\fR did not return values in
OpenSSL before version 1.0.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
