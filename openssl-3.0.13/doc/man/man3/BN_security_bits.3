.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BN_SECURITY_BITS 3ossl"
.TH BN_SECURITY_BITS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BN_security_bits \- returns bits of security based on given numbers
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bn.h>
\&
\& int BN_security_bits(int L, int N);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBN_security_bits()\fR returns the number of bits of security provided by a
specific algorithm and a particular key size. The bits of security is
defined in NIST SP800\-57. Currently, \fBBN_security_bits()\fR support two types
of asymmetric algorithms: the FFC (Finite Field Cryptography) and IFC
(Integer Factorization Cryptography). For FFC, e.g., DSA and DH, both
parameters \fBL\fR and \fBN\fR are used to decide the bits of security, where
\&\fBL\fR is the size of the public key and \fBN\fR is the size of the private
key. For IFC, e.g., RSA, only \fBL\fR is used and it's commonly considered
to be the key size (modulus).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
Number of security bits.
.SH NOTES
.IX Header "NOTES"
ECC (Elliptic Curve Cryptography) is not covered by the \fBBN_security_bits()\fR
function. The symmetric algorithms are not covered neither.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBDH_security_bits\fR\|(3), \fBDSA_security_bits\fR\|(3), \fBRSA_security_bits\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBBN_security_bits()\fR function was added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
