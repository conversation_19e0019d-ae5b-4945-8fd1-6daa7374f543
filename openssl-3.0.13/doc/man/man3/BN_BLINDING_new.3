.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BN_BLINDING_NEW 3ossl"
.TH BN_BLINDING_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BN_BLINDING_new, BN_BLINDING_free, BN_BLINDING_update, BN_BLINDING_convert,
BN_BLINDING_invert, BN_BLINDING_convert_ex, BN_BLINDING_invert_ex,
BN_BLINDING_is_current_thread, BN_BLINDING_set_current_thread,
BN_BLINDING_lock, BN_BLINDING_unlock, BN_BLINDING_get_flags,
BN_BLINDING_set_flags, BN_BLINDING_create_param \- blinding related BIGNUM functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bn.h>
\&
\& BN_BLINDING *BN_BLINDING_new(const BIGNUM *A, const BIGNUM *Ai,
\&                              BIGNUM *mod);
\& void BN_BLINDING_free(BN_BLINDING *b);
\& int BN_BLINDING_update(BN_BLINDING *b, BN_CTX *ctx);
\& int BN_BLINDING_convert(BIGNUM *n, BN_BLINDING *b, BN_CTX *ctx);
\& int BN_BLINDING_invert(BIGNUM *n, BN_BLINDING *b, BN_CTX *ctx);
\& int BN_BLINDING_convert_ex(BIGNUM *n, BIGNUM *r, BN_BLINDING *b,
\&                            BN_CTX *ctx);
\& int BN_BLINDING_invert_ex(BIGNUM *n, const BIGNUM *r, BN_BLINDING *b,
\&                           BN_CTX *ctx);
\& int BN_BLINDING_is_current_thread(BN_BLINDING *b);
\& void BN_BLINDING_set_current_thread(BN_BLINDING *b);
\& int BN_BLINDING_lock(BN_BLINDING *b);
\& int BN_BLINDING_unlock(BN_BLINDING *b);
\& unsigned long BN_BLINDING_get_flags(const BN_BLINDING *b);
\& void BN_BLINDING_set_flags(BN_BLINDING *b, unsigned long flags);
\& BN_BLINDING *BN_BLINDING_create_param(BN_BLINDING *b,
\&                                       const BIGNUM *e, BIGNUM *m, BN_CTX *ctx,
\&                                       int (*bn_mod_exp)(BIGNUM *r,
\&                                                         const BIGNUM *a,
\&                                                         const BIGNUM *p,
\&                                                         const BIGNUM *m,
\&                                                         BN_CTX *ctx,
\&                                                         BN_MONT_CTX *m_ctx),
\&                                       BN_MONT_CTX *m_ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBN_BLINDING_new()\fR allocates a new \fBBN_BLINDING\fR structure and copies
the \fBA\fR and \fBAi\fR values into the newly created \fBBN_BLINDING\fR object.
.PP
\&\fBBN_BLINDING_free()\fR frees the \fBBN_BLINDING\fR structure.
If \fBb\fR is NULL, nothing is done.
.PP
\&\fBBN_BLINDING_update()\fR updates the \fBBN_BLINDING\fR parameters by squaring
the \fBA\fR and \fBAi\fR or, after specific number of uses and if the
necessary parameters are set, by re-creating the blinding parameters.
.PP
\&\fBBN_BLINDING_convert_ex()\fR multiplies \fBn\fR with the blinding factor \fBA\fR.
If \fBr\fR is not NULL a copy the inverse blinding factor \fBAi\fR will be
returned in \fBr\fR (this is useful if a \fBRSA\fR object is shared among
several threads). \fBBN_BLINDING_invert_ex()\fR multiplies \fBn\fR with the
inverse blinding factor \fBAi\fR. If \fBr\fR is not NULL it will be used as
the inverse blinding.
.PP
\&\fBBN_BLINDING_convert()\fR and \fBBN_BLINDING_invert()\fR are wrapper
functions for \fBBN_BLINDING_convert_ex()\fR and \fBBN_BLINDING_invert_ex()\fR
with \fBr\fR set to NULL.
.PP
\&\fBBN_BLINDING_is_current_thread()\fR returns whether the \fBBN_BLINDING\fR
structure is owned by the current thread. This is to help users
provide proper locking if needed for multi-threaded use.
.PP
\&\fBBN_BLINDING_set_current_thread()\fR sets the current thread as the
owner of the \fBBN_BLINDING\fR structure.
.PP
\&\fBBN_BLINDING_lock()\fR locks the \fBBN_BLINDING\fR structure.
.PP
\&\fBBN_BLINDING_unlock()\fR unlocks the \fBBN_BLINDING\fR structure.
.PP
\&\fBBN_BLINDING_get_flags()\fR returns the BN_BLINDING flags. Currently
there are two supported flags: \fBBN_BLINDING_NO_UPDATE\fR and
\&\fBBN_BLINDING_NO_RECREATE\fR. \fBBN_BLINDING_NO_UPDATE\fR inhibits the
automatic update of the \fBBN_BLINDING\fR parameters after each use
and \fBBN_BLINDING_NO_RECREATE\fR inhibits the automatic re-creation
of the \fBBN_BLINDING\fR parameters after a fixed number of uses (currently
32). In newly allocated \fBBN_BLINDING\fR objects no flags are set.
\&\fBBN_BLINDING_set_flags()\fR sets the \fBBN_BLINDING\fR parameters flags.
.PP
\&\fBBN_BLINDING_create_param()\fR creates new \fBBN_BLINDING\fR parameters
using the exponent \fBe\fR and the modulus \fBm\fR. \fBbn_mod_exp\fR and
\&\fBm_ctx\fR can be used to pass special functions for exponentiation
(normally \fBBN_mod_exp_mont()\fR and \fBBN_MONT_CTX\fR).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBN_BLINDING_new()\fR returns the newly allocated \fBBN_BLINDING\fR structure
or NULL in case of an error.
.PP
\&\fBBN_BLINDING_update()\fR, \fBBN_BLINDING_convert()\fR, \fBBN_BLINDING_invert()\fR,
\&\fBBN_BLINDING_convert_ex()\fR and \fBBN_BLINDING_invert_ex()\fR return 1 on
success and 0 if an error occurred.
.PP
\&\fBBN_BLINDING_is_current_thread()\fR returns 1 if the current thread owns
the \fBBN_BLINDING\fR object, 0 otherwise.
.PP
\&\fBBN_BLINDING_set_current_thread()\fR doesn't return anything.
.PP
\&\fBBN_BLINDING_lock()\fR, \fBBN_BLINDING_unlock()\fR return 1 if the operation
succeeded or 0 on error.
.PP
\&\fBBN_BLINDING_get_flags()\fR returns the currently set \fBBN_BLINDING\fR flags
(a \fBunsigned long\fR value).
.PP
\&\fBBN_BLINDING_create_param()\fR returns the newly created \fBBN_BLINDING\fR
parameters or NULL on error.
.SH HISTORY
.IX Header "HISTORY"
\&\fBBN_BLINDING_thread_id()\fR was first introduced in OpenSSL 1.0.0, and it
deprecates \fBBN_BLINDING_set_thread_id()\fR and \fBBN_BLINDING_get_thread_id()\fR.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2005\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
