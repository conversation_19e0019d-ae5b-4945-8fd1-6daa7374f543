.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_CRMF_MSG_SET0_VALIDITY 3ossl"
.TH OSSL_CRMF_MSG_SET0_VALIDITY 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_CRMF_MSG_set0_validity,
OSSL_CRMF_MSG_set_certReqId,
OSSL_CRMF_CERTTEMPLATE_fill,
OSSL_CRMF_MSG_set0_extensions,
OSSL_CRMF_MSG_push0_extension,
OSSL_CRMF_MSG_create_popo,
OSSL_CRMF_MSGS_verify_popo
\&\- functions populating and verifying CRMF CertReqMsg structures
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/crmf.h>
\&
\& int OSSL_CRMF_MSG_set0_validity(OSSL_CRMF_MSG *crm,
\&                                 ASN1_TIME *notBefore, ASN1_TIME *notAfter);
\&
\& int OSSL_CRMF_MSG_set_certReqId(OSSL_CRMF_MSG *crm, int rid);
\&
\& int OSSL_CRMF_CERTTEMPLATE_fill(OSSL_CRMF_CERTTEMPLATE *tmpl,
\&                                 EVP_PKEY *pubkey,
\&                                 const X509_NAME *subject,
\&                                 const X509_NAME *issuer,
\&                                 const ASN1_INTEGER *serial);
\&
\& int OSSL_CRMF_MSG_set0_extensions(OSSL_CRMF_MSG *crm, X509_EXTENSIONS *exts);
\&
\& int OSSL_CRMF_MSG_push0_extension(OSSL_CRMF_MSG *crm, X509_EXTENSION *ext);
\&
\& int OSSL_CRMF_MSG_create_popo(int meth, OSSL_CRMF_MSG *crm,
\&                               EVP_PKEY *pkey, const EVP_MD *digest,
\&                               OSSL_LIB_CTX *libctx, const char *propq);
\&
\& int OSSL_CRMF_MSGS_verify_popo(const OSSL_CRMF_MSGS *reqs,
\&                                int rid, int acceptRAVerified,
\&                                OSSL_LIB_CTX *libctx, const char *propq);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOSSL_CRMF_MSG_set0_validity()\fR sets the \fInotBefore\fR and \fInotAfter\fR fields
as validity constraints in the certTemplate of \fIcrm\fR.
Any of the \fInotBefore\fR and \fInotAfter\fR parameters may be NULL,
which means no constraint for the respective field.
On success ownership of \fInotBefore\fR and \fInotAfter\fR is transferred to \fIcrm\fR.
.PP
\&\fBOSSL_CRMF_MSG_set_certReqId()\fR sets \fIrid\fR as the certReqId of \fIcrm\fR.
.PP
\&\fBOSSL_CRMF_CERTTEMPLATE_fill()\fR sets those fields of the certTemplate \fItmpl\fR
for which non-NULL values are provided: \fIpubkey\fR, \fIsubject\fR, \fIissuer\fR,
and/or \fIserial\fR.
X.509 extensions may be set using \fBOSSL_CRMF_MSG_set0_extensions()\fR.
On success the reference counter of the \fIpubkey\fR (if given) is incremented,
while the \fIsubject\fR, \fIissuer\fR, and \fIserial\fR structures (if given) are copied.
.PP
\&\fBOSSL_CRMF_MSG_set0_extensions()\fR sets \fIexts\fR as the extensions in the
certTemplate of \fIcrm\fR. Frees any pre-existing ones and consumes \fIexts\fR.
.PP
\&\fBOSSL_CRMF_MSG_push0_extension()\fR pushes the X509 extension \fIext\fR to the
extensions in the certTemplate of \fIcrm\fR.  Consumes \fIext\fR.
.PP
\&\fBOSSL_CRMF_MSG_create_popo()\fR creates and sets the Proof-of-Possession (POPO)
according to the method \fImeth\fR in \fIcrm\fR.
The library context \fIlibctx\fR and property query string \fIpropq\fR,
may be NULL to select the defaults.
In case the method is OSSL_CRMF_POPO_SIGNATURE the POPO is calculated
using the private key \fIpkey\fR and the digest method \fIdigest\fR,
where the \fIdigest\fR argument is ignored if \fIpkey\fR is of a type (such as
Ed25519 and Ed448) that is implicitly associated with a digest algorithm.
.PP
\&\fImeth\fR can be one of the following:
.IP \(bu 8
OSSL_CRMF_POPO_NONE       \- RFC 4211, section 4, POP field omitted.
CA/RA uses out-of-band method to verify POP. Note that servers may fail in this
case, resulting for instance in HTTP error code 500 (Internal error).
.IP \(bu 8
OSSL_CRMF_POPO_RAVERIFIED \- RFC 4211, section 4, explicit indication
that the RA has already verified the POP.
.IP \(bu 8
OSSL_CRMF_POPO_SIGNATURE  \- RFC 4211, section 4.1, only case 3 supported
so far.
.IP \(bu 8
OSSL_CRMF_POPO_KEYENC     \- RFC 4211, section 4.2, only indirect method
(subsequentMessage/enccert) supported,
challenge-response exchange (challengeResp) not yet supported.
.IP \(bu 8
OSSL_CRMF_POPO_KEYAGREE   \- RFC 4211, section 4.3, not yet supported.
.PP
OSSL_CRMF_MSGS_verify_popo verifies the Proof-of-Possession of the request with
the given \fIrid\fR in the list of \fIreqs\fR. Optionally accepts RAVerified. It can
make use of the library context \fIlibctx\fR and property query string \fIpropq\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All functions return 1 on success, 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
RFC 4211
.SH HISTORY
.IX Header "HISTORY"
The OpenSSL CRMF support was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2007\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
