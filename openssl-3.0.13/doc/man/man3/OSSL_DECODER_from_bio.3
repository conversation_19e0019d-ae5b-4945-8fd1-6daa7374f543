.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_DECODER_FROM_BIO 3ossl"
.TH OSSL_DECODER_FROM_BIO 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_DECODER_from_data,
OSSL_DECODER_from_bio,
OSSL_DECODER_from_fp
\&\- Routines to perform a decoding
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/decoder.h>
\&
\& int OSSL_DECODER_from_bio(OSSL_DECODER_CTX *ctx, BIO *in);
\& int OSSL_DECODER_from_fp(OSSL_DECODER_CTX *ctx, FILE *fp);
\& int OSSL_DECODER_from_data(OSSL_DECODER_CTX *ctx, const unsigned char **pdata,
\&                            size_t *pdata_len);
.Ve
.PP
Feature availability macros:
.IP "\fBOSSL_DECODER_from_fp()\fR is only available when \fBOPENSSL_NO_STDIO\fR is undefined." 4
.IX Item "OSSL_DECODER_from_fp() is only available when OPENSSL_NO_STDIO is undefined."
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOSSL_DECODER_from_data()\fR runs the decoding process for the context \fIctx\fR,
with input coming from \fI*pdata\fR, \fI*pdata_len\fR bytes long.  Both \fI*pdata\fR
and \fI*pdata_len\fR must be non-NULL.  When \fBOSSL_DECODER_from_data()\fR returns,
\&\fI*pdata\fR is updated to point at the location after what has been decoded,
and \fI*pdata_len\fR to have the number of remaining bytes.
.PP
\&\fBOSSL_DECODER_from_bio()\fR runs the decoding process for the context \fIctx\fR,
with the input coming from the \fBBIO\fR \fIin\fR.  Should it make a difference,
it's recommended to have the BIO set in binary mode rather than text mode.
.PP
\&\fBOSSL_DECODER_from_fp()\fR does the same thing as \fBOSSL_DECODER_from_bio()\fR,
except that the input is coming from the \fBFILE\fR \fIfp\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_DECODER_from_bio()\fR, \fBOSSL_DECODER_from_data()\fR and \fBOSSL_DECODER_from_fp()\fR
return 1 on success, or 0 on failure.
.SH EXAMPLES
.IX Header "EXAMPLES"
To decode an RSA key encoded with PEM from a bio:
.PP
.Vb 6
\& OSSL_DECODER_CTX *dctx;
\& EVP_PKEY *pkey = NULL;
\& const char *format = "PEM";   /* NULL for any format */
\& const char *structure = NULL; /* any structure */
\& const char *keytype = "RSA";  /* NULL for any key */
\& const unsigned char *pass = "my password";
\&
\& dctx = OSSL_DECODER_CTX_new_for_pkey(&pkey, format, structure,
\&                                      keytype,
\&                                      OSSL_KEYMGMT_SELECT_KEYPAIR,
\&                                      NULL, NULL);
\& if (dctx == NULL) {
\&     /* error: no suitable potential decoders found */
\& }
\& if (pass != NULL)
\&     OSSL_DECODER_CTX_set_passphrase(dctx, pass, strlen(pass));
\& if (OSSL_DECODER_from_bio(dctx, bio)) {
\&     /* pkey is created with the decoded data from the bio */
\& } else {
\&     /* decoding failure */
\& }
\& OSSL_DECODER_CTX_free(dctx);
.Ve
.PP
To decode an EC key encoded with DER from a buffer:
.PP
.Vb 8
\& OSSL_DECODER_CTX *dctx;
\& EVP_PKEY *pkey = NULL;
\& const char *format = "DER";   /* NULL for any format */
\& const char *structure = NULL; /* any structure */
\& const char *keytype = "EC";   /* NULL for any key */
\& const unsigned char *pass = NULL
\& const unsigned char *data = buffer;
\& size_t datalen = sizeof(buffer);
\&
\& dctx = OSSL_DECODER_CTX_new_for_pkey(&pkey, format, structure,
\&                                      keytype,
\&                                      OSSL_KEYMGMT_SELECT_KEYPAIR
\&                                      | OSSL_KEYMGMT_SELECT_DOMAIN_PARAMETERS,
\&                                      NULL, NULL);
\& if (dctx == NULL) {
\&     /* error: no suitable potential decoders found */
\& }
\& if (pass != NULL)
\&     OSSL_DECODER_CTX_set_passphrase(dctx, pass, strlen(pass));
\& if (OSSL_DECODER_from_data(dctx, &data, &datalen)) {
\&     /* pkey is created with the decoded data from the buffer */
\& } else {
\&     /* decoding failure */
\& }
\& OSSL_DECODER_CTX_free(dctx);
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\fR\|(7), \fBOSSL_DECODER_CTX\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The functions described here were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
