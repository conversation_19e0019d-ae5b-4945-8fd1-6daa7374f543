.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_KEYGEN 3ossl"
.TH EVP_PKEY_KEYGEN 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_Q_keygen,
EVP_PKEY_keygen_init, EVP_PKEY_paramgen_init, EVP_PKEY_generate,
EVP_PKEY_CTX_set_cb, EVP_PKEY_CTX_get_cb,
EVP_PKEY_CTX_get_keygen_info, EVP_PKEY_CTX_set_app_data,
EVP_PKEY_CTX_get_app_data,
EVP_PKEY_gen_cb,
EVP_PKEY_paramgen, EVP_PKEY_keygen
\&\- key and parameter generation and check functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& EVP_PKEY *EVP_PKEY_Q_keygen(OSSL_LIB_CTX *libctx, const char *propq,
\&                             const char *type, ...);
\&
\& int EVP_PKEY_keygen_init(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_paramgen_init(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_generate(EVP_PKEY_CTX *ctx, EVP_PKEY **ppkey);
\& int EVP_PKEY_paramgen(EVP_PKEY_CTX *ctx, EVP_PKEY **ppkey);
\& int EVP_PKEY_keygen(EVP_PKEY_CTX *ctx, EVP_PKEY **ppkey);
\&
\& typedef int EVP_PKEY_gen_cb(EVP_PKEY_CTX *ctx);
\&
\& void EVP_PKEY_CTX_set_cb(EVP_PKEY_CTX *ctx, EVP_PKEY_gen_cb *cb);
\& EVP_PKEY_gen_cb *EVP_PKEY_CTX_get_cb(EVP_PKEY_CTX *ctx);
\&
\& int EVP_PKEY_CTX_get_keygen_info(EVP_PKEY_CTX *ctx, int idx);
\&
\& void EVP_PKEY_CTX_set_app_data(EVP_PKEY_CTX *ctx, void *data);
\& void *EVP_PKEY_CTX_get_app_data(EVP_PKEY_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Generating keys is sometimes straight forward, just generate the key's
numbers and be done with it.  However, there are certain key types that need
key parameters, often called domain parameters but not necessarily limited
to that, that also need to be generated.  In addition to this, the caller
may want to set user provided generation parameters that further affect key
parameter or key generation, such as the desired key size.
.PP
To flexibly allow all that's just been described, key parameter and key
generation is divided into an initialization of a key algorithm context,
functions to set user provided parameters, and finally the key parameter or
key generation function itself.
.PP
The key algorithm context must be created using \fBEVP_PKEY_CTX_new\fR\|(3) or
variants thereof, see that manual for details.
.PP
\&\fBEVP_PKEY_keygen_init()\fR initializes a public key algorithm context \fIctx\fR
for a key generation operation.
.PP
\&\fBEVP_PKEY_paramgen_init()\fR is similar to \fBEVP_PKEY_keygen_init()\fR except key
parameters are generated.
.PP
After initialization, generation parameters may be provided with
\&\fBEVP_PKEY_CTX_ctrl\fR\|(3) or \fBEVP_PKEY_CTX_set_params\fR\|(3), or any other
function described in those manuals.
.PP
\&\fBEVP_PKEY_generate()\fR performs the generation operation, the resulting key
parameters or key are written to \fI*ppkey\fR.  If \fI*ppkey\fR is NULL when this
function is called, it will be allocated, and should be freed by the caller
when no longer useful, using \fBEVP_PKEY_free\fR\|(3).
.PP
\&\fBEVP_PKEY_paramgen()\fR and \fBEVP_PKEY_keygen()\fR do exactly the same thing as
\&\fBEVP_PKEY_generate()\fR, after checking that the corresponding \fBEVP_PKEY_paramgen_init()\fR
or \fBEVP_PKEY_keygen_init()\fR was used to initialize \fIctx\fR.
These are older functions that are kept for backward compatibility.
It is safe to use \fBEVP_PKEY_generate()\fR instead.
.PP
The function \fBEVP_PKEY_set_cb()\fR sets the key or parameter generation callback
to \fIcb\fR. The function \fBEVP_PKEY_CTX_get_cb()\fR returns the key or parameter
generation callback.
.PP
The function \fBEVP_PKEY_CTX_get_keygen_info()\fR returns parameters associated
with the generation operation. If \fIidx\fR is \-1 the total number of
parameters available is returned. Any non negative value returns the value of
that parameter. \fBEVP_PKEY_CTX_gen_keygen_info()\fR with a nonnegative value for
\&\fIidx\fR should only be called within the generation callback.
.PP
If the callback returns 0 then the key generation operation is aborted and an
error occurs. This might occur during a time consuming operation where
a user clicks on a "cancel" button.
.PP
The functions \fBEVP_PKEY_CTX_set_app_data()\fR and \fBEVP_PKEY_CTX_get_app_data()\fR set
and retrieve an opaque pointer. This can be used to set some application
defined value which can be retrieved in the callback: for example a handle
which is used to update a "progress dialog".
.PP
\&\fBEVP_PKEY_Q_keygen()\fR abstracts from the explicit use of \fBEVP_PKEY_CTX\fR while
providing a 'quick' but limited way of generating a new asymmetric key pair.
It provides shorthands for simple and common cases of key generation.
As usual, the library context \fIlibctx\fR and property query \fIpropq\fR
can be given for fetching algorithms from providers.
If \fItype\fR is \f(CW\*(C`RSA\*(C'\fR,
a \fBsize_t\fR parameter must be given to specify the size of the RSA key.
If \fItype\fR is \f(CW\*(C`EC\*(C'\fR,
a string parameter must be given to specify the name of the EC curve.
If \fItype\fR is \f(CW\*(C`X25519\*(C'\fR, \f(CW\*(C`X448\*(C'\fR, \f(CW\*(C`ED25519\*(C'\fR, \f(CW\*(C`ED448\*(C'\fR, or \f(CW\*(C`SM2\*(C'\fR
no further parameter is needed.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_keygen_init()\fR, \fBEVP_PKEY_paramgen_init()\fR, \fBEVP_PKEY_keygen()\fR and
\&\fBEVP_PKEY_paramgen()\fR return 1 for success and 0 or a negative value for failure.
In particular a return value of \-2 indicates the operation is not supported by
the public key algorithm.
.PP
\&\fBEVP_PKEY_Q_keygen()\fR returns an \fBEVP_PKEY\fR, or NULL on failure.
.SH NOTES
.IX Header "NOTES"
After the call to \fBEVP_PKEY_keygen_init()\fR or \fBEVP_PKEY_paramgen_init()\fR algorithm
specific control operations can be performed to set any appropriate parameters
for the operation.
.PP
The functions \fBEVP_PKEY_keygen()\fR and \fBEVP_PKEY_paramgen()\fR can be called more than
once on the same context if several operations are performed using the same
parameters.
.PP
The meaning of the parameters passed to the callback will depend on the
algorithm and the specific implementation of the algorithm. Some might not
give any useful information at all during key or parameter generation. Others
might not even call the callback.
.PP
The operation performed by key or parameter generation depends on the algorithm
used. In some cases (e.g. EC with a supplied named curve) the "generation"
option merely sets the appropriate fields in an EVP_PKEY structure.
.PP
In OpenSSL an EVP_PKEY structure containing a private key also contains the
public key components and parameters (if any). An OpenSSL private key is
equivalent to what some libraries call a "key pair". A private key can be used
in functions which require the use of a public key or parameters.
.SH EXAMPLES
.IX Header "EXAMPLES"
Generate a 2048 bit RSA key:
.PP
.Vb 2
\& #include <openssl/evp.h>
\& #include <openssl/rsa.h>
\&
\& EVP_PKEY_CTX *ctx;
\& EVP_PKEY *pkey = NULL;
\&
\& ctx = EVP_PKEY_CTX_new_id(EVP_PKEY_RSA, NULL);
\& if (!ctx)
\&     /* Error occurred */
\& if (EVP_PKEY_keygen_init(ctx) <= 0)
\&     /* Error */
\& if (EVP_PKEY_CTX_set_rsa_keygen_bits(ctx, 2048) <= 0)
\&     /* Error */
\&
\& /* Generate key */
\& if (EVP_PKEY_keygen(ctx, &pkey) <= 0)
\&     /* Error */
.Ve
.PP
Generate a key from a set of parameters:
.PP
.Vb 2
\& #include <openssl/evp.h>
\& #include <openssl/rsa.h>
\&
\& EVP_PKEY_CTX *ctx;
\& ENGINE *eng;
\& EVP_PKEY *pkey = NULL, *param;
\&
\& /* Assumed param, eng are set up already */
\& ctx = EVP_PKEY_CTX_new(param, eng);
\& if (!ctx)
\&     /* Error occurred */
\& if (EVP_PKEY_keygen_init(ctx) <= 0)
\&     /* Error */
\&
\& /* Generate key */
\& if (EVP_PKEY_keygen(ctx, &pkey) <= 0)
\&     /* Error */
.Ve
.PP
Example of generation callback for OpenSSL public key implementations:
.PP
.Vb 1
\& /* Application data is a BIO to output status to */
\&
\& EVP_PKEY_CTX_set_app_data(ctx, status_bio);
\&
\& static int genpkey_cb(EVP_PKEY_CTX *ctx)
\& {
\&     char c = \*(Aq*\*(Aq;
\&     BIO *b = EVP_PKEY_CTX_get_app_data(ctx);
\&     int p = EVP_PKEY_CTX_get_keygen_info(ctx, 0);
\&
\&     if (p == 0)
\&         c = \*(Aq.\*(Aq;
\&     if (p == 1)
\&         c = \*(Aq+\*(Aq;
\&     if (p == 2)
\&         c = \*(Aq*\*(Aq;
\&     if (p == 3)
\&         c = \*(Aq\en\*(Aq;
\&     BIO_write(b, &c, 1);
\&     (void)BIO_flush(b);
\&     return 1;
\& }
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_RSA_gen\fR\|(3), \fBEVP_EC_gen\fR\|(3),
\&\fBEVP_PKEY_CTX_new\fR\|(3),
\&\fBEVP_PKEY_encrypt\fR\|(3),
\&\fBEVP_PKEY_decrypt\fR\|(3),
\&\fBEVP_PKEY_sign\fR\|(3),
\&\fBEVP_PKEY_verify\fR\|(3),
\&\fBEVP_PKEY_verify_recover\fR\|(3),
\&\fBEVP_PKEY_derive\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBEVP_PKEY_keygen_init()\fR, int \fBEVP_PKEY_paramgen_init()\fR, \fBEVP_PKEY_keygen()\fR,
\&\fBEVP_PKEY_paramgen()\fR, \fBEVP_PKEY_gen_cb()\fR, \fBEVP_PKEY_CTX_set_cb()\fR,
\&\fBEVP_PKEY_CTX_get_cb()\fR, \fBEVP_PKEY_CTX_get_keygen_info()\fR,
\&\fBEVP_PKEY_CTX_set_app_data()\fR and \fBEVP_PKEY_CTX_get_app_data()\fR were added in
OpenSSL 1.0.0.
.PP
\&\fBEVP_PKEY_Q_keygen()\fR and \fBEVP_PKEY_generate()\fR were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
