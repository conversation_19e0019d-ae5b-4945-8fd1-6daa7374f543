.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_CRMF_PBMP_NEW 3ossl"
.TH OSSL_CRMF_PBMP_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_CRMF_pbm_new,
OSSL_CRMF_pbmp_new
\&\- functions for producing Password\-Based MAC (PBM)
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/crmf.h>
\&
\& int OSSL_CRMF_pbm_new(OSSL_LIB_CTX *libctx, const char *propq,
\&                       const OSSL_CRMF_PBMPARAMETER *pbmp,
\&                       const unsigned char *msg, size_t msglen,
\&                       const unsigned char *sec, size_t seclen,
\&                       unsigned char **mac, size_t *maclen);
\&
\& OSSL_CRMF_PBMPARAMETER *OSSL_CRMF_pbmp_new(OSSL_LIB_CTX *libctx, size_t saltlen,
\&                                            int owfnid, size_t itercnt,
\&                                            int macnid);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOSSL_CRMF_pbm_new()\fR generates a PBM (Password-Based MAC) based on given PBM
parameters \fIpbmp\fR, message \fImsg\fR, and secret \fIsec\fR, along with the respective
lengths \fImsglen\fR and \fIseclen\fR.
The optional library context \fIlibctx\fR and \fIpropq\fR parameters may be used
to influence the selection of the MAC algorithm referenced in the \fIpbmp\fR;
see "ALGORITHM FETCHING" in \fBcrypto\fR\|(7) for further information.
On success writes the address of the newly
allocated MAC via the \fImac\fR reference parameter and writes the length via the
\&\fImaclen\fR reference parameter unless it its NULL.
.PP
\&\fBOSSL_CRMF_pbmp_new()\fR initializes and returns a new \fBPBMParameter\fR structure
with a new random salt of given length \fIsaltlen\fR,
OWF (one-way function) NID \fIowfnid\fR, OWF iteration count \fIitercnt\fR,
and MAC NID \fImacnid\fR.
The library context \fIlibctx\fR parameter may be used to select the provider
for the random number generation (DRBG) and may be NULL for the default.
.SH NOTES
.IX Header "NOTES"
The algorithms for the OWF (one-way function) and for the MAC (message
authentication code) may be any with a NID defined in \fI<openssl/objects.h>\fR.
As specified by RFC 4210, these should include NID_hmac_sha1.
.PP
RFC 4210 recommends that the salt SHOULD be at least 8 bytes (64 bits) long,
where 16 bytes is common.
.PP
The iteration count must be at least 100, as stipulated by RFC 4211, and is
limited to at most 100000 to avoid DoS through manipulated or otherwise
malformed input.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_CRMF_pbm_new()\fR returns 1 on success, 0 on error.
.PP
\&\fBOSSL_CRMF_pbmp_new()\fR returns a new and initialized OSSL_CRMF_PBMPARAMETER
structure, or NULL on error.
.SH EXAMPLES
.IX Header "EXAMPLES"
.Vb 5
\& OSSL_CRMF_PBMPARAMETER *pbm = NULL;
\& unsigned char *msg = "Hello";
\& unsigned char *sec = "SeCrEt";
\& unsigned char *mac = NULL;
\& size_t maclen;
\&
\& if ((pbm = OSSL_CRMF_pbmp_new(16, NID_sha256, 500, NID_hmac_sha1) == NULL))
\&     goto err;
\& if (!OSSL_CRMF_pbm_new(pbm, msg, 5, sec, 6, &mac, &maclen))
\&     goto err;
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
RFC 4211 section 4.4
.SH HISTORY
.IX Header "HISTORY"
The OpenSSL CRMF support was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2007\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
