.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ASN1_STRING_LENGTH 3ossl"
.TH ASN1_STRING_LENGTH 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ASN1_STRING_dup, ASN1_STRING_cmp, ASN1_STRING_set, ASN1_STRING_length,
ASN1_STRING_type, ASN1_STRING_get0_data, ASN1_STRING_data,
ASN1_STRING_to_UTF8 \- ASN1_STRING utility functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/asn1.h>
\&
\& int ASN1_STRING_length(ASN1_STRING *x);
\& const unsigned char *ASN1_STRING_get0_data(const ASN1_STRING *x);
\& unsigned char *ASN1_STRING_data(ASN1_STRING *x);
\&
\& ASN1_STRING *ASN1_STRING_dup(const ASN1_STRING *a);
\&
\& int ASN1_STRING_cmp(ASN1_STRING *a, ASN1_STRING *b);
\&
\& int ASN1_STRING_set(ASN1_STRING *str, const void *data, int len);
\&
\& int ASN1_STRING_type(const ASN1_STRING *x);
\&
\& int ASN1_STRING_to_UTF8(unsigned char **out, const ASN1_STRING *in);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions allow an \fBASN1_STRING\fR structure to be manipulated.
.PP
\&\fBASN1_STRING_length()\fR returns the length of the content of \fIx\fR.
.PP
\&\fBASN1_STRING_get0_data()\fR returns an internal pointer to the data of \fIx\fR.
Since this is an internal pointer it should \fBnot\fR be freed or
modified in any way.
.PP
\&\fBASN1_STRING_data()\fR is similar to \fBASN1_STRING_get0_data()\fR except the
returned value is not constant. This function is deprecated:
applications should use \fBASN1_STRING_get0_data()\fR instead.
.PP
\&\fBASN1_STRING_dup()\fR returns a copy of the structure \fIa\fR.
.PP
\&\fBASN1_STRING_cmp()\fR compares \fIa\fR and \fIb\fR returning 0 if the two
are identical. The string types and content are compared.
.PP
\&\fBASN1_STRING_set()\fR sets the data of string \fIstr\fR to the buffer
\&\fIdata\fR or length \fIlen\fR. The supplied data is copied. If \fIlen\fR
is \-1 then the length is determined by strlen(data).
.PP
\&\fBASN1_STRING_type()\fR returns the type of \fIx\fR, using standard constants
such as \fBV_ASN1_OCTET_STRING\fR.
.PP
\&\fBASN1_STRING_to_UTF8()\fR converts the string \fIin\fR to UTF8 format, the
converted data is allocated in a buffer in \fI*out\fR. The length of
\&\fIout\fR is returned or a negative error code. The buffer \fI*out\fR
should be freed using \fBOPENSSL_free()\fR.
.SH NOTES
.IX Header "NOTES"
Almost all ASN1 types in OpenSSL are represented as an \fBASN1_STRING\fR
structure. Other types such as \fBASN1_OCTET_STRING\fR are simply typedef'ed
to \fBASN1_STRING\fR and the functions call the \fBASN1_STRING\fR equivalents.
\&\fBASN1_STRING\fR is also used for some \fBCHOICE\fR types which consist
entirely of primitive string types such as \fBDirectoryString\fR and
\&\fBTime\fR.
.PP
These functions should \fBnot\fR be used to examine or modify \fBASN1_INTEGER\fR
or \fBASN1_ENUMERATED\fR types: the relevant \fBINTEGER\fR or \fBENUMERATED\fR
utility functions should be used instead.
.PP
In general it cannot be assumed that the data returned by \fBASN1_STRING_data()\fR
is null terminated or does not contain embedded nulls. The actual format
of the data will depend on the actual string type itself: for example
for an IA5String the data will be ASCII, for a BMPString two bytes per
character in big endian format, and for a UTF8String it will be in UTF8 format.
.PP
Similar care should be take to ensure the data is in the correct format
when calling \fBASN1_STRING_set()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBASN1_STRING_length()\fR returns the length of the content of \fIx\fR.
.PP
\&\fBASN1_STRING_get0_data()\fR and \fBASN1_STRING_data()\fR return an internal pointer to
the data of \fIx\fR.
.PP
\&\fBASN1_STRING_dup()\fR returns a valid \fBASN1_STRING\fR structure or NULL if an
error occurred.
.PP
\&\fBASN1_STRING_cmp()\fR returns an integer greater than, equal to, or less than 0,
according to whether \fIa\fR is greater than, equal to, or less than \fIb\fR.
.PP
\&\fBASN1_STRING_set()\fR returns 1 on success or 0 on error.
.PP
\&\fBASN1_STRING_type()\fR returns the type of \fIx\fR.
.PP
\&\fBASN1_STRING_to_UTF8()\fR returns the number of bytes in output string \fIout\fR or a
negative value if an error occurred.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
