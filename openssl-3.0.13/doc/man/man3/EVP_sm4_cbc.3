.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_SM4_CBC 3ossl"
.TH EVP_SM4_CBC 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_sm4_cbc,
EVP_sm4_ecb,
EVP_sm4_cfb,
EVP_sm4_cfb128,
EVP_sm4_ofb,
EVP_sm4_ctr
\&\- EVP SM4 cipher
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& const EVP_CIPHER *EVP_sm4_cbc(void);
\& const EVP_CIPHER *EVP_sm4_ecb(void);
\& const EVP_CIPHER *EVP_sm4_cfb(void);
\& const EVP_CIPHER *EVP_sm4_cfb128(void);
\& const EVP_CIPHER *EVP_sm4_ofb(void);
\& const EVP_CIPHER *EVP_sm4_ctr(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The SM4 blockcipher (GB/T 32907\-2016) for EVP.
.PP
All modes below use a key length of 128 bits and acts on blocks of 128 bits.
.IP "\fBEVP_sm4_cbc()\fR, \fBEVP_sm4_ecb()\fR, \fBEVP_sm4_cfb()\fR, \fBEVP_sm4_cfb128()\fR, \fBEVP_sm4_ofb()\fR, \fBEVP_sm4_ctr()\fR" 4
.IX Item "EVP_sm4_cbc(), EVP_sm4_ecb(), EVP_sm4_cfb(), EVP_sm4_cfb128(), EVP_sm4_ofb(), EVP_sm4_ctr()"
The SM4 blockcipher with a 128\-bit key in CBC, ECB, CFB, OFB and CTR modes
respectively.
.SH NOTES
.IX Header "NOTES"
Developers should be aware of the negative performance implications of
calling these functions multiple times and should consider using
\&\fBEVP_CIPHER_fetch\fR\|(3) with \fBEVP_CIPHER\-SM4\fR\|(7) instead.
See "Performance" in \fBcrypto\fR\|(7) for further information.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
These functions return a \fBEVP_CIPHER\fR structure that contains the
implementation of the symmetric cipher. See \fBEVP_CIPHER_meth_new\fR\|(3) for
details of the \fBEVP_CIPHER\fR structure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBevp\fR\|(7),
\&\fBEVP_EncryptInit\fR\|(3),
\&\fBEVP_CIPHER_meth_new\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2023 The OpenSSL Project Authors. All Rights Reserved.
Copyright 2017 Ribose Inc. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
