.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CRYPTO_THREAD_RUN_ONCE 3ossl"
.TH CRYPTO_THREAD_RUN_ONCE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CRYPTO_THREAD_run_once,
CRYPTO_THREAD_lock_new, CRYPTO_THREAD_read_lock, CRYPTO_THREAD_write_lock,
CRYPTO_THREAD_unlock, CRYPTO_THREAD_lock_free,
CRYPTO_atomic_add, CRYPTO_atomic_or, CRYPTO_atomic_load \- OpenSSL thread support
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/crypto.h>
\&
\& CRYPTO_ONCE CRYPTO_ONCE_STATIC_INIT;
\& int CRYPTO_THREAD_run_once(CRYPTO_ONCE *once, void (*init)(void));
\&
\& CRYPTO_RWLOCK *CRYPTO_THREAD_lock_new(void);
\& int CRYPTO_THREAD_read_lock(CRYPTO_RWLOCK *lock);
\& int CRYPTO_THREAD_write_lock(CRYPTO_RWLOCK *lock);
\& int CRYPTO_THREAD_unlock(CRYPTO_RWLOCK *lock);
\& void CRYPTO_THREAD_lock_free(CRYPTO_RWLOCK *lock);
\&
\& int CRYPTO_atomic_add(int *val, int amount, int *ret, CRYPTO_RWLOCK *lock);
\& int CRYPTO_atomic_or(uint64_t *val, uint64_t op, uint64_t *ret,
\&                      CRYPTO_RWLOCK *lock);
\& int CRYPTO_atomic_load(uint64_t *val, uint64_t *ret, CRYPTO_RWLOCK *lock);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
OpenSSL can be safely used in multi-threaded applications provided that
support for the underlying OS threading API is built-in. Currently, OpenSSL
supports the pthread and Windows APIs. OpenSSL can also be built without
any multi-threading support, for example on platforms that don't provide
any threading support or that provide a threading API that is not yet
supported by OpenSSL.
.PP
The following multi-threading function are provided:
.IP \(bu 2
\&\fBCRYPTO_THREAD_run_once()\fR can be used to perform one-time initialization.
The \fIonce\fR argument must be a pointer to a static object of type
\&\fBCRYPTO_ONCE\fR that was statically initialized to the value
\&\fBCRYPTO_ONCE_STATIC_INIT\fR.
The \fIinit\fR argument is a pointer to a function that performs the desired
exactly once initialization.
In particular, this can be used to allocate locks in a thread-safe manner,
which can then be used with the locking functions below.
.IP \(bu 2
\&\fBCRYPTO_THREAD_lock_new()\fR allocates, initializes and returns a new read/write
lock.
.IP \(bu 2
\&\fBCRYPTO_THREAD_read_lock()\fR locks the provided \fIlock\fR for reading.
.IP \(bu 2
\&\fBCRYPTO_THREAD_write_lock()\fR locks the provided \fIlock\fR for writing.
.IP \(bu 2
\&\fBCRYPTO_THREAD_unlock()\fR unlocks the previously locked \fIlock\fR.
.IP \(bu 2
\&\fBCRYPTO_THREAD_lock_free()\fR frees the provided \fIlock\fR.
.IP \(bu 2
\&\fBCRYPTO_atomic_add()\fR atomically adds \fIamount\fR to \fI*val\fR and returns the
result of the operation in \fI*ret\fR. \fIlock\fR will be locked, unless atomic
operations are supported on the specific platform. Because of this, if a
variable is modified by \fBCRYPTO_atomic_add()\fR then \fBCRYPTO_atomic_add()\fR must
be the only way that the variable is modified. If atomic operations are not
supported and \fIlock\fR is NULL, then the function will fail.
.IP \(bu 2
\&\fBCRYPTO_atomic_or()\fR performs an atomic bitwise or of \fIop\fR and \fI*val\fR and stores
the result back in \fI*val\fR. It also returns the result of the operation in
\&\fI*ret\fR. \fIlock\fR will be locked, unless atomic operations are supported on the
specific platform. Because of this, if a variable is modified by
\&\fBCRYPTO_atomic_or()\fR or read by \fBCRYPTO_atomic_load()\fR then \fBCRYPTO_atomic_or()\fR must
be the only way that the variable is modified. If atomic operations are not
supported and \fIlock\fR is NULL, then the function will fail.
.IP \(bu 2
\&\fBCRYPTO_atomic_load()\fR atomically loads the contents of \fI*val\fR into \fI*ret\fR.
\&\fIlock\fR will be locked, unless atomic operations are supported on the specific
platform. Because of this, if a variable is modified by \fBCRYPTO_atomic_or()\fR or
read by \fBCRYPTO_atomic_load()\fR then \fBCRYPTO_atomic_load()\fR must be the only way that
the variable is read. If atomic operations are not supported and \fIlock\fR is
NULL, then the function will fail.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCRYPTO_THREAD_run_once()\fR returns 1 on success, or 0 on error.
.PP
\&\fBCRYPTO_THREAD_lock_new()\fR returns the allocated lock, or NULL on error.
.PP
\&\fBCRYPTO_THREAD_lock_free()\fR returns no value.
.PP
The other functions return 1 on success, or 0 on error.
.SH NOTES
.IX Header "NOTES"
On Windows platforms the CRYPTO_THREAD_* types and functions in the
\&\fI<openssl/crypto.h>\fR header are dependent on some of the types
customarily made available by including \fI<windows.h>\fR. The application
developer is likely to require control over when the latter is included,
commonly as one of the first included headers. Therefore, it is defined as an
application developer's responsibility to include \fI<windows.h>\fR prior to
\&\fI<openssl/crypto.h>\fR where use of CRYPTO_THREAD_* types and functions is
required.
.SH EXAMPLES
.IX Header "EXAMPLES"
You can find out if OpenSSL was configured with thread support:
.PP
.Vb 6
\& #include <openssl/opensslconf.h>
\& #if defined(OPENSSL_THREADS)
\&     /* thread support enabled */
\& #else
\&     /* no thread support */
\& #endif
.Ve
.PP
This example safely initializes and uses a lock.
.PP
.Vb 4
\& #ifdef _WIN32
\& # include <windows.h>
\& #endif
\& #include <openssl/crypto.h>
\&
\& static CRYPTO_ONCE once = CRYPTO_ONCE_STATIC_INIT;
\& static CRYPTO_RWLOCK *lock;
\&
\& static void myinit(void)
\& {
\&     lock = CRYPTO_THREAD_lock_new();
\& }
\&
\& static int mylock(void)
\& {
\&     if (!CRYPTO_THREAD_run_once(&once, void init) || lock == NULL)
\&         return 0;
\&     return CRYPTO_THREAD_write_lock(lock);
\& }
\&
\& static int myunlock(void)
\& {
\&     return CRYPTO_THREAD_unlock(lock);
\& }
\&
\& int serialized(void)
\& {
\&     int ret = 0;
\&
\&     if (mylock()) {
\&         /* Your code here, do not return without releasing the lock! */
\&         ret = ... ;
\&     }
\&     myunlock();
\&     return ret;
\& }
.Ve
.PP
Finalization of locks is an advanced topic, not covered in this example.
This can only be done at process exit or when a dynamically loaded library is
no longer in use and is unloaded.
The simplest solution is to just "leak" the lock in applications and not
repeatedly load/unload shared libraries that allocate locks.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7), \fBopenssl\-threads\fR\|(7).
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
