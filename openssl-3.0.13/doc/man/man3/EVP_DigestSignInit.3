.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_DIGESTSIGNINIT 3ossl"
.TH EVP_DIGESTSIGNINIT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_DigestSignInit_ex, EVP_DigestSignInit, EVP_DigestSignUpdate,
EVP_DigestSignFinal, EVP_DigestSign \- EVP signing functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_DigestSignInit_ex(EVP_MD_CTX *ctx, EVP_PKEY_CTX **pctx,
\&                           const char *mdname, OSSL_LIB_CTX *libctx,
\&                           const char *props, EVP_PKEY *pkey,
\&                           const OSSL_PARAM params[]);
\& int EVP_DigestSignInit(EVP_MD_CTX *ctx, EVP_PKEY_CTX **pctx,
\&                        const EVP_MD *type, ENGINE *e, EVP_PKEY *pkey);
\& int EVP_DigestSignUpdate(EVP_MD_CTX *ctx, const void *d, size_t cnt);
\& int EVP_DigestSignFinal(EVP_MD_CTX *ctx, unsigned char *sig, size_t *siglen);
\&
\& int EVP_DigestSign(EVP_MD_CTX *ctx, unsigned char *sigret,
\&                    size_t *siglen, const unsigned char *tbs,
\&                    size_t tbslen);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The EVP signature routines are a high-level interface to digital signatures.
Input data is digested first before the signing takes place.
.PP
\&\fBEVP_DigestSignInit_ex()\fR sets up signing context \fIctx\fR to use a digest
with the name \fImdname\fR and private key \fIpkey\fR. The name of the digest to be
used is passed to the provider of the signature algorithm in use. How that
provider interprets the digest name is provider specific. The provider may
implement that digest directly itself or it may (optionally) choose to fetch it
(which could result in a digest from a different provider being selected). If the
provider supports fetching the digest then it may use the \fIprops\fR argument for
the properties to be used during the fetch. Finally, the passed parameters
\&\fIparams\fR, if not NULL, are set on the context before returning.
.PP
The \fIpkey\fR algorithm is used to fetch a \fBEVP_SIGNATURE\fR method implicitly, to
be used for the actual signing. See "Implicit fetch" in \fBprovider\fR\|(7) for
more information about implicit fetches.
.PP
The OpenSSL default and legacy providers support fetching digests and can fetch
those digests from any available provider. The OpenSSL FIPS provider also
supports fetching digests but will only fetch digests that are themselves
implemented inside the FIPS provider.
.PP
\&\fIctx\fR must be created with \fBEVP_MD_CTX_new()\fR before calling this function. If
\&\fIpctx\fR is not NULL, the EVP_PKEY_CTX of the signing operation will be written
to \fI*pctx\fR: this can be used to set alternative signing options. Note that any
existing value in \fI*pctx\fR is overwritten. The EVP_PKEY_CTX value returned must
not be freed directly by the application if \fIctx\fR is not assigned an
EVP_PKEY_CTX value before being passed to \fBEVP_DigestSignInit_ex()\fR
(which means the EVP_PKEY_CTX is created inside \fBEVP_DigestSignInit_ex()\fR
and it will be freed automatically when the EVP_MD_CTX is freed). If the
EVP_PKEY_CTX to be used is created by EVP_DigestSignInit_ex then it
will use the \fBOSSL_LIB_CTX\fR specified in \fIlibctx\fR and the property query string
specified in \fIprops\fR.
.PP
The digest \fImdname\fR may be NULL if the signing algorithm supports it. The
\&\fIprops\fR argument can always be NULL.
.PP
No \fBEVP_PKEY_CTX\fR will be created by \fBEVP_DigestSignInit_ex()\fR if the
passed \fIctx\fR has already been assigned one via \fBEVP_MD_CTX_set_pkey_ctx\fR\|(3).
See also \fBSM2\fR\|(7).
.PP
Only EVP_PKEY types that support signing can be used with these functions. This
includes MAC algorithms where the MAC generation is considered as a form of
"signing". Built-in EVP_PKEY types supported by these functions are CMAC,
Poly1305, DSA, ECDSA, HMAC, RSA, SipHash, Ed25519 and Ed448.
.PP
Not all digests can be used for all key types. The following combinations apply.
.IP DSA 4
.IX Item "DSA"
Supports SHA1, SHA224, SHA256, SHA384 and SHA512
.IP ECDSA 4
.IX Item "ECDSA"
Supports SHA1, SHA224, SHA256, SHA384, SHA512 and SM3
.IP "RSA with no padding" 4
.IX Item "RSA with no padding"
Supports no digests (the digest \fItype\fR must be NULL)
.IP "RSA with X931 padding" 4
.IX Item "RSA with X931 padding"
Supports SHA1, SHA256, SHA384 and SHA512
.IP "All other RSA padding types" 4
.IX Item "All other RSA padding types"
Support SHA1, SHA224, SHA256, SHA384, SHA512, MD5, MD5_SHA1, MD2, MD4, MDC2,
SHA3\-224, SHA3\-256, SHA3\-384, SHA3\-512
.IP "Ed25519 and Ed448" 4
.IX Item "Ed25519 and Ed448"
Support no digests (the digest \fItype\fR must be NULL)
.IP HMAC 4
.IX Item "HMAC"
Supports any digest
.IP "CMAC, Poly1305 and SipHash" 4
.IX Item "CMAC, Poly1305 and SipHash"
Will ignore any digest provided.
.PP
If RSA-PSS is used and restrictions apply then the digest must match.
.PP
\&\fBEVP_DigestSignInit()\fR works in the same way as \fBEVP_DigestSignInit_ex()\fR
except that the \fImdname\fR parameter will be inferred from the supplied
digest \fItype\fR, and \fIprops\fR will be NULL. Where supplied the ENGINE \fIe\fR will
be used for the signing and digest algorithm implementations. \fIe\fR may be NULL.
.PP
\&\fBEVP_DigestSignUpdate()\fR hashes \fIcnt\fR bytes of data at \fId\fR into the
signature context \fIctx\fR. This function can be called several times on the
same \fIctx\fR to include additional data.
.PP
Unless \fIsig\fR is NULL \fBEVP_DigestSignFinal()\fR signs the data in \fIctx\fR
and places the signature in \fIsig\fR.
Otherwise the maximum necessary size of the output buffer is written to
the \fIsiglen\fR parameter. If \fIsig\fR is not NULL then before the call the
\&\fIsiglen\fR parameter should contain the length of the \fIsig\fR buffer. If the
call is successful the signature is written to \fIsig\fR and the amount of data
written to \fIsiglen\fR.
.PP
\&\fBEVP_DigestSign()\fR signs \fItbslen\fR bytes of data at \fItbs\fR and places the
signature in \fIsig\fR and its length in \fIsiglen\fR in a similar way to
\&\fBEVP_DigestSignFinal()\fR. In the event of a failure \fBEVP_DigestSign()\fR cannot be
called again without reinitialising the EVP_MD_CTX. If \fIsig\fR is NULL before the
call then \fIsiglen\fR will be populated with the required size for the \fIsig\fR
buffer. If \fIsig\fR is non-NULL before the call then \fIsiglen\fR should contain the
length of the \fIsig\fR buffer.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_DigestSignInit()\fR, \fBEVP_DigestSignUpdate()\fR, \fBEVP_DigestSignFinal()\fR and
\&\fBEVP_DigestSign()\fR return 1 for success and 0 for failure.
.PP
The error codes can be obtained from \fBERR_get_error\fR\|(3).
.SH NOTES
.IX Header "NOTES"
The \fBEVP\fR interface to digital signatures should almost always be used in
preference to the low-level interfaces. This is because the code then becomes
transparent to the algorithm used and much more flexible.
.PP
\&\fBEVP_DigestSign()\fR is a one shot operation which signs a single block of data
in one function. For algorithms that support streaming it is equivalent to
calling \fBEVP_DigestSignUpdate()\fR and \fBEVP_DigestSignFinal()\fR. For algorithms which
do not support streaming (e.g. PureEdDSA) it is the only way to sign data.
.PP
In previous versions of OpenSSL there was a link between message digest types
and public key algorithms. This meant that "clone" digests such as \fBEVP_dss1()\fR
needed to be used to sign using SHA1 and DSA. This is no longer necessary and
the use of clone digest is now discouraged.
.PP
For some key types and parameters the random number generator must be seeded.
If the automatic seeding or reseeding of the OpenSSL CSPRNG fails due to
external circumstances (see \fBRAND\fR\|(7)), the operation will fail.
.PP
The call to \fBEVP_DigestSignFinal()\fR internally finalizes a copy of the digest
context. This means that calls to \fBEVP_DigestSignUpdate()\fR and
\&\fBEVP_DigestSignFinal()\fR can be called later to digest and sign additional data.
.PP
\&\fBEVP_DigestSignInit()\fR and \fBEVP_DigestSignInit_ex()\fR functions can be called
multiple times on a context and the parameters set by previous calls should be
preserved if the \fIpkey\fR parameter is NULL. The call then just resets the state
of the \fIctx\fR.
.PP
Ignoring failure returns of \fBEVP_DigestSignInit()\fR and \fBEVP_DigestSignInit_ex()\fR
functions can lead to subsequent undefined behavior when calling
\&\fBEVP_DigestSignUpdate()\fR, \fBEVP_DigestSignFinal()\fR, or \fBEVP_DigestSign()\fR.
.PP
The use of \fBEVP_PKEY_get_size()\fR with these functions is discouraged because some
signature operations may have a signature length which depends on the
parameters set. As a result \fBEVP_PKEY_get_size()\fR would have to return a value
which indicates the maximum possible signature for any set of parameters.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_DigestVerifyInit\fR\|(3),
\&\fBEVP_DigestInit\fR\|(3),
\&\fBevp\fR\|(7), \fBHMAC\fR\|(3), \fBMD2\fR\|(3),
\&\fBMD5\fR\|(3), \fBMDC2\fR\|(3), \fBRIPEMD160\fR\|(3),
\&\fBSHA1\fR\|(3), \fBopenssl\-dgst\fR\|(1),
\&\fBRAND\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
\&\fBEVP_DigestSignInit()\fR, \fBEVP_DigestSignUpdate()\fR and \fBEVP_DigestSignFinal()\fR
were added in OpenSSL 1.0.0.
.PP
\&\fBEVP_DigestSignInit_ex()\fR was added in OpenSSL 3.0.
.PP
\&\fBEVP_DigestSignUpdate()\fR was converted from a macro to a function in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
