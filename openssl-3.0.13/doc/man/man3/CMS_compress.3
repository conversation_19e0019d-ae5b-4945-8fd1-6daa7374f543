.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CMS_COMPRESS 3ossl"
.TH CMS_COMPRESS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CMS_compress \- create a CMS CompressedData structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& CMS_ContentInfo *CMS_compress(BIO *in, int comp_nid, unsigned int flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBCMS_compress()\fR creates and returns a CMS CompressedData structure. \fBcomp_nid\fR
is the compression algorithm to use or \fBNID_undef\fR to use the default
algorithm (zlib compression). \fBin\fR is the content to be compressed.
\&\fBflags\fR is an optional set of flags.
.PP
The only currently supported compression algorithm is zlib using the NID
NID_zlib_compression.
.PP
If zlib support is not compiled into OpenSSL then \fBCMS_compress()\fR will return
an error.
.PP
If the \fBCMS_TEXT\fR flag is set MIME headers for type \fBtext/plain\fR are
prepended to the data.
.PP
Normally the supplied content is translated into MIME canonical format (as
required by the S/MIME specifications) if \fBCMS_BINARY\fR is set no translation
occurs. This option should be used if the supplied data is in binary format
otherwise the translation will corrupt it. If \fBCMS_BINARY\fR is set then
\&\fBCMS_TEXT\fR is ignored.
.PP
If the \fBCMS_STREAM\fR flag is set a partial \fBCMS_ContentInfo\fR structure is
returned suitable for streaming I/O: no data is read from the BIO \fBin\fR.
.PP
The compressed data is included in the CMS_ContentInfo structure, unless
\&\fBCMS_DETACHED\fR is set in which case it is omitted. This is rarely used in
practice and is not supported by \fBSMIME_write_CMS()\fR.
.PP
If the flag \fBCMS_STREAM\fR is set the returned \fBCMS_ContentInfo\fR structure is
\&\fBnot\fR complete and outputting its contents via a function that does not
properly finalize the \fBCMS_ContentInfo\fR structure will give unpredictable
results.
.PP
Several functions including \fBSMIME_write_CMS()\fR, \fBi2d_CMS_bio_stream()\fR,
\&\fBPEM_write_bio_CMS_stream()\fR finalize the structure. Alternatively finalization
can be performed by obtaining the streaming ASN1 \fBBIO\fR directly using
\&\fBBIO_new_CMS()\fR.
.PP
Additional compression parameters such as the zlib compression level cannot
currently be set.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCMS_compress()\fR returns either a CMS_ContentInfo structure or NULL if an error
occurred. The error can be obtained from \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBCMS_uncompress\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBCMS_STREAM\fR flag was added in OpenSSL 1.0.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2008\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
