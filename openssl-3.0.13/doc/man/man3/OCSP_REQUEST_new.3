.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OCSP_REQUEST_NEW 3ossl"
.TH OCSP_REQUEST_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OCSP_REQUEST_new, OCSP_REQUEST_free, OCSP_request_add0_id, OCSP_request_sign,
OCSP_request_add1_cert, OCSP_request_onereq_count,
OCSP_request_onereq_get0 \- OCSP request functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ocsp.h>
\&
\& OCSP_REQUEST *OCSP_REQUEST_new(void);
\& void OCSP_REQUEST_free(OCSP_REQUEST *req);
\&
\& OCSP_ONEREQ *OCSP_request_add0_id(OCSP_REQUEST *req, OCSP_CERTID *cid);
\&
\& int OCSP_request_sign(OCSP_REQUEST *req,
\&                       X509 *signer, EVP_PKEY *key, const EVP_MD *dgst,
\&                       STACK_OF(X509) *certs, unsigned long flags);
\&
\& int OCSP_request_add1_cert(OCSP_REQUEST *req, X509 *cert);
\&
\& int OCSP_request_onereq_count(OCSP_REQUEST *req);
\& OCSP_ONEREQ *OCSP_request_onereq_get0(OCSP_REQUEST *req, int i);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOCSP_REQUEST_new()\fR allocates and returns an empty \fBOCSP_REQUEST\fR structure.
.PP
\&\fBOCSP_REQUEST_free()\fR frees up the request structure \fBreq\fR.
.PP
\&\fBOCSP_request_add0_id()\fR adds certificate ID \fBcid\fR to \fBreq\fR. It returns
the \fBOCSP_ONEREQ\fR structure added so an application can add additional
extensions to the request. The \fBid\fR parameter \fBMUST NOT\fR be freed up after
the operation.
.PP
\&\fBOCSP_request_sign()\fR signs OCSP request \fBreq\fR using certificate
\&\fBsigner\fR, private key \fBkey\fR, digest \fBdgst\fR and additional certificates
\&\fBcerts\fR. If the \fBflags\fR option \fBOCSP_NOCERTS\fR is set then no certificates
will be included in the request.
.PP
\&\fBOCSP_request_add1_cert()\fR adds certificate \fBcert\fR to request \fBreq\fR. The
application is responsible for freeing up \fBcert\fR after use.
.PP
\&\fBOCSP_request_onereq_count()\fR returns the total number of \fBOCSP_ONEREQ\fR
structures in \fBreq\fR.
.PP
\&\fBOCSP_request_onereq_get0()\fR returns an internal pointer to the \fBOCSP_ONEREQ\fR
contained in \fBreq\fR of index \fBi\fR. The index value \fBi\fR runs from 0 to
OCSP_request_onereq_count(req) \- 1.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOCSP_REQUEST_new()\fR returns an empty \fBOCSP_REQUEST\fR structure or \fBNULL\fR if
an error occurred.
.PP
\&\fBOCSP_request_add0_id()\fR returns the \fBOCSP_ONEREQ\fR structure containing \fBcid\fR
or \fBNULL\fR if an error occurred.
.PP
\&\fBOCSP_request_sign()\fR and \fBOCSP_request_add1_cert()\fR return 1 for success and 0
for failure.
.PP
\&\fBOCSP_request_onereq_count()\fR returns the total number of \fBOCSP_ONEREQ\fR
structures in \fBreq\fR and \-1 on error.
.PP
\&\fBOCSP_request_onereq_get0()\fR returns a pointer to an \fBOCSP_ONEREQ\fR structure
or \fBNULL\fR if the index value is out or range.
.SH NOTES
.IX Header "NOTES"
An OCSP request structure contains one or more \fBOCSP_ONEREQ\fR structures
corresponding to each certificate.
.PP
\&\fBOCSP_request_onereq_count()\fR and \fBOCSP_request_onereq_get0()\fR are mainly used by
OCSP responders.
.SH EXAMPLES
.IX Header "EXAMPLES"
Create an \fBOCSP_REQUEST\fR structure for certificate \fBcert\fR with issuer
\&\fBissuer\fR:
.PP
.Vb 2
\& OCSP_REQUEST *req;
\& OCSP_ID *cid;
\&
\& req = OCSP_REQUEST_new();
\& if (req == NULL)
\&    /* error */
\& cid = OCSP_cert_to_id(EVP_sha1(), cert, issuer);
\& if (cid == NULL)
\&    /* error */
\&
\& if (OCSP_REQUEST_add0_id(req, cid) == NULL)
\&    /* error */
\&
\& /* Do something with req, e.g. query responder */
\&
\& OCSP_REQUEST_free(req);
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7),
\&\fBOCSP_cert_to_id\fR\|(3),
\&\fBOCSP_request_add1_nonce\fR\|(3),
\&\fBOCSP_resp_find_status\fR\|(3),
\&\fBOCSP_response_status\fR\|(3),
\&\fBOCSP_sendreq_new\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
