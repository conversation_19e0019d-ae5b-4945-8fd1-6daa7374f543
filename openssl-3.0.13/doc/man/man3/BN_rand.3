.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BN_RAND 3ossl"
.TH BN_RAND 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BN_rand_ex, BN_rand, BN_priv_rand_ex, BN_priv_rand, BN_pseudo_rand,
BN_rand_range_ex, BN_rand_range, BN_priv_rand_range_ex, BN_priv_rand_range,
BN_pseudo_rand_range
\&\- generate pseudo\-random number
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bn.h>
\&
\& int BN_rand_ex(BIGNUM *rnd, int bits, int top, int bottom,
\&                unsigned int strength, BN_CTX *ctx);
\& int BN_rand(BIGNUM *rnd, int bits, int top, int bottom);
\&
\& int BN_priv_rand_ex(BIGNUM *rnd, int bits, int top, int bottom,
\&                     unsigned int strength, BN_CTX *ctx);
\& int BN_priv_rand(BIGNUM *rnd, int bits, int top, int bottom);
\&
\& int BN_rand_range_ex(BIGNUM *rnd, const BIGNUM *range, unsigned int strength,
\&                      BN_CTX *ctx);
\& int BN_rand_range(BIGNUM *rnd, const BIGNUM *range);
\&
\& int BN_priv_rand_range_ex(BIGNUM *rnd, const BIGNUM *range, unsigned int strength,
\&                           BN_CTX *ctx);
\& int BN_priv_rand_range(BIGNUM *rnd, const BIGNUM *range);
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 2
\& int BN_pseudo_rand(BIGNUM *rnd, int bits, int top, int bottom);
\& int BN_pseudo_rand_range(BIGNUM *rnd, const BIGNUM *range);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBN_rand_ex()\fR generates a cryptographically strong pseudo-random
number of \fIbits\fR in length and security strength at least \fIstrength\fR bits
using the random number generator for the library context associated with
\&\fIctx\fR. The function stores the generated data in \fIrnd\fR. The parameter \fIctx\fR
may be NULL in which case the default library context is used.
If \fIbits\fR is less than zero, or too small to
accommodate the requirements specified by the \fItop\fR and \fIbottom\fR
parameters, an error is returned.
The \fItop\fR parameters specifies
requirements on the most significant bit of the generated number.
If it is \fBBN_RAND_TOP_ANY\fR, there is no constraint.
If it is \fBBN_RAND_TOP_ONE\fR, the top bit must be one.
If it is \fBBN_RAND_TOP_TWO\fR, the two most significant bits of
the number will be set to 1, so that the product of two such random
numbers will always have 2*\fIbits\fR length.
If \fIbottom\fR is \fBBN_RAND_BOTTOM_ODD\fR, the number will be odd; if it
is \fBBN_RAND_BOTTOM_ANY\fR it can be odd or even.
If \fIbits\fR is 1 then \fItop\fR cannot also be \fBBN_RAND_TOP_TWO\fR.
.PP
\&\fBBN_rand()\fR is the same as \fBBN_rand_ex()\fR except that the default library context
is always used.
.PP
\&\fBBN_rand_range_ex()\fR generates a cryptographically strong pseudo-random
number \fIrnd\fR, of security strength at least \fIstrength\fR bits,
in the range 0 <= \fIrnd\fR < \fIrange\fR using the random number
generator for the library context associated with \fIctx\fR. The parameter \fIctx\fR
may be NULL in which case the default library context is used.
.PP
\&\fBBN_rand_range()\fR is the same as \fBBN_rand_range_ex()\fR except that the default
library context is always used.
.PP
\&\fBBN_priv_rand_ex()\fR, \fBBN_priv_rand()\fR, \fBBN_priv_rand_rand_ex()\fR and
\&\fBBN_priv_rand_range()\fR have the same semantics as \fBBN_rand_ex()\fR, \fBBN_rand()\fR,
\&\fBBN_rand_range_ex()\fR and \fBBN_rand_range()\fR respectively.  They are intended to be
used for generating values that should remain private, and mirror the
same difference between \fBRAND_bytes\fR\|(3) and \fBRAND_priv_bytes\fR\|(3).
.SH NOTES
.IX Header "NOTES"
Always check the error return value of these functions and do not take
randomness for granted: an error occurs if the CSPRNG has not been
seeded with enough randomness to ensure an unpredictable byte sequence.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The functions return 1 on success, 0 on error.
The error codes can be obtained by \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3),
\&\fBRAND_add\fR\|(3),
\&\fBRAND_bytes\fR\|(3),
\&\fBRAND_priv_bytes\fR\|(3),
\&\fBRAND\fR\|(7),
\&\fBEVP_RAND\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
.IP \(bu 2
Starting with OpenSSL release 1.1.0, \fBBN_pseudo_rand()\fR has been identical
to \fBBN_rand()\fR and \fBBN_pseudo_rand_range()\fR has been identical to
\&\fBBN_rand_range()\fR.
The \fBBN_pseudo_rand()\fR and \fBBN_pseudo_rand_range()\fR functions were
deprecated in OpenSSL 3.0.
.IP \(bu 2
The \fBBN_priv_rand()\fR and \fBBN_priv_rand_range()\fR functions were added in
OpenSSL 1.1.1.
.IP \(bu 2
The \fBBN_rand_ex()\fR, \fBBN_priv_rand_ex()\fR, \fBBN_rand_range_ex()\fR and
\&\fBBN_priv_rand_range_ex()\fR functions were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
