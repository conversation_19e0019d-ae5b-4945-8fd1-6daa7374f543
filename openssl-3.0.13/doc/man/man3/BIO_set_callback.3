.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_SET_CALLBACK 3ossl"
.TH BIO_SET_CALLBACK 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_set_callback_ex, BIO_get_callback_ex, BIO_set_callback, BIO_get_callback,
BIO_set_callback_arg, BIO_get_callback_arg, BIO_debug_callback,
BIO_debug_callback_ex, BIO_callback_fn_ex, BIO_callback_fn
\&\- BIO callback functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& typedef long (*BIO_callback_fn_ex)(BIO *b, int oper, const char *argp,
\&                                    size_t len, int argi,
\&                                    long argl, int ret, size_t *processed);
\&
\& void BIO_set_callback_ex(BIO *b, BIO_callback_fn_ex callback);
\& BIO_callback_fn_ex BIO_get_callback_ex(const BIO *b);
\&
\& void BIO_set_callback_arg(BIO *b, char *arg);
\& char *BIO_get_callback_arg(const BIO *b);
\&
\& long BIO_debug_callback_ex(BIO *bio, int oper, const char *argp, size_t len,
\&                            int argi, long argl, int ret, size_t *processed);
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 6
\& typedef long (*BIO_callback_fn)(BIO *b, int oper, const char *argp, int argi,
\&                                 long argl, long ret);
\& void BIO_set_callback(BIO *b, BIO_callback_fn cb);
\& BIO_callback_fn BIO_get_callback(const BIO *b);
\& long BIO_debug_callback(BIO *bio, int cmd, const char *argp, int argi,
\&                         long argl, long ret);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_set_callback_ex()\fR and \fBBIO_get_callback_ex()\fR set and retrieve the BIO
callback. The callback is called during most high-level BIO operations. It can
be used for debugging purposes to trace operations on a BIO or to modify its
operation.
.PP
\&\fBBIO_set_callback()\fR and \fBBIO_get_callback()\fR set and retrieve the old format BIO
callback. New code should not use these functions, but they are retained for
backwards compatibility. Any callback set via \fBBIO_set_callback_ex()\fR will get
called in preference to any set by \fBBIO_set_callback()\fR.
.PP
\&\fBBIO_set_callback_arg()\fR and \fBBIO_get_callback_arg()\fR are macros which can be
used to set and retrieve an argument for use in the callback.
.PP
\&\fBBIO_debug_callback_ex()\fR is a standard debugging callback which prints
out information relating to each BIO operation. If the callback
argument is set it is interpreted as a BIO to send the information
to, otherwise stderr is used. The \fBBIO_debug_callback()\fR function is the
deprecated version of the same callback for use with the old callback
format \fBBIO_set_callback()\fR function.
.PP
BIO_callback_fn_ex is the type of the callback function and BIO_callback_fn
is the type of the old format callback function. The meaning of each argument
is described below:
.IP \fBb\fR 4
.IX Item "b"
The BIO the callback is attached to is passed in \fBb\fR.
.IP \fBoper\fR 4
.IX Item "oper"
\&\fBoper\fR is set to the operation being performed. For some operations
the callback is called twice, once before and once after the actual
operation, the latter case has \fBoper\fR or'ed with BIO_CB_RETURN.
.IP \fBlen\fR 4
.IX Item "len"
The length of the data requested to be read or written. This is only useful if
\&\fBoper\fR is BIO_CB_READ, BIO_CB_WRITE or BIO_CB_GETS.
.IP "\fBargp\fR \fBargi\fR \fBargl\fR" 4
.IX Item "argp argi argl"
The meaning of the arguments \fBargp\fR, \fBargi\fR and \fBargl\fR depends on
the value of \fBoper\fR, that is the operation being performed.
.IP \fBprocessed\fR 4
.IX Item "processed"
\&\fBprocessed\fR is a pointer to a location which will be updated with the amount of
data that was actually read or written. Only used for BIO_CB_READ, BIO_CB_WRITE,
BIO_CB_GETS and BIO_CB_PUTS.
.IP \fBret\fR 4
.IX Item "ret"
\&\fBret\fR is the return value that would be returned to the
application if no callback were present. The actual value returned
is the return value of the callback itself. In the case of callbacks
called before the actual BIO operation 1 is placed in \fBret\fR, if
the return value is not positive it will be immediately returned to
the application and the BIO operation will not be performed.
.PP
The callback should normally simply return \fBret\fR when it has
finished processing, unless it specifically wishes to modify the
value returned to the application.
.SH "CALLBACK OPERATIONS"
.IX Header "CALLBACK OPERATIONS"
In the notes below, \fBcallback\fR defers to the actual callback
function that is called.
.IP \fBBIO_free(b)\fR 4
.IX Item "BIO_free(b)"
.Vb 1
\& callback_ex(b, BIO_CB_FREE, NULL, 0, 0, 0L, 1L, NULL)
.Ve
.Sp
or
.Sp
.Vb 1
\& callback(b, BIO_CB_FREE, NULL, 0L, 0L, 1L)
.Ve
.Sp
is called before the free operation.
.IP "\fBBIO_read_ex(b, data, dlen, readbytes)\fR" 4
.IX Item "BIO_read_ex(b, data, dlen, readbytes)"
.Vb 1
\& callback_ex(b, BIO_CB_READ, data, dlen, 0, 0L, 1L, NULL)
.Ve
.Sp
or
.Sp
.Vb 1
\& callback(b, BIO_CB_READ, data, dlen, 0L, 1L)
.Ve
.Sp
is called before the read and
.Sp
.Vb 2
\& callback_ex(b, BIO_CB_READ | BIO_CB_RETURN, data, dlen, 0, 0L, retvalue,
\&             &readbytes)
.Ve
.Sp
or
.Sp
.Vb 1
\& callback(b, BIO_CB_READ|BIO_CB_RETURN, data, dlen, 0L, retvalue)
.Ve
.Sp
after.
.IP "\fBBIO_write(b, data, dlen, written)\fR" 4
.IX Item "BIO_write(b, data, dlen, written)"
.Vb 1
\& callback_ex(b, BIO_CB_WRITE, data, dlen, 0, 0L, 1L, NULL)
.Ve
.Sp
or
.Sp
.Vb 1
\& callback(b, BIO_CB_WRITE, datat, dlen, 0L, 1L)
.Ve
.Sp
is called before the write and
.Sp
.Vb 2
\& callback_ex(b, BIO_CB_WRITE | BIO_CB_RETURN, data, dlen, 0, 0L, retvalue,
\&             &written)
.Ve
.Sp
or
.Sp
.Vb 1
\& callback(b, BIO_CB_WRITE|BIO_CB_RETURN, data, dlen, 0L, retvalue)
.Ve
.Sp
after.
.IP "\fBBIO_gets(b, buf, size)\fR" 4
.IX Item "BIO_gets(b, buf, size)"
.Vb 1
\& callback_ex(b, BIO_CB_GETS, buf, size, 0, 0L, 1, NULL, NULL)
.Ve
.Sp
or
.Sp
.Vb 1
\& callback(b, BIO_CB_GETS, buf, size, 0L, 1L)
.Ve
.Sp
is called before the operation and
.Sp
.Vb 2
\& callback_ex(b, BIO_CB_GETS | BIO_CB_RETURN, buf, size, 0, 0L, retvalue,
\&             &readbytes)
.Ve
.Sp
or
.Sp
.Vb 1
\& callback(b, BIO_CB_GETS|BIO_CB_RETURN, buf, size, 0L, retvalue)
.Ve
.Sp
after.
.IP "\fBBIO_puts(b, buf)\fR" 4
.IX Item "BIO_puts(b, buf)"
.Vb 1
\& callback_ex(b, BIO_CB_PUTS, buf, 0, 0, 0L, 1L, NULL);
.Ve
.Sp
or
.Sp
.Vb 1
\& callback(b, BIO_CB_PUTS, buf, 0, 0L, 1L)
.Ve
.Sp
is called before the operation and
.Sp
.Vb 1
\& callback_ex(b, BIO_CB_PUTS | BIO_CB_RETURN, buf, 0, 0, 0L, retvalue, &written)
.Ve
.Sp
or
.Sp
.Vb 1
\& callback(b, BIO_CB_PUTS|BIO_CB_RETURN, buf, 0, 0L, retvalue)
.Ve
.Sp
after.
.IP "\fBBIO_ctrl(BIO *b, int cmd, long larg, void *parg)\fR" 4
.IX Item "BIO_ctrl(BIO *b, int cmd, long larg, void *parg)"
.Vb 1
\& callback_ex(b, BIO_CB_CTRL, parg, 0, cmd, larg, 1L, NULL)
.Ve
.Sp
or
.Sp
.Vb 1
\& callback(b, BIO_CB_CTRL, parg, cmd, larg, 1L)
.Ve
.Sp
is called before the call and
.Sp
.Vb 1
\& callback_ex(b, BIO_CB_CTRL | BIO_CB_RETURN, parg, 0, cmd, larg, ret, NULL)
.Ve
.Sp
or
.Sp
.Vb 1
\& callback(b, BIO_CB_CTRL|BIO_CB_RETURN, parg, cmd, larg, ret)
.Ve
.Sp
after.
.Sp
Note: \fBcmd\fR == \fBBIO_CTRL_SET_CALLBACK\fR is special, because \fBparg\fR is not the
argument of type \fBBIO_info_cb\fR itself.  In this case \fBparg\fR is a pointer to
the actual call parameter, see \fBBIO_callback_ctrl\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_get_callback_ex()\fR and \fBBIO_get_callback()\fR return the callback function
previously set by a call to \fBBIO_set_callback_ex()\fR and \fBBIO_set_callback()\fR
respectively.
.PP
\&\fBBIO_get_callback_arg()\fR returns a \fBchar\fR pointer to the value previously set
via a call to \fBBIO_set_callback_arg()\fR.
.PP
\&\fBBIO_debug_callback()\fR returns 1 or \fBret\fR if it's called after specific BIO
operations.
.SH EXAMPLES
.IX Header "EXAMPLES"
The \fBBIO_debug_callback_ex()\fR function is an example, its source is
in crypto/bio/bio_cb.c
.SH HISTORY
.IX Header "HISTORY"
The \fBBIO_debug_callback_ex()\fR function was added in OpenSSL 3.0.
.PP
\&\fBBIO_set_callback()\fR, \fBBIO_get_callback()\fR, and \fBBIO_debug_callback()\fR were
deprecated in OpenSSL 3.0. Use the non-deprecated _ex functions instead.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
