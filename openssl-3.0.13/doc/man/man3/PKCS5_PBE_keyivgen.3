.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS5_PBE_KEYIVGEN 3ossl"
.TH PKCS5_PBE_KEYIVGEN 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS5_PBE_keyivgen, PKCS5_PBE_keyivgen_ex, PKCS5_pbe2_set, PKCS5_pbe2_set_iv,
PKCS5_pbe2_set_iv_ex, PKCS5_pbe_set, PKCS5_pbe_set_ex, PKCS5_pbe2_set_scrypt,
PKCS5_pbe_set0_algor, PKCS5_pbe_set0_algor_ex,
PKCS5_v2_PBE_keyivgen, PKCS5_v2_PBE_keyivgen_ex,
PKCS5_v2_scrypt_keyivgen, PKCS5_v2_scrypt_keyivgen_ex,
PKCS5_pbkdf2_set, PKCS5_pbkdf2_set_ex, EVP_PBE_scrypt, EVP_PBE_scrypt_ex
\&\- PKCS#5 Password based encryption routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int PKCS5_PBE_keyivgen(EVP_CIPHER_CTX *ctx, const char *pass, int passlen,
\&                        ASN1_TYPE *param, const EVP_CIPHER *cipher,
\&                        const EVP_MD *md, int en_de);
\& int PKCS5_PBE_keyivgen_ex(EVP_CIPHER_CTX *cctx, const char *pass, int passlen,
\&                           ASN1_TYPE *param, const EVP_CIPHER *cipher,
\&                           const EVP_MD *md, int en_de, OSSL_LIB_CTX *libctx,
\&                           const char *propq);
\& int PKCS5_v2_PBE_keyivgen(EVP_CIPHER_CTX *ctx, const char *pass, int passlen,
\&                           ASN1_TYPE *param, const EVP_CIPHER *cipher,
\&                           const EVP_MD *md, int en_de);
\& int PKCS5_v2_PBE_keyivgen_ex(EVP_CIPHER_CTX *ctx, const char *pass, int passlen,
\&                              ASN1_TYPE *param, const EVP_CIPHER *cipher,
\&                              const EVP_MD *md, int en_de,
\&                              OSSL_LIB_CTX *libctx, const char *propq);
\& int EVP_PBE_scrypt(const char *pass, size_t passlen,
\&                    const unsigned char *salt, size_t saltlen,
\&                    uint64_t N, uint64_t r, uint64_t p, uint64_t maxmem,
\&                    unsigned char *key, size_t keylen);
\& int EVP_PBE_scrypt_ex(const char *pass, size_t passlen,
\&                       const unsigned char *salt, size_t saltlen,
\&                       uint64_t N, uint64_t r, uint64_t p, uint64_t maxmem,
\&                       unsigned char *key, size_t keylen,
\&                       OSSL_LIB_CTX *ctx, const char *propq);
\& int PKCS5_v2_scrypt_keyivgen(EVP_CIPHER_CTX *ctx, const char *pass,
\&                              int passlen, ASN1_TYPE *param,
\&                              const EVP_CIPHER *c, const EVP_MD *md, int en_de);
\& int PKCS5_v2_scrypt_keyivgen_ex(EVP_CIPHER_CTX *ctx, const char *pass,
\&                                 int passlen, ASN1_TYPE *param,
\&                                 const EVP_CIPHER *c, const EVP_MD *md, int en_de,
\&                                 OSSL_LIB_CTX *libctx, const char *propq);
\&
\& #include <openssl/x509.h>
\&
\& int PKCS5_pbe_set0_algor(X509_ALGOR *algor, int alg, int iter,
\&                          const unsigned char *salt, int saltlen);
\& int PKCS5_pbe_set0_algor_ex(X509_ALGOR *algor, int alg, int iter,
\&                             const unsigned char *salt, int saltlen,
\&                             OSSL_LIB_CTX *libctx);
\&
\& X509_ALGOR *PKCS5_pbe_set(int alg, int iter,
\&                           const unsigned char *salt, int saltlen);
\& X509_ALGOR *PKCS5_pbe_set_ex(int alg, int iter,
\&                              const unsigned char *salt, int saltlen,
\&                              OSSL_LIB_CTX *libctx);
\&
\& X509_ALGOR *PKCS5_pbe2_set(const EVP_CIPHER *cipher, int iter,
\&                            unsigned char *salt, int saltlen);
\& X509_ALGOR *PKCS5_pbe2_set_iv(const EVP_CIPHER *cipher, int iter,
\&                               unsigned char *salt, int saltlen,
\&                               unsigned char *aiv, int prf_nid);
\& X509_ALGOR *PKCS5_pbe2_set_iv_ex(const EVP_CIPHER *cipher, int iter,
\&                                  unsigned char *salt, int saltlen,
\&                                  unsigned char *aiv, int prf_nid,
\&                                  OSSL_LIB_CTX *libctx);
\& X509_ALGOR *PKCS5_pbe2_set_scrypt(const EVP_CIPHER *cipher,
\&                                   const unsigned char *salt, int saltlen,
\&                                   unsigned char *aiv, uint64_t N, uint64_t r,
\&                                   uint64_t p);
\&
\& X509_ALGOR *PKCS5_pbkdf2_set(int iter, unsigned char *salt, int saltlen,
\&                              int prf_nid, int keylen);
\& X509_ALGOR *PKCS5_pbkdf2_set_ex(int iter, unsigned char *salt, int saltlen,
\&                                 int prf_nid, int keylen,
\&                                 OSSL_LIB_CTX *libctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
.SS "Key Derivation"
.IX Subsection "Key Derivation"
\&\fBPKCS5_PBE_keyivgen()\fR and \fBPKCS5_PBE_keyivgen_ex()\fR take a password \fIpass\fR of
length \fIpasslen\fR, parameters \fIparam\fR and a message digest function \fImd_type\fR
and performs a key derivation according to PKCS#5 PBES1. The resulting key is
then used to initialise the cipher context \fIctx\fR with a cipher \fIcipher\fR for
encryption (\fIen_de\fR=1) or decryption (\fIen_de\fR=0).
.PP
\&\fIpass\fR is an optional parameter and can be NULL. If \fIpasslen\fR is \-1, then the
function will calculate the length of \fIpass\fR using \fBstrlen()\fR.
.PP
\&\fBPKCS5_v2_PBE_keyivgen()\fR and \fBPKCS5_v2_PBE_keyivgen_ex()\fR are similar to the above
but instead use PKCS#5 PBES2 as the encryption algorithm using the supplied
parameters.
.PP
\&\fBPKCS5_v2_scrypt_keyivgen()\fR and \fBPKCS5_v2_scrypt_keyivgen_ex()\fR use SCRYPT as the
key derivation part of the encryption algorithm.
.PP
\&\fIsalt\fR is the salt used in the derivation of length \fIsaltlen\fR. If the
\&\fIsalt\fR is NULL, then \fIsaltlen\fR must be 0. The function will not
attempt to calculate the length of the \fIsalt\fR because it is not assumed to
be NULL terminated.
.PP
\&\fIiter\fR is the iteration count and its value should be greater than or
equal to 1. RFC 2898 suggests an iteration count of at least 1000. Any
\&\fIiter\fR less than 1 is treated as a single iteration.
.PP
\&\fIdigest\fR is the message digest function used in the derivation.
.PP
Functions ending in \fB_ex()\fR take optional parameters \fIlibctx\fR and \fIpropq\fR which
are used to select appropriate algorithm implementations.
.SS "Algorithm Identifier Creation"
.IX Subsection "Algorithm Identifier Creation"
\&\fBPKCS5_pbe_set()\fR, \fBPKCS5_pbe_set_ex()\fR, \fBPKCS5_pbe2_set()\fR, \fBPKCS5_pbe2_set_iv()\fR,
\&\fBPKCS5_pbe2_set_iv_ex()\fR and \fBPKCS5_pbe2_set_scrypt()\fR generate an \fBX509_ALGOR\fR
object which represents an AlgorithmIdentifier containing the algorithm OID and
associated parameters for the PBE algorithm.
.PP
\&\fBPKCS5_pbkdf2_set()\fR and \fBPKCS5_pbkdf2_set_ex()\fR generate an \fBX509_ALGOR\fR
object which represents an AlgorithmIdentifier containing the algorithm OID and
associated parameters for the PBKDF2 algorithm.
.PP
\&\fBPKCS5_pbe_set0_algor()\fR and \fBPKCS5_pbe_set0_algor_ex()\fR set the PBE algorithm OID and
parameters into the supplied \fBX509_ALGOR\fR.
.SH NOTES
.IX Header "NOTES"
The *\fB_keyivgen()\fR functions are typically used in PKCS#12 to encrypt objects.
.PP
These functions make no assumption regarding the given password.
It will simply be treated as a byte sequence.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPKCS5_PBE_keyivgen()\fR, \fBPKCS5_v2_PBE_keyivgen()\fR,
\&\fBPKCS5_v2_PBE_keyivgen_ex()\fR, \fBPKCS5_v2_scrypt_keyivgen()\fR,
\&\fBPKCS5_v2_scrypt_keyivgen_ex()\fR, \fBPKCS5_pbe_set0_algor()\fR and
\&\fBPKCS5_pbe_set0_algor_ex()\fR return 1 for success and 0 if an error occurs.
.PP
\&\fBPKCS5_pbe_set()\fR, \fBPKCS5_pbe_set_ex()\fR, \fBPKCS5_pbe2_set()\fR, \fBPKCS5_pbe2_set_iv()\fR,
\&\fBPKCS5_pbe2_set_iv_ex()\fR, \fBPKCS5_pbe2_set_scrypt()\fR,
\&\fBPKCS5_pbkdf2_set()\fR and \fBPKCS5_pbkdf2_set_ex()\fR return an \fBX509_ALGOR\fR object or
NULL if an error occurs.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
IETF RFC 8018 (<https://tools.ietf.org/html/rfc8018>)
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PBE_CipherInit_ex\fR\|(3),
\&\fBPKCS12_pbe_crypt_ex\fR\|(3),
\&\fBpassphrase\-encoding\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
\&\fBPKCS5_v2_PBE_keyivgen_ex()\fR, \fBEVP_PBE_scrypt_ex()\fR, \fBPKCS5_v2_scrypt_keyivgen_ex()\fR,
\&\fBPKCS5_pbe_set0_algor_ex()\fR, \fBPKCS5_pbe_set_ex()\fR, \fBPKCS5_pbe2_set_iv_ex()\fR and
\&\fBPKCS5_pbkdf2_set_ex()\fR were added in OpenSSL 3.0.
.PP
From OpenSSL 3.0 the PBKDF1 algorithm used in \fBPKCS5_PBE_keyivgen()\fR and
\&\fBPKCS5_PBE_keyivgen_ex()\fR has been moved to the legacy provider as an EVP_KDF.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
