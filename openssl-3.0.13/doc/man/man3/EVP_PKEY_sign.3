.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_SIGN 3ossl"
.TH EVP_PKEY_SIGN 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_sign_init, EVP_PKEY_sign_init_ex, EVP_PKEY_sign
\&\- sign using a public key algorithm
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_sign_init(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_sign_init_ex(EVP_PKEY_CTX *ctx, const OSSL_PARAM params[]);
\& int EVP_PKEY_sign(EVP_PKEY_CTX *ctx,
\&                   unsigned char *sig, size_t *siglen,
\&                   const unsigned char *tbs, size_t tbslen);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_PKEY_sign_init()\fR initializes a public key algorithm context \fIctx\fR for
signing using the algorithm given when the context was created
using \fBEVP_PKEY_CTX_new\fR\|(3) or variants thereof.  The algorithm is used to
fetch a \fBEVP_SIGNATURE\fR method implicitly, see "Implicit fetch" in \fBprovider\fR\|(7)
for more information about implicit fetches.
.PP
\&\fBEVP_PKEY_sign_init_ex()\fR is the same as \fBEVP_PKEY_sign_init()\fR but additionally
sets the passed parameters \fIparams\fR on the context before returning.
.PP
The \fBEVP_PKEY_sign()\fR function performs a public key signing operation
using \fIctx\fR. The data to be signed is specified using the \fItbs\fR and
\&\fItbslen\fR parameters. If \fIsig\fR is NULL then the maximum size of the output
buffer is written to the \fIsiglen\fR parameter. If \fIsig\fR is not NULL then
before the call the \fIsiglen\fR parameter should contain the length of the
\&\fIsig\fR buffer, if the call is successful the signature is written to
\&\fIsig\fR and the amount of data written to \fIsiglen\fR.
.SH NOTES
.IX Header "NOTES"
\&\fBEVP_PKEY_sign()\fR does not hash the data to be signed, and therefore is
normally used to sign digests. For signing arbitrary messages, see the
\&\fBEVP_DigestSignInit\fR\|(3) and
\&\fBEVP_SignInit\fR\|(3) signing interfaces instead.
.PP
After the call to \fBEVP_PKEY_sign_init()\fR algorithm specific control
operations can be performed to set any appropriate parameters for the
operation (see \fBEVP_PKEY_CTX_ctrl\fR\|(3)).
.PP
The function \fBEVP_PKEY_sign()\fR can be called more than once on the same
context if several operations are performed using the same parameters.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_sign_init()\fR and \fBEVP_PKEY_sign()\fR return 1 for success and 0
or a negative value for failure. In particular a return value of \-2
indicates the operation is not supported by the public key algorithm.
.SH EXAMPLES
.IX Header "EXAMPLES"
Sign data using RSA with PKCS#1 padding and SHA256 digest:
.PP
.Vb 2
\& #include <openssl/evp.h>
\& #include <openssl/rsa.h>
\&
\& EVP_PKEY_CTX *ctx;
\& /* md is a SHA\-256 digest in this example. */
\& unsigned char *md, *sig;
\& size_t mdlen = 32, siglen;
\& EVP_PKEY *signing_key;
\&
\& /*
\&  * NB: assumes signing_key and md are set up before the next
\&  * step. signing_key must be an RSA private key and md must
\&  * point to the SHA\-256 digest to be signed.
\&  */
\& ctx = EVP_PKEY_CTX_new(signing_key, NULL /* no engine */);
\& if (!ctx)
\&     /* Error occurred */
\& if (EVP_PKEY_sign_init(ctx) <= 0)
\&     /* Error */
\& if (EVP_PKEY_CTX_set_rsa_padding(ctx, RSA_PKCS1_PADDING) <= 0)
\&     /* Error */
\& if (EVP_PKEY_CTX_set_signature_md(ctx, EVP_sha256()) <= 0)
\&     /* Error */
\&
\& /* Determine buffer length */
\& if (EVP_PKEY_sign(ctx, NULL, &siglen, md, mdlen) <= 0)
\&     /* Error */
\&
\& sig = OPENSSL_malloc(siglen);
\&
\& if (!sig)
\&     /* malloc failure */
\&
\& if (EVP_PKEY_sign(ctx, sig, &siglen, md, mdlen) <= 0)
\&     /* Error */
\&
\& /* Signature is siglen bytes written to buffer sig */
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new\fR\|(3),
\&\fBEVP_PKEY_CTX_ctrl\fR\|(3),
\&\fBEVP_PKEY_encrypt\fR\|(3),
\&\fBEVP_PKEY_decrypt\fR\|(3),
\&\fBEVP_PKEY_verify\fR\|(3),
\&\fBEVP_PKEY_verify_recover\fR\|(3),
\&\fBEVP_PKEY_derive\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBEVP_PKEY_sign_init()\fR and \fBEVP_PKEY_sign()\fR functions were added in
OpenSSL 1.0.0.
.PP
The \fBEVP_PKEY_sign_init_ex()\fR function was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
