.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_CMP_VALIDATE_MSG 3ossl"
.TH OSSL_CMP_VALIDATE_MSG 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_CMP_validate_msg,
OSSL_CMP_validate_cert_path
\&\- functions for verifying CMP message protection
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 4
\& #include <openssl/cmp.h>
\& int OSSL_CMP_validate_msg(OSSL_CMP_CTX *ctx, OSSL_CMP_MSG *msg);
\& int OSSL_CMP_validate_cert_path(const OSSL_CMP_CTX *ctx,
\&                                 X509_STORE *trusted_store, X509 *cert);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This is the API for validating the protection of CMP messages,
which includes validating CMP message sender certificates and their paths
while optionally checking the revocation status of the certificates(s).
.PP
\&\fBOSSL_CMP_validate_msg()\fR validates the protection of the given \fImsg\fR,
which must be signature-based or using password-based MAC (PBM).
In the former case a suitable trust anchor must be given in the CMP context
\&\fIctx\fR, and in the latter case the matching secret must have been set there
using \fBOSSL_CMP_CTX_set1_secretValue\fR\|(3).
.PP
In case of signature algorithm, the certificate to use for the signature check
is preferably the one provided by a call to \fBOSSL_CMP_CTX_set1_srvCert\fR\|(3).
If no such sender cert has been pinned then candidate sender certificates are
taken from the list of certificates received in the \fImsg\fR extraCerts, then any
certificates provided before via \fBOSSL_CMP_CTX_set1_untrusted\fR\|(3), and
then all trusted certificates provided via \fBOSSL_CMP_CTX_set0_trustedStore\fR\|(3),
where a candidate is acceptable only if has not expired, its subject DN matches
the \fImsg\fR sender DN (as far as present), and its subject key identifier
is present and matches the senderKID (as far as the latter present).
Each acceptable cert is tried in the given order to see if the message
signature check succeeds and the cert and its path can be verified
using any trust store set via \fBOSSL_CMP_CTX_set0_trustedStore\fR\|(3).
.PP
If the option OSSL_CMP_OPT_PERMIT_TA_IN_EXTRACERTS_FOR_IR was set by calling
\&\fBOSSL_CMP_CTX_set_option\fR\|(3), for an Initialization Response (IP) message
any self-issued certificate from the \fImsg\fR extraCerts field may also be used
as trust anchor for the path verification of an acceptable cert if it can be
used also to validate the issued certificate returned in the IP message. This is
according to TS 33.310 [Network Domain Security (NDS); Authentication Framework
(AF)] document specified by the The 3rd Generation Partnership Project (3GPP).
.PP
Any cert that has been found as described above is cached and tried first when
validating the signatures of subsequent messages in the same transaction.
.PP
\&\fBOSSL_CMP_validate_cert_path()\fR attempts to validate the given certificate and its
path using the given store of trusted certs (possibly including CRLs and a cert
verification callback) and non-trusted intermediate certs from the \fIctx\fR.
.SH NOTES
.IX Header "NOTES"
CMP is defined in RFC 4210 (and CRMF in RFC 4211).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_CMP_validate_msg()\fR and \fBOSSL_CMP_validate_cert_path()\fR
return 1 on success, 0 on error or validation failed.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOSSL_CMP_CTX_new\fR\|(3), \fBOSSL_CMP_exec_certreq\fR\|(3),
\&\fBOSSL_CMP_CTX_set1_secretValue\fR\|(3), \fBOSSL_CMP_CTX_set1_srvCert\fR\|(3),
\&\fBOSSL_CMP_CTX_set1_untrusted\fR\|(3), \fBOSSL_CMP_CTX_set0_trustedStore\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The OpenSSL CMP support was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2007\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
