.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DSA_DO_SIGN 3ossl"
.TH DSA_DO_SIGN 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
DSA_do_sign, DSA_do_verify \- raw DSA signature operations
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/dsa.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& DSA_SIG *DSA_do_sign(const unsigned char *dgst, int dlen, DSA *dsa);
\&
\& int DSA_do_verify(const unsigned char *dgst, int dgst_len,
\&                   DSA_SIG *sig, DSA *dsa);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated.
Applications should instead use \fBEVP_PKEY_sign_init\fR\|(3), \fBEVP_PKEY_sign\fR\|(3),
\&\fBEVP_PKEY_verify_init\fR\|(3) and \fBEVP_PKEY_verify\fR\|(3).
.PP
\&\fBDSA_do_sign()\fR computes a digital signature on the \fBlen\fR byte message
digest \fBdgst\fR using the private key \fBdsa\fR and returns it in a
newly allocated \fBDSA_SIG\fR structure.
.PP
\&\fBDSA_sign_setup\fR\|(3) may be used to precompute part
of the signing operation in case signature generation is
time-critical.
.PP
\&\fBDSA_do_verify()\fR verifies that the signature \fBsig\fR matches a given
message digest \fBdgst\fR of size \fBlen\fR.  \fBdsa\fR is the signer's public
key.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBDSA_do_sign()\fR returns the signature, NULL on error.  \fBDSA_do_verify()\fR
returns 1 for a valid signature, 0 for an incorrect signature and \-1
on error. The error codes can be obtained by
\&\fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBDSA_new\fR\|(3), \fBERR_get_error\fR\|(3), \fBRAND_bytes\fR\|(3),
\&\fBDSA_SIG_new\fR\|(3),
\&\fBDSA_sign\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
All of these functions were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
