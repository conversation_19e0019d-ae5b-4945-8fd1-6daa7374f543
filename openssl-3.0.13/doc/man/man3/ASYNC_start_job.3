.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ASYNC_START_JOB 3ossl"
.TH ASYNC_START_JOB 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ASYNC_get_wait_ctx,
ASYNC_init_thread, ASYNC_cleanup_thread, ASYNC_start_job, ASYNC_pause_job,
ASYNC_get_current_job, ASYNC_block_pause, ASYNC_unblock_pause, ASYNC_is_capable
\&\- asynchronous job management functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/async.h>
\&
\& int ASYNC_init_thread(size_t max_size, size_t init_size);
\& void ASYNC_cleanup_thread(void);
\&
\& int ASYNC_start_job(ASYNC_JOB **job, ASYNC_WAIT_CTX *ctx, int *ret,
\&                     int (*func)(void *), void *args, size_t size);
\& int ASYNC_pause_job(void);
\&
\& ASYNC_JOB *ASYNC_get_current_job(void);
\& ASYNC_WAIT_CTX *ASYNC_get_wait_ctx(ASYNC_JOB *job);
\& void ASYNC_block_pause(void);
\& void ASYNC_unblock_pause(void);
\&
\& int ASYNC_is_capable(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
OpenSSL implements asynchronous capabilities through an \fBASYNC_JOB\fR. This
represents code that can be started and executes until some event occurs. At
that point the code can be paused and control returns to user code until some
subsequent event indicates that the job can be resumed.
.PP
The creation of an \fBASYNC_JOB\fR is a relatively expensive operation. Therefore,
for efficiency reasons, jobs can be created up front and reused many times. They
are held in a pool until they are needed, at which point they are removed from
the pool, used, and then returned to the pool when the job completes. If the
user application is multi-threaded, then \fBASYNC_init_thread()\fR may be called for
each thread that will initiate asynchronous jobs. Before
user code exits per-thread resources need to be cleaned up. This will normally
occur automatically (see \fBOPENSSL_init_crypto\fR\|(3)) but may be explicitly
initiated by using \fBASYNC_cleanup_thread()\fR. No asynchronous jobs must be
outstanding for the thread when \fBASYNC_cleanup_thread()\fR is called. Failing to
ensure this will result in memory leaks.
.PP
The \fImax_size\fR argument limits the number of \fBASYNC_JOB\fRs that will be held in
the pool. If \fImax_size\fR is set to 0 then no upper limit is set. When an
\&\fBASYNC_JOB\fR is needed but there are none available in the pool already then one
will be automatically created, as long as the total of \fBASYNC_JOB\fRs managed by
the pool does not exceed \fImax_size\fR. When the pool is first initialised
\&\fIinit_size\fR \fBASYNC_JOB\fRs will be created immediately. If \fBASYNC_init_thread()\fR
is not called before the pool is first used then it will be called automatically
with a \fImax_size\fR of 0 (no upper limit) and an \fIinit_size\fR of 0 (no
\&\fBASYNC_JOB\fRs created up front).
.PP
An asynchronous job is started by calling the \fBASYNC_start_job()\fR function.
Initially \fI*job\fR should be NULL. \fIctx\fR should point to an \fBASYNC_WAIT_CTX\fR
object created through the \fBASYNC_WAIT_CTX_new\fR\|(3) function. \fIret\fR should
point to a location where the return value of the asynchronous function should
be stored on completion of the job. \fIfunc\fR represents the function that should
be started asynchronously. The data pointed to by \fIargs\fR and of size \fIsize\fR
will be copied and then passed as an argument to \fIfunc\fR when the job starts.
ASYNC_start_job will return one of the following values:
.IP \fBASYNC_ERR\fR 4
.IX Item "ASYNC_ERR"
An error occurred trying to start the job. Check the OpenSSL error queue (e.g.
see \fBERR_print_errors\fR\|(3)) for more details.
.IP \fBASYNC_NO_JOBS\fR 4
.IX Item "ASYNC_NO_JOBS"
There are no jobs currently available in the pool. This call can be retried
again at a later time.
.IP \fBASYNC_PAUSE\fR 4
.IX Item "ASYNC_PAUSE"
The job was successfully started but was "paused" before it completed (see
\&\fBASYNC_pause_job()\fR below). A handle to the job is placed in \fI*job\fR. Other work
can be performed (if desired) and the job restarted at a later time. To restart
a job call \fBASYNC_start_job()\fR again passing the job handle in \fI*job\fR. The
\&\fIfunc\fR, \fIargs\fR and \fIsize\fR parameters will be ignored when restarting a job.
When restarting a job \fBASYNC_start_job()\fR \fBmust\fR be called from the same thread
that the job was originally started from.
.IP \fBASYNC_FINISH\fR 4
.IX Item "ASYNC_FINISH"
The job completed. \fI*job\fR will be NULL and the return value from \fIfunc\fR will
be placed in \fI*ret\fR.
.PP
At any one time there can be a maximum of one job actively running per thread
(you can have many that are paused). \fBASYNC_get_current_job()\fR can be used to get
a pointer to the currently executing \fBASYNC_JOB\fR. If no job is currently
executing then this will return NULL.
.PP
If executing within the context of a job (i.e. having been called directly or
indirectly by the function "func" passed as an argument to \fBASYNC_start_job()\fR)
then \fBASYNC_pause_job()\fR will immediately return control to the calling
application with \fBASYNC_PAUSE\fR returned from the \fBASYNC_start_job()\fR call. A
subsequent call to ASYNC_start_job passing in the relevant \fBASYNC_JOB\fR in the
\&\fI*job\fR parameter will resume execution from the \fBASYNC_pause_job()\fR call. If
\&\fBASYNC_pause_job()\fR is called whilst not within the context of a job then no
action is taken and \fBASYNC_pause_job()\fR returns immediately.
.PP
\&\fBASYNC_get_wait_ctx()\fR can be used to get a pointer to the \fBASYNC_WAIT_CTX\fR
for the \fIjob\fR. \fBASYNC_WAIT_CTX\fRs contain two different ways to notify
applications that a job is ready to be resumed. One is a "wait" file
descriptor, and the other is a "callback" mechanism.
.PP
The "wait" file descriptor associated with \fBASYNC_WAIT_CTX\fR is used for
applications to wait for the file descriptor to be ready for "read" using a
system function call such as select or poll (being ready for "read" indicates
that the job should be resumed). If no file descriptor is made available then
an application will have to periodically "poll" the job by attempting to restart
it to see if it is ready to continue.
.PP
\&\fBASYNC_WAIT_CTX\fRs also have a "callback" mechanism to notify applications. The
callback is set by an application, and it will be automatically called when an
engine completes a cryptography operation, so that the application can resume
the paused work flow without polling. An engine could be written to look whether
the callback has been set. If it has then it would use the callback mechanism
in preference to the file descriptor notifications. If a callback is not set
then the engine may use file descriptor based notifications. Please note that
not all engines may support the callback mechanism, so the callback may not be
used even if it has been set. See \fBASYNC_WAIT_CTX_new()\fR for more details.
.PP
The \fBASYNC_block_pause()\fR function will prevent the currently active job from
pausing. The block will remain in place until a subsequent call to
\&\fBASYNC_unblock_pause()\fR. These functions can be nested, e.g. if you call
\&\fBASYNC_block_pause()\fR twice then you must call \fBASYNC_unblock_pause()\fR twice in
order to re-enable pausing. If these functions are called while there is no
currently active job then they have no effect. This functionality can be useful
to avoid deadlock scenarios. For example during the execution of an \fBASYNC_JOB\fR
an application acquires a lock. It then calls some cryptographic function which
invokes \fBASYNC_pause_job()\fR. This returns control back to the code that created
the \fBASYNC_JOB\fR. If that code then attempts to acquire the same lock before
resuming the original job then a deadlock can occur. By calling
\&\fBASYNC_block_pause()\fR immediately after acquiring the lock and
\&\fBASYNC_unblock_pause()\fR immediately before releasing it then this situation cannot
occur.
.PP
Some platforms cannot support async operations. The \fBASYNC_is_capable()\fR function
can be used to detect whether the current platform is async capable or not.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
ASYNC_init_thread returns 1 on success or 0 otherwise.
.PP
ASYNC_start_job returns one of \fBASYNC_ERR\fR, \fBASYNC_NO_JOBS\fR, \fBASYNC_PAUSE\fR or
\&\fBASYNC_FINISH\fR as described above.
.PP
ASYNC_pause_job returns 0 if an error occurred or 1 on success. If called when
not within the context of an \fBASYNC_JOB\fR then this is counted as success so 1
is returned.
.PP
ASYNC_get_current_job returns a pointer to the currently executing \fBASYNC_JOB\fR
or NULL if not within the context of a job.
.PP
\&\fBASYNC_get_wait_ctx()\fR returns a pointer to the \fBASYNC_WAIT_CTX\fR for the job.
.PP
\&\fBASYNC_is_capable()\fR returns 1 if the current platform is async capable or 0
otherwise.
.SH NOTES
.IX Header "NOTES"
On Windows platforms the \fI<openssl/async.h>\fR header is dependent on some
of the types customarily made available by including \fI<windows.h>\fR. The
application developer is likely to require control over when the latter
is included, commonly as one of the first included headers. Therefore,
it is defined as an application developer's responsibility to include
\&\fI<windows.h>\fR prior to \fI<openssl/async.h>\fR.
.SH EXAMPLES
.IX Header "EXAMPLES"
The following example demonstrates how to use most of the core async APIs:
.PP
.Vb 7
\& #ifdef _WIN32
\& # include <windows.h>
\& #endif
\& #include <stdio.h>
\& #include <unistd.h>
\& #include <openssl/async.h>
\& #include <openssl/crypto.h>
\&
\& int unique = 0;
\&
\& void cleanup(ASYNC_WAIT_CTX *ctx, const void *key, OSSL_ASYNC_FD r, void *vw)
\& {
\&     OSSL_ASYNC_FD *w = (OSSL_ASYNC_FD *)vw;
\&
\&     close(r);
\&     close(*w);
\&     OPENSSL_free(w);
\& }
\&
\& int jobfunc(void *arg)
\& {
\&     ASYNC_JOB *currjob;
\&     unsigned char *msg;
\&     int pipefds[2] = {0, 0};
\&     OSSL_ASYNC_FD *wptr;
\&     char buf = \*(AqX\*(Aq;
\&
\&     currjob = ASYNC_get_current_job();
\&     if (currjob != NULL) {
\&         printf("Executing within a job\en");
\&     } else {
\&         printf("Not executing within a job \- should not happen\en");
\&         return 0;
\&     }
\&
\&     msg = (unsigned char *)arg;
\&     printf("Passed in message is: %s\en", msg);
\&
\&     if (pipe(pipefds) != 0) {
\&         printf("Failed to create pipe\en");
\&         return 0;
\&     }
\&     wptr = OPENSSL_malloc(sizeof(OSSL_ASYNC_FD));
\&     if (wptr == NULL) {
\&         printf("Failed to malloc\en");
\&         return 0;
\&     }
\&     *wptr = pipefds[1];
\&     ASYNC_WAIT_CTX_set_wait_fd(ASYNC_get_wait_ctx(currjob), &unique,
\&                                pipefds[0], wptr, cleanup);
\&
\&     /*
\&      * Normally some external event would cause this to happen at some
\&      * later point \- but we do it here for demo purposes, i.e.
\&      * immediately signalling that the job is ready to be woken up after
\&      * we return to main via ASYNC_pause_job().
\&      */
\&     write(pipefds[1], &buf, 1);
\&
\&     /* Return control back to main */
\&     ASYNC_pause_job();
\&
\&     /* Clear the wake signal */
\&     read(pipefds[0], &buf, 1);
\&
\&     printf ("Resumed the job after a pause\en");
\&
\&     return 1;
\& }
\&
\& int main(void)
\& {
\&     ASYNC_JOB *job = NULL;
\&     ASYNC_WAIT_CTX *ctx = NULL;
\&     int ret;
\&     OSSL_ASYNC_FD waitfd;
\&     fd_set waitfdset;
\&     size_t numfds;
\&     unsigned char msg[13] = "Hello world!";
\&
\&     printf("Starting...\en");
\&
\&     ctx = ASYNC_WAIT_CTX_new();
\&     if (ctx == NULL) {
\&         printf("Failed to create ASYNC_WAIT_CTX\en");
\&         abort();
\&     }
\&
\&     for (;;) {
\&         switch (ASYNC_start_job(&job, ctx, &ret, jobfunc, msg, sizeof(msg))) {
\&         case ASYNC_ERR:
\&         case ASYNC_NO_JOBS:
\&             printf("An error occurred\en");
\&             goto end;
\&         case ASYNC_PAUSE:
\&             printf("Job was paused\en");
\&             break;
\&         case ASYNC_FINISH:
\&             printf("Job finished with return value %d\en", ret);
\&             goto end;
\&         }
\&
\&         /* Wait for the job to be woken */
\&         printf("Waiting for the job to be woken up\en");
\&
\&         if (!ASYNC_WAIT_CTX_get_all_fds(ctx, NULL, &numfds)
\&                 || numfds > 1) {
\&             printf("Unexpected number of fds\en");
\&             abort();
\&         }
\&         ASYNC_WAIT_CTX_get_all_fds(ctx, &waitfd, &numfds);
\&         FD_ZERO(&waitfdset);
\&         FD_SET(waitfd, &waitfdset);
\&         select(waitfd + 1, &waitfdset, NULL, NULL, NULL);
\&     }
\&
\& end:
\&     ASYNC_WAIT_CTX_free(ctx);
\&     printf("Finishing\en");
\&
\&     return 0;
\& }
.Ve
.PP
The expected output from executing the above example program is:
.PP
.Vb 8
\& Starting...
\& Executing within a job
\& Passed in message is: Hello world!
\& Job was paused
\& Waiting for the job to be woken up
\& Resumed the job after a pause
\& Job finished with return value 1
\& Finishing
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7), \fBERR_print_errors\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
ASYNC_init_thread, ASYNC_cleanup_thread,
ASYNC_start_job, ASYNC_pause_job, ASYNC_get_current_job, \fBASYNC_get_wait_ctx()\fR,
\&\fBASYNC_block_pause()\fR, \fBASYNC_unblock_pause()\fR and \fBASYNC_is_capable()\fR were first
added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
