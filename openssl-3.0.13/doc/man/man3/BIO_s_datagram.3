.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_S_DATAGRAM 3ossl"
.TH BIO_S_DATAGRAM 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_s_datagram, BIO_new_dgram,
BIO_ctrl_dgram_connect,
BIO_ctrl_set_connected,
BIO_dgram_recv_timedout,
BIO_dgram_send_timedout,
BIO_dgram_get_peer,
BIO_dgram_set_peer,
BIO_dgram_get_mtu_overhead \- Network BIO with datagram semantics
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& BIO_METHOD *BIO_s_datagram(void);
\& BIO *BIO_new_dgram(int fd, int close_flag);
\&
\& int BIO_ctrl_dgram_connect(BIO *bio, const BIO_ADDR *peer);
\& int BIO_ctrl_set_connected(BIO *bio, const BIO_ADDR *peer);
\& int BIO_dgram_recv_timedout(BIO *bio);
\& int BIO_dgram_send_timedout(BIO *bio);
\& int BIO_dgram_get_peer(BIO *bio, BIO_ADDR *peer);
\& int BIO_dgram_set_peer(BIO *bio, const BIO_ADDR *peer);
\& int BIO_dgram_get_mtu_overhead(BIO *bio);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_s_datagram()\fR is a BIO implementation designed for use with network sockets
which provide datagram semantics, such as UDP sockets. It is suitable for use
with DTLSv1.
.PP
Because \fBBIO_s_datagram()\fR has datagram semantics, a single \fBBIO_write()\fR call sends
a single datagram and a single \fBBIO_read()\fR call receives a single datagram. If
the size of the buffer passed to \fBBIO_read()\fR is inadequate, the datagram is
silently truncated.
.PP
When using \fBBIO_s_datagram()\fR, it is important to note that:
.IP \(bu 4
This BIO can be used with either a connected or unconnected network socket. A
connected socket is a network socket which has had \fBBIO_connect\fR\|(3) or a
similar OS-specific function called on it. Such a socket can only receive
datagrams from the specified peer. Any other socket is an unconnected socket and
can receive datagrams from any host.
.IP \(bu 4
Despite their naming,
neither \fBBIO_ctrl_dgram_connect()\fR nor \fBBIO_ctrl_set_connected()\fR cause a socket
to become connected. These controls are provided to indicate to the BIO how
the underlying socket is configured and how it is to be used; see below.
.IP \(bu 4
Use of \fBBIO_s_datagram()\fR with an unconnected network socket is hazardous hecause
any successful call to \fBBIO_read()\fR results in the peer address used for any
subsequent call to \fBBIO_write()\fR being set to the source address of the datagram
received by that call to \fBBIO_read()\fR. Thus, unless the caller calls
\&\fBBIO_dgram_set_peer()\fR immediately prior to every call to \fBBIO_write()\fR, or never
calls \fBBIO_read()\fR, any host on the network may cause future datagrams written to
be redirected to that host. Therefore, it is recommended that users use
\&\fBBIO_s_dgram()\fR only with a connected socket. An exception is where
\&\fBDTLSv1_listen\fR\|(3) must be used; see \fBDTLSv1_listen\fR\|(3) for further
discussion.
.PP
Various controls are available for configuring the \fBBIO_s_datagram()\fR using
\&\fBBIO_ctrl\fR\|(3):
.IP "BIO_ctrl_dgram_connect (BIO_CTRL_DGRAM_CONNECT)" 4
.IX Item "BIO_ctrl_dgram_connect (BIO_CTRL_DGRAM_CONNECT)"
This is equivalent to calling \fBBIO_dgram_set_peer\fR\|(3).
.Sp
Despite its name, this function does not cause the underlying socket to become
connected.
.IP "BIO_ctrl_set_connected (BIO_CTRL_SET_CONNECTED)" 4
.IX Item "BIO_ctrl_set_connected (BIO_CTRL_SET_CONNECTED)"
This informs the \fBBIO_s_datagram()\fR whether the underlying socket has been
connected, and therefore how the \fBBIO_s_datagram()\fR should attempt to use the
socket.
.Sp
If the \fIpeer\fR argument is non-NULL, \fBBIO_s_datagram()\fR assumes that the
underlying socket has been connected and will attempt to use the socket using OS
APIs which do not specify peer addresses (for example, \fBsend\fR\|(3) and \fBrecv\fR\|(3) or
similar). The \fIpeer\fR argument should specify the peer address to which the socket
is connected.
.Sp
If the \fIpeer\fR argument is NULL, \fBBIO_s_datagram()\fR assumes that the underlying
socket is not connected and will attempt to use the socket using an OS APIs
which specify peer addresses (for example, \fBsendto\fR\|(3) and \fBrecvfrom\fR\|(3)).
.IP "BIO_dgram_get_peer (BIO_CTRL_DGRAM_GET_PEER)" 4
.IX Item "BIO_dgram_get_peer (BIO_CTRL_DGRAM_GET_PEER)"
This outputs a \fBBIO_ADDR\fR which specifies one of the following values,
whichever happened most recently:
.RS 4
.IP \(bu 4
The peer address last passed to \fBBIO_dgram_set_peer()\fR, \fBBIO_ctrl_dgram_connect()\fR
or \fBBIO_ctrl_set_connected()\fR.
.IP \(bu 4
The peer address of the datagram last received by a call to \fBBIO_read()\fR.
.RE
.RS 4
.RE
.IP "BIO_dgram_set_peer (BIO_CTRL_DGRAM_SET_PEER)" 4
.IX Item "BIO_dgram_set_peer (BIO_CTRL_DGRAM_SET_PEER)"
Sets the peer address to be used for subsequent writes to this BIO.
.Sp
Warning: When used with an unconnected network socket, the value set may be
modified by future calls to \fBBIO_read\fR\|(3), making use of \fBBIO_s_datagram()\fR
hazardous when used with unconnected network sockets; see above.
.IP "BIO_dgram_recv_timeout (BIO_CTRL_DGRAM_GET_RECV_TIMER_EXP)" 4
.IX Item "BIO_dgram_recv_timeout (BIO_CTRL_DGRAM_GET_RECV_TIMER_EXP)"
Returns 1 if the last I/O operation performed on the BIO (for example, via a
call to \fBBIO_read\fR\|(3)) may have been caused by a receive timeout.
.IP "BIO_dgram_send_timedout (BIO_CTRL_DGRAM_GET_SEND_TIMER_EXP)" 4
.IX Item "BIO_dgram_send_timedout (BIO_CTRL_DGRAM_GET_SEND_TIMER_EXP)"
Returns 1 if the last I/O operation performed on the BIO (for example, via a
call to \fBBIO_write\fR\|(3)) may have been caused by a send timeout.
.IP "BIO_dgram_get_mtu_overhead (BIO_CTRL_DGRAM_GET_MTU_OVERHEAD)" 4
.IX Item "BIO_dgram_get_mtu_overhead (BIO_CTRL_DGRAM_GET_MTU_OVERHEAD)"
Returns a quantity in bytes which is a rough estimate of the number of bytes of
overhead which should typically be added to a datagram payload size in order to
estimate the final size of the Layer 3 (e.g. IP) packet which will contain the
datagram. In most cases, the maximum datagram payload size which can be
transmitted can be determined by determining the link MTU in bytes and
subtracting the value returned by this call.
.Sp
The value returned by this call depends on the network layer protocol being
used.
.Sp
The value returned is not fully reliable because datagram overheads can be
higher in atypical network configurations, for example where IPv6 extension
headers or IPv4 options are used.
.IP BIO_CTRL_DGRAM_SET_DONT_FRAG 4
.IX Item "BIO_CTRL_DGRAM_SET_DONT_FRAG"
If \fInum\fR is nonzero, configures the underlying network socket to enable Don't
Fragment mode, in which datagrams will be set with the IP Don't Fragment (DF)
bit set. If \fInum\fR is zero, Don't Fragment mode is disabled.
.IP BIO_CTRL_DGRAM_QUERY_MTU 4
.IX Item "BIO_CTRL_DGRAM_QUERY_MTU"
Queries the OS for its assessment of the Path MTU for the destination to which
the underlying network socket, and returns that Path MTU in bytes. This control
can only be used with a connected socket.
.Sp
This is not supported on all platforms and depends on OS support being
available. Returns 0 on failure.
.IP BIO_CTRL_DGRAM_MTU_DISCOVER 4
.IX Item "BIO_CTRL_DGRAM_MTU_DISCOVER"
This control requests that Path MTU discovery be enabled on the underlying
network socket.
.IP BIO_CTRL_DGRAM_GET_FALLBACK_MTU 4
.IX Item "BIO_CTRL_DGRAM_GET_FALLBACK_MTU"
Returns the estimated minimum size of datagram payload which should always be
supported on the BIO. This size is determined by the minimum MTU required to be
supported by the applicable underlying network layer. Use of datagrams of this
size may lead to suboptimal performance, but should be routable in all
circumstances. The value returned is the datagram payload size in bytes and does
not include the size of layer 3 or layer 4 protocol headers.
.IP BIO_CTRL_DGRAM_MTU_EXCEEDED 4
.IX Item "BIO_CTRL_DGRAM_MTU_EXCEEDED"
Returns 1 if the last attempted write to the BIO failed due to the size of the
attempted write exceeding the applicable MTU.
.IP BIO_CTRL_DGRAM_SET_NEXT_TIMEOUT 4
.IX Item "BIO_CTRL_DGRAM_SET_NEXT_TIMEOUT"
Accepts a pointer to a \fBstruct timeval\fR. If the time specified is zero,
disables receive timeouts. Otherwise, configures the specified time interval as
the receive timeout for the socket for the purposes of future \fBBIO_read\fR\|(3)
calls.
.IP BIO_CTRL_DGRAM_SET_PEEK_MODE 4
.IX Item "BIO_CTRL_DGRAM_SET_PEEK_MODE"
If \fBnum\fR is nonzero, enables peek mode; otherwise, disables peek mode. Where
peek mode is enabled, calls to \fBBIO_read\fR\|(3) read datagrams from the underlying
network socket in peek mode, meaning that a future call to \fBBIO_read\fR\|(3) will
yield the same datagram until peek mode is disabled.
.PP
\&\fBBIO_new_dgram()\fR is a helper function which instantiates a \fBBIO_s_datagram()\fR and
sets the BIO to use the socket given in \fIfd\fR by calling \fBBIO_set_fd()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_s_datagram()\fR returns a BIO method.
.PP
\&\fBBIO_new_dgram()\fR returns a BIO on success and NULL on failure.
.PP
\&\fBBIO_ctrl_dgram_connect()\fR, \fBBIO_ctrl_set_connected()\fR,
\&\fBBIO_dgram_get_peer()\fR, \fBBIO_dgram_set_peer()\fR return 1 on success and 0 on failure.
.PP
\&\fBBIO_dgram_recv_timedout()\fR and \fBBIO_dgram_send_timedout()\fR return 0 or 1 depending
on the circumstance; see discussion above.
.PP
\&\fBBIO_dgram_get_mtu_overhead()\fR returns a value in bytes.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBDTLSv1_listen\fR\|(3), \fBbio\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2022\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
