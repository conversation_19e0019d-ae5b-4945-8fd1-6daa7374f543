.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS12_NEWPASS 3ossl"
.TH PKCS12_NEWPASS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS12_newpass \- change the password of a PKCS12 structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pkcs12.h>
\&
\& int PKCS12_newpass(PKCS12 *p12, const char *oldpass, const char *newpass);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPKCS12_newpass()\fR changes the password of a PKCS12 structure.
.PP
\&\fBp12\fR is a pointer to a PKCS12 structure. \fBoldpass\fR is the existing password
and \fBnewpass\fR is the new password.
.PP
Each of \fBoldpass\fR and \fBnewpass\fR is independently interpreted as a string in
the UTF\-8 encoding. If it is not valid UTF\-8, it is assumed to be ISO8859\-1
instead.
.PP
In particular, this means that passwords in the locale character set
(or code page on Windows) must potentially be converted to UTF\-8 before
use. This may include passwords from local text files, or input from
the terminal or command line. Refer to the documentation of
\&\fBUI_OpenSSL\fR\|(3), for example.
.PP
If the PKCS#12 structure does not have a password, then you must use the empty
string "" for \fBoldpass\fR. Using NULL for \fBoldpass\fR will result in a
\&\fBPKCS12_newpass()\fR failure.
.PP
If the wrong password is used for \fBoldpass\fR then the function will fail,
with a MAC verification error. In rare cases the PKCS12 structure does not
contain a MAC: in this case it will usually fail with a decryption padding
error.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPKCS12_newpass()\fR returns 1 on success or 0 on failure. Applications can
retrieve the most recent error from \fBPKCS12_newpass()\fR with \fBERR_get_error()\fR.
.SH EXAMPLES
.IX Header "EXAMPLES"
This example loads a PKCS#12 file, changes its password and writes out
the result to a new file.
.PP
.Vb 5
\& #include <stdio.h>
\& #include <stdlib.h>
\& #include <openssl/pem.h>
\& #include <openssl/err.h>
\& #include <openssl/pkcs12.h>
\&
\& int main(int argc, char **argv)
\& {
\&     FILE *fp;
\&     PKCS12 *p12;
\&
\&     if (argc != 5) {
\&         fprintf(stderr, "Usage: pkread p12file password newpass opfile\en");
\&         return 1;
\&     }
\&     if ((fp = fopen(argv[1], "rb")) == NULL) {
\&         fprintf(stderr, "Error opening file %s\en", argv[1]);
\&         return 1;
\&     }
\&     p12 = d2i_PKCS12_fp(fp, NULL);
\&     fclose(fp);
\&     if (p12 == NULL) {
\&         fprintf(stderr, "Error reading PKCS#12 file\en");
\&         ERR_print_errors_fp(stderr);
\&         return 1;
\&     }
\&     if (PKCS12_newpass(p12, argv[2], argv[3]) == 0) {
\&         fprintf(stderr, "Error changing password\en");
\&         ERR_print_errors_fp(stderr);
\&         PKCS12_free(p12);
\&         return 1;
\&     }
\&     if ((fp = fopen(argv[4], "wb")) == NULL) {
\&         fprintf(stderr, "Error opening file %s\en", argv[4]);
\&         PKCS12_free(p12);
\&         return 1;
\&     }
\&     i2d_PKCS12_fp(fp, p12);
\&     PKCS12_free(p12);
\&     fclose(fp);
\&     return 0;
\& }
.Ve
.SH BUGS
.IX Header "BUGS"
The password format is a NULL terminated ASCII string which is converted to
Unicode form internally. As a result some passwords cannot be supplied to
this function.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBPKCS12_create\fR\|(3), \fBERR_get_error\fR\|(3),
\&\fBpassphrase\-encoding\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
