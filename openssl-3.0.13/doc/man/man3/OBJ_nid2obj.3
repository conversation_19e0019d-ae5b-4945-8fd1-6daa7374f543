.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OBJ_NID2OBJ 3ossl"
.TH OBJ_NID2OBJ 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
i2t_ASN1_OBJECT,
OBJ_length, OBJ_get0_data, OBJ_nid2obj, OBJ_nid2ln,
OBJ_nid2sn, OBJ_obj2nid, OBJ_txt2nid, OBJ_ln2nid, OBJ_sn2nid, OBJ_cmp,
OBJ_dup, OBJ_txt2obj, OBJ_obj2txt, OBJ_create, OBJ_cleanup, OBJ_add_sigid
\&\- ASN1 object utility functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/objects.h>
\&
\& ASN1_OBJECT *OBJ_nid2obj(int n);
\& const char *OBJ_nid2ln(int n);
\& const char *OBJ_nid2sn(int n);
\&
\& int OBJ_obj2nid(const ASN1_OBJECT *o);
\& int OBJ_ln2nid(const char *ln);
\& int OBJ_sn2nid(const char *sn);
\&
\& int OBJ_txt2nid(const char *s);
\&
\& ASN1_OBJECT *OBJ_txt2obj(const char *s, int no_name);
\& int OBJ_obj2txt(char *buf, int buf_len, const ASN1_OBJECT *a, int no_name);
\&
\& int i2t_ASN1_OBJECT(char *buf, int buf_len, const ASN1_OBJECT *a);
\&
\& int OBJ_cmp(const ASN1_OBJECT *a, const ASN1_OBJECT *b);
\& ASN1_OBJECT *OBJ_dup(const ASN1_OBJECT *o);
\&
\& int OBJ_create(const char *oid, const char *sn, const char *ln);
\&
\& size_t OBJ_length(const ASN1_OBJECT *obj);
\& const unsigned char *OBJ_get0_data(const ASN1_OBJECT *obj);
\&
\& int OBJ_add_sigid(int signid, int dig_id, int pkey_id);
.Ve
.PP
The following function has been deprecated since OpenSSL 1.1.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& void OBJ_cleanup(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The ASN1 object utility functions process ASN1_OBJECT structures which are
a representation of the ASN1 OBJECT IDENTIFIER (OID) type.
For convenience, OIDs are usually represented in source code as numeric
identifiers, or \fBNID\fRs.  OpenSSL has an internal table of OIDs that
are generated when the library is built, and their corresponding NIDs
are available as defined constants.  For the functions below, application
code should treat all returned values \-\- OIDs, NIDs, or names \-\- as
constants.
.PP
\&\fBOBJ_nid2obj()\fR, \fBOBJ_nid2ln()\fR and \fBOBJ_nid2sn()\fR convert the NID \fIn\fR to
an ASN1_OBJECT structure, its long name and its short name respectively,
or \fBNULL\fR if an error occurred.
.PP
\&\fBOBJ_obj2nid()\fR, \fBOBJ_ln2nid()\fR, \fBOBJ_sn2nid()\fR return the corresponding NID
for the object \fIo\fR, the long name \fIln\fR or the short name \fIsn\fR respectively
or NID_undef if an error occurred.
.PP
\&\fBOBJ_txt2nid()\fR returns NID corresponding to text string \fIs\fR. \fIs\fR can be
a long name, a short name or the numerical representation of an object.
.PP
\&\fBOBJ_txt2obj()\fR converts the text string \fIs\fR into an ASN1_OBJECT structure.
If \fIno_name\fR is 0 then long names and short names will be interpreted
as well as numerical forms. If \fIno_name\fR is 1 only the numerical form
is acceptable.
.PP
\&\fBOBJ_obj2txt()\fR converts the \fBASN1_OBJECT\fR \fIa\fR into a textual representation.
Unless \fIbuf\fR is NULL,
the representation is written as a NUL-terminated string to \fIbuf\fR, where
at most \fIbuf_len\fR bytes are written, truncating the result if necessary.
In any case it returns the total string length, excluding the NUL character,
required for non-truncated representation, or \-1 on error.
If \fIno_name\fR is 0 then if the object has a long or short name
then that will be used, otherwise the numerical form will be used.
If \fIno_name\fR is 1 then the numerical form will always be used.
.PP
\&\fBi2t_ASN1_OBJECT()\fR is the same as \fBOBJ_obj2txt()\fR with the \fIno_name\fR set to zero.
.PP
\&\fBOBJ_cmp()\fR compares \fIa\fR to \fIb\fR. If the two are identical 0 is returned.
.PP
\&\fBOBJ_dup()\fR returns a copy of \fIo\fR.
.PP
\&\fBOBJ_create()\fR adds a new object to the internal table. \fIoid\fR is the
numerical form of the object, \fIsn\fR the short name and \fIln\fR the
long name. A new NID is returned for the created object in case of
success and NID_undef in case of failure.
.PP
\&\fBOBJ_length()\fR returns the size of the content octets of \fIobj\fR.
.PP
\&\fBOBJ_get0_data()\fR returns a pointer to the content octets of \fIobj\fR.
The returned pointer is an internal pointer which \fBmust not\fR be freed.
.PP
\&\fBOBJ_add_sigid()\fR creates a new composite "Signature Algorithm" that associates a
given NID with two other NIDs \- one representing the underlying signature
algorithm and the other representing a digest algorithm to be used in
conjunction with it. \fIsignid\fR represents the NID for the composite "Signature
Algorithm", \fIdig_id\fR is the NID for the digest algorithm and \fIpkey_id\fR is the
NID for the underlying signature algorithm. As there are signature algorithms
that do not require a digest, NID_undef is a valid \fIdig_id\fR.
.PP
\&\fBOBJ_cleanup()\fR releases any resources allocated by creating new objects.
.SH NOTES
.IX Header "NOTES"
Objects in OpenSSL can have a short name, a long name and a numerical
identifier (NID) associated with them. A standard set of objects is
represented in an internal table. The appropriate values are defined
in the header file \fBobjects.h\fR.
.PP
For example the OID for commonName has the following definitions:
.PP
.Vb 3
\& #define SN_commonName                   "CN"
\& #define LN_commonName                   "commonName"
\& #define NID_commonName                  13
.Ve
.PP
New objects can be added by calling \fBOBJ_create()\fR.
.PP
Table objects have certain advantages over other objects: for example
their NIDs can be used in a C language switch statement. They are
also static constant structures which are shared: that is there
is only a single constant structure for each table object.
.PP
Objects which are not in the table have the NID value NID_undef.
.PP
Objects do not need to be in the internal tables to be processed,
the functions \fBOBJ_txt2obj()\fR and \fBOBJ_obj2txt()\fR can process the numerical
form of an OID.
.PP
Some objects are used to represent algorithms which do not have a
corresponding ASN.1 OBJECT IDENTIFIER encoding (for example no OID currently
exists for a particular algorithm). As a result they \fBcannot\fR be encoded or
decoded as part of ASN.1 structures. Applications can determine if there
is a corresponding OBJECT IDENTIFIER by checking \fBOBJ_length()\fR is not zero.
.PP
These functions cannot return \fBconst\fR because an \fBASN1_OBJECT\fR can
represent both an internal, constant, OID and a dynamically-created one.
The latter cannot be constant because it needs to be freed after use.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOBJ_nid2obj()\fR returns an \fBASN1_OBJECT\fR structure or \fBNULL\fR is an
error occurred.
.PP
\&\fBOBJ_nid2ln()\fR and \fBOBJ_nid2sn()\fR returns a valid string or \fBNULL\fR
on error.
.PP
\&\fBOBJ_obj2nid()\fR, \fBOBJ_ln2nid()\fR, \fBOBJ_sn2nid()\fR and \fBOBJ_txt2nid()\fR return
a NID or \fBNID_undef\fR on error.
.PP
\&\fBOBJ_add_sigid()\fR returns 1 on success or 0 on error.
.PP
\&\fBi2t_ASN1_OBJECT()\fR an \fBOBJ_obj2txt()\fR return \-1 on error.
On success, they return the length of the string written to \fIbuf\fR if \fIbuf\fR is
not NULL and \fIbuf_len\fR is big enough, otherwise the total string length.
Note that this does not count the trailing NUL character.
.SH EXAMPLES
.IX Header "EXAMPLES"
Create an object for \fBcommonName\fR:
.PP
.Vb 1
\& ASN1_OBJECT *o = OBJ_nid2obj(NID_commonName);
.Ve
.PP
Check if an object is \fBcommonName\fR
.PP
.Vb 2
\& if (OBJ_obj2nid(obj) == NID_commonName)
\&     /* Do something */
.Ve
.PP
Create a new NID and initialize an object from it:
.PP
.Vb 2
\& int new_nid = OBJ_create("1.2.3.4", "NewOID", "New Object Identifier");
\& ASN1_OBJECT *obj = OBJ_nid2obj(new_nid);
.Ve
.PP
Create a new object directly:
.PP
.Vb 1
\& obj = OBJ_txt2obj("1.2.3.4", 1);
.Ve
.SH BUGS
.IX Header "BUGS"
Neither \fBOBJ_create()\fR nor \fBOBJ_add_sigid()\fR do any locking and are thus not
thread safe.  Moreover, none of the other functions should be called while
concurrent calls to these two functions are possible.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBOBJ_cleanup()\fR was deprecated in OpenSSL 1.1.0 by \fBOPENSSL_init_crypto\fR\|(3)
and should not be used.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
