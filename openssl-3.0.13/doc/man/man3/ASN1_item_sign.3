.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ASN1_ITEM_SIGN 3ossl"
.TH ASN1_ITEM_SIGN 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ASN1_item_sign, ASN1_item_sign_ex, ASN1_item_sign_ctx,
ASN1_item_verify, ASN1_item_verify_ex, ASN1_item_verify_ctx \-
ASN1 sign and verify
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& int ASN1_item_sign_ex(const ASN1_ITEM *it, X509_ALGOR *algor1,
\&                       X509_ALGOR *algor2, ASN1_BIT_STRING *signature,
\&                       const void *data, const ASN1_OCTET_STRING *id,
\&                       EVP_PKEY *pkey, const EVP_MD *md, OSSL_LIB_CTX *libctx,
\&                       const char *propq);
\&
\& int ASN1_item_sign(const ASN1_ITEM *it, X509_ALGOR *algor1, X509_ALGOR *algor2,
\&                    ASN1_BIT_STRING *signature, const void *data,
\&                    EVP_PKEY *pkey, const EVP_MD *md);
\&
\& int ASN1_item_sign_ctx(const ASN1_ITEM *it, X509_ALGOR *algor1,
\&                        X509_ALGOR *algor2, ASN1_BIT_STRING *signature,
\&                        const void *data, EVP_MD_CTX *ctx);
\&
\& int ASN1_item_verify_ex(const ASN1_ITEM *it, const X509_ALGOR *alg,
\&                         const ASN1_BIT_STRING *signature, const void *data,
\&                         const ASN1_OCTET_STRING *id, EVP_PKEY *pkey,
\&                         OSSL_LIB_CTX *libctx, const char *propq);
\&
\& int ASN1_item_verify(const ASN1_ITEM *it, const X509_ALGOR *alg,
\&                      const ASN1_BIT_STRING *signature, const void *data,
\&                      EVP_PKEY *pkey);
\&
\& int ASN1_item_verify_ctx(const ASN1_ITEM *it, const X509_ALGOR *alg,
\&                          const ASN1_BIT_STRING *signature, const void *data,
\&                          EVP_MD_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBASN1_item_sign_ex()\fR is used to sign arbitrary ASN1 data using a data object
\&\fIdata\fR, the ASN.1 structure \fIit\fR, private key \fIpkey\fR and message digest \fImd\fR.
The data that is signed is formed by taking the data object in \fIdata\fR and
converting it to der format using the ASN.1 structure \fIit\fR.
The \fIdata\fR that will be signed, and a structure containing the signature may
both have a copy of the \fBX509_ALGOR\fR. The \fBASN1_item_sign_ex()\fR function will
write the correct \fBX509_ALGOR\fR to the structs based on the algorithms and
parameters that have been set up. If one of \fIalgor1\fR or \fIalgor2\fR points to the
\&\fBX509_ALGOR\fR of the \fIdata\fR to be signed, then that \fBX509_ALGOR\fR will first be
written before the signature is generated.
Examples of valid values that can be used by the ASN.1 structure \fIit\fR are
ASN1_ITEM_rptr(X509_CINF), ASN1_ITEM_rptr(X509_REQ_INFO) and
ASN1_ITEM_rptr(X509_CRL_INFO).
The \fBOSSL_LIB_CTX\fR specified in \fIlibctx\fR and the property query string
specified in \fIprops\fR are used when searching for algorithms in providers.
The generated signature is set into \fIsignature\fR.
The optional parameter \fIid\fR can be NULL, but can be set for special key types.
See \fBEVP_PKEY_CTX_set1_id()\fR for further info. The output parameters <algor1> and
\&\fIalgor2\fR are ignored if they are NULL.
.PP
\&\fBASN1_item_sign()\fR is similar to \fBASN1_item_sign_ex()\fR but uses default values of
NULL for the \fIid\fR, \fIlibctx\fR and \fIpropq\fR.
.PP
\&\fBASN1_item_sign_ctx()\fR is similar to \fBASN1_item_sign()\fR but uses the parameters
contained in digest context \fIctx\fR.
.PP
\&\fBASN1_item_verify_ex()\fR is used to verify the signature \fIsignature\fR of internal
data \fIdata\fR using the public key \fIpkey\fR and algorithm identifier \fIalg\fR.
The data that is verified is formed by taking the data object in \fIdata\fR and
converting it to der format using the ASN.1 structure \fIit\fR.
The \fBOSSL_LIB_CTX\fR specified in \fIlibctx\fR and the property query string
specified in \fIprops\fR are used when searching for algorithms in providers.
The optional parameter \fIid\fR can be NULL, but can be set for special key types.
See \fBEVP_PKEY_CTX_set1_id()\fR for further info.
.PP
\&\fBASN1_item_verify()\fR is similar to \fBASN1_item_verify_ex()\fR but uses default values of
NULL for the \fIid\fR, \fIlibctx\fR and \fIpropq\fR.
.PP
\&\fBASN1_item_verify_ctx()\fR is similar to \fBASN1_item_verify()\fR but uses the parameters
contained in digest context \fIctx\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All sign functions return the size of the signature in bytes for success and
zero for failure.
.PP
All verify functions return 1 if the signature is valid and 0 if the signature
check fails. If the signature could not be checked at all because it was
ill-formed or some other error occurred then \-1 is returned.
.SH EXAMPLES
.IX Header "EXAMPLES"
In the following example a 'MyObject' object is signed using the key contained
in an EVP_MD_CTX. The signature is written to MyObject.signature. The object is
then output in DER format and then loaded back in and verified.
.PP
.Vb 2
\& #include <openssl/x509.h>
\& #include <openssl/asn1t.h>
\&
\& /* An object used to store the ASN1 data fields that will be signed */
\& typedef struct MySignInfoObject_st
\& {
\&     ASN1_INTEGER *version;
\&     X509_ALGOR sig_alg;
\& } MySignInfoObject;
\&
\& DECLARE_ASN1_FUNCTIONS(MySignInfoObject)
\& /*
\&  * A higher level object containing the ASN1 fields, signature alg and
\&  * output signature.
\&  */
\& typedef struct MyObject_st
\& {
\&     MySignInfoObject info;
\&     X509_ALGOR sig_alg;
\&     ASN1_BIT_STRING *signature;
\& } MyObject;
\&
\& DECLARE_ASN1_FUNCTIONS(MyObject)
\&
\& /* The ASN1 definition of MySignInfoObject */
\& ASN1_SEQUENCE_cb(MySignInfoObject, NULL) = {
\&     ASN1_SIMPLE(MySignInfoObject, version, ASN1_INTEGER)
\&     ASN1_EMBED(MySignInfoObject, sig_alg, X509_ALGOR),
\& } ASN1_SEQUENCE_END_cb(MySignInfoObject, MySignInfoObject)
\&
\& /* new, free, d2i & i2d functions for MySignInfoObject */
\& IMPLEMENT_ASN1_FUNCTIONS(MySignInfoObject)
\&
\& /* The ASN1 definition of MyObject */
\& ASN1_SEQUENCE_cb(MyObject, NULL) = {
\&     ASN1_EMBED(MyObject, info, MySignInfoObject),
\&     ASN1_EMBED(MyObject, sig_alg, X509_ALGOR),
\&     ASN1_SIMPLE(MyObject, signature, ASN1_BIT_STRING)
\& } ASN1_SEQUENCE_END_cb(MyObject, MyObject)
\&
\& /* new, free, d2i & i2d functions for MyObject */
\& IMPLEMENT_ASN1_FUNCTIONS(MyObject)
\&
\& int test_asn1_item_sign_verify(const char *mdname, EVP_PKEY *pkey, long version)
\& {
\&    int ret = 0;
\&    unsigned char *obj_der = NULL;
\&    const unsigned char *p = NULL;
\&    MyObject *obj = NULL, *loaded_obj = NULL;
\&    const ASN1_ITEM *it = ASN1_ITEM_rptr(MySignInfoObject);
\&    EVP_MD_CTX *sctx = NULL, *vctx = NULL;
\&    int len;
\&
\&    /* Create MyObject and set its version */
\&    obj = MyObject_new();
\&    if (obj == NULL)
\&        goto err;
\&    if (!ASN1_INTEGER_set(obj\->info.version, version))
\&        goto err;
\&
\&    /* Set the key and digest used for signing */
\&    sctx = EVP_MD_CTX_new();
\&    if (sctx == NULL
\&        || !EVP_DigestSignInit_ex(sctx, NULL, mdname, NULL, NULL, pkey))
\&        goto err;
\&
\&    /*
\&     * it contains the mapping between ASN.1 data and an object MySignInfoObject
\&     * obj\->info is the \*(AqMySignInfoObject\*(Aq object that will be
\&     *   converted into DER data and then signed.
\&     * obj\->signature will contain the output signature.
\&     * obj\->sig_alg is filled with the private key\*(Aqs signing algorithm id.
\&     * obj\->info.sig_alg is another copy of the signing algorithm id that sits
\&     * within MyObject.
\&     */
\&    len = ASN1_item_sign_ctx(it, &obj\->sig_alg, &obj\->info.sig_alg,
\&                             obj\->signature, &obj\->info, sctx);
\&    if (len <= 0
\&        || X509_ALGOR_cmp(&obj\->sig_alg, &obj\->info.sig_alg) != 0)
\&        goto err;
\&
\&    /* Output MyObject in der form */
\&    len = i2d_MyObject(obj, &obj_der);
\&    if (len <= 0)
\&        goto err;
\&
\&    /* Set the key and digest used for verifying */
\&    vctx = EVP_MD_CTX_new();
\&    if (vctx == NULL
\&        || !EVP_DigestVerifyInit_ex(vctx, NULL, mdname, NULL, NULL, pkey))
\&        goto err;
\&
\&    /* Load the der data back into an object */
\&    p = obj_der;
\&    loaded_obj = d2i_MyObject(NULL, &p, len);
\&    if (loaded_obj == NULL)
\&        goto err;
\&    /* Verify the loaded object */
\&    ret = ASN1_item_verify_ctx(it, &loaded_obj\->sig_alg, loaded_obj\->signature,
\&                               &loaded_obj\->info, vctx);
\&err:
\&    OPENSSL_free(obj_der);
\&    MyObject_free(loaded_obj);
\&    MyObject_free(obj);
\&    EVP_MD_CTX_free(sctx);
\&    EVP_MD_CTX_free(vctx);
\&    return ret;
\& }
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_sign\fR\|(3),
\&\fBX509_verify\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBASN1_item_sign_ex()\fR and \fBASN1_item_verify_ex()\fR were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
