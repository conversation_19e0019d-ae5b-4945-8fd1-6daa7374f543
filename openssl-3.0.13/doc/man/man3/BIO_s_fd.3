.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_S_FD 3ossl"
.TH BIO_S_FD 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_s_fd, BIO_set_fd, BIO_get_fd, BIO_new_fd \- file descriptor BIO
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& const BIO_METHOD *BIO_s_fd(void);
\&
\& int BIO_set_fd(BIO *b, int fd, int c);
\& int BIO_get_fd(BIO *b, int *c);
\&
\& BIO *BIO_new_fd(int fd, int close_flag);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_s_fd()\fR returns the file descriptor BIO method. This is a wrapper
round the platforms file descriptor routines such as \fBread()\fR and \fBwrite()\fR.
.PP
\&\fBBIO_read_ex()\fR and \fBBIO_write_ex()\fR read or write the underlying descriptor.
\&\fBBIO_puts()\fR is supported but \fBBIO_gets()\fR is not.
.PP
If the close flag is set then \fBclose()\fR is called on the underlying
file descriptor when the BIO is freed.
.PP
\&\fBBIO_reset()\fR attempts to change the file pointer to the start of file
such as by using \fBlseek(fd, 0, 0)\fR.
.PP
\&\fBBIO_seek()\fR sets the file pointer to position \fBofs\fR from start of file
such as by using \fBlseek(fd, ofs, 0)\fR.
.PP
\&\fBBIO_tell()\fR returns the current file position such as by calling
\&\fBlseek(fd, 0, 1)\fR.
.PP
\&\fBBIO_set_fd()\fR sets the file descriptor of BIO \fBb\fR to \fBfd\fR and the close
flag to \fBc\fR.
.PP
\&\fBBIO_get_fd()\fR places the file descriptor of BIO \fBb\fR in \fBc\fR if it is not NULL.
It also returns the file descriptor.
.PP
\&\fBBIO_new_fd()\fR returns a file descriptor BIO using \fBfd\fR and \fBclose_flag\fR.
.SH NOTES
.IX Header "NOTES"
The behaviour of \fBBIO_read_ex()\fR and \fBBIO_write_ex()\fR depends on the behavior of the
platforms \fBread()\fR and \fBwrite()\fR calls on the descriptor. If the underlying
file descriptor is in a non blocking mode then the BIO will behave in the
manner described in the \fBBIO_read_ex\fR\|(3) and \fBBIO_should_retry\fR\|(3)
manual pages.
.PP
File descriptor BIOs should not be used for socket I/O. Use socket BIOs
instead.
.PP
\&\fBBIO_set_fd()\fR and \fBBIO_get_fd()\fR are implemented as macros.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_s_fd()\fR returns the file descriptor BIO method.
.PP
\&\fBBIO_set_fd()\fR returns 1 on success or <=0 for failure.
.PP
\&\fBBIO_get_fd()\fR returns the file descriptor or \-1 if the BIO has not
been initialized. It also returns zero and negative values if other error occurs.
.PP
\&\fBBIO_new_fd()\fR returns the newly allocated BIO or NULL is an error
occurred.
.SH EXAMPLES
.IX Header "EXAMPLES"
This is a file descriptor BIO version of "Hello World":
.PP
.Vb 1
\& BIO *out;
\&
\& out = BIO_new_fd(fileno(stdout), BIO_NOCLOSE);
\& BIO_printf(out, "Hello World\en");
\& BIO_free(out);
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBBIO_seek\fR\|(3), \fBBIO_tell\fR\|(3),
\&\fBBIO_reset\fR\|(3), \fBBIO_read_ex\fR\|(3),
\&\fBBIO_write_ex\fR\|(3), \fBBIO_puts\fR\|(3),
\&\fBBIO_gets\fR\|(3), \fBBIO_printf\fR\|(3),
\&\fBBIO_set_close\fR\|(3), \fBBIO_get_close\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
