.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_SHA3_224 3ossl"
.TH EVP_SHA3_224 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_sha3_224,
EVP_sha3_256,
EVP_sha3_384,
EVP_sha3_512,
EVP_shake128,
EVP_shake256
\&\- SHA\-3 For EVP
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& const EVP_MD *EVP_sha3_224(void);
\& const EVP_MD *EVP_sha3_256(void);
\& const EVP_MD *EVP_sha3_384(void);
\& const EVP_MD *EVP_sha3_512(void);
\&
\& const EVP_MD *EVP_shake128(void);
\& const EVP_MD *EVP_shake256(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
SHA\-3 (Secure Hash Algorithm 3) is a family of cryptographic hash functions
standardized in NIST FIPS 202, first published in 2015. It is based on the
Keccak algorithm.
.IP "\fBEVP_sha3_224()\fR, \fBEVP_sha3_256()\fR, \fBEVP_sha3_384()\fR, \fBEVP_sha3_512()\fR" 4
.IX Item "EVP_sha3_224(), EVP_sha3_256(), EVP_sha3_384(), EVP_sha3_512()"
The SHA\-3 SHA\-3\-224, SHA\-3\-256, SHA\-3\-384, and SHA\-3\-512 algorithms
respectively. They produce 224, 256, 384 and 512 bits of output from a given
input.
.IP "\fBEVP_shake128()\fR, \fBEVP_shake256()\fR" 4
.IX Item "EVP_shake128(), EVP_shake256()"
The SHAKE\-128 and SHAKE\-256 Extendable Output Functions (XOF) that can generate
a variable hash length.
.Sp
Specifically, \fBEVP_shake128\fR provides an overall security of 128 bits, while
\&\fBEVP_shake256\fR provides that of 256 bits.
.SH NOTES
.IX Header "NOTES"
Developers should be aware of the negative performance implications of
calling these functions multiple times and should consider using
\&\fBEVP_MD_fetch\fR\|(3) with \fBEVP_MD\-SHA3\fR\|(7) or \fBEVP_MD\-SHAKE\fR\|(7) instead.
See "Performance" in \fBcrypto\fR\|(7) for further information.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
These functions return a \fBEVP_MD\fR structure that contains the
implementation of the message digest. See \fBEVP_MD_meth_new\fR\|(3) for
details of the \fBEVP_MD\fR structure.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
NIST FIPS 202.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBevp\fR\|(7),
\&\fBEVP_DigestInit\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
