.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_CMP_HDR_GET0_TRANSACTIONID 3ossl"
.TH OSSL_CMP_HDR_GET0_TRANSACTIONID 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_CMP_HDR_get0_transactionID,
OSSL_CMP_HDR_get0_recipNonce
\&\- functions manipulating CMP message headers
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\&  #include <openssl/cmp.h>
\&
\&  ASN1_OCTET_STRING *OSSL_CMP_HDR_get0_transactionID(const
\&                                                     OSSL_CMP_PKIHEADER *hdr);
\&  ASN1_OCTET_STRING *OSSL_CMP_HDR_get0_recipNonce(const
\&                                                  OSSL_CMP_PKIHEADER *hdr);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
OSSL_CMP_HDR_get0_transactionID returns the transaction ID of the given
PKIHeader.
.PP
OSSL_CMP_HDR_get0_recipNonce returns the recipient nonce of the given PKIHeader.
.SH NOTES
.IX Header "NOTES"
CMP is defined in RFC 4210.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The functions return the intended pointer value as described above
or NULL if the respective entry does not exist and on error.
.SH HISTORY
.IX Header "HISTORY"
The OpenSSL CMP support was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2007\-2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
