.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CMS_UNCOMPRESS 3ossl"
.TH CMS_UNCOMPRESS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CMS_uncompress \- uncompress a CMS CompressedData structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& int CMS_uncompress(CMS_ContentInfo *cms, BIO *dcont, BIO *out, unsigned int flags);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBCMS_uncompress()\fR extracts and uncompresses the content from a CMS
CompressedData structure \fBcms\fR. \fBdata\fR is a BIO to write the content to and
\&\fBflags\fR is an optional set of flags.
.PP
The \fBdcont\fR parameter is used in the rare case where the compressed content
is detached. It will normally be set to NULL.
.SH NOTES
.IX Header "NOTES"
The only currently supported compression algorithm is zlib: if the structure
indicates the use of any other algorithm an error is returned.
.PP
If zlib support is not compiled into OpenSSL then \fBCMS_uncompress()\fR will always
return an error.
.PP
The following flags can be passed in the \fBflags\fR parameter.
.PP
If the \fBCMS_TEXT\fR flag is set MIME headers for type \fBtext/plain\fR are deleted
from the content. If the content is not of type \fBtext/plain\fR then an error is
returned.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCMS_uncompress()\fR returns either 1 for success or 0 for failure. The error can
be obtained from \fBERR_get_error\fR\|(3)
.SH BUGS
.IX Header "BUGS"
The lack of single pass processing and the need to hold all data in memory as
mentioned in \fBCMS_verify()\fR also applies to \fBCMS_decompress()\fR.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBCMS_compress\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2008\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
