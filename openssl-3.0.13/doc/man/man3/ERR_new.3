.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ERR_NEW 3ossl"
.TH ERR_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ERR_new, ERR_set_debug, ERR_set_error, ERR_vset_error
\&\- Error recording building blocks
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/err.h>
\&
\& void ERR_new(void);
\& void ERR_set_debug(const char *file, int line, const char *func);
\& void ERR_set_error(int lib, int reason, const char *fmt, ...);
\& void ERR_vset_error(int lib, int reason, const char *fmt, va_list args);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The functions described here are generally not used directly, but
rather through macros such as \fBERR_raise\fR\|(3).
They can still be useful for anyone that wants to make their own
macros.
.PP
\&\fBERR_new()\fR allocates a new slot in the thread's error queue.
.PP
\&\fBERR_set_debug()\fR sets the debug information related to the current
error in the thread's error queue.
The values that can be given are the filename \fIfile\fR, line in the
file \fIline\fR and the name of the function \fIfunc\fR where the error
occurred.
The names must be constant, this function will only save away the
pointers, not copy the strings.
.PP
\&\fBERR_set_error()\fR sets the error information, which are the library
number \fIlib\fR and the reason code \fIreason\fR, and additional data as a
format string \fIfmt\fR and an arbitrary number of arguments.
The additional data is processed with \fBBIO_snprintf\fR\|(3) to form the
additional data string, which is allocated and store in the error
record.
.PP
\&\fBERR_vset_error()\fR works like \fBERR_set_error()\fR, but takes a \fBva_list\fR
argument instead of a variable number of arguments.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
ERR_new, ERR_set_debug, ERR_set_error and ERR_vset_error
do not return any values.
.SH NOTES
.IX Header "NOTES"
The library number is unique to each unit that records errors.
OpenSSL has a number of preallocated ones for its own uses, but
others may allocate their own library number dynamically with
\&\fBERR_get_next_error_library\fR\|(3).
.PP
Reason codes are unique within each library, and may have an
associated set of strings as a short description of the reason.
For dynamically allocated library numbers, reason strings are recorded
with \fBERR_load_strings\fR\|(3).
.PP
Provider authors are supplied with core versions of these functions,
see \fBprovider\-base\fR\|(7).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_raise\fR\|(3), \fBERR_get_next_error_library\fR\|(3),
\&\fBERR_load_strings\fR\|(3), \fBBIO_snprintf\fR\|(3), \fBprovider\-base\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
