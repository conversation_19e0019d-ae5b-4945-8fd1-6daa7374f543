.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_CALLBACK 3ossl"
.TH OSSL_CALLBACK 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_CALLBACK, OSSL_PASSPHRASE_CALLBACK \- OpenSSL Core type to define callbacks
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 6
\& #include <openssl/core.h>
\& typedef int (OSSL_CALLBACK)(const OSSL_PARAM params[], void *arg);
\& typedef int (OSSL_PASSPHRASE_CALLBACK)(char *pass, size_t pass_size,
\&                                        size_t *pass_len,
\&                                        const OSSL_PARAM params[],
\&                                        void *arg);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
For certain events or activities, provider functionality may need help from
the application or the calling OpenSSL libraries themselves.  For example,
user input or direct (possibly optional) user output could be implemented
this way.
.PP
Callback functions themselves are always provided by or through the calling
OpenSSL libraries, along with a generic pointer to data \fIarg\fR.  As far as
the function receiving the pointer to the function pointer and \fIarg\fR is
concerned, the data that \fIarg\fR points at is opaque, and the pointer should
simply be passed back to the callback function when it's called.
.IP \fBOSSL_CALLBACK\fR 4
.IX Item "OSSL_CALLBACK"
This is a generic callback function.  When calling this callback function,
the caller is expected to build an \fBOSSL_PARAM\fR\|(3) array of data it wants or
is expected to pass back, and pass that as \fIparams\fR, as well as the opaque
data pointer it received, as \fIarg\fR.
.IP \fBOSSL_PASSPHRASE_CALLBACK\fR 4
.IX Item "OSSL_PASSPHRASE_CALLBACK"
This is a specialised callback function, used specifically to prompt the
user for a passphrase.  When calling this callback function, a buffer to
store the pass phrase needs to be given with \fIpass\fR, and its size with
\&\fIpass_size\fR.  The length of the prompted pass phrase will be given back in
\&\fI*pass_len\fR.
.Sp
Additional parameters can be passed with the \fBOSSL_PARAM\fR\|(3) array \fIparams\fR,
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\-core.h\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The types described here were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
