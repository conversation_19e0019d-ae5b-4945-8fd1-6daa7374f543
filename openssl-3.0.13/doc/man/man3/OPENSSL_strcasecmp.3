.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL_STRCASECMP 3ossl"
.TH OPENSSL_STRCASECMP 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OPENSSL_strcasecmp, OPENSSL_strncasecmp \- compare two strings ignoring case
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/crypto.h>
\&
\& int OPENSSL_strcasecmp(const char *s1, const char *s2);
\& int OPENSSL_strncasecmp(const char *s1, const char *s2, size_t n);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The OPENSSL_strcasecmp function performs a byte-by-byte comparison of the strings
\&\fBs1\fR and \fBs2\fR, ignoring the case of the characters.
.PP
The OPENSSL_strncasecmp function is similar, except that it compares no more than
\&\fBn\fR bytes of \fBs1\fR and \fBs2\fR.
.PP
In POSIX-compatible system and on Windows these functions use "C" locale for
case insensitive. Otherwise the comparison is done in current locale.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
Both functions return an integer less than, equal to, or greater than zero if
s1 is found, respectively, to be less than, to match, or be greater than s2.
.SH NOTES
.IX Header "NOTES"
OpenSSL extensively uses case insensitive comparison of ASCII strings. Though
OpenSSL itself is locale-agnostic, the applications using OpenSSL libraries may
unpredictably suffer when they use localization (e.g. Turkish locale is
well-known with a specific I/i cases). These functions use C locale for string
comparison.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
