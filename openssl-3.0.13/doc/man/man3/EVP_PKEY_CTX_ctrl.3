.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_CTX_CTRL 3ossl"
.TH EVP_PKEY_CTX_CTRL 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_CTX_ctrl,
EVP_PKEY_CTX_ctrl_str,
EVP_PKEY_CTX_ctrl_uint64,
EVP_PKEY_CTX_md,
EVP_PKEY_CTX_set_signature_md,
EVP_PKEY_CTX_get_signature_md,
EVP_PKEY_CTX_set_mac_key,
EVP_PKEY_CTX_set_group_name,
EVP_PKEY_CTX_get_group_name,
EVP_PKEY_CTX_set_rsa_padding,
EVP_PKEY_CTX_get_rsa_padding,
EVP_PKEY_CTX_set_rsa_pss_saltlen,
EVP_PKEY_CTX_get_rsa_pss_saltlen,
EVP_PKEY_CTX_set_rsa_keygen_bits,
EVP_PKEY_CTX_set_rsa_keygen_pubexp,
EVP_PKEY_CTX_set1_rsa_keygen_pubexp,
EVP_PKEY_CTX_set_rsa_keygen_primes,
EVP_PKEY_CTX_set_rsa_mgf1_md_name,
EVP_PKEY_CTX_set_rsa_mgf1_md,
EVP_PKEY_CTX_get_rsa_mgf1_md,
EVP_PKEY_CTX_get_rsa_mgf1_md_name,
EVP_PKEY_CTX_set_rsa_oaep_md_name,
EVP_PKEY_CTX_set_rsa_oaep_md,
EVP_PKEY_CTX_get_rsa_oaep_md,
EVP_PKEY_CTX_get_rsa_oaep_md_name,
EVP_PKEY_CTX_set0_rsa_oaep_label,
EVP_PKEY_CTX_get0_rsa_oaep_label,
EVP_PKEY_CTX_set_dsa_paramgen_bits,
EVP_PKEY_CTX_set_dsa_paramgen_q_bits,
EVP_PKEY_CTX_set_dsa_paramgen_md,
EVP_PKEY_CTX_set_dsa_paramgen_md_props,
EVP_PKEY_CTX_set_dsa_paramgen_gindex,
EVP_PKEY_CTX_set_dsa_paramgen_type,
EVP_PKEY_CTX_set_dsa_paramgen_seed,
EVP_PKEY_CTX_set_dh_paramgen_prime_len,
EVP_PKEY_CTX_set_dh_paramgen_subprime_len,
EVP_PKEY_CTX_set_dh_paramgen_generator,
EVP_PKEY_CTX_set_dh_paramgen_type,
EVP_PKEY_CTX_set_dh_paramgen_gindex,
EVP_PKEY_CTX_set_dh_paramgen_seed,
EVP_PKEY_CTX_set_dh_rfc5114,
EVP_PKEY_CTX_set_dhx_rfc5114,
EVP_PKEY_CTX_set_dh_pad,
EVP_PKEY_CTX_set_dh_nid,
EVP_PKEY_CTX_set_dh_kdf_type,
EVP_PKEY_CTX_get_dh_kdf_type,
EVP_PKEY_CTX_set0_dh_kdf_oid,
EVP_PKEY_CTX_get0_dh_kdf_oid,
EVP_PKEY_CTX_set_dh_kdf_md,
EVP_PKEY_CTX_get_dh_kdf_md,
EVP_PKEY_CTX_set_dh_kdf_outlen,
EVP_PKEY_CTX_get_dh_kdf_outlen,
EVP_PKEY_CTX_set0_dh_kdf_ukm,
EVP_PKEY_CTX_get0_dh_kdf_ukm,
EVP_PKEY_CTX_set_ec_paramgen_curve_nid,
EVP_PKEY_CTX_set_ec_param_enc,
EVP_PKEY_CTX_set_ecdh_cofactor_mode,
EVP_PKEY_CTX_get_ecdh_cofactor_mode,
EVP_PKEY_CTX_set_ecdh_kdf_type,
EVP_PKEY_CTX_get_ecdh_kdf_type,
EVP_PKEY_CTX_set_ecdh_kdf_md,
EVP_PKEY_CTX_get_ecdh_kdf_md,
EVP_PKEY_CTX_set_ecdh_kdf_outlen,
EVP_PKEY_CTX_get_ecdh_kdf_outlen,
EVP_PKEY_CTX_set0_ecdh_kdf_ukm,
EVP_PKEY_CTX_get0_ecdh_kdf_ukm,
EVP_PKEY_CTX_set1_id, EVP_PKEY_CTX_get1_id, EVP_PKEY_CTX_get1_id_len,
EVP_PKEY_CTX_set_kem_op
\&\- algorithm specific control operations
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_CTX_ctrl(EVP_PKEY_CTX *ctx, int keytype, int optype,
\&                       int cmd, int p1, void *p2);
\& int EVP_PKEY_CTX_ctrl_uint64(EVP_PKEY_CTX *ctx, int keytype, int optype,
\&                              int cmd, uint64_t value);
\& int EVP_PKEY_CTX_ctrl_str(EVP_PKEY_CTX *ctx, const char *type,
\&                           const char *value);
\&
\& int EVP_PKEY_CTX_md(EVP_PKEY_CTX *ctx, int optype, int cmd, const char *md);
\&
\& int EVP_PKEY_CTX_set_signature_md(EVP_PKEY_CTX *ctx, const EVP_MD *md);
\& int EVP_PKEY_CTX_get_signature_md(EVP_PKEY_CTX *ctx, const EVP_MD **pmd);
\&
\& int EVP_PKEY_CTX_set_mac_key(EVP_PKEY_CTX *ctx, const unsigned char *key,
\&                              int len);
\& int EVP_PKEY_CTX_set_group_name(EVP_PKEY_CTX *ctx, const char *name);
\& int EVP_PKEY_CTX_get_group_name(EVP_PKEY_CTX *ctx, char *name, size_t namelen);
\&
\& int EVP_PKEY_CTX_set_kem_op(EVP_PKEY_CTX *ctx, const char *op);
\&
\& #include <openssl/rsa.h>
\&
\& int EVP_PKEY_CTX_set_rsa_padding(EVP_PKEY_CTX *ctx, int pad);
\& int EVP_PKEY_CTX_get_rsa_padding(EVP_PKEY_CTX *ctx, int *pad);
\& int EVP_PKEY_CTX_set_rsa_pss_saltlen(EVP_PKEY_CTX *ctx, int saltlen);
\& int EVP_PKEY_CTX_get_rsa_pss_saltlen(EVP_PKEY_CTX *ctx, int *saltlen);
\& int EVP_PKEY_CTX_set_rsa_keygen_bits(EVP_PKEY_CTX *ctx, int mbits);
\& int EVP_PKEY_CTX_set1_rsa_keygen_pubexp(EVP_PKEY_CTX *ctx, BIGNUM *pubexp);
\& int EVP_PKEY_CTX_set_rsa_keygen_primes(EVP_PKEY_CTX *ctx, int primes);
\& int EVP_PKEY_CTX_set_rsa_mgf1_md_name(EVP_PKEY_CTX *ctx, const char *mdname,
\&                                     const char *mdprops);
\& int EVP_PKEY_CTX_set_rsa_mgf1_md(EVP_PKEY_CTX *ctx, const EVP_MD *md);
\& int EVP_PKEY_CTX_get_rsa_mgf1_md(EVP_PKEY_CTX *ctx, const EVP_MD **md);
\& int EVP_PKEY_CTX_get_rsa_mgf1_md_name(EVP_PKEY_CTX *ctx, char *name,
\&                                       size_t namelen);
\& int EVP_PKEY_CTX_set_rsa_oaep_md_name(EVP_PKEY_CTX *ctx, const char *mdname,
\&                                       const char *mdprops);
\& int EVP_PKEY_CTX_set_rsa_oaep_md(EVP_PKEY_CTX *ctx, const EVP_MD *md);
\& int EVP_PKEY_CTX_get_rsa_oaep_md(EVP_PKEY_CTX *ctx, const EVP_MD **md);
\& int EVP_PKEY_CTX_get_rsa_oaep_md_name(EVP_PKEY_CTX *ctx, char *name,
\&                                       size_t namelen);
\& int EVP_PKEY_CTX_set0_rsa_oaep_label(EVP_PKEY_CTX *ctx, void *label,
\&                                      int len);
\& int EVP_PKEY_CTX_get0_rsa_oaep_label(EVP_PKEY_CTX *ctx, unsigned char **label);
\&
\& #include <openssl/dsa.h>
\&
\& int EVP_PKEY_CTX_set_dsa_paramgen_bits(EVP_PKEY_CTX *ctx, int nbits);
\& int EVP_PKEY_CTX_set_dsa_paramgen_q_bits(EVP_PKEY_CTX *ctx, int qbits);
\& int EVP_PKEY_CTX_set_dsa_paramgen_md(EVP_PKEY_CTX *ctx, const EVP_MD *md);
\& int EVP_PKEY_CTX_set_dsa_paramgen_md_props(EVP_PKEY_CTX *ctx,
\&                                            const char *md_name,
\&                                            const char *md_properties);
\& int EVP_PKEY_CTX_set_dsa_paramgen_type(EVP_PKEY_CTX *ctx, const char *name);
\& int EVP_PKEY_CTX_set_dsa_paramgen_gindex(EVP_PKEY_CTX *ctx, int gindex);
\& int EVP_PKEY_CTX_set_dsa_paramgen_seed(EVP_PKEY_CTX *ctx,
\&                                        const unsigned char *seed,
\&                                        size_t seedlen);
\&
\& #include <openssl/dh.h>
\&
\& int EVP_PKEY_CTX_set_dh_paramgen_prime_len(EVP_PKEY_CTX *ctx, int len);
\& int EVP_PKEY_CTX_set_dh_paramgen_subprime_len(EVP_PKEY_CTX *ctx, int len);
\& int EVP_PKEY_CTX_set_dh_paramgen_generator(EVP_PKEY_CTX *ctx, int gen);
\& int EVP_PKEY_CTX_set_dh_paramgen_type(EVP_PKEY_CTX *ctx, int type);
\& int EVP_PKEY_CTX_set_dh_pad(EVP_PKEY_CTX *ctx, int pad);
\& int EVP_PKEY_CTX_set_dh_nid(EVP_PKEY_CTX *ctx, int nid);
\& int EVP_PKEY_CTX_set_dh_rfc5114(EVP_PKEY_CTX *ctx, int rfc5114);
\& int EVP_PKEY_CTX_set_dhx_rfc5114(EVP_PKEY_CTX *ctx, int rfc5114);
\& int EVP_PKEY_CTX_set_dh_paramgen_gindex(EVP_PKEY_CTX *ctx, int gindex);
\& int EVP_PKEY_CTX_set_dh_paramgen_seed(EVP_PKEY_CTX *ctx,
\&                                        const unsigned char *seed,
\&                                        size_t seedlen);
\& int EVP_PKEY_CTX_set_dh_kdf_type(EVP_PKEY_CTX *ctx, int kdf);
\& int EVP_PKEY_CTX_get_dh_kdf_type(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_CTX_set0_dh_kdf_oid(EVP_PKEY_CTX *ctx, ASN1_OBJECT *oid);
\& int EVP_PKEY_CTX_get0_dh_kdf_oid(EVP_PKEY_CTX *ctx, ASN1_OBJECT **oid);
\& int EVP_PKEY_CTX_set_dh_kdf_md(EVP_PKEY_CTX *ctx, const EVP_MD *md);
\& int EVP_PKEY_CTX_get_dh_kdf_md(EVP_PKEY_CTX *ctx, const EVP_MD **md);
\& int EVP_PKEY_CTX_set_dh_kdf_outlen(EVP_PKEY_CTX *ctx, int len);
\& int EVP_PKEY_CTX_get_dh_kdf_outlen(EVP_PKEY_CTX *ctx, int *len);
\& int EVP_PKEY_CTX_set0_dh_kdf_ukm(EVP_PKEY_CTX *ctx, unsigned char *ukm, int len);
\&
\& #include <openssl/ec.h>
\&
\& int EVP_PKEY_CTX_set_ec_paramgen_curve_nid(EVP_PKEY_CTX *ctx, int nid);
\& int EVP_PKEY_CTX_set_ec_param_enc(EVP_PKEY_CTX *ctx, int param_enc);
\& int EVP_PKEY_CTX_set_ecdh_cofactor_mode(EVP_PKEY_CTX *ctx, int cofactor_mode);
\& int EVP_PKEY_CTX_get_ecdh_cofactor_mode(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_CTX_set_ecdh_kdf_type(EVP_PKEY_CTX *ctx, int kdf);
\& int EVP_PKEY_CTX_get_ecdh_kdf_type(EVP_PKEY_CTX *ctx);
\& int EVP_PKEY_CTX_set_ecdh_kdf_md(EVP_PKEY_CTX *ctx, const EVP_MD *md);
\& int EVP_PKEY_CTX_get_ecdh_kdf_md(EVP_PKEY_CTX *ctx, const EVP_MD **md);
\& int EVP_PKEY_CTX_set_ecdh_kdf_outlen(EVP_PKEY_CTX *ctx, int len);
\& int EVP_PKEY_CTX_get_ecdh_kdf_outlen(EVP_PKEY_CTX *ctx, int *len);
\& int EVP_PKEY_CTX_set0_ecdh_kdf_ukm(EVP_PKEY_CTX *ctx, unsigned char *ukm, int len);
\&
\& int EVP_PKEY_CTX_set1_id(EVP_PKEY_CTX *ctx, void *id, size_t id_len);
\& int EVP_PKEY_CTX_get1_id(EVP_PKEY_CTX *ctx, void *id);
\& int EVP_PKEY_CTX_get1_id_len(EVP_PKEY_CTX *ctx, size_t *id_len);
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& #include <openssl/rsa.h>
\&
\& int EVP_PKEY_CTX_set_rsa_keygen_pubexp(EVP_PKEY_CTX *ctx, BIGNUM *pubexp);
\&
\& #include <openssl/dh.h>
\&
\& int EVP_PKEY_CTX_get0_dh_kdf_ukm(EVP_PKEY_CTX *ctx, unsigned char **ukm);
\&
\& #include <openssl/ec.h>
\&
\& int EVP_PKEY_CTX_get0_ecdh_kdf_ukm(EVP_PKEY_CTX *ctx, unsigned char **ukm);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_PKEY_CTX_ctrl()\fR sends a control operation to the context \fIctx\fR. The key
type used must match \fIkeytype\fR if it is not \-1. The parameter \fIoptype\fR is a
mask indicating which operations the control can be applied to.
The control command is indicated in \fIcmd\fR and any additional arguments in
\&\fIp1\fR and \fIp2\fR.
.PP
For \fIcmd\fR = \fBEVP_PKEY_CTRL_SET_MAC_KEY\fR, \fIp1\fR is the length of the MAC key,
and \fIp2\fR is the MAC key. This is used by Poly1305, SipHash, HMAC and CMAC.
.PP
Applications will not normally call \fBEVP_PKEY_CTX_ctrl()\fR directly but will
instead call one of the algorithm specific functions below.
.PP
\&\fBEVP_PKEY_CTX_ctrl_uint64()\fR is a wrapper that directly passes a
uint64 value as \fIp2\fR to \fBEVP_PKEY_CTX_ctrl()\fR.
.PP
\&\fBEVP_PKEY_CTX_ctrl_str()\fR allows an application to send an algorithm
specific control operation to a context \fIctx\fR in string form. This is
intended to be used for options specified on the command line or in text
files. The commands supported are documented in the openssl utility
command line pages for the option \fI\-pkeyopt\fR which is supported by the
\&\fIpkeyutl\fR, \fIgenpkey\fR and \fIreq\fR commands.
.PP
\&\fBEVP_PKEY_CTX_md()\fR sends a message digest control operation to the context
\&\fIctx\fR. The message digest is specified by its name \fImd\fR.
.PP
\&\fBEVP_PKEY_CTX_set_signature_md()\fR sets the message digest type used
in a signature. It can be used in the RSA, DSA and ECDSA algorithms.
.PP
\&\fBEVP_PKEY_CTX_get_signature_md()\fRgets the message digest type used
in a signature. It can be used in the RSA, DSA and ECDSA algorithms.
.PP
Key generation typically involves setting up parameters to be used and
generating the private and public key data. Some algorithm implementations
allow private key data to be set explicitly using \fBEVP_PKEY_CTX_set_mac_key()\fR.
In this case key generation is simply the process of setting up the
parameters for the key and then setting the raw key data to the value explicitly.
Normally applications would call \fBEVP_PKEY_new_raw_private_key\fR\|(3) or similar
functions instead.
.PP
\&\fBEVP_PKEY_CTX_set_mac_key()\fR can be used with any of the algorithms supported by
the \fBEVP_PKEY_new_raw_private_key\fR\|(3) function.
.PP
\&\fBEVP_PKEY_CTX_set_group_name()\fR sets the group name to \fIname\fR for parameter and
key generation. For example for EC keys this will set the curve name and for
DH keys it will set the name of the finite field group.
.PP
\&\fBEVP_PKEY_CTX_get_group_name()\fR finds the group name that's currently
set with \fIctx\fR, and writes it to the location that \fIname\fR points at, as long
as its size \fInamelen\fR is large enough to store that name, including a
terminating NUL byte.
.SS "RSA parameters"
.IX Subsection "RSA parameters"
\&\fBEVP_PKEY_CTX_set_rsa_padding()\fR sets the RSA padding mode for \fIctx\fR.
The \fIpad\fR parameter can take the value \fBRSA_PKCS1_PADDING\fR for PKCS#1
padding, \fBRSA_NO_PADDING\fR for
no padding, \fBRSA_PKCS1_OAEP_PADDING\fR for OAEP padding (encrypt and
decrypt only), \fBRSA_X931_PADDING\fR for X9.31 padding (signature operations
only), \fBRSA_PKCS1_PSS_PADDING\fR (sign and verify only) and
\&\fBRSA_PKCS1_WITH_TLS_PADDING\fR for TLS RSA ClientKeyExchange message padding
(decryption only).
.PP
Two RSA padding modes behave differently if \fBEVP_PKEY_CTX_set_signature_md()\fR
is used. If this function is called for PKCS#1 padding the plaintext buffer is
an actual digest value and is encapsulated in a DigestInfo structure according
to PKCS#1 when signing and this structure is expected (and stripped off) when
verifying. If this control is not used with RSA and PKCS#1 padding then the
supplied data is used directly and not encapsulated. In the case of X9.31
padding for RSA the algorithm identifier byte is added or checked and removed
if this control is called. If it is not called then the first byte of the plaintext
buffer is expected to be the algorithm identifier byte.
.PP
\&\fBEVP_PKEY_CTX_get_rsa_padding()\fR gets the RSA padding mode for \fIctx\fR.
.PP
\&\fBEVP_PKEY_CTX_set_rsa_pss_saltlen()\fR sets the RSA PSS salt length to \fIsaltlen\fR.
As its name implies it is only supported for PSS padding. If this function is
not called then the maximum salt length is used when signing and auto detection
when verifying. Three special values are supported:
.IP \fBRSA_PSS_SALTLEN_DIGEST\fR 4
.IX Item "RSA_PSS_SALTLEN_DIGEST"
sets the salt length to the digest length.
.IP \fBRSA_PSS_SALTLEN_MAX\fR 4
.IX Item "RSA_PSS_SALTLEN_MAX"
sets the salt length to the maximum permissible value.
.IP \fBRSA_PSS_SALTLEN_AUTO\fR 4
.IX Item "RSA_PSS_SALTLEN_AUTO"
causes the salt length to be automatically determined based on the
\&\fBPSS\fR block structure when verifying.  When signing, it has the same
meaning as \fBRSA_PSS_SALTLEN_MAX\fR.
.PP
\&\fBEVP_PKEY_CTX_get_rsa_pss_saltlen()\fR gets the RSA PSS salt length for \fIctx\fR.
The padding mode must already have been set to \fBRSA_PKCS1_PSS_PADDING\fR.
.PP
\&\fBEVP_PKEY_CTX_set_rsa_keygen_bits()\fR sets the RSA key length for
RSA key generation to \fIbits\fR. If not specified 2048 bits is used.
.PP
\&\fBEVP_PKEY_CTX_set1_rsa_keygen_pubexp()\fR sets the public exponent value for RSA key
generation to the value stored in \fIpubexp\fR. Currently it should be an odd
integer. In accordance with the OpenSSL naming convention, the \fIpubexp\fR pointer
must be freed independently of the EVP_PKEY_CTX (ie, it is internally copied).
If not specified 65537 is used.
.PP
\&\fBEVP_PKEY_CTX_set_rsa_keygen_pubexp()\fR does the same as
\&\fBEVP_PKEY_CTX_set1_rsa_keygen_pubexp()\fR except that there is no internal copy and
therefore \fIpubexp\fR should not be modified or freed after the call.
.PP
\&\fBEVP_PKEY_CTX_set_rsa_keygen_primes()\fR sets the number of primes for
RSA key generation to \fIprimes\fR. If not specified 2 is used.
.PP
\&\fBEVP_PKEY_CTX_set_rsa_mgf1_md_name()\fR sets the MGF1 digest for RSA
padding schemes to the digest named \fImdname\fR. If the RSA algorithm
implementation for the selected provider supports it then the digest will be
fetched using the properties \fImdprops\fR. If not explicitly set the signing
digest is used. The padding mode must have been set to \fBRSA_PKCS1_OAEP_PADDING\fR
or \fBRSA_PKCS1_PSS_PADDING\fR.
.PP
\&\fBEVP_PKEY_CTX_set_rsa_mgf1_md()\fR does the same as
\&\fBEVP_PKEY_CTX_set_rsa_mgf1_md_name()\fR except that the name of the digest is
inferred from the supplied \fImd\fR and it is not possible to specify any
properties.
.PP
\&\fBEVP_PKEY_CTX_get_rsa_mgf1_md_name()\fR gets the name of the MGF1
digest algorithm for \fIctx\fR. If not explicitly set the signing digest is used.
The padding mode must have been set to \fBRSA_PKCS1_OAEP_PADDING\fR or
\&\fBRSA_PKCS1_PSS_PADDING\fR.
.PP
\&\fBEVP_PKEY_CTX_get_rsa_mgf1_md()\fR does the same as
\&\fBEVP_PKEY_CTX_get_rsa_mgf1_md_name()\fR except that it returns a pointer to an
EVP_MD object instead. Note that only known, built-in EVP_MD objects will be
returned. The EVP_MD object may be NULL if the digest is not one of these (such
as a digest only implemented in a third party provider).
.PP
\&\fBEVP_PKEY_CTX_set_rsa_oaep_md_name()\fR sets the message digest type
used in RSA OAEP to the digest named \fImdname\fR.  If the RSA algorithm
implementation for the selected provider supports it then the digest will be
fetched using the properties \fImdprops\fR. The padding mode must have been set to
\&\fBRSA_PKCS1_OAEP_PADDING\fR.
.PP
\&\fBEVP_PKEY_CTX_set_rsa_oaep_md()\fR does the same as
\&\fBEVP_PKEY_CTX_set_rsa_oaep_md_name()\fR except that the name of the digest is
inferred from the supplied \fImd\fR and it is not possible to specify any
properties.
.PP
\&\fBEVP_PKEY_CTX_get_rsa_oaep_md_name()\fR gets the message digest
algorithm name used in RSA OAEP and stores it in the buffer \fIname\fR which is of
size \fInamelen\fR. The padding mode must have been set to
\&\fBRSA_PKCS1_OAEP_PADDING\fR. The buffer should be sufficiently large for any
expected digest algorithm names or the function will fail.
.PP
\&\fBEVP_PKEY_CTX_get_rsa_oaep_md()\fR does the same as
\&\fBEVP_PKEY_CTX_get_rsa_oaep_md_name()\fR except that it returns a pointer to an
EVP_MD object instead. Note that only known, built-in EVP_MD objects will be
returned. The EVP_MD object may be NULL if the digest is not one of these (such
as a digest only implemented in a third party provider).
.PP
\&\fBEVP_PKEY_CTX_set0_rsa_oaep_label()\fR sets the RSA OAEP label to binary data
\&\fIlabel\fR and its length in bytes to \fIlen\fR. If \fIlabel\fR is NULL or \fIlen\fR is 0,
the label is cleared. The library takes ownership of the label so the
caller should not free the original memory pointed to by \fIlabel\fR.
The padding mode must have been set to \fBRSA_PKCS1_OAEP_PADDING\fR.
.PP
\&\fBEVP_PKEY_CTX_get0_rsa_oaep_label()\fR gets the RSA OAEP label to
\&\fIlabel\fR. The return value is the label length. The padding mode
must have been set to \fBRSA_PKCS1_OAEP_PADDING\fR. The resulting pointer is owned
by the library and should not be freed by the caller.
.PP
\&\fBRSA_PKCS1_WITH_TLS_PADDING\fR is used when decrypting an RSA encrypted TLS
pre-master secret in a TLS ClientKeyExchange message. It is the same as
RSA_PKCS1_PADDING except that it additionally verifies that the result is the
correct length and the first two bytes are the protocol version initially
requested by the client. If the encrypted content is publicly invalid then the
decryption will fail. However, if the padding checks fail then decryption will
still appear to succeed but a random TLS premaster secret will be returned
instead. This padding mode accepts two parameters which can be set using the
\&\fBEVP_PKEY_CTX_set_params\fR\|(3) function. These are
OSSL_ASYM_CIPHER_PARAM_TLS_CLIENT_VERSION and
OSSL_ASYM_CIPHER_PARAM_TLS_NEGOTIATED_VERSION, both of which are expected to be
unsigned integers. Normally only the first of these will be set and represents
the TLS protocol version that was first requested by the client (e.g. 0x0303 for
TLSv1.2, 0x0302 for TLSv1.1 etc). Historically some buggy clients would use the
negotiated protocol version instead of the protocol version first requested. If
this behaviour should be tolerated then
OSSL_ASYM_CIPHER_PARAM_TLS_NEGOTIATED_VERSION should be set to the actual
negotiated protocol version. Otherwise it should be left unset.
.SS "DSA parameters"
.IX Subsection "DSA parameters"
\&\fBEVP_PKEY_CTX_set_dsa_paramgen_bits()\fR sets the number of bits used for DSA
parameter generation to \fBnbits\fR. If not specified, 2048 is used.
.PP
\&\fBEVP_PKEY_CTX_set_dsa_paramgen_q_bits()\fR sets the number of bits in the subprime
parameter \fIq\fR for DSA parameter generation to \fIqbits\fR. If not specified, 224
is used. If a digest function is specified below, this parameter is ignored and
instead, the number of bits in \fIq\fR matches the size of the digest.
.PP
\&\fBEVP_PKEY_CTX_set_dsa_paramgen_md()\fR sets the digest function used for DSA
parameter generation to \fImd\fR. If not specified, one of SHA\-1, SHA\-224, or
SHA\-256 is selected to match the bit length of \fIq\fR above.
.PP
\&\fBEVP_PKEY_CTX_set_dsa_paramgen_md_props()\fR sets the digest function used for DSA
parameter generation using \fImd_name\fR and \fImd_properties\fR to retrieve the
digest from a provider.
If not specified, \fImd_name\fR will be set to one of SHA\-1, SHA\-224, or
SHA\-256 depending on the bit length of \fIq\fR above. \fImd_properties\fR is a
property query string that has a default value of '' if not specified.
.PP
\&\fBEVP_PKEY_CTX_set_dsa_paramgen_gindex()\fR sets the \fIgindex\fR used by the generator
G. The default value is \-1 which uses unverifiable g, otherwise a positive value
uses verifiable g. This value must be saved if key validation of g is required,
since it is not part of a persisted key.
.PP
\&\fBEVP_PKEY_CTX_set_dsa_paramgen_seed()\fR sets the \fIseed\fR to use for generation
rather than using a randomly generated value for the seed. This is useful for
testing purposes only and can fail if the seed does not produce primes for both
p & q on its first iteration. This value must be saved if key validation of
p, q, and verifiable g are required, since it is not part of a persisted key.
.PP
\&\fBEVP_PKEY_CTX_set_dsa_paramgen_type()\fR sets the generation type to use FIPS186\-4
generation if \fIname\fR is "fips186_4", or FIPS186\-2 generation if \fIname\fR is
"fips186_2". The default value for the default provider is "fips186_2". The
default value for the FIPS provider is "fips186_4".
.SS "DH parameters"
.IX Subsection "DH parameters"
\&\fBEVP_PKEY_CTX_set_dh_paramgen_prime_len()\fR sets the length of the DH prime
parameter \fIp\fR for DH parameter generation. If this function is not called then
2048 is used. Only accepts lengths greater than or equal to 256.
.PP
\&\fBEVP_PKEY_CTX_set_dh_paramgen_subprime_len()\fR sets the length of the DH
optional subprime parameter \fIq\fR for DH parameter generation. The default is
256 if the prime is at least 2048 bits long or 160 otherwise. The DH paramgen
type must have been set to "fips186_4".
.PP
\&\fBEVP_PKEY_CTX_set_dh_paramgen_generator()\fR sets DH generator to \fIgen\fR for DH
parameter generation. If not specified 2 is used.
.PP
\&\fBEVP_PKEY_CTX_set_dh_paramgen_type()\fR sets the key type for DH parameter
generation. The supported parameters are:
.IP \fBDH_PARAMGEN_TYPE_GROUP\fR 4
.IX Item "DH_PARAMGEN_TYPE_GROUP"
Use a named group. If only the safe prime parameter \fIp\fR is set this can be
used to select a ffdhe safe prime group of the correct size.
.IP \fBDH_PARAMGEN_TYPE_FIPS_186_4\fR 4
.IX Item "DH_PARAMGEN_TYPE_FIPS_186_4"
FIPS186\-4 FFC parameter generator.
.IP \fBDH_PARAMGEN_TYPE_FIPS_186_2\fR 4
.IX Item "DH_PARAMGEN_TYPE_FIPS_186_2"
FIPS186\-2 FFC parameter generator (X9.42 DH).
.IP \fBDH_PARAMGEN_TYPE_GENERATOR\fR 4
.IX Item "DH_PARAMGEN_TYPE_GENERATOR"
Uses a safe prime generator g (PKCS#3 format).
.PP
The default in the default provider is \fBDH_PARAMGEN_TYPE_GENERATOR\fR for the
"DH" keytype, and \fBDH_PARAMGEN_TYPE_FIPS_186_2\fR for the "DHX" keytype. In the
FIPS provider the default value is \fBDH_PARAMGEN_TYPE_GROUP\fR for the "DH"
keytype and <\fBDH_PARAMGEN_TYPE_FIPS_186_4\fR for the "DHX" keytype.
.PP
\&\fBEVP_PKEY_CTX_set_dh_paramgen_gindex()\fR sets the \fIgindex\fR used by the generator G.
The default value is \-1 which uses unverifiable g, otherwise a positive value
uses verifiable g. This value must be saved if key validation of g is required,
since it is not part of a persisted key.
.PP
\&\fBEVP_PKEY_CTX_set_dh_paramgen_seed()\fR sets the \fIseed\fR to use for generation
rather than using a randomly generated value for the seed. This is useful for
testing purposes only and can fail if the seed does not produce primes for both
p & q on its first iteration. This value must be saved if key validation of p, q,
and verifiable g are required, since it is not part of a persisted key.
.PP
\&\fBEVP_PKEY_CTX_set_dh_pad()\fR sets the DH padding mode.
If \fIpad\fR is 1 the shared secret is padded with zeros up to the size of the DH
prime \fIp\fR.
If \fIpad\fR is zero (the default) then no padding is performed.
.PP
\&\fBEVP_PKEY_CTX_set_dh_nid()\fR sets the DH parameters to values corresponding to
\&\fInid\fR as defined in RFC7919 or RFC3526. The \fInid\fR parameter must be
\&\fBNID_ffdhe2048\fR, \fBNID_ffdhe3072\fR, \fBNID_ffdhe4096\fR, \fBNID_ffdhe6144\fR,
\&\fBNID_ffdhe8192\fR, \fBNID_modp_1536\fR, \fBNID_modp_2048\fR, \fBNID_modp_3072\fR,
\&\fBNID_modp_4096\fR, \fBNID_modp_6144\fR, \fBNID_modp_8192\fR or \fBNID_undef\fR to clear
the stored value. This function can be called during parameter or key generation.
The nid parameter and the rfc5114 parameter are mutually exclusive.
.PP
\&\fBEVP_PKEY_CTX_set_dh_rfc5114()\fR and \fBEVP_PKEY_CTX_set_dhx_rfc5114()\fR both set the
DH parameters to the values defined in RFC5114. The \fIrfc5114\fR parameter must
be 1, 2 or 3 corresponding to RFC5114 sections 2.1, 2.2 and 2.3. or 0 to clear
the stored value. This macro can be called during parameter generation. The
\&\fIctx\fR must have a key type of \fBEVP_PKEY_DHX\fR.
The rfc5114 parameter and the nid parameter are mutually exclusive.
.SS "DH key derivation function parameters"
.IX Subsection "DH key derivation function parameters"
Note that all of the following functions require that the \fIctx\fR parameter has
a private key type of \fBEVP_PKEY_DHX\fR. When using key derivation, the output of
\&\fBEVP_PKEY_derive()\fR is the output of the KDF instead of the DH shared secret.
The KDF output is typically used as a Key Encryption Key (KEK) that in turn
encrypts a Content Encryption Key (CEK).
.PP
\&\fBEVP_PKEY_CTX_set_dh_kdf_type()\fR sets the key derivation function type to \fIkdf\fR
for DH key derivation. Possible values are \fBEVP_PKEY_DH_KDF_NONE\fR and
\&\fBEVP_PKEY_DH_KDF_X9_42\fR which uses the key derivation specified in RFC2631
(based on the keying algorithm described in X9.42). When using key derivation,
the \fIkdf_oid\fR, \fIkdf_md\fR and \fIkdf_outlen\fR parameters must also be specified.
.PP
\&\fBEVP_PKEY_CTX_get_dh_kdf_type()\fR gets the key derivation function type for \fIctx\fR
used for DH key derivation. Possible values are \fBEVP_PKEY_DH_KDF_NONE\fR and
\&\fBEVP_PKEY_DH_KDF_X9_42\fR.
.PP
\&\fBEVP_PKEY_CTX_set0_dh_kdf_oid()\fR sets the key derivation function object
identifier to \fIoid\fR for DH key derivation. This OID should identify the
algorithm to be used with the Content Encryption Key.
The library takes ownership of the object identifier so the caller should not
free the original memory pointed to by \fIoid\fR.
.PP
\&\fBEVP_PKEY_CTX_get0_dh_kdf_oid()\fR gets the key derivation function oid for \fIctx\fR
used for DH key derivation. The resulting pointer is owned by the library and
should not be freed by the caller.
.PP
\&\fBEVP_PKEY_CTX_set_dh_kdf_md()\fR sets the key derivation function message digest to
\&\fImd\fR for DH key derivation. Note that RFC2631 specifies that this digest should
be SHA1 but OpenSSL tolerates other digests.
.PP
\&\fBEVP_PKEY_CTX_get_dh_kdf_md()\fR gets the key derivation function message digest for
\&\fIctx\fR used for DH key derivation.
.PP
\&\fBEVP_PKEY_CTX_set_dh_kdf_outlen()\fR sets the key derivation function output length
to \fIlen\fR for DH key derivation.
.PP
\&\fBEVP_PKEY_CTX_get_dh_kdf_outlen()\fR gets the key derivation function output length
for \fIctx\fR used for DH key derivation.
.PP
\&\fBEVP_PKEY_CTX_set0_dh_kdf_ukm()\fR sets the user key material to \fIukm\fR and its
length to \fIlen\fR for DH key derivation. This parameter is optional and
corresponds to the partyAInfo field in RFC2631 terms. The specification
requires that it is 512 bits long but this is not enforced by OpenSSL.
The library takes ownership of the user key material so the caller should not
free the original memory pointed to by \fIukm\fR.
.PP
\&\fBEVP_PKEY_CTX_get0_dh_kdf_ukm()\fR gets the user key material for \fIctx\fR.
The return value is the user key material length. The resulting pointer is owned
by the library and should not be freed by the caller.
.SS "EC parameters"
.IX Subsection "EC parameters"
Use \fBEVP_PKEY_CTX_set_group_name()\fR (described above) to set the curve name to
\&\fIname\fR for parameter and key generation.
.PP
\&\fBEVP_PKEY_CTX_set_ec_paramgen_curve_nid()\fR does the same as
\&\fBEVP_PKEY_CTX_set_group_name()\fR, but is specific to EC and uses a \fInid\fR rather
than a name string.
.PP
For EC parameter generation, one of \fBEVP_PKEY_CTX_set_group_name()\fR
or \fBEVP_PKEY_CTX_set_ec_paramgen_curve_nid()\fR must be called or an error occurs
because there is no default curve.
These function can also be called to set the curve explicitly when
generating an EC key.
.PP
\&\fBEVP_PKEY_CTX_get_group_name()\fR (described above) can be used to obtain the curve
name that's currently set with \fIctx\fR.
.PP
\&\fBEVP_PKEY_CTX_set_ec_param_enc()\fR sets the EC parameter encoding to \fIparam_enc\fR
when generating EC parameters or an EC key. The encoding can be
\&\fBOPENSSL_EC_EXPLICIT_CURVE\fR for explicit parameters (the default in versions
of OpenSSL before 1.1.0) or \fBOPENSSL_EC_NAMED_CURVE\fR to use named curve form.
For maximum compatibility the named curve form should be used. Note: the
\&\fBOPENSSL_EC_NAMED_CURVE\fR value was added in OpenSSL 1.1.0; previous
versions should use 0 instead.
.SS "ECDH parameters"
.IX Subsection "ECDH parameters"
\&\fBEVP_PKEY_CTX_set_ecdh_cofactor_mode()\fR sets the cofactor mode to \fIcofactor_mode\fR
for ECDH key derivation. Possible values are 1 to enable cofactor
key derivation, 0 to disable it and \-1 to clear the stored cofactor mode and
fallback to the private key cofactor mode.
.PP
\&\fBEVP_PKEY_CTX_get_ecdh_cofactor_mode()\fR returns the cofactor mode for \fIctx\fR used
for ECDH key derivation. Possible values are 1 when cofactor key derivation is
enabled and 0 otherwise.
.SS "ECDH key derivation function parameters"
.IX Subsection "ECDH key derivation function parameters"
\&\fBEVP_PKEY_CTX_set_ecdh_kdf_type()\fR sets the key derivation function type to
\&\fIkdf\fR for ECDH key derivation. Possible values are \fBEVP_PKEY_ECDH_KDF_NONE\fR
and \fBEVP_PKEY_ECDH_KDF_X9_63\fR which uses the key derivation specified in X9.63.
When using key derivation, the \fIkdf_md\fR and \fIkdf_outlen\fR parameters must
also be specified.
.PP
\&\fBEVP_PKEY_CTX_get_ecdh_kdf_type()\fR returns the key derivation function type for
\&\fIctx\fR used for ECDH key derivation. Possible values are
\&\fBEVP_PKEY_ECDH_KDF_NONE\fR and \fBEVP_PKEY_ECDH_KDF_X9_63\fR.
.PP
\&\fBEVP_PKEY_CTX_set_ecdh_kdf_md()\fR sets the key derivation function message digest
to \fImd\fR for ECDH key derivation. Note that X9.63 specifies that this digest
should be SHA1 but OpenSSL tolerates other digests.
.PP
\&\fBEVP_PKEY_CTX_get_ecdh_kdf_md()\fR gets the key derivation function message digest
for \fIctx\fR used for ECDH key derivation.
.PP
\&\fBEVP_PKEY_CTX_set_ecdh_kdf_outlen()\fR sets the key derivation function output
length to \fIlen\fR for ECDH key derivation.
.PP
\&\fBEVP_PKEY_CTX_get_ecdh_kdf_outlen()\fR gets the key derivation function output
length for \fIctx\fR used for ECDH key derivation.
.PP
\&\fBEVP_PKEY_CTX_set0_ecdh_kdf_ukm()\fR sets the user key material to \fIukm\fR for ECDH
key derivation. This parameter is optional and corresponds to the shared info in
X9.63 terms. The library takes ownership of the user key material so the caller
should not free the original memory pointed to by \fIukm\fR.
.PP
\&\fBEVP_PKEY_CTX_get0_ecdh_kdf_ukm()\fR gets the user key material for \fIctx\fR.
The return value is the user key material length. The resulting pointer is owned
by the library and should not be freed by the caller.
.SS "Other parameters"
.IX Subsection "Other parameters"
\&\fBEVP_PKEY_CTX_set1_id()\fR, \fBEVP_PKEY_CTX_get1_id()\fR and \fBEVP_PKEY_CTX_get1_id_len()\fR
are used to manipulate the special identifier field for specific signature
algorithms such as SM2. The \fBEVP_PKEY_CTX_set1_id()\fR sets an ID pointed by \fIid\fR with
the length \fIid_len\fR to the library. The library takes a copy of the id so that
the caller can safely free the original memory pointed to by \fIid\fR.
\&\fBEVP_PKEY_CTX_get1_id_len()\fR returns the length of the ID set via a previous call
to \fBEVP_PKEY_CTX_set1_id()\fR. The length is usually used to allocate adequate
memory for further calls to \fBEVP_PKEY_CTX_get1_id()\fR. \fBEVP_PKEY_CTX_get1_id()\fR
returns the previously set ID value to caller in \fIid\fR. The caller should
allocate adequate memory space for the \fIid\fR before calling \fBEVP_PKEY_CTX_get1_id()\fR.
.PP
\&\fBEVP_PKEY_CTX_set_kem_op()\fR sets the KEM operation to run. This can be set after
\&\fBEVP_PKEY_encapsulate_init()\fR or \fBEVP_PKEY_decapsulate_init()\fR to select the
kem operation. RSA is the only key type that supports encapsulation currently,
and as there is no default operation for the RSA type, this function must be
called before \fBEVP_PKEY_encapsulate()\fR or \fBEVP_PKEY_decapsulate()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All other functions described on this page return a positive value for success
and 0 or a negative value for failure. In particular a return value of \-2
indicates the operation is not supported by the public key algorithm.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_set_params\fR\|(3),
\&\fBEVP_PKEY_CTX_new\fR\|(3),
\&\fBEVP_PKEY_encrypt\fR\|(3),
\&\fBEVP_PKEY_decrypt\fR\|(3),
\&\fBEVP_PKEY_sign\fR\|(3),
\&\fBEVP_PKEY_verify\fR\|(3),
\&\fBEVP_PKEY_verify_recover\fR\|(3),
\&\fBEVP_PKEY_derive\fR\|(3),
\&\fBEVP_PKEY_keygen\fR\|(3)
\&\fBEVP_PKEY_encapsulate\fR\|(3)
\&\fBEVP_PKEY_decapsulate\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBEVP_PKEY_CTX_get_rsa_oaep_md_name()\fR, \fBEVP_PKEY_CTX_get_rsa_mgf1_md_name()\fR,
\&\fBEVP_PKEY_CTX_set_rsa_mgf1_md_name()\fR, \fBEVP_PKEY_CTX_set_rsa_oaep_md_name()\fR,
\&\fBEVP_PKEY_CTX_set_dsa_paramgen_md_props()\fR, \fBEVP_PKEY_CTX_set_dsa_paramgen_gindex()\fR,
\&\fBEVP_PKEY_CTX_set_dsa_paramgen_type()\fR, \fBEVP_PKEY_CTX_set_dsa_paramgen_seed()\fR,
\&\fBEVP_PKEY_CTX_set_group_name()\fR and \fBEVP_PKEY_CTX_get_group_name()\fR
were added in OpenSSL 3.0.
.PP
The \fBEVP_PKEY_CTX_set1_id()\fR, \fBEVP_PKEY_CTX_get1_id()\fR and
\&\fBEVP_PKEY_CTX_get1_id_len()\fR macros were added in 1.1.1, other functions were
added in OpenSSL 1.0.0.
.PP
In OpenSSL 1.1.1 and below the functions were mostly macros.
From OpenSSL 3.0 they are all functions.
.PP
\&\fBEVP_PKEY_CTX_set_rsa_keygen_pubexp()\fR, \fBEVP_PKEY_CTX_get0_dh_kdf_ukm()\fR,
and \fBEVP_PKEY_CTX_get0_ecdh_kdf_ukm()\fR were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2006\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
