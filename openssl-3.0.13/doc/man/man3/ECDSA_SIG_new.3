.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ECDSA_SIG_NEW 3ossl"
.TH ECDSA_SIG_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ECDSA_SIG_new, ECDSA_SIG_free,
ECDSA_SIG_get0, ECDSA_SIG_get0_r, ECDSA_SIG_get0_s, ECDSA_SIG_set0
\&\- Functions for creating, destroying and manipulating ECDSA_SIG objects
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ecdsa.h>
\&
\& ECDSA_SIG *ECDSA_SIG_new(void);
\& void ECDSA_SIG_free(ECDSA_SIG *sig);
\& void ECDSA_SIG_get0(const ECDSA_SIG *sig, const BIGNUM **pr, const BIGNUM **ps);
\& const BIGNUM *ECDSA_SIG_get0_r(const ECDSA_SIG *sig);
\& const BIGNUM *ECDSA_SIG_get0_s(const ECDSA_SIG *sig);
\& int ECDSA_SIG_set0(ECDSA_SIG *sig, BIGNUM *r, BIGNUM *s);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBECDSA_SIG\fR is an opaque structure consisting of two BIGNUMs for the
\&\fIr\fR and \fIs\fR value of an Elliptic Curve Digital Signature Algorithm (ECDSA) signature
(see FIPS186\-4 or X9.62).
The \fBECDSA_SIG\fR object was mainly used by the deprecated low level functions described in
\&\fBECDSA_sign\fR\|(3), it is still required in order to be able to set or get the values of
\&\fIr\fR and \fIs\fR into or from a signature. This is mainly used for testing purposes as shown
in the "EXAMPLES".
.PP
\&\fBECDSA_SIG_new()\fR allocates an empty \fBECDSA_SIG\fR structure.
Note: before OpenSSL 1.1.0, the \fIr\fR and \fIs\fR components were initialised.
.PP
\&\fBECDSA_SIG_free()\fR frees the \fBECDSA_SIG\fR structure \fIsig\fR.
.PP
\&\fBECDSA_SIG_get0()\fR returns internal pointers the \fIr\fR and \fIs\fR values contained
in \fIsig\fR and stores them in \fI*pr\fR and \fI*ps\fR, respectively.
The pointer \fIpr\fR or \fIps\fR can be NULL, in which case the corresponding value
is not returned.
.PP
The values \fIr\fR, \fIs\fR can also be retrieved separately by the corresponding
function \fBECDSA_SIG_get0_r()\fR and \fBECDSA_SIG_get0_s()\fR, respectively.
.PP
Non-NULL \fIr\fR and \fIs\fR values can be set on the \fIsig\fR by calling
\&\fBECDSA_SIG_set0()\fR. Calling this function transfers the memory management of the
values to the \fBECDSA_SIG\fR object, and therefore the values that have been
passed in should not be freed by the caller.
.PP
See \fBi2d_ECDSA_SIG\fR\|(3) and \fBd2i_ECDSA_SIG\fR\|(3) for information about encoding
and decoding ECDSA signatures to/from DER.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBECDSA_SIG_new()\fR returns NULL if the allocation fails.
.PP
\&\fBECDSA_SIG_set0()\fR returns 1 on success or 0 on failure.
.PP
\&\fBECDSA_SIG_get0_r()\fR and \fBECDSA_SIG_get0_s()\fR return the corresponding value,
or NULL if it is unset.
.SH EXAMPLES
.IX Header "EXAMPLES"
Extract signature \fIr\fR and \fIs\fR values from a ECDSA \fIsignature\fR
of size \fIsignaturelen\fR:
.PP
.Vb 2
\& ECDSA_SIG *obj;
\& const BIGNUM *r, *s;
\&
\& /* Load a signature into the ECDSA_SIG object */
\& obj = d2i_ECDSA_SIG(NULL, &signature, signaturelen);
\& if (obj == NULL)
\&     /* error */
\&
\& r = ECDSA_SIG_get0_r(obj);
\& s = ECDSA_SIG_get0_s(obj);
\& if (r == NULL || s == NULL)
\&     /* error */
\&
\& /* Use BN_bn2binpad() here to convert to r and s into byte arrays */
\&
\& /*
\&  * Do not try to access I<r> or I<s> after calling ECDSA_SIG_free(),
\&  * as they are both freed by this call.
\&  */
\& ECDSA_SIG_free(obj);
.Ve
.PP
Convert \fIr\fR and \fIs\fR byte arrays into an ECDSA_SIG \fIsignature\fR of
size \fIsignaturelen\fR:
.PP
.Vb 4
\& ECDSA_SIG *obj = NULL;
\& unsigned char *signature = NULL;
\& size_t signaturelen;
\& BIGNUM *rbn = NULL, *sbn = NULL;
\&
\& obj = ECDSA_SIG_new();
\& if (obj == NULL)
\&     /* error */
\& rbn = BN_bin2bn(r, rlen, NULL);
\& sbn = BN_bin2bn(s, slen, NULL);
\& if (rbn == NULL || sbn == NULL)
\&     /* error */
\&
\& if (!ECDSA_SIG_set0(obj, rbn, sbn))
\&     /* error */
\& /* Set these to NULL since they are now owned by obj */
\& rbn = sbn = NULL;
\&
\& signaturelen = i2d_ECDSA_SIG(obj, &signature);
\& if (signaturelen <= 0)
\&     /* error */
\&
\& /*
\&  * This signature could now be passed to L<EVP_DigestVerify(3)>
\&  * or L<EVP_DigestVerifyFinal(3)>
\&  */
\&
\& BN_free(rbn);
\& BN_free(sbn);
\& OPENSSL_free(signature);
\& ECDSA_SIG_free(obj);
.Ve
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
ANSI X9.62,
US Federal Information Processing Standard FIPS186\-4
(Digital Signature Standard, DSS)
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEC_KEY_new\fR\|(3),
\&\fBEVP_DigestSignInit\fR\|(3),
\&\fBEVP_DigestVerifyInit\fR\|(3),
\&\fBEVP_PKEY_sign\fR\|(3)
\&\fBi2d_ECDSA_SIG\fR\|(3),
\&\fBd2i_ECDSA_SIG\fR\|(3),
\&\fBECDSA_sign\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2004\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
