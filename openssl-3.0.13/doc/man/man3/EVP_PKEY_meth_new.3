.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_METH_NEW 3ossl"
.TH EVP_PKEY_METH_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_meth_new, EVP_PKEY_meth_free, EVP_PKEY_meth_copy, EVP_PKEY_meth_find,
EVP_PKEY_meth_add0, EVP_PKEY_METHOD,
EVP_PKEY_meth_set_init, EVP_PKEY_meth_set_copy, EVP_PKEY_meth_set_cleanup,
EVP_PKEY_meth_set_paramgen, EVP_PKEY_meth_set_keygen, EVP_PKEY_meth_set_sign,
EVP_PKEY_meth_set_verify, EVP_PKEY_meth_set_verify_recover, EVP_PKEY_meth_set_signctx,
EVP_PKEY_meth_set_verifyctx, EVP_PKEY_meth_set_encrypt, EVP_PKEY_meth_set_decrypt,
EVP_PKEY_meth_set_derive, EVP_PKEY_meth_set_ctrl,
EVP_PKEY_meth_set_digestsign, EVP_PKEY_meth_set_digestverify,
EVP_PKEY_meth_set_check,
EVP_PKEY_meth_set_public_check, EVP_PKEY_meth_set_param_check,
EVP_PKEY_meth_set_digest_custom,
EVP_PKEY_meth_get_init, EVP_PKEY_meth_get_copy, EVP_PKEY_meth_get_cleanup,
EVP_PKEY_meth_get_paramgen, EVP_PKEY_meth_get_keygen, EVP_PKEY_meth_get_sign,
EVP_PKEY_meth_get_verify, EVP_PKEY_meth_get_verify_recover, EVP_PKEY_meth_get_signctx,
EVP_PKEY_meth_get_verifyctx, EVP_PKEY_meth_get_encrypt, EVP_PKEY_meth_get_decrypt,
EVP_PKEY_meth_get_derive, EVP_PKEY_meth_get_ctrl,
EVP_PKEY_meth_get_digestsign, EVP_PKEY_meth_get_digestverify,
EVP_PKEY_meth_get_check,
EVP_PKEY_meth_get_public_check, EVP_PKEY_meth_get_param_check,
EVP_PKEY_meth_get_digest_custom,
EVP_PKEY_meth_remove
\&\- manipulating EVP_PKEY_METHOD structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& typedef struct evp_pkey_method_st EVP_PKEY_METHOD;
\&
\& EVP_PKEY_METHOD *EVP_PKEY_meth_new(int id, int flags);
\& void EVP_PKEY_meth_free(EVP_PKEY_METHOD *pmeth);
\& void EVP_PKEY_meth_copy(EVP_PKEY_METHOD *dst, const EVP_PKEY_METHOD *src);
\& const EVP_PKEY_METHOD *EVP_PKEY_meth_find(int type);
\& int EVP_PKEY_meth_add0(const EVP_PKEY_METHOD *pmeth);
\& int EVP_PKEY_meth_remove(const EVP_PKEY_METHOD *pmeth);
\&
\& void EVP_PKEY_meth_set_init(EVP_PKEY_METHOD *pmeth,
\&                             int (*init) (EVP_PKEY_CTX *ctx));
\& void EVP_PKEY_meth_set_copy(EVP_PKEY_METHOD *pmeth,
\&                             int (*copy) (EVP_PKEY_CTX *dst,
\&                                          const EVP_PKEY_CTX *src));
\& void EVP_PKEY_meth_set_cleanup(EVP_PKEY_METHOD *pmeth,
\&                                void (*cleanup) (EVP_PKEY_CTX *ctx));
\& void EVP_PKEY_meth_set_paramgen(EVP_PKEY_METHOD *pmeth,
\&                                 int (*paramgen_init) (EVP_PKEY_CTX *ctx),
\&                                 int (*paramgen) (EVP_PKEY_CTX *ctx,
\&                                                  EVP_PKEY *pkey));
\& void EVP_PKEY_meth_set_keygen(EVP_PKEY_METHOD *pmeth,
\&                               int (*keygen_init) (EVP_PKEY_CTX *ctx),
\&                               int (*keygen) (EVP_PKEY_CTX *ctx,
\&                                              EVP_PKEY *pkey));
\& void EVP_PKEY_meth_set_sign(EVP_PKEY_METHOD *pmeth,
\&                             int (*sign_init) (EVP_PKEY_CTX *ctx),
\&                             int (*sign) (EVP_PKEY_CTX *ctx,
\&                                          unsigned char *sig, size_t *siglen,
\&                                          const unsigned char *tbs,
\&                                          size_t tbslen));
\& void EVP_PKEY_meth_set_verify(EVP_PKEY_METHOD *pmeth,
\&                               int (*verify_init) (EVP_PKEY_CTX *ctx),
\&                               int (*verify) (EVP_PKEY_CTX *ctx,
\&                                              const unsigned char *sig,
\&                                              size_t siglen,
\&                                              const unsigned char *tbs,
\&                                              size_t tbslen));
\& void EVP_PKEY_meth_set_verify_recover(EVP_PKEY_METHOD *pmeth,
\&                                       int (*verify_recover_init) (EVP_PKEY_CTX
\&                                                                   *ctx),
\&                                       int (*verify_recover) (EVP_PKEY_CTX
\&                                                              *ctx,
\&                                                              unsigned char
\&                                                              *sig,
\&                                                              size_t *siglen,
\&                                                              const unsigned
\&                                                              char *tbs,
\&                                                              size_t tbslen));
\& void EVP_PKEY_meth_set_signctx(EVP_PKEY_METHOD *pmeth,
\&                                int (*signctx_init) (EVP_PKEY_CTX *ctx,
\&                                                     EVP_MD_CTX *mctx),
\&                                int (*signctx) (EVP_PKEY_CTX *ctx,
\&                                                unsigned char *sig,
\&                                                size_t *siglen,
\&                                                EVP_MD_CTX *mctx));
\& void EVP_PKEY_meth_set_verifyctx(EVP_PKEY_METHOD *pmeth,
\&                                  int (*verifyctx_init) (EVP_PKEY_CTX *ctx,
\&                                                         EVP_MD_CTX *mctx),
\&                                  int (*verifyctx) (EVP_PKEY_CTX *ctx,
\&                                                    const unsigned char *sig,
\&                                                    int siglen,
\&                                                    EVP_MD_CTX *mctx));
\& void EVP_PKEY_meth_set_encrypt(EVP_PKEY_METHOD *pmeth,
\&                                int (*encrypt_init) (EVP_PKEY_CTX *ctx),
\&                                int (*encryptfn) (EVP_PKEY_CTX *ctx,
\&                                                  unsigned char *out,
\&                                                  size_t *outlen,
\&                                                  const unsigned char *in,
\&                                                  size_t inlen));
\& void EVP_PKEY_meth_set_decrypt(EVP_PKEY_METHOD *pmeth,
\&                                int (*decrypt_init) (EVP_PKEY_CTX *ctx),
\&                                int (*decrypt) (EVP_PKEY_CTX *ctx,
\&                                                unsigned char *out,
\&                                                size_t *outlen,
\&                                                const unsigned char *in,
\&                                                size_t inlen));
\& void EVP_PKEY_meth_set_derive(EVP_PKEY_METHOD *pmeth,
\&                               int (*derive_init) (EVP_PKEY_CTX *ctx),
\&                               int (*derive) (EVP_PKEY_CTX *ctx,
\&                                              unsigned char *key,
\&                                              size_t *keylen));
\& void EVP_PKEY_meth_set_ctrl(EVP_PKEY_METHOD *pmeth,
\&                             int (*ctrl) (EVP_PKEY_CTX *ctx, int type, int p1,
\&                                          void *p2),
\&                             int (*ctrl_str) (EVP_PKEY_CTX *ctx,
\&                                              const char *type,
\&                                              const char *value));
\& void EVP_PKEY_meth_set_digestsign(EVP_PKEY_METHOD *pmeth,
\&                                   int (*digestsign) (EVP_MD_CTX *ctx,
\&                                                      unsigned char *sig,
\&                                                      size_t *siglen,
\&                                                      const unsigned char *tbs,
\&                                                      size_t tbslen));
\& void EVP_PKEY_meth_set_digestverify(EVP_PKEY_METHOD *pmeth,
\&                                     int (*digestverify) (EVP_MD_CTX *ctx,
\&                                                          const unsigned char *sig,
\&                                                          size_t siglen,
\&                                                          const unsigned char *tbs,
\&                                                          size_t tbslen));
\& void EVP_PKEY_meth_set_check(EVP_PKEY_METHOD *pmeth,
\&                              int (*check) (EVP_PKEY *pkey));
\& void EVP_PKEY_meth_set_public_check(EVP_PKEY_METHOD *pmeth,
\&                                     int (*check) (EVP_PKEY *pkey));
\& void EVP_PKEY_meth_set_param_check(EVP_PKEY_METHOD *pmeth,
\&                                    int (*check) (EVP_PKEY *pkey));
\& void EVP_PKEY_meth_set_digest_custom(EVP_PKEY_METHOD *pmeth,
\&                                     int (*digest_custom) (EVP_PKEY_CTX *ctx,
\&                                                           EVP_MD_CTX *mctx));
\&
\& void EVP_PKEY_meth_get_init(const EVP_PKEY_METHOD *pmeth,
\&                             int (**pinit) (EVP_PKEY_CTX *ctx));
\& void EVP_PKEY_meth_get_copy(const EVP_PKEY_METHOD *pmeth,
\&                             int (**pcopy) (EVP_PKEY_CTX *dst,
\&                                            EVP_PKEY_CTX *src));
\& void EVP_PKEY_meth_get_cleanup(const EVP_PKEY_METHOD *pmeth,
\&                                void (**pcleanup) (EVP_PKEY_CTX *ctx));
\& void EVP_PKEY_meth_get_paramgen(const EVP_PKEY_METHOD *pmeth,
\&                                 int (**pparamgen_init) (EVP_PKEY_CTX *ctx),
\&                                 int (**pparamgen) (EVP_PKEY_CTX *ctx,
\&                                                    EVP_PKEY *pkey));
\& void EVP_PKEY_meth_get_keygen(const EVP_PKEY_METHOD *pmeth,
\&                               int (**pkeygen_init) (EVP_PKEY_CTX *ctx),
\&                               int (**pkeygen) (EVP_PKEY_CTX *ctx,
\&                                                EVP_PKEY *pkey));
\& void EVP_PKEY_meth_get_sign(const EVP_PKEY_METHOD *pmeth,
\&                             int (**psign_init) (EVP_PKEY_CTX *ctx),
\&                             int (**psign) (EVP_PKEY_CTX *ctx,
\&                                            unsigned char *sig, size_t *siglen,
\&                                            const unsigned char *tbs,
\&                                            size_t tbslen));
\& void EVP_PKEY_meth_get_verify(const EVP_PKEY_METHOD *pmeth,
\&                               int (**pverify_init) (EVP_PKEY_CTX *ctx),
\&                               int (**pverify) (EVP_PKEY_CTX *ctx,
\&                                                const unsigned char *sig,
\&                                                size_t siglen,
\&                                                const unsigned char *tbs,
\&                                                size_t tbslen));
\& void EVP_PKEY_meth_get_verify_recover(const EVP_PKEY_METHOD *pmeth,
\&                                       int (**pverify_recover_init) (EVP_PKEY_CTX
\&                                                                     *ctx),
\&                                       int (**pverify_recover) (EVP_PKEY_CTX
\&                                                                *ctx,
\&                                                                unsigned char
\&                                                                *sig,
\&                                                                size_t *siglen,
\&                                                                const unsigned
\&                                                                char *tbs,
\&                                                                size_t tbslen));
\& void EVP_PKEY_meth_get_signctx(const EVP_PKEY_METHOD *pmeth,
\&                                int (**psignctx_init) (EVP_PKEY_CTX *ctx,
\&                                                       EVP_MD_CTX *mctx),
\&                                int (**psignctx) (EVP_PKEY_CTX *ctx,
\&                                                  unsigned char *sig,
\&                                                  size_t *siglen,
\&                                                  EVP_MD_CTX *mctx));
\& void EVP_PKEY_meth_get_verifyctx(const EVP_PKEY_METHOD *pmeth,
\&                                  int (**pverifyctx_init) (EVP_PKEY_CTX *ctx,
\&                                                           EVP_MD_CTX *mctx),
\&                                  int (**pverifyctx) (EVP_PKEY_CTX *ctx,
\&                                                      const unsigned char *sig,
\&                                                      int siglen,
\&                                                      EVP_MD_CTX *mctx));
\& void EVP_PKEY_meth_get_encrypt(const EVP_PKEY_METHOD *pmeth,
\&                                int (**pencrypt_init) (EVP_PKEY_CTX *ctx),
\&                                int (**pencryptfn) (EVP_PKEY_CTX *ctx,
\&                                                    unsigned char *out,
\&                                                    size_t *outlen,
\&                                                    const unsigned char *in,
\&                                                    size_t inlen));
\& void EVP_PKEY_meth_get_decrypt(const EVP_PKEY_METHOD *pmeth,
\&                                int (**pdecrypt_init) (EVP_PKEY_CTX *ctx),
\&                                int (**pdecrypt) (EVP_PKEY_CTX *ctx,
\&                                                  unsigned char *out,
\&                                                  size_t *outlen,
\&                                                  const unsigned char *in,
\&                                                  size_t inlen));
\& void EVP_PKEY_meth_get_derive(const EVP_PKEY_METHOD *pmeth,
\&                               int (**pderive_init) (EVP_PKEY_CTX *ctx),
\&                               int (**pderive) (EVP_PKEY_CTX *ctx,
\&                                                unsigned char *key,
\&                                                size_t *keylen));
\& void EVP_PKEY_meth_get_ctrl(const EVP_PKEY_METHOD *pmeth,
\&                             int (**pctrl) (EVP_PKEY_CTX *ctx, int type, int p1,
\&                                            void *p2),
\&                             int (**pctrl_str) (EVP_PKEY_CTX *ctx,
\&                                                const char *type,
\&                                                const char *value));
\& void EVP_PKEY_meth_get_digestsign(const EVP_PKEY_METHOD *pmeth,
\&                                   int (**digestsign) (EVP_MD_CTX *ctx,
\&                                                       unsigned char *sig,
\&                                                       size_t *siglen,
\&                                                       const unsigned char *tbs,
\&                                                       size_t tbslen));
\& void EVP_PKEY_meth_get_digestverify(const EVP_PKEY_METHOD *pmeth,
\&                                     int (**digestverify) (EVP_MD_CTX *ctx,
\&                                                           const unsigned char *sig,
\&                                                           size_t siglen,
\&                                                           const unsigned char *tbs,
\&                                                           size_t tbslen));
\& void EVP_PKEY_meth_get_check(const EVP_PKEY_METHOD *pmeth,
\&                              int (**pcheck) (EVP_PKEY *pkey));
\& void EVP_PKEY_meth_get_public_check(const EVP_PKEY_METHOD *pmeth,
\&                                     int (**pcheck) (EVP_PKEY *pkey));
\& void EVP_PKEY_meth_get_param_check(const EVP_PKEY_METHOD *pmeth,
\&                                    int (**pcheck) (EVP_PKEY *pkey));
\& void EVP_PKEY_meth_get_digest_custom(const EVP_PKEY_METHOD *pmeth,
\&                                     int (**pdigest_custom) (EVP_PKEY_CTX *ctx,
\&                                                             EVP_MD_CTX *mctx));
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated.
Applications should instead use the OSSL_PROVIDER APIs.
.PP
\&\fBEVP_PKEY_METHOD\fR is a structure which holds a set of methods for a
specific public key cryptographic algorithm. Those methods are usually
used to perform different jobs, such as generating a key, signing or
verifying, encrypting or decrypting, etc.
.PP
There are two places where the \fBEVP_PKEY_METHOD\fR objects are stored: one
is a built-in static array representing the standard methods for different
algorithms, and the other one is a stack of user-defined application-specific
methods, which can be manipulated by using \fBEVP_PKEY_meth_add0\fR\|(3).
.PP
The \fBEVP_PKEY_METHOD\fR objects are usually referenced by \fBEVP_PKEY_CTX\fR
objects.
.SS Methods
.IX Subsection "Methods"
The methods are the underlying implementations of a particular public key
algorithm present by the \fBEVP_PKEY_CTX\fR object.
.PP
.Vb 3
\& int (*init) (EVP_PKEY_CTX *ctx);
\& int (*copy) (EVP_PKEY_CTX *dst, const EVP_PKEY_CTX *src);
\& void (*cleanup) (EVP_PKEY_CTX *ctx);
.Ve
.PP
The \fBinit()\fR method is called to initialize algorithm-specific data when a new
\&\fBEVP_PKEY_CTX\fR is created. As opposed to \fBinit()\fR, the \fBcleanup()\fR method is called
when an \fBEVP_PKEY_CTX\fR is freed. The \fBcopy()\fR method is called when an \fBEVP_PKEY_CTX\fR
is being duplicated. Refer to \fBEVP_PKEY_CTX_new\fR\|(3), \fBEVP_PKEY_CTX_new_id\fR\|(3),
\&\fBEVP_PKEY_CTX_free\fR\|(3) and \fBEVP_PKEY_CTX_dup\fR\|(3).
.PP
.Vb 2
\& int (*paramgen_init) (EVP_PKEY_CTX *ctx);
\& int (*paramgen) (EVP_PKEY_CTX *ctx, EVP_PKEY *pkey);
.Ve
.PP
The \fBparamgen_init()\fR and \fBparamgen()\fR methods deal with key parameter generation.
They are called by \fBEVP_PKEY_paramgen_init\fR\|(3) and \fBEVP_PKEY_paramgen\fR\|(3) to
handle the parameter generation process.
.PP
.Vb 2
\& int (*keygen_init) (EVP_PKEY_CTX *ctx);
\& int (*keygen) (EVP_PKEY_CTX *ctx, EVP_PKEY *pkey);
.Ve
.PP
The \fBkeygen_init()\fR and \fBkeygen()\fR methods are used to generate the actual key for
the specified algorithm. They are called by \fBEVP_PKEY_keygen_init\fR\|(3) and
\&\fBEVP_PKEY_keygen\fR\|(3).
.PP
.Vb 3
\& int (*sign_init) (EVP_PKEY_CTX *ctx);
\& int (*sign) (EVP_PKEY_CTX *ctx, unsigned char *sig, size_t *siglen,
\&              const unsigned char *tbs, size_t tbslen);
.Ve
.PP
The \fBsign_init()\fR and \fBsign()\fR methods are used to generate the signature of a
piece of data using a private key. They are called by \fBEVP_PKEY_sign_init\fR\|(3)
and \fBEVP_PKEY_sign\fR\|(3).
.PP
.Vb 4
\& int (*verify_init) (EVP_PKEY_CTX *ctx);
\& int (*verify) (EVP_PKEY_CTX *ctx,
\&                const unsigned char *sig, size_t siglen,
\&                const unsigned char *tbs, size_t tbslen);
.Ve
.PP
The \fBverify_init()\fR and \fBverify()\fR methods are used to verify whether a signature is
valid. They are called by \fBEVP_PKEY_verify_init\fR\|(3) and \fBEVP_PKEY_verify\fR\|(3).
.PP
.Vb 4
\& int (*verify_recover_init) (EVP_PKEY_CTX *ctx);
\& int (*verify_recover) (EVP_PKEY_CTX *ctx,
\&                        unsigned char *rout, size_t *routlen,
\&                        const unsigned char *sig, size_t siglen);
.Ve
.PP
The \fBverify_recover_init()\fR and \fBverify_recover()\fR methods are used to verify a
signature and then recover the digest from the signature (for instance, a
signature that was generated by RSA signing algorithm). They are called by
\&\fBEVP_PKEY_verify_recover_init\fR\|(3) and \fBEVP_PKEY_verify_recover\fR\|(3).
.PP
.Vb 3
\& int (*signctx_init) (EVP_PKEY_CTX *ctx, EVP_MD_CTX *mctx);
\& int (*signctx) (EVP_PKEY_CTX *ctx, unsigned char *sig, size_t *siglen,
\&                 EVP_MD_CTX *mctx);
.Ve
.PP
The \fBsignctx_init()\fR and \fBsignctx()\fR methods are used to sign a digest present by
a \fBEVP_MD_CTX\fR object. They are called by the EVP_DigestSign functions. See
\&\fBEVP_DigestSignInit\fR\|(3) for details.
.PP
.Vb 3
\& int (*verifyctx_init) (EVP_PKEY_CTX *ctx, EVP_MD_CTX *mctx);
\& int (*verifyctx) (EVP_PKEY_CTX *ctx, const unsigned char *sig, int siglen,
\&                   EVP_MD_CTX *mctx);
.Ve
.PP
The \fBverifyctx_init()\fR and \fBverifyctx()\fR methods are used to verify a signature
against the data in a \fBEVP_MD_CTX\fR object. They are called by the various
EVP_DigestVerify functions. See \fBEVP_DigestVerifyInit\fR\|(3) for details.
.PP
.Vb 3
\& int (*encrypt_init) (EVP_PKEY_CTX *ctx);
\& int (*encrypt) (EVP_PKEY_CTX *ctx, unsigned char *out, size_t *outlen,
\&                 const unsigned char *in, size_t inlen);
.Ve
.PP
The \fBencrypt_init()\fR and \fBencrypt()\fR methods are used to encrypt a piece of data.
They are called by \fBEVP_PKEY_encrypt_init\fR\|(3) and \fBEVP_PKEY_encrypt\fR\|(3).
.PP
.Vb 3
\& int (*decrypt_init) (EVP_PKEY_CTX *ctx);
\& int (*decrypt) (EVP_PKEY_CTX *ctx, unsigned char *out, size_t *outlen,
\&                 const unsigned char *in, size_t inlen);
.Ve
.PP
The \fBdecrypt_init()\fR and \fBdecrypt()\fR methods are used to decrypt a piece of data.
They are called by \fBEVP_PKEY_decrypt_init\fR\|(3) and \fBEVP_PKEY_decrypt\fR\|(3).
.PP
.Vb 2
\& int (*derive_init) (EVP_PKEY_CTX *ctx);
\& int (*derive) (EVP_PKEY_CTX *ctx, unsigned char *key, size_t *keylen);
.Ve
.PP
The \fBderive_init()\fR and \fBderive()\fR methods are used to derive the shared secret
from a public key algorithm (for instance, the DH algorithm). They are called by
\&\fBEVP_PKEY_derive_init\fR\|(3) and \fBEVP_PKEY_derive\fR\|(3).
.PP
.Vb 2
\& int (*ctrl) (EVP_PKEY_CTX *ctx, int type, int p1, void *p2);
\& int (*ctrl_str) (EVP_PKEY_CTX *ctx, const char *type, const char *value);
.Ve
.PP
The \fBctrl()\fR and \fBctrl_str()\fR methods are used to adjust algorithm-specific
settings. See \fBEVP_PKEY_CTX_ctrl\fR\|(3) and related functions for details.
.PP
.Vb 5
\& int (*digestsign) (EVP_MD_CTX *ctx, unsigned char *sig, size_t *siglen,
\&                    const unsigned char *tbs, size_t tbslen);
\& int (*digestverify) (EVP_MD_CTX *ctx, const unsigned char *sig,
\&                      size_t siglen, const unsigned char *tbs,
\&                      size_t tbslen);
.Ve
.PP
The \fBdigestsign()\fR and \fBdigestverify()\fR methods are used to generate or verify
a signature in a one-shot mode. They could be called by \fBEVP_DigestSign\fR\|(3)
and \fBEVP_DigestVerify\fR\|(3).
.PP
.Vb 3
\& int (*check) (EVP_PKEY *pkey);
\& int (*public_check) (EVP_PKEY *pkey);
\& int (*param_check) (EVP_PKEY *pkey);
.Ve
.PP
The \fBcheck()\fR, \fBpublic_check()\fR and \fBparam_check()\fR methods are used to validate a
key-pair, the public component and parameters respectively for a given \fBpkey\fR.
They could be called by \fBEVP_PKEY_check\fR\|(3), \fBEVP_PKEY_public_check\fR\|(3) and
\&\fBEVP_PKEY_param_check\fR\|(3) respectively.
.PP
.Vb 1
\& int (*digest_custom) (EVP_PKEY_CTX *ctx, EVP_MD_CTX *mctx);
.Ve
.PP
The \fBdigest_custom()\fR method is used to generate customized digest content before
the real message is passed to functions like \fBEVP_DigestSignUpdate\fR\|(3) or
\&\fBEVP_DigestVerifyInit\fR\|(3). This is usually required by some public key
signature algorithms like SM2 which requires a hashed prefix to the message to
be signed. The \fBdigest_custom()\fR function will be called by \fBEVP_DigestSignInit\fR\|(3)
and \fBEVP_DigestVerifyInit\fR\|(3).
.SS Functions
.IX Subsection "Functions"
\&\fBEVP_PKEY_meth_new()\fR creates and returns a new \fBEVP_PKEY_METHOD\fR object,
and associates the given \fBid\fR and \fBflags\fR. The following flags are
supported:
.PP
.Vb 2
\& EVP_PKEY_FLAG_AUTOARGLEN
\& EVP_PKEY_FLAG_SIGCTX_CUSTOM
.Ve
.PP
If an \fBEVP_PKEY_METHOD\fR is set with the \fBEVP_PKEY_FLAG_AUTOARGLEN\fR flag, the
maximum size of the output buffer will be automatically calculated or checked
in corresponding EVP methods by the EVP framework. Thus the implementations of
these methods don't need to care about handling the case of returning output
buffer size by themselves. For details on the output buffer size, refer to
\&\fBEVP_PKEY_sign\fR\|(3).
.PP
The \fBEVP_PKEY_FLAG_SIGCTX_CUSTOM\fR is used to indicate the \fBsignctx()\fR method
of an \fBEVP_PKEY_METHOD\fR is always called by the EVP framework while doing a
digest signing operation by calling \fBEVP_DigestSignFinal\fR\|(3).
.PP
\&\fBEVP_PKEY_meth_free()\fR frees an existing \fBEVP_PKEY_METHOD\fR pointed by
\&\fBpmeth\fR.
.PP
\&\fBEVP_PKEY_meth_copy()\fR copies an \fBEVP_PKEY_METHOD\fR object from \fBsrc\fR
to \fBdst\fR.
.PP
\&\fBEVP_PKEY_meth_find()\fR finds an \fBEVP_PKEY_METHOD\fR object with the \fBid\fR.
This function first searches through the user-defined method objects and
then the built-in objects.
.PP
\&\fBEVP_PKEY_meth_add0()\fR adds \fBpmeth\fR to the user defined stack of methods.
.PP
\&\fBEVP_PKEY_meth_remove()\fR removes an \fBEVP_PKEY_METHOD\fR object added by
\&\fBEVP_PKEY_meth_add0()\fR.
.PP
The EVP_PKEY_meth_set functions set the corresponding fields of
\&\fBEVP_PKEY_METHOD\fR structure with the arguments passed.
.PP
The EVP_PKEY_meth_get functions get the corresponding fields of
\&\fBEVP_PKEY_METHOD\fR structure to the arguments provided.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_meth_new()\fR returns a pointer to a new \fBEVP_PKEY_METHOD\fR
object or returns NULL on error.
.PP
\&\fBEVP_PKEY_meth_free()\fR and \fBEVP_PKEY_meth_copy()\fR do not return values.
.PP
\&\fBEVP_PKEY_meth_find()\fR returns a pointer to the found \fBEVP_PKEY_METHOD\fR
object or returns NULL if not found.
.PP
\&\fBEVP_PKEY_meth_add0()\fR returns 1 if method is added successfully or 0
if an error occurred.
.PP
\&\fBEVP_PKEY_meth_remove()\fR returns 1 if method is removed successfully or
0 if an error occurred.
.PP
All EVP_PKEY_meth_set and EVP_PKEY_meth_get functions have no return
values. For the 'get' functions, function pointers are returned by
arguments.
.SH HISTORY
.IX Header "HISTORY"
All of these functions were deprecated in OpenSSL 3.0.
.PP
The signature of the \fIcopy\fR functional argument of \fBEVP_PKEY_meth_set_copy()\fR
has changed in OpenSSL 3.0 so its \fIsrc\fR parameter is now constified.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
