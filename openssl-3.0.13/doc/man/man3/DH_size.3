.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DH_SIZE 3ossl"
.TH DH_SIZE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
DH_size, DH_bits, DH_security_bits \- get Diffie\-Hellman prime size and
security bits
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/dh.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& int DH_bits(const DH *dh);
\&
\& int DH_size(const DH *dh);
\&
\& int DH_security_bits(const DH *dh);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The functions described on this page are deprecated.
Applications should instead use \fBEVP_PKEY_get_bits\fR\|(3),
\&\fBEVP_PKEY_get_security_bits\fR\|(3) and \fBEVP_PKEY_get_size\fR\|(3).
.PP
\&\fBDH_bits()\fR returns the number of significant bits.
.PP
\&\fBdh\fR and \fBdh\->p\fR must not be \fBNULL\fR.
.PP
\&\fBDH_size()\fR returns the Diffie-Hellman prime size in bytes. It can be used
to determine how much memory must be allocated for the shared secret
computed by \fBDH_compute_key\fR\|(3).
.PP
\&\fBDH_security_bits()\fR returns the number of security bits of the given \fBdh\fR
key. See \fBBN_security_bits\fR\|(3).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBDH_bits()\fR returns the number of bits in the key, or \-1 if
\&\fBdh\fR doesn't hold any key parameters.
.PP
\&\fBDH_size()\fR returns the prime size of Diffie-Hellman in bytes, or \-1 if
\&\fBdh\fR doesn't hold any key parameters.
.PP
\&\fBDH_security_bits()\fR returns the number of security bits, or \-1 if
\&\fBdh\fR doesn't hold any key parameters.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_get_bits\fR\|(3),
\&\fBDH_new\fR\|(3), \fBDH_generate_key\fR\|(3),
\&\fBBN_num_bits\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
All functions were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
