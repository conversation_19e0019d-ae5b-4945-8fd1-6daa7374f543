.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS12_CREATE 3ossl"
.TH PKCS12_CREATE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS12_create, PKCS12_create_ex \- create a PKCS#12 structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pkcs12.h>
\&
\& PKCS12 *PKCS12_create(const char *pass, const char *name, EVP_PKEY *pkey,
\&                       X509 *cert, STACK_OF(X509) *ca,
\&                       int nid_key, int nid_cert, int iter, int mac_iter, int keytype);
\& PKCS12 *PKCS12_create_ex(const char *pass, const char *name, EVP_PKEY *pkey,
\&                          X509 *cert, STACK_OF(X509) *ca, int nid_key, int nid_cert,
\&                          int iter, int mac_iter, int keytype,
\&                          OSSL_LIB_CTX *ctx, const char *propq);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPKCS12_create()\fR creates a PKCS#12 structure.
.PP
\&\fIpass\fR is the passphrase to use. \fIname\fR is the \fBfriendlyName\fR to use for
the supplied certificate and key. \fIpkey\fR is the private key to include in
the structure and \fIcert\fR its corresponding certificates. \fIca\fR, if not \fBNULL\fR
is an optional set of certificates to also include in the structure.
.PP
\&\fInid_key\fR and \fInid_cert\fR are the encryption algorithms that should be used
for the key and certificate respectively. The modes
GCM, CCM, XTS, and OCB are unsupported. \fIiter\fR is the encryption algorithm
iteration count to use and \fImac_iter\fR is the MAC iteration count to use.
\&\fIkeytype\fR is the type of key.
.PP
\&\fBPKCS12_create_ex()\fR is identical to \fBPKCS12_create()\fR but allows for a library context
\&\fIctx\fR and property query \fIpropq\fR to be used to select algorithm implementations.
.SH NOTES
.IX Header "NOTES"
The parameters \fInid_key\fR, \fInid_cert\fR, \fIiter\fR, \fImac_iter\fR and \fIkeytype\fR
can all be set to zero and sensible defaults will be used.
.PP
These defaults are: AES password based encryption (PBES2 with PBKDF2 and
AES\-256\-CBC) for private keys and certificates, the PBKDF2 and MAC key
derivation iteration count of \fBPKCS12_DEFAULT_ITER\fR (currently 2048), and
MAC algorithm HMAC with SHA2\-256. The MAC key derivation algorithm used
for the outer PKCS#12 structure is PKCS12KDF.
.PP
The default MAC iteration count is 1 in order to retain compatibility with
old software which did not interpret MAC iteration counts. If such compatibility
is not required then \fImac_iter\fR should be set to PKCS12_DEFAULT_ITER.
.PP
\&\fIkeytype\fR adds a flag to the store private key. This is a non standard extension
that is only currently interpreted by MSIE. If set to zero the flag is omitted,
if set to \fBKEY_SIG\fR the key can be used for signing only, if set to \fBKEY_EX\fR
it can be used for signing and encryption. This option was useful for old
export grade software which could use signing only keys of arbitrary size but
had restrictions on the permissible sizes of keys which could be used for
encryption.
.PP
If \fIname\fR is \fBNULL\fR and \fIcert\fR contains an \fIalias\fR then this will be
used for the corresponding \fBfriendlyName\fR in the PKCS12 structure instead.
Similarly, if \fIpkey\fR is NULL and \fIcert\fR contains a \fIkeyid\fR then this will be
used for the corresponding \fBlocalKeyID\fR in the PKCS12 structure instead of the
id calculated from the \fIpkey\fR.
.PP
For all certificates in \fIca\fR then if a certificate contains an \fIalias\fR or
\&\fIkeyid\fR then this will be used for the corresponding \fBfriendlyName\fR or
\&\fBlocalKeyID\fR in the PKCS12 structure.
.PP
Either \fIpkey\fR, \fIcert\fR or both can be \fBNULL\fR to indicate that no key or
certificate is required. In previous versions both had to be present or
a fatal error is returned.
.PP
\&\fInid_key\fR or \fInid_cert\fR can be set to \-1 indicating that no encryption
should be used.
.PP
\&\fImac_iter\fR can be set to \-1 and the MAC will then be omitted entirely.
This can be useful when running with the FIPS provider as the PKCS12KDF
is not a FIPS approvable algorithm.
.PP
\&\fBPKCS12_create()\fR makes assumptions regarding the encoding of the given pass
phrase.
See \fBpassphrase\-encoding\fR\|(7) for more information.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPKCS12_create()\fR returns a valid \fBPKCS12\fR structure or NULL if an error occurred.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
IETF RFC 7292 (<https://tools.ietf.org/html/rfc7292>)
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_KDF\-PKCS12KDF\fR\|(7),
\&\fBd2i_PKCS12\fR\|(3),
\&\fBOSSL_PROVIDER\-FIPS\fR\|(7),
\&\fBpassphrase\-encoding\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
\&\fBPKCS12_create_ex()\fR was added in OpenSSL 3.0.
.PP
The defaults for encryption algorithms, MAC algorithm, and the MAC key
derivation iteration count were changed in OpenSSL 3.0 to more modern
standards.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2024 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
