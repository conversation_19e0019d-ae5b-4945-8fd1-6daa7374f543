.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_CMP_SRV_CTX_NEW 3ossl"
.TH OSSL_CMP_SRV_CTX_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_CMP_SRV_process_request,
OSSL_CMP_CTX_server_perform,
OSSL_CMP_SRV_CTX_new,
OSSL_CMP_SRV_CTX_free,
OSSL_CMP_SRV_cert_request_cb_t,
OSSL_CMP_SRV_rr_cb_t,
OSSL_CMP_SRV_certConf_cb_t,
OSSL_CMP_SRV_genm_cb_t,
OSSL_CMP_SRV_error_cb_t,
OSSL_CMP_SRV_pollReq_cb_t,
OSSL_CMP_SRV_CTX_init,
OSSL_CMP_SRV_CTX_get0_cmp_ctx,
OSSL_CMP_SRV_CTX_get0_custom_ctx,
OSSL_CMP_SRV_CTX_set_send_unprotected_errors,
OSSL_CMP_SRV_CTX_set_accept_unprotected,
OSSL_CMP_SRV_CTX_set_accept_raverified,
OSSL_CMP_SRV_CTX_set_grant_implicit_confirm
\&\- generic functions to set up and control a CMP server
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cmp.h>
\&
\& OSSL_CMP_MSG *OSSL_CMP_SRV_process_request(OSSL_CMP_SRV_CTX *srv_ctx,
\&                                            const OSSL_CMP_MSG *req);
\& OSSL_CMP_MSG *OSSL_CMP_CTX_server_perform(OSSL_CMP_CTX *client_ctx,
\&                                           const OSSL_CMP_MSG *req);
\& OSSL_CMP_SRV_CTX *OSSL_CMP_SRV_CTX_new(OSSL_LIB_CTX *libctx, const char *propq);
\& void OSSL_CMP_SRV_CTX_free(OSSL_CMP_SRV_CTX *srv_ctx);
\&
\& typedef OSSL_CMP_PKISI *(*OSSL_CMP_SRV_cert_request_cb_t)(
\&                                                 OSSL_CMP_SRV_CTX *srv_ctx,
\&                                                 const OSSL_CMP_MSG *req,
\&                                                 int certReqId,
\&                                                 const OSSL_CRMF_MSG *crm,
\&                                                 const X509_REQ *p10cr,
\&                                                 X509 **certOut,
\&                                                 STACK_OF(X509) **chainOut,
\&                                                 STACK_OF(X509) **caPubs);
\& typedef OSSL_CMP_PKISI *(*OSSL_CMP_SRV_rr_cb_t)(OSSL_CMP_SRV_CTX *srv_ctx,
\&                                                 const OSSL_CMP_MSG *req,
\&                                                 const X509_NAME *issuer,
\&                                                 const ASN1_INTEGER *serial);
\& typedef int (*OSSL_CMP_SRV_genm_cb_t)(OSSL_CMP_SRV_CTX *srv_ctx,
\&                                       const OSSL_CMP_MSG *req,
\&                                       STACK_OF(OSSL_CMP_ITAV) *in,
\&                                       STACK_OF(OSSL_CMP_ITAV) **out);
\& typedef void (*OSSL_CMP_SRV_error_cb_t)(OSSL_CMP_SRV_CTX *srv_ctx,
\&                                         const OSSL_CMP_MSG *req,
\&                                         const OSSL_CMP_PKISI *statusInfo,
\&                                         const ASN1_INTEGER *errorCode,
\&                                         const OSSL_CMP_PKIFREETEXT *errorDetails);
\& typedef int (*OSSL_CMP_SRV_certConf_cb_t)(OSSL_CMP_SRV_CTX *srv_ctx,
\&                                           const OSSL_CMP_MSG *req,
\&                                           int certReqId,
\&                                           const ASN1_OCTET_STRING *certHash,
\&                                           const OSSL_CMP_PKISI *si);
\& typedef int (*OSSL_CMP_SRV_pollReq_cb_t)(OSSL_CMP_SRV_CTX *srv_ctx,
\&                                          const OSSL_CMP_MSG *req,
\&                                          int certReqId,
\&                                          OSSL_CMP_MSG **certReq,
\&                                          int64_t *check_after);
\& int OSSL_CMP_SRV_CTX_init(OSSL_CMP_SRV_CTX *srv_ctx, void *custom_ctx,
\&                           OSSL_CMP_SRV_cert_request_cb_t process_cert_request,
\&                           OSSL_CMP_SRV_rr_cb_t process_rr,
\&                           OSSL_CMP_SRV_genm_cb_t process_genm,
\&                           OSSL_CMP_SRV_error_cb_t process_error,
\&                           OSSL_CMP_SRV_certConf_cb_t process_certConf,
\&                           OSSL_CMP_SRV_pollReq_cb_t process_pollReq);
\&
\& OSSL_CMP_CTX *OSSL_CMP_SRV_CTX_get0_cmp_ctx(const OSSL_CMP_SRV_CTX *srv_ctx);
\& void *OSSL_CMP_SRV_CTX_get0_custom_ctx(const OSSL_CMP_SRV_CTX *srv_ctx);
\&
\& int OSSL_CMP_SRV_CTX_set_send_unprotected_errors(OSSL_CMP_SRV_CTX *srv_ctx,
\&                                                  int val);
\& int OSSL_CMP_SRV_CTX_set_accept_unprotected(OSSL_CMP_SRV_CTX *srv_ctx, int val);
\& int OSSL_CMP_SRV_CTX_set_accept_raverified(OSSL_CMP_SRV_CTX *srv_ctx, int val);
\& int OSSL_CMP_SRV_CTX_set_grant_implicit_confirm(OSSL_CMP_SRV_CTX *srv_ctx,
\&                                                 int val);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOSSL_CMP_SRV_process_request()\fR implements the generic aspects of a CMP server.
Its arguments are the \fBOSSL_CMP_SRV_CTX\fR \fIsrv_ctx\fR and the CMP request message
\&\fIreq\fR. It does the typical generic checks on \fIreq\fR, calls
the respective callback function (if present) for more specific processing,
and then assembles a result message, which may be a CMP error message.
If after return of the function the expression
\&\fIOSSL_CMP_CTX_get_status(OSSL_CMP_SRV_CTX_get0_cmp_ctx(srv_ctx))\fR yields \-1
then the function has closed the current transaction,
which may be due to normal successful end of the transaction or due to an error.
.PP
\&\fBOSSL_CMP_CTX_server_perform()\fR is an interface to
\&\fBOSSL_CMP_SRV_process_request()\fR that can be used by a CMP client
in the same way as \fBOSSL_CMP_MSG_http_perform\fR\|(3).
The \fBOSSL_CMP_SRV_CTX\fR must be set as \fItransfer_cb_arg\fR of \fIclient_ctx\fR.
.PP
\&\fBOSSL_CMP_SRV_CTX_new()\fR creates and initializes an \fBOSSL_CMP_SRV_CTX\fR structure
associated with the library context \fIlibctx\fR and property query string
\&\fIpropq\fR, both of which may be NULL to select the defaults.
.PP
\&\fBOSSL_CMP_SRV_CTX_free()\fR deletes the given \fIsrv_ctx\fR.
.PP
\&\fBOSSL_CMP_SRV_CTX_init()\fR sets in the given \fIsrv_ctx\fR a custom server context
pointer as well as callback functions performing the specific processing of CMP
certificate requests, revocation requests, certificate confirmation requests,
general messages, error messages, and poll requests.
All arguments except \fIsrv_ctx\fR may be NULL.
If a callback for some message type is not given this means that the respective
type of CMP message is not supported by the server.
.PP
\&\fBOSSL_CMP_SRV_CTX_get0_cmp_ctx()\fR returns the \fBOSSL_CMP_CTX\fR from the \fIsrv_ctx\fR.
.PP
\&\fBOSSL_CMP_SRV_CTX_get0_custom_ctx()\fR returns the custom server context from
\&\fIsrv_ctx\fR that has been set using \fBOSSL_CMP_SRV_CTX_init()\fR.
.PP
\&\fBOSSL_CMP_SRV_CTX_set_send_unprotected_errors()\fR enables sending error messages
and other forms of negative responses unprotected.
.PP
\&\fBOSSL_CMP_SRV_CTX_set_accept_unprotected()\fR enables acceptance of requests
without protection of with invalid protection.
.PP
\&\fBOSSL_CMP_SRV_CTX_set_accept_raverified()\fR enables acceptance of ir/cr/kur
messages with POPO 'RAVerified'.
.PP
\&\fBOSSL_CMP_SRV_CTX_set_grant_implicit_confirm()\fR enables granting implicit
confirmation of newly enrolled certificates if requested.
.SH NOTES
.IX Header "NOTES"
CMP is defined in RFC 4210 (and CRMF in RFC 4211).
.PP
So far the CMP server implementation is limited to one request per CMP message
(and consequently to at most one response component per CMP message).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_CMP_SRV_CTX_new()\fR returns a \fBOSSL_CMP_SRV_CTX\fR structure on success,
NULL on error.
.PP
\&\fBOSSL_CMP_SRV_CTX_free()\fR does not return a value.
.PP
\&\fBOSSL_CMP_SRV_CTX_get0_cmp_ctx()\fR returns a \fBOSSL_CMP_CTX\fR structure on success,
NULL on error.
.PP
\&\fBOSSL_CMP_SRV_CTX_get0_custom_ctx()\fR returns the custom server context
that has been set using \fBOSSL_CMP_SRV_CTX_init()\fR.
.PP
All other functions return 1 on success, 0 on error.
.SH HISTORY
.IX Header "HISTORY"
The OpenSSL CMP support was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2007\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
