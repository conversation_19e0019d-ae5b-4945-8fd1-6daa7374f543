.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_ENCODER 3ossl"
.TH OSSL_ENCODER 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_ENCODER,
OSSL_ENCODER_fetch,
OSSL_ENCODER_up_ref,
OSSL_ENCODER_free,
OSSL_ENCODER_get0_provider,
OSSL_ENCODER_get0_properties,
OSSL_ENCODER_is_a,
OSSL_ENCODER_get0_name,
OSSL_ENCODER_get0_description,
OSSL_ENCODER_do_all_provided,
OSSL_ENCODER_names_do_all,
OSSL_ENCODER_gettable_params,
OSSL_ENCODER_get_params
\&\- Encoder method routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/encoder.h>
\&
\& typedef struct ossl_encoder_st OSSL_ENCODER;
\&
\& OSSL_ENCODER *OSSL_ENCODER_fetch(OSSL_LIB_CTX *ctx, const char *name,
\&                                  const char *properties);
\& int OSSL_ENCODER_up_ref(OSSL_ENCODER *encoder);
\& void OSSL_ENCODER_free(OSSL_ENCODER *encoder);
\& const OSSL_PROVIDER *OSSL_ENCODER_get0_provider(const OSSL_ENCODER *encoder);
\& const char *OSSL_ENCODER_get0_properties(const OSSL_ENCODER *encoder);
\& int OSSL_ENCODER_is_a(const OSSL_ENCODER *encoder, const char *name);
\& const char *OSSL_ENCODER_get0_name(const OSSL_ENCODER *encoder);
\& const char *OSSL_ENCODER_get0_description(const OSSL_ENCODER *encoder);
\& void OSSL_ENCODER_do_all_provided(OSSL_LIB_CTX *libctx,
\&                                   void (*fn)(OSSL_ENCODER *encoder, void *arg),
\&                                   void *arg);
\& int OSSL_ENCODER_names_do_all(const OSSL_ENCODER *encoder,
\&                               void (*fn)(const char *name, void *data),
\&                               void *data);
\& const OSSL_PARAM *OSSL_ENCODER_gettable_params(OSSL_ENCODER *encoder);
\& int OSSL_ENCODER_get_params(OSSL_ENCODER_CTX *ctx, const OSSL_PARAM params[]);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOSSL_ENCODER\fR is a method for encoders, which know how to
encode an object of some kind to a encoded form, such as PEM,
DER, or even human readable text.
.PP
\&\fBOSSL_ENCODER_fetch()\fR looks for an algorithm within the provider that
has been loaded into the \fBOSSL_LIB_CTX\fR given by \fIctx\fR, having the
name given by \fIname\fR and the properties given by \fIproperties\fR.
The \fIname\fR determines what type of object the fetched encoder
method is expected to be able to encode, and the properties are
used to determine the expected output type.
For known properties and the values they may have, please have a look
in "Names and properties" in \fBprovider\-encoder\fR\|(7).
.PP
\&\fBOSSL_ENCODER_up_ref()\fR increments the reference count for the given
\&\fIencoder\fR.
.PP
\&\fBOSSL_ENCODER_free()\fR decrements the reference count for the given
\&\fIencoder\fR, and when the count reaches zero, frees it.
.PP
\&\fBOSSL_ENCODER_get0_provider()\fR returns the provider of the given
\&\fIencoder\fR.
.PP
\&\fBOSSL_ENCODER_get0_properties()\fR returns the property definition associated
with the given \fIencoder\fR.
.PP
\&\fBOSSL_ENCODER_is_a()\fR checks if \fIencoder\fR is an implementation of an
algorithm that's identifiable with \fIname\fR.
.PP
\&\fBOSSL_ENCODER_get0_name()\fR returns the name used to fetch the given \fIencoder\fR.
.PP
\&\fBOSSL_ENCODER_get0_description()\fR returns a description of the \fIloader\fR, meant
for display and human consumption.  The description is at the discretion of the
\&\fIloader\fR implementation.
.PP
\&\fBOSSL_ENCODER_names_do_all()\fR traverses all names for the given
\&\fIencoder\fR, and calls \fIfn\fR with each name and \fIdata\fR as arguments.
.PP
\&\fBOSSL_ENCODER_do_all_provided()\fR traverses all encoder
implementations by all activated providers in the library context
\&\fIlibctx\fR, and for each of the implementations, calls \fIfn\fR with the
implementation method and \fIarg\fR as arguments.
.PP
\&\fBOSSL_ENCODER_gettable_params()\fR returns an \fBOSSL_PARAM\fR\|(3)
array of parameter descriptors.
.PP
\&\fBOSSL_ENCODER_get_params()\fR attempts to get parameters specified
with an \fBOSSL_PARAM\fR\|(3) array \fIparams\fR.  Parameters that the
implementation doesn't recognise should be ignored.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_ENCODER_fetch()\fR returns a pointer to the key management
implementation represented by an OSSL_ENCODER object, or NULL on
error.
.PP
\&\fBOSSL_ENCODER_up_ref()\fR returns 1 on success, or 0 on error.
.PP
\&\fBOSSL_ENCODER_free()\fR doesn't return any value.
.PP
\&\fBOSSL_ENCODER_get0_provider()\fR returns a pointer to a provider object, or
NULL on error.
.PP
\&\fBOSSL_ENCODER_get0_properties()\fR returns a pointer to a property
definition string, or NULL on error.
.PP
\&\fBOSSL_ENCODER_is_a()\fR returns 1 of \fIencoder\fR was identifiable,
otherwise 0.
.PP
\&\fBOSSL_ENCODER_get0_name()\fR returns the algorithm name from the provided
implementation for the given \fIencoder\fR. Note that the \fIencoder\fR may have
multiple synonyms associated with it. In this case the first name from the
algorithm definition is returned. Ownership of the returned string is retained
by the \fIencoder\fR object and should not be freed by the caller.
.PP
\&\fBOSSL_ENCODER_get0_description()\fR returns a pointer to a description, or NULL if
there isn't one.
.PP
\&\fBOSSL_ENCODER_names_do_all()\fR returns 1 if the callback was called for all
names. A return value of 0 means that the callback was not called for any names.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\fR\|(7), \fBOSSL_ENCODER_CTX\fR\|(3), \fBOSSL_ENCODER_to_bio\fR\|(3),
\&\fBOSSL_ENCODER_CTX_new_for_pkey\fR\|(3), \fBOSSL_LIB_CTX\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The functions described here were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
