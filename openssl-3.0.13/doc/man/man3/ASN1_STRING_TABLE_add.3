.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ASN1_STRING_TABLE_ADD 3ossl"
.TH ASN1_STRING_TABLE_ADD 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ASN1_STRING_TABLE, ASN1_STRING_TABLE_add, ASN1_STRING_TABLE_get,
ASN1_STRING_TABLE_cleanup \- ASN1_STRING_TABLE manipulation functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/asn1.h>
\&
\& typedef struct asn1_string_table_st ASN1_STRING_TABLE;
\&
\& int ASN1_STRING_TABLE_add(int nid, long minsize, long maxsize,
\&                           unsigned long mask, unsigned long flags);
\& ASN1_STRING_TABLE *ASN1_STRING_TABLE_get(int nid);
\& void ASN1_STRING_TABLE_cleanup(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
.SS Types
.IX Subsection "Types"
\&\fBASN1_STRING_TABLE\fR is a table which holds string information
(basically minimum size, maximum size, type and etc) for a NID object.
.SS Functions
.IX Subsection "Functions"
\&\fBASN1_STRING_TABLE_add()\fR adds a new \fBASN1_STRING_TABLE\fR item into the
local ASN1 string table based on the \fInid\fR along with other parameters.
.PP
If the item is already in the table, fields of \fBASN1_STRING_TABLE\fR are
updated (depending on the values of those parameters, e.g., \fIminsize\fR
and \fImaxsize\fR >= 0, \fImask\fR and \fIflags\fR != 0). If the \fInid\fR is standard,
a copy of the standard \fBASN1_STRING_TABLE\fR is created and updated with
other parameters.
.PP
\&\fBASN1_STRING_TABLE_get()\fR searches for an \fBASN1_STRING_TABLE\fR item based
on \fInid\fR. It will search the local table first, then the standard one.
.PP
\&\fBASN1_STRING_TABLE_cleanup()\fR frees all \fBASN1_STRING_TABLE\fR items added
by \fBASN1_STRING_TABLE_add()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBASN1_STRING_TABLE_add()\fR returns 1 on success, 0 if an error occurred.
.PP
\&\fBASN1_STRING_TABLE_get()\fR returns a valid \fBASN1_STRING_TABLE\fR structure
or NULL if nothing is found.
.PP
\&\fBASN1_STRING_TABLE_cleanup()\fR does not return a value.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
