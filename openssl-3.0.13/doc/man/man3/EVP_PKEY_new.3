.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_NEW 3ossl"
.TH EVP_PKEY_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY,
EVP_PKEY_new,
EVP_PKEY_up_ref,
EVP_PKEY_dup,
EVP_PKEY_free,
EVP_PKEY_new_raw_private_key_ex,
EVP_PKEY_new_raw_private_key,
EVP_PKEY_new_raw_public_key_ex,
EVP_PKEY_new_raw_public_key,
EVP_PKEY_new_CMAC_key,
EVP_PKEY_new_mac_key,
EVP_PKEY_get_raw_private_key,
EVP_PKEY_get_raw_public_key
\&\- public/private key allocation and raw key handling functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& typedef evp_pkey_st EVP_PKEY;
\&
\& EVP_PKEY *EVP_PKEY_new(void);
\& int EVP_PKEY_up_ref(EVP_PKEY *key);
\& EVP_PKEY *EVP_PKEY_dup(EVP_PKEY *key);
\& void EVP_PKEY_free(EVP_PKEY *key);
\&
\& EVP_PKEY *EVP_PKEY_new_raw_private_key_ex(OSSL_LIB_CTX *libctx,
\&                                           const char *keytype,
\&                                           const char *propq,
\&                                           const unsigned char *key,
\&                                           size_t keylen);
\& EVP_PKEY *EVP_PKEY_new_raw_private_key(int type, ENGINE *e,
\&                                        const unsigned char *key, size_t keylen);
\& EVP_PKEY *EVP_PKEY_new_raw_public_key_ex(OSSL_LIB_CTX *libctx,
\&                                          const char *keytype,
\&                                          const char *propq,
\&                                          const unsigned char *key,
\&                                          size_t keylen);
\& EVP_PKEY *EVP_PKEY_new_raw_public_key(int type, ENGINE *e,
\&                                       const unsigned char *key, size_t keylen);
\& EVP_PKEY *EVP_PKEY_new_mac_key(int type, ENGINE *e, const unsigned char *key,
\&                                int keylen);
\&
\& int EVP_PKEY_get_raw_private_key(const EVP_PKEY *pkey, unsigned char *priv,
\&                                  size_t *len);
\& int EVP_PKEY_get_raw_public_key(const EVP_PKEY *pkey, unsigned char *pub,
\&                                 size_t *len);
.Ve
.PP
The following function has been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 2
\& EVP_PKEY *EVP_PKEY_new_CMAC_key(ENGINE *e, const unsigned char *priv,
\&                                 size_t len, const EVP_CIPHER *cipher);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_PKEY\fR is a generic structure to hold diverse types of asymmetric keys
(also known as "key pairs"), and can be used for diverse operations, like
signing, verifying signatures, key derivation, etc.  The asymmetric keys
themselves are often referred to as the "internal key", and are handled by
backends, such as providers (through \fBEVP_KEYMGMT\fR\|(3)) or \fBENGINE\fRs.
.PP
Conceptually, an \fBEVP_PKEY\fR internal key may hold a private key, a public
key, or both (a keypair), and along with those, key parameters if the key type
requires them.  The presence of these components determine what operations can
be made; for example, signing normally requires the presence of a private key,
and verifying normally requires the presence of a public key.
.PP
\&\fBEVP_PKEY\fR has also been used for MAC algorithm that were conceived as
producing signatures, although not being public key algorithms; "POLY1305",
"SIPHASH", "HMAC", "CMAC".  This usage is considered legacy and is discouraged
in favor of the \fBEVP_MAC\fR\|(3) API.
.PP
The \fBEVP_PKEY_new()\fR function allocates an empty \fBEVP_PKEY\fR structure which is
used by OpenSSL to store public and private keys. The reference count is set to
\&\fB1\fR.
.PP
\&\fBEVP_PKEY_up_ref()\fR increments the reference count of \fIkey\fR.
.PP
\&\fBEVP_PKEY_dup()\fR duplicates the \fIkey\fR. The \fIkey\fR must not be ENGINE based or
a raw key, otherwise the duplication will fail.
.PP
\&\fBEVP_PKEY_free()\fR decrements the reference count of \fIkey\fR and, if the reference
count is zero, frees it up. If \fIkey\fR is NULL, nothing is done.
.PP
\&\fBEVP_PKEY_new_raw_private_key_ex()\fR allocates a new \fBEVP_PKEY\fR. Unless an
engine should be used for the key type, a provider for the key is found using
the library context \fIlibctx\fR and the property query string \fIpropq\fR. The
\&\fIkeytype\fR argument indicates what kind of key this is. The value should be a
string for a public key algorithm that supports raw private keys, i.e one of
"X25519", "ED25519", "X448" or "ED448". \fIkey\fR points to the raw private key
data for this \fBEVP_PKEY\fR which should be of length \fIkeylen\fR. The length
should be appropriate for the type of the key. The public key data will be
automatically derived from the given private key data (if appropriate for the
algorithm type).
.PP
\&\fBEVP_PKEY_new_raw_private_key()\fR does the same as
\&\fBEVP_PKEY_new_raw_private_key_ex()\fR except that the default library context and
default property query are used instead. If \fIe\fR is non-NULL then the new
\&\fBEVP_PKEY\fR structure is associated with the engine \fIe\fR. The \fItype\fR argument
indicates what kind of key this is. The value should be a NID for a public key
algorithm that supports raw private keys, i.e. one of \fBEVP_PKEY_X25519\fR,
\&\fBEVP_PKEY_ED25519\fR, \fBEVP_PKEY_X448\fR or \fBEVP_PKEY_ED448\fR.
.PP
\&\fBEVP_PKEY_new_raw_private_key_ex()\fR and \fBEVP_PKEY_new_raw_private_key()\fR may also
be used with most MACs implemented as public key algorithms, so key types such
as "HMAC", "POLY1305", "SIPHASH", or their NID form \fBEVP_PKEY_POLY1305\fR,
\&\fBEVP_PKEY_SIPHASH\fR, \fBEVP_PKEY_HMAC\fR are also accepted.  This usage is,
as mentioned above, discouraged in favor of the \fBEVP_MAC\fR\|(3) API.
.PP
\&\fBEVP_PKEY_new_raw_public_key_ex()\fR works in the same way as
\&\fBEVP_PKEY_new_raw_private_key_ex()\fR except that \fIkey\fR points to the raw
public key data. The \fBEVP_PKEY\fR structure will be initialised without any
private key information. Algorithm types that support raw public keys are
"X25519", "ED25519", "X448" or "ED448".
.PP
\&\fBEVP_PKEY_new_raw_public_key()\fR works in the same way as
\&\fBEVP_PKEY_new_raw_private_key()\fR except that \fIkey\fR points to the raw public key
data. The \fBEVP_PKEY\fR structure will be initialised without any private key
information. Algorithm types that support raw public keys are
\&\fBEVP_PKEY_X25519\fR, \fBEVP_PKEY_ED25519\fR, \fBEVP_PKEY_X448\fR or \fBEVP_PKEY_ED448\fR.
.PP
\&\fBEVP_PKEY_new_mac_key()\fR works in the same way as \fBEVP_PKEY_new_raw_private_key()\fR.
New applications should use \fBEVP_PKEY_new_raw_private_key()\fR instead.
.PP
\&\fBEVP_PKEY_get_raw_private_key()\fR fills the buffer provided by \fIpriv\fR with raw
private key data. The size of the \fIpriv\fR buffer should be in \fI*len\fR on entry
to the function, and on exit \fI*len\fR is updated with the number of bytes
actually written. If the buffer \fIpriv\fR is NULL then \fI*len\fR is populated with
the number of bytes required to hold the key. The calling application is
responsible for ensuring that the buffer is large enough to receive the private
key data. This function only works for algorithms that support raw private keys.
Currently this is: \fBEVP_PKEY_HMAC\fR, \fBEVP_PKEY_POLY1305\fR, \fBEVP_PKEY_SIPHASH\fR,
\&\fBEVP_PKEY_X25519\fR, \fBEVP_PKEY_ED25519\fR, \fBEVP_PKEY_X448\fR or \fBEVP_PKEY_ED448\fR.
.PP
\&\fBEVP_PKEY_get_raw_public_key()\fR fills the buffer provided by \fIpub\fR with raw
public key data. The size of the \fIpub\fR buffer should be in \fI*len\fR on entry
to the function, and on exit \fI*len\fR is updated with the number of bytes
actually written. If the buffer \fIpub\fR is NULL then \fI*len\fR is populated with
the number of bytes required to hold the key. The calling application is
responsible for ensuring that the buffer is large enough to receive the public
key data. This function only works for algorithms that support raw public  keys.
Currently this is: \fBEVP_PKEY_X25519\fR, \fBEVP_PKEY_ED25519\fR, \fBEVP_PKEY_X448\fR or
\&\fBEVP_PKEY_ED448\fR.
.PP
\&\fBEVP_PKEY_new_CMAC_key()\fR works in the same way as \fBEVP_PKEY_new_raw_private_key()\fR
except it is only for the \fBEVP_PKEY_CMAC\fR algorithm type. In addition to the
raw private key data, it also takes a cipher algorithm to be used during
creation of a CMAC in the \fBcipher\fR argument. The cipher should be a standard
encryption-only cipher. For example AEAD and XTS ciphers should not be used.
.PP
Applications should use the \fBEVP_MAC\fR\|(3) API instead
and set the \fBOSSL_MAC_PARAM_CIPHER\fR parameter on the \fBEVP_MAC_CTX\fR object
with the name of the cipher being used.
.SH NOTES
.IX Header "NOTES"
The \fBEVP_PKEY\fR structure is used by various OpenSSL functions which require a
general private key without reference to any particular algorithm.
.PP
The structure returned by \fBEVP_PKEY_new()\fR is empty. To add a private or public
key to this empty structure use the appropriate functions described in
\&\fBEVP_PKEY_set1_RSA\fR\|(3), \fBEVP_PKEY_set1_DSA\fR\|(3), \fBEVP_PKEY_set1_DH\fR\|(3) or
\&\fBEVP_PKEY_set1_EC_KEY\fR\|(3).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_new()\fR, \fBEVP_PKEY_new_raw_private_key()\fR, \fBEVP_PKEY_new_raw_public_key()\fR,
\&\fBEVP_PKEY_new_CMAC_key()\fR and \fBEVP_PKEY_new_mac_key()\fR return either the newly
allocated \fBEVP_PKEY\fR structure or NULL if an error occurred.
.PP
\&\fBEVP_PKEY_dup()\fR returns the key duplicate or NULL if an error occurred.
.PP
\&\fBEVP_PKEY_up_ref()\fR, \fBEVP_PKEY_get_raw_private_key()\fR and
\&\fBEVP_PKEY_get_raw_public_key()\fR return 1 for success and 0 for failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_set1_RSA\fR\|(3), \fBEVP_PKEY_set1_DSA\fR\|(3), \fBEVP_PKEY_set1_DH\fR\|(3) or
\&\fBEVP_PKEY_set1_EC_KEY\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The
\&\fBEVP_PKEY_new()\fR and \fBEVP_PKEY_free()\fR functions exist in all versions of OpenSSL.
.PP
The \fBEVP_PKEY_up_ref()\fR function was added in OpenSSL 1.1.0.
.PP
The
\&\fBEVP_PKEY_new_raw_private_key()\fR, \fBEVP_PKEY_new_raw_public_key()\fR,
\&\fBEVP_PKEY_new_CMAC_key()\fR, \fBEVP_PKEY_new_raw_private_key()\fR and
\&\fBEVP_PKEY_get_raw_public_key()\fR functions were added in OpenSSL 1.1.1.
.PP
The \fBEVP_PKEY_dup()\fR, \fBEVP_PKEY_new_raw_private_key_ex()\fR, and
\&\fBEVP_PKEY_new_raw_public_key_ex()\fR
functions were added in OpenSSL 3.0.
.PP
The \fBEVP_PKEY_new_CMAC_key()\fR was deprecated in OpenSSL 3.0.
.PP
The documentation of \fBEVP_PKEY\fR was amended in OpenSSL 3.0 to allow there to
be the private part of the keypair without the public part, where this was
previously implied to be disallowed.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
