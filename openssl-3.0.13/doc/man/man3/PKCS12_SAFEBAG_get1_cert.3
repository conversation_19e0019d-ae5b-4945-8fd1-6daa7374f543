.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS12_SAFEBAG_GET1_CERT 3ossl"
.TH PKCS12_SAFEBAG_GET1_CERT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS12_SAFEBAG_get0_attr, PKCS12_SAFEBAG_get0_type,
PKCS12_SAFEBAG_get_nid, PKCS12_SAFEBAG_get_bag_nid,
PKCS12_SAFEBAG_get0_bag_obj, PKCS12_SAFEBAG_get0_bag_type,
PKCS12_SAFEBAG_get1_cert, PKCS12_SAFEBAG_get1_crl,
PKCS12_SAFEBAG_get0_safes, PKCS12_SAFEBAG_get0_p8inf,
PKCS12_SAFEBAG_get0_pkcs8 \- Get objects from a PKCS#12 safeBag
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pkcs12.h>
\&
\& const ASN1_TYPE *PKCS12_SAFEBAG_get0_attr(const PKCS12_SAFEBAG *bag,
\&                                           int attr_nid);
\& const ASN1_OBJECT *PKCS12_SAFEBAG_get0_type(const PKCS12_SAFEBAG *bag);
\& int PKCS12_SAFEBAG_get_nid(const PKCS12_SAFEBAG *bag);
\& int PKCS12_SAFEBAG_get_bag_nid(const PKCS12_SAFEBAG *bag);
\& const ASN1_TYPE *PKCS12_SAFEBAG_get0_bag_obj(const PKCS12_SAFEBAG *bag);
\& const ASN1_OBJECT *PKCS12_SAFEBAG_get0_bag_type(const PKCS12_SAFEBAG *bag);
\& X509 *PKCS12_SAFEBAG_get1_cert(const PKCS12_SAFEBAG *bag);
\& X509_CRL *PKCS12_SAFEBAG_get1_crl(const PKCS12_SAFEBAG *bag);
\& const STACK_OF(PKCS12_SAFEBAG) *PKCS12_SAFEBAG_get0_safes(const PKCS12_SAFEBAG *bag);
\& const PKCS8_PRIV_KEY_INFO *PKCS12_SAFEBAG_get0_p8inf(const PKCS12_SAFEBAG *bag);
\& const X509_SIG *PKCS12_SAFEBAG_get0_pkcs8(const PKCS12_SAFEBAG *bag);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPKCS12_SAFEBAG_get0_attr()\fR gets the attribute value corresponding to the \fBattr_nid\fR.
.PP
\&\fBPKCS12_SAFEBAG_get0_type()\fR gets the \fBsafeBag\fR type as an OID, whereas
\&\fBPKCS12_SAFEBAG_get_nid()\fR gets the \fBsafeBag\fR type as an NID, which could be
\&\fBNID_certBag\fR, \fBNID_crlBag\fR, \fBNID_keyBag\fR, \fBNID_secretBag\fR, \fBNID_safeContentsBag\fR
or \fBNID_pkcs8ShroudedKeyBag\fR.
.PP
\&\fBPKCS12_SAFEBAG_get_bag_nid()\fR gets the type of the object contained within the
\&\fBPKCS12_SAFEBAG\fR. This corresponds to the bag type for most bags, but can be
arbitrary for \fBsecretBag\fRs. \fBPKCS12_SAFEBAG_get0_bag_type()\fR gets this type as an OID.
.PP
\&\fBPKCS12_SAFEBAG_get0_bag_obj()\fR retrieves the object contained within the safeBag.
.PP
\&\fBPKCS12_SAFEBAG_get1_cert()\fR and \fBPKCS12_SAFEBAG_get1_crl()\fR return new \fBX509\fR or
\&\fBX509_CRL\fR objects from the item in the safeBag.
.PP
\&\fBPKCS12_SAFEBAG_get0_p8inf()\fR and \fBPKCS12_SAFEBAG_get0_pkcs8()\fR return the PKCS8 object
from a PKCS8shroudedKeyBag or a keyBag.
.PP
\&\fBPKCS12_SAFEBAG_get0_safes()\fR retrieves the set of \fBsafeBags\fR contained within a
safeContentsBag.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPKCS12_SAFEBAG_get_nid()\fR and \fBPKCS12_SAFEBAG_get_bag_nid()\fR return the NID of the safeBag
or bag object, or \-1 if there is no corresponding NID.
Other functions return a valid object of the specified type or NULL if an error occurred.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBPKCS12_create\fR\|(3),
\&\fBPKCS12_add_safe\fR\|(3),
\&\fBPKCS12_add_safes\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
