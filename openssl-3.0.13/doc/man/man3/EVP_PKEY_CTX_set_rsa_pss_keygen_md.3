.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_CTX_SET_RSA_PSS_KEYGEN_MD 3ossl"
.TH EVP_PKEY_CTX_SET_RSA_PSS_KEYGEN_MD 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_CTX_set_rsa_pss_keygen_md,
EVP_PKEY_CTX_set_rsa_pss_keygen_md_name,
EVP_PKEY_CTX_set_rsa_pss_keygen_mgf1_md,
EVP_PKEY_CTX_set_rsa_pss_keygen_mgf1_md_name,
EVP_PKEY_CTX_set_rsa_pss_keygen_saltlen
\&\- EVP_PKEY RSA\-PSS algorithm support functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/rsa.h>
\&
\& int EVP_PKEY_CTX_set_rsa_pss_keygen_md(EVP_PKEY_CTX *pctx,
\&                                        const EVP_MD *md);
\& int EVP_PKEY_CTX_set_rsa_pss_keygen_md_name(EVP_PKEY_CTX *ctx,
\&                                             const char *mdname,
\&                                             const char *mdprops);
\& int EVP_PKEY_CTX_set_rsa_pss_keygen_mgf1_md(EVP_PKEY_CTX *pctx,
\&                                             const EVP_MD *md);
\& int EVP_PKEY_CTX_set_rsa_pss_keygen_mgf1_md_name(EVP_PKEY_CTX *pctx,
\&                                                  const char *mdname);
\& int EVP_PKEY_CTX_set_rsa_pss_keygen_saltlen(EVP_PKEY_CTX *pctx,
\&                                             int saltlen);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These are the functions that implement \fBRSA\-PSS\fR\|(7).
.SS "Signing and Verification"
.IX Subsection "Signing and Verification"
The macro \fBEVP_PKEY_CTX_set_rsa_padding()\fR is supported but an error is
returned if an attempt is made to set the padding mode to anything other
than \fBPSS\fR. It is otherwise similar to the \fBRSA\fR version.
.PP
The \fBEVP_PKEY_CTX_set_rsa_pss_saltlen()\fR macro is used to set the salt length.
If the key has usage restrictions then an error is returned if an attempt is
made to set the salt length below the minimum value. It is otherwise similar
to the \fBRSA\fR operation except detection of the salt length (using
RSA_PSS_SALTLEN_AUTO) is not supported for verification if the key has
usage restrictions.
.PP
The \fBEVP_PKEY_CTX_set_signature_md\fR\|(3) and \fBEVP_PKEY_CTX_set_rsa_mgf1_md\fR\|(3)
functions are used to set the digest and MGF1 algorithms respectively. If the
key has usage restrictions then an error is returned if an attempt is made to
set the digest to anything other than the restricted value. Otherwise these are
similar to the \fBRSA\fR versions.
.SS "Key Generation"
.IX Subsection "Key Generation"
As with RSA key generation the \fBEVP_PKEY_CTX_set_rsa_keygen_bits()\fR
and \fBEVP_PKEY_CTX_set_rsa_keygen_pubexp()\fR macros are supported for RSA-PSS:
they have exactly the same meaning as for the RSA algorithm.
.PP
Optional parameter restrictions can be specified when generating a PSS key.
If any restrictions are set (using the macros described below) then \fBall\fR
parameters are restricted. For example, setting a minimum salt length also
restricts the digest and MGF1 algorithms. If any restrictions are in place
then they are reflected in the corresponding parameters of the public key
when (for example) a certificate request is signed.
.PP
\&\fBEVP_PKEY_CTX_set_rsa_pss_keygen_md()\fR restricts the digest algorithm the
generated key can use to \fImd\fR.
\&\fBEVP_PKEY_CTX_set_rsa_pss_keygen_md_name()\fR does the same thing, but
passes the algorithm by name rather than by \fBEVP_MD\fR.
.PP
\&\fBEVP_PKEY_CTX_set_rsa_pss_keygen_mgf1_md()\fR restricts the MGF1 algorithm the
generated key can use to \fImd\fR.
\&\fBEVP_PKEY_CTX_set_rsa_pss_keygen_mgf1_md_name()\fR does the same thing, but
passes the algorithm by name rather than by \fBEVP_MD\fR.
.PP
\&\fBEVP_PKEY_CTX_set_rsa_pss_keygen_saltlen()\fR restricts the minimum salt length
to \fIsaltlen\fR.
.SH NOTES
.IX Header "NOTES"
A context for the \fBRSA-PSS\fR algorithm can be obtained by calling:
.PP
.Vb 1
\& EVP_PKEY_CTX *pctx = EVP_PKEY_CTX_new_id(EVP_PKEY_RSA_PSS, NULL);
.Ve
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All these functions return 1 for success and 0 or a negative value for failure.
In particular a return value of \-2 indicates the operation is not supported by
the public key algorithm.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBRSA\-PSS\fR\|(7),
\&\fBEVP_PKEY_CTX_new\fR\|(3),
\&\fBEVP_PKEY_CTX_ctrl_str\fR\|(3),
\&\fBEVP_PKEY_derive\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
