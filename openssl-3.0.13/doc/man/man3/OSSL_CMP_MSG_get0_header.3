.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_CMP_MSG_GET0_HEADER 3ossl"
.TH OSSL_CMP_MSG_GET0_HEADER 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_CMP_MSG_get0_header,
OSSL_CMP_MSG_get_bodytype,
OSSL_CMP_MSG_update_transactionID,
OSSL_CMP_MSG_update_recipNonce,
OSSL_CMP_CTX_setup_CRM,
OSSL_CMP_MSG_read,
OSSL_CMP_MSG_write,
d2i_OSSL_CMP_MSG_bio,
i2d_OSSL_CMP_MSG_bio
\&\- function(s) manipulating CMP messages
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\&  #include <openssl/cmp.h>
\&
\&  OSSL_CMP_PKIHEADER *OSSL_CMP_MSG_get0_header(const OSSL_CMP_MSG *msg);
\&  int OSSL_CMP_MSG_get_bodytype(const OSSL_CMP_MSG *msg);
\&  int OSSL_CMP_MSG_update_transactionID(OSSL_CMP_CTX *ctx, OSSL_CMP_MSG *msg);
\&  int OSSL_CMP_MSG_update_recipNonce(OSSL_CMP_CTX *ctx, OSSL_CMP_MSG *msg);
\&  OSSL_CRMF_MSG *OSSL_CMP_CTX_setup_CRM(OSSL_CMP_CTX *ctx, int for_KUR, int rid);
\&  OSSL_CMP_MSG *OSSL_CMP_MSG_read(const char *file, OSSL_LIB_CTX *libctx, const char *propq);
\&  int OSSL_CMP_MSG_write(const char *file, const OSSL_CMP_MSG *msg);
\&  OSSL_CMP_MSG *d2i_OSSL_CMP_MSG_bio(BIO *bio, OSSL_CMP_MSG **msg);
\&  int i2d_OSSL_CMP_MSG_bio(BIO *bio, const OSSL_CMP_MSG *msg);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOSSL_CMP_MSG_get0_header()\fR returns the header of the given CMP message.
.PP
\&\fBOSSL_CMP_MSG_get_bodytype()\fR returns the body type of the given CMP message.
.PP
\&\fBOSSL_CMP_MSG_update_transactionID()\fR updates the transactionID field
in the header of the given message according to the CMP_CTX.
If \fIctx\fR does not contain a transaction ID, a fresh one is created before.
The message gets re-protected (if protecting requests is required).
.PP
\&\fBOSSL_CMP_MSG_update_recipNonce()\fR updates the recipNonce field
in the header of the given message according to the CMP_CTX.
The message gets re-protected (if protecting requests is required).
.PP
\&\fBOSSL_CMP_CTX_setup_CRM()\fR creates a CRMF certificate request message
from various information provided in the CMP context argument \fIctx\fR
for inclusion in a CMP request message based on details contained in \fIctx\fR.
The \fIrid\fR argument defines the request identifier to use, which typically is 0.
.PP
The subject DN included in the certificate template is
the first available value of these:
.IP "any subject name in \fIctx\fR set via \fBOSSL_CMP_CTX_set1_subjectName\fR\|(3) \- if it is the NULL-DN (i.e., any empty sequence of RDNs), no subject is included," 4
.IX Item "any subject name in ctx set via OSSL_CMP_CTX_set1_subjectName - if it is the NULL-DN (i.e., any empty sequence of RDNs), no subject is included,"
.PD 0
.IP "the subject field of any PKCS#10 CSR set in \fIctx\fR via \fBOSSL_CMP_CTX_set1_p10CSR\fR\|(3)," 4
.IX Item "the subject field of any PKCS#10 CSR set in ctx via OSSL_CMP_CTX_set1_p10CSR,"
.IP "the subject field of any reference certificate given in \fIctx\fR (see \fBOSSL_CMP_CTX_set1_oldCert\fR\|(3)), but only if \fIfor_KUR\fR is nonzero or the \fIctx\fR does not include a Subject Alternative Name." 4
.IX Item "the subject field of any reference certificate given in ctx (see OSSL_CMP_CTX_set1_oldCert), but only if for_KUR is nonzero or the ctx does not include a Subject Alternative Name."
.PD
.PP
The public key included is the first available value of these:
.IP "the public key derived from any key set via \fBOSSL_CMP_CTX_set0_newPkey\fR\|(3)," 4
.IX Item "the public key derived from any key set via OSSL_CMP_CTX_set0_newPkey,"
.PD 0
.IP "the public key of any PKCS#10 CSR given in \fIctx\fR," 4
.IX Item "the public key of any PKCS#10 CSR given in ctx,"
.IP "the public key of any reference certificate given in \fIctx\fR (see \fBOSSL_CMP_CTX_set1_oldCert\fR\|(3))," 4
.IX Item "the public key of any reference certificate given in ctx (see OSSL_CMP_CTX_set1_oldCert),"
.IP "the public key derived from any client's private key set via \fBOSSL_CMP_CTX_set1_pkey\fR\|(3)." 4
.IX Item "the public key derived from any client's private key set via OSSL_CMP_CTX_set1_pkey."
.PD
.PP
The set of X.509 extensions to include is computed as follows.
If a PKCS#10 CSR is present in \fIctx\fR, default extensions are taken from there,
otherwise the empty set is taken as the initial value.
If there is a reference certificate in \fIctx\fR and contains Subject Alternative
Names (SANs) and \fBOSSL_CMP_OPT_SUBJECTALTNAME_NODEFAULT\fR is not set,
these override any SANs from the PKCS#10 CSR.
The extensions are further augmented or overridden by any extensions with the
same OIDs included in the \fIctx\fR via \fBOSSL_CMP_CTX_set0_reqExtensions\fR\|(3).
The SANs are further overridden by any SANs included in \fIctx\fR via
\&\fBOSSL_CMP_CTX_push1_subjectAltName\fR\|(3).
Finally, policies are overridden by any policies included in \fIctx\fR via
\&\fBOSSL_CMP_CTX_push0_policy\fR\|(3).
.PP
\&\fBOSSL_CMP_CTX_setup_CRM()\fR also sets the sets the regToken control \fBoldCertID\fR
for KUR messages using the issuer name and serial number of the reference
certificate, if present.
.PP
\&\fBOSSL_CMP_MSG_read()\fR loads a DER-encoded OSSL_CMP_MSG from \fIfile\fR.
.PP
\&\fBOSSL_CMP_MSG_write()\fR stores the given OSSL_CMP_MSG to \fIfile\fR in DER encoding.
.PP
\&\fBd2i_OSSL_CMP_MSG_bio()\fR parses an ASN.1\-encoded OSSL_CMP_MSG from the BIO \fIbio\fR.
It assigns a pointer to the new structure to \fI*msg\fR if \fImsg\fR is not NULL.
.PP
\&\fBi2d_OSSL_CMP_MSG_bio()\fR writes the OSSL_CMP_MSG \fImsg\fR in ASN.1 encoding
to BIO \fIbio\fR.
.SH NOTES
.IX Header "NOTES"
CMP is defined in RFC 4210.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_CMP_MSG_get0_header()\fR returns the intended pointer value as described above
or NULL if the respective entry does not exist and on error.
.PP
\&\fBOSSL_CMP_MSG_get_bodytype()\fR returns the body type or \-1 on error.
.PP
\&\fBOSSL_CMP_CTX_setup_CRM()\fR returns a pointer to a \fBOSSL_CRMF_MSG\fR on success,
NULL on error.
.PP
\&\fBd2i_OSSL_CMP_MSG_bio()\fR returns the parsed message or NULL on error.
.PP
\&\fBOSSL_CMP_MSG_read()\fR and \fBd2i_OSSL_CMP_MSG_bio()\fR
return the parsed CMP message or NULL on error.
.PP
\&\fBOSSL_CMP_MSG_write()\fR returns the number of bytes successfully encoded or a
negative value if an error occurs.
.PP
\&\fBi2d_OSSL_CMP_MSG_bio()\fR, \fBOSSL_CMP_MSG_update_transactionID()\fR,
and \fBOSSL_CMP_MSG_update_recipNonce()\fR
return 1 on success, 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOSSL_CMP_CTX_set1_subjectName\fR\|(3), \fBOSSL_CMP_CTX_set1_p10CSR\fR\|(3),
\&\fBOSSL_CMP_CTX_set1_oldCert\fR\|(3), \fBOSSL_CMP_CTX_set0_newPkey\fR\|(3),
\&\fBOSSL_CMP_CTX_set1_pkey\fR\|(3), \fBOSSL_CMP_CTX_set0_reqExtensions\fR\|(3),
\&\fBOSSL_CMP_CTX_push1_subjectAltName\fR\|(3), \fBOSSL_CMP_CTX_push0_policy\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The OpenSSL CMP support was added in OpenSSL 3.0.
.PP
\&\fBOSSL_CMP_MSG_update_recipNonce()\fR was added in OpenSSL 3.0.9.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2007\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
