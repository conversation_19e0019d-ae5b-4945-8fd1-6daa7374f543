.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EC_POINT_ADD 3ossl"
.TH EC_POINT_ADD 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EC_POINT_add, EC_POINT_dbl, EC_POINT_invert, EC_POINT_is_at_infinity, EC_POINT_is_on_curve, EC_POINT_cmp, EC_POINT_make_affine, EC_POINTs_make_affine, EC_POINTs_mul, EC_POINT_mul, EC_GROUP_precompute_mult, EC_GROUP_have_precompute_mult \- Functions for performing mathematical operations and tests on EC_POINT objects
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ec.h>
\&
\& int EC_POINT_add(const EC_GROUP *group, EC_POINT *r, const EC_POINT *a,
\&                  const EC_POINT *b, BN_CTX *ctx);
\& int EC_POINT_dbl(const EC_GROUP *group, EC_POINT *r, const EC_POINT *a, BN_CTX *ctx);
\& int EC_POINT_invert(const EC_GROUP *group, EC_POINT *a, BN_CTX *ctx);
\& int EC_POINT_is_at_infinity(const EC_GROUP *group, const EC_POINT *p);
\& int EC_POINT_is_on_curve(const EC_GROUP *group, const EC_POINT *point, BN_CTX *ctx);
\& int EC_POINT_cmp(const EC_GROUP *group, const EC_POINT *a, const EC_POINT *b, BN_CTX *ctx);
\& int EC_POINT_mul(const EC_GROUP *group, EC_POINT *r, const BIGNUM *n,
\&                  const EC_POINT *q, const BIGNUM *m, BN_CTX *ctx);
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 7
\& int EC_POINT_make_affine(const EC_GROUP *group, EC_POINT *point, BN_CTX *ctx);
\& int EC_POINTs_make_affine(const EC_GROUP *group, size_t num,
\&                           EC_POINT *points[], BN_CTX *ctx);
\& int EC_POINTs_mul(const EC_GROUP *group, EC_POINT *r, const BIGNUM *n, size_t num,
\&                   const EC_POINT *p[], const BIGNUM *m[], BN_CTX *ctx);
\& int EC_GROUP_precompute_mult(EC_GROUP *group, BN_CTX *ctx);
\& int EC_GROUP_have_precompute_mult(const EC_GROUP *group);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
EC_POINT_add adds the two points \fBa\fR and \fBb\fR and places the result in \fBr\fR. Similarly EC_POINT_dbl doubles the point \fBa\fR and places the
result in \fBr\fR. In both cases it is valid for \fBr\fR to be one of \fBa\fR or \fBb\fR.
.PP
EC_POINT_invert calculates the inverse of the supplied point \fBa\fR. The result is placed back in \fBa\fR.
.PP
The function EC_POINT_is_at_infinity tests whether the supplied point is at infinity or not.
.PP
EC_POINT_is_on_curve tests whether the supplied point is on the curve or not.
.PP
EC_POINT_cmp compares the two supplied points and tests whether or not they are equal.
.PP
The functions EC_POINT_make_affine and EC_POINTs_make_affine force the internal representation of the EC_POINT(s) into the affine
coordinate system. In the case of EC_POINTs_make_affine the value \fBnum\fR provides the number of points in the array \fBpoints\fR to be
forced. These functions were deprecated in OpenSSL 3.0 and should no longer be used.
Modern versions automatically perform this conversion when needed.
.PP
EC_POINT_mul calculates the value generator * \fBn\fR + \fBq\fR * \fBm\fR and stores the result in \fBr\fR.
The value \fBn\fR may be NULL in which case the result is just \fBq\fR * \fBm\fR (variable point multiplication). Alternatively, both \fBq\fR and \fBm\fR may be NULL, and \fBn\fR non-NULL, in which case the result is just generator * \fBn\fR (fixed point multiplication).
When performing a single fixed or variable point multiplication, the underlying implementation uses a constant time algorithm, when the input scalar (either \fBn\fR or \fBm\fR) is in the range [0, ec_group_order).
.PP
Although deprecated in OpenSSL 3.0 and should no longer be used,
EC_POINTs_mul calculates the value generator * \fBn\fR + \fBq[0]\fR * \fBm[0]\fR + ... + \fBq[num\-1]\fR * \fBm[num\-1]\fR. As for EC_POINT_mul the value \fBn\fR may be NULL or \fBnum\fR may be zero.
When performing a fixed point multiplication (\fBn\fR is non-NULL and \fBnum\fR is 0) or a variable point multiplication (\fBn\fR is NULL and \fBnum\fR is 1), the underlying implementation uses a constant time algorithm, when the input scalar (either \fBn\fR or \fBm[0]\fR) is in the range [0, ec_group_order).
Modern versions should instead use \fBEC_POINT_mul()\fR, combined (if needed) with \fBEC_POINT_add()\fR in such rare circumstances.
.PP
The function EC_GROUP_precompute_mult stores multiples of the generator for faster point multiplication, whilst
EC_GROUP_have_precompute_mult tests whether precomputation has already been done. See \fBEC_GROUP_copy\fR\|(3) for information
about the generator. Precomputation functionality was deprecated in OpenSSL 3.0.
Users of \fBEC_GROUP_precompute_mult()\fR and \fBEC_GROUP_have_precompute_mult()\fR should
switch to named curves which OpenSSL has hardcoded lookup tables for.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The following functions return 1 on success or 0 on error: EC_POINT_add, EC_POINT_dbl, EC_POINT_invert, EC_POINT_make_affine,
EC_POINTs_make_affine, EC_POINTs_make_affine, EC_POINT_mul, EC_POINTs_mul and EC_GROUP_precompute_mult.
.PP
EC_POINT_is_at_infinity returns 1 if the point is at infinity, or 0 otherwise.
.PP
EC_POINT_is_on_curve returns 1 if the point is on the curve, 0 if not, or \-1 on error.
.PP
EC_POINT_cmp returns 1 if the points are not equal, 0 if they are, or \-1 on error.
.PP
EC_GROUP_have_precompute_mult return 1 if a precomputation has been done, or 0 if not.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7), \fBEC_GROUP_new\fR\|(3), \fBEC_GROUP_copy\fR\|(3),
\&\fBEC_POINT_new\fR\|(3), \fBEC_KEY_new\fR\|(3),
\&\fBEC_GFp_simple_method\fR\|(3), \fBd2i_ECPKParameters\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBEC_POINT_make_affine()\fR, \fBEC_POINTs_make_affine()\fR, \fBEC_POINTs_mul()\fR,
\&\fBEC_GROUP_precompute_mult()\fR, and \fBEC_GROUP_have_precompute_mult()\fR
were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2013\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
