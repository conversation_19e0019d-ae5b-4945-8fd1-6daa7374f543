.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_DECODER_CTX_NEW_FOR_PKEY 3ossl"
.TH OSSL_DECODER_CTX_NEW_FOR_PKEY 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_DECODER_CTX_new_for_pkey,
OSSL_DECODER_CTX_set_passphrase,
OSSL_DECODER_CTX_set_pem_password_cb,
OSSL_DECODER_CTX_set_passphrase_ui,
OSSL_DECODER_CTX_set_passphrase_cb
\&\- Decoder routines to decode EVP_PKEYs
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/decoder.h>
\&
\& OSSL_DECODER_CTX *
\& OSSL_DECODER_CTX_new_for_pkey(EVP_PKEY **pkey,
\&                               const char *input_type,
\&                               const char *input_struct,
\&                               const char *keytype, int selection,
\&                               OSSL_LIB_CTX *libctx, const char *propquery);
\&
\& int OSSL_DECODER_CTX_set_passphrase(OSSL_DECODER_CTX *ctx,
\&                                     const unsigned char *kstr,
\&                                     size_t klen);
\& int OSSL_DECODER_CTX_set_pem_password_cb(OSSL_DECODER_CTX *ctx,
\&                                          pem_password_cb *cb,
\&                                          void *cbarg);
\& int OSSL_DECODER_CTX_set_passphrase_ui(OSSL_DECODER_CTX *ctx,
\&                                        const UI_METHOD *ui_method,
\&                                        void *ui_data);
\& int OSSL_DECODER_CTX_set_passphrase_cb(OSSL_DECODER_CTX *ctx,
\&                                        OSSL_PASSPHRASE_CALLBACK *cb,
\&                                        void *cbarg);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOSSL_DECODER_CTX_new_for_pkey()\fR is a utility function that creates a
\&\fBOSSL_DECODER_CTX\fR, finds all applicable decoder implementations and sets
them up, so all the caller has to do next is call functions like
\&\fBOSSL_DECODER_from_bio\fR\|(3).  The caller may use the optional \fIinput_type\fR,
\&\fIinput_struct\fR, \fIkeytype\fR and \fIselection\fR to specify what the input is
expected to contain.  The \fIpkey\fR must reference an \fBEVP_PKEY *\fR variable
that will be set to the newly created \fBEVP_PKEY\fR on successful decoding.
The referenced variable must be initialized to NULL before calling the
function.
.PP
Internally \fBOSSL_DECODER_CTX_new_for_pkey()\fR searches for all available
\&\fBEVP_KEYMGMT\fR\|(3) implementations, and then builds a list of all potential
decoder implementations that may be able to process the encoded input into
data suitable for \fBEVP_PKEY\fRs.  All these implementations are implicitly
fetched using \fIlibctx\fR and \fIpropquery\fR.
.PP
The search of decoder implementations can be limited with \fIinput_type\fR and
\&\fIinput_struct\fR which specifies a starting input type and input structure.
NULL is valid for both of them and signifies that the decoder implementations
will find out the input type on their own.
They are set with \fBOSSL_DECODER_CTX_set_input_type\fR\|(3) and
\&\fBOSSL_DECODER_CTX_set_input_structure\fR\|(3).
See "Input Types" and "Input Structures" below for further information.
.PP
The search of decoder implementations can also be limited with \fIkeytype\fR
and \fIselection\fR, which specifies the expected resulting keytype and contents.
NULL and zero are valid and signify that the decoder implementations will
find out the keytype and key contents on their own from the input they get.
.PP
If no suitable decoder implementation is found,
\&\fBOSSL_DECODER_CTX_new_for_pkey()\fR still creates a \fBOSSL_DECODER_CTX\fR, but
with no associated decoder (\fBOSSL_DECODER_CTX_get_num_decoders\fR\|(3) returns
zero).  This helps the caller to distinguish between an error when creating
the \fBOSSL_ENCODER_CTX\fR and missing encoder implementation, and allows it to
act accordingly.
.PP
\&\fBOSSL_DECODER_CTX_set_passphrase()\fR gives the implementation a pass phrase to
use when decrypting the encoded private key. Alternatively, a pass phrase
callback may be specified with the following functions.
.PP
\&\fBOSSL_DECODER_CTX_set_pem_password_cb()\fR, \fBOSSL_DECODER_CTX_set_passphrase_ui()\fR
and \fBOSSL_DECODER_CTX_set_passphrase_cb()\fR set up a callback method that the
implementation can use to prompt for a pass phrase, giving the caller the
choice of preferred pass phrase callback form.  These are called indirectly,
through an internal \fBOSSL_PASSPHRASE_CALLBACK\fR\|(3) function.
.PP
The internal \fBOSSL_PASSPHRASE_CALLBACK\fR\|(3) function caches the pass phrase, to
be re-used in all decodings that are performed in the same decoding run (for
example, within one \fBOSSL_DECODER_from_bio\fR\|(3) call).
.SS "Input Types"
.IX Subsection "Input Types"
Available input types depend on the implementations that available providers
offer, and provider documentation should have the details.
.PP
Among the known input types that OpenSSL decoder implementations offer
for \fBEVP_PKEY\fRs are \f(CW\*(C`DER\*(C'\fR, \f(CW\*(C`PEM\*(C'\fR, \f(CW\*(C`MSBLOB\*(C'\fR and \f(CW\*(C`PVK\*(C'\fR.
See \fBopenssl\-glossary\fR\|(7) for further information on what these input
types mean.
.SS "Input Structures"
.IX Subsection "Input Structures"
Available input structures depend on the implementations that available
providers offer, and provider documentation should have the details.
.PP
Among the known input structures that OpenSSL decoder implementations
offer for \fBEVP_PKEY\fRs are \f(CW\*(C`pkcs8\*(C'\fR and \f(CW\*(C`SubjectPublicKeyInfo\*(C'\fR.
.PP
OpenSSL decoder implementations also support the input structure
\&\f(CW\*(C`type\-specific\*(C'\fR.  This is the structure used for keys encoded
according to key type specific specifications.  For example, RSA keys
encoded according to PKCS#1.
.SS Selections
.IX Subsection "Selections"
\&\fIselection\fR can be any one of the values described in
"Selections" in \fBEVP_PKEY_fromdata\fR\|(3).
Additionally \fIselection\fR can also be set to \fB0\fR to indicate that the code will
auto detect the selection.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_DECODER_CTX_new_for_pkey()\fR returns a pointer to a
\&\fBOSSL_DECODER_CTX\fR, or NULL if it couldn't be created.
.PP
\&\fBOSSL_DECODER_CTX_set_passphrase()\fR, \fBOSSL_DECODER_CTX_set_pem_password_cb()\fR,
\&\fBOSSL_DECODER_CTX_set_passphrase_ui()\fR and
\&\fBOSSL_DECODER_CTX_set_passphrase_cb()\fR all return 1 on success, or 0 on
failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\fR\|(7), \fBOSSL_DECODER\fR\|(3), \fBOSSL_DECODER_CTX\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The functions described here were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
