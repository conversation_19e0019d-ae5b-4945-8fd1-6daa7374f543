.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DSA_GENERATE_PARAMETERS 3ossl"
.TH DSA_GENERATE_PARAMETERS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
DSA_generate_parameters_ex, DSA_generate_parameters \- generate DSA parameters
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/dsa.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 4
\& int DSA_generate_parameters_ex(DSA *dsa, int bits,
\&                                const unsigned char *seed, int seed_len,
\&                                int *counter_ret, unsigned long *h_ret,
\&                                BN_GENCB *cb);
.Ve
.PP
The following functions have been deprecated since OpenSSL 0.9.8, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 3
\& DSA *DSA_generate_parameters(int bits, unsigned char *seed, int seed_len,
\&                              int *counter_ret, unsigned long *h_ret,
\&                              void (*callback)(int, int, void *), void *cb_arg);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated.
Applications should instead use \fBEVP_PKEY_paramgen_init\fR\|(3) and
\&\fBEVP_PKEY_keygen\fR\|(3) as described in \fBEVP_PKEY\-DSA\fR\|(7).
.PP
\&\fBDSA_generate_parameters_ex()\fR generates primes p and q and a generator g
for use in the DSA and stores the result in \fBdsa\fR.
.PP
\&\fBbits\fR is the length of the prime p to be generated.
For lengths under 2048 bits, the length of q is 160 bits; for lengths
greater than or equal to 2048 bits, the length of q is set to 256 bits.
.PP
If \fBseed\fR is NULL, the primes will be generated at random.
If \fBseed_len\fR is less than the length of q, an error is returned.
.PP
\&\fBDSA_generate_parameters_ex()\fR places the iteration count in
*\fBcounter_ret\fR and a counter used for finding a generator in
*\fBh_ret\fR, unless these are \fBNULL\fR.
.PP
A callback function may be used to provide feedback about the progress
of the key generation. If \fBcb\fR is not \fBNULL\fR, it will be
called as shown below. For information on the BN_GENCB structure and the
BN_GENCB_call function discussed below, refer to
\&\fBBN_generate_prime\fR\|(3).
.PP
\&\fBDSA_generate_parameters()\fR is similar to \fBDSA_generate_parameters_ex()\fR but
expects an old-style callback function; see
\&\fBBN_generate_prime\fR\|(3) for information on the old-style callback.
.IP \(bu 2
When a candidate for q is generated, \fBBN_GENCB_call(cb, 0, m++)\fR is called
(m is 0 for the first candidate).
.IP \(bu 2
When a candidate for q has passed a test by trial division,
\&\fBBN_GENCB_call(cb, 1, \-1)\fR is called.
While a candidate for q is tested by Miller-Rabin primality tests,
\&\fBBN_GENCB_call(cb, 1, i)\fR is called in the outer loop
(once for each witness that confirms that the candidate may be prime);
i is the loop counter (starting at 0).
.IP \(bu 2
When a prime q has been found, \fBBN_GENCB_call(cb, 2, 0)\fR and
\&\fBBN_GENCB_call(cb, 3, 0)\fR are called.
.IP \(bu 2
Before a candidate for p (other than the first) is generated and tested,
\&\fBBN_GENCB_call(cb, 0, counter)\fR is called.
.IP \(bu 2
When a candidate for p has passed the test by trial division,
\&\fBBN_GENCB_call(cb, 1, \-1)\fR is called.
While it is tested by the Miller-Rabin primality test,
\&\fBBN_GENCB_call(cb, 1, i)\fR is called in the outer loop
(once for each witness that confirms that the candidate may be prime).
i is the loop counter (starting at 0).
.IP \(bu 2
When p has been found, \fBBN_GENCB_call(cb, 2, 1)\fR is called.
.IP \(bu 2
When the generator has been found, \fBBN_GENCB_call(cb, 3, 1)\fR is called.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBDSA_generate_parameters_ex()\fR returns a 1 on success, or 0 otherwise.
The error codes can be obtained by \fBERR_get_error\fR\|(3).
.PP
\&\fBDSA_generate_parameters()\fR returns a pointer to the DSA structure or
\&\fBNULL\fR if the parameter generation fails.
.SH BUGS
.IX Header "BUGS"
Seed lengths greater than 20 are not supported.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBDSA_new\fR\|(3), \fBERR_get_error\fR\|(3), \fBRAND_bytes\fR\|(3),
\&\fBDSA_free\fR\|(3), \fBBN_generate_prime\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBDSA_generate_parameters_ex()\fR was deprecated in OpenSSL 3.0.
.PP
\&\fBDSA_generate_parameters()\fR was deprecated in OpenSSL 0.9.8; use
\&\fBDSA_generate_parameters_ex()\fR instead.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
