.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_CRMF_MSG_SET1_REGINFO_CERTREQ 3ossl"
.TH OSSL_CRMF_MSG_SET1_REGINFO_CERTREQ 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_CRMF_MSG_get0_regInfo_utf8Pairs,
OSSL_CRMF_MSG_set1_regInfo_utf8Pairs,
OSSL_CRMF_MSG_get0_regInfo_certReq,
OSSL_CRMF_MSG_set1_regInfo_certReq
\&\- functions getting or setting CRMF Registration Info
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/crmf.h>
\&
\& ASN1_UTF8STRING
\&     *OSSL_CRMF_MSG_get0_regInfo_utf8Pairs(const OSSL_CRMF_MSG *msg);
\& int OSSL_CRMF_MSG_set1_regInfo_utf8Pairs(OSSL_CRMF_MSG *msg,
\&                                          const ASN1_UTF8STRING *utf8pairs);
\& OSSL_CRMF_CERTREQUEST
\&     *OSSL_CRMF_MSG_get0_regInfo_certReq(const OSSL_CRMF_MSG *msg);
\& int OSSL_CRMF_MSG_set1_regInfo_certReq(OSSL_CRMF_MSG *msg,
\&                                        const OSSL_CRMF_CERTREQUEST *cr);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOSSL_CRMF_MSG_get0_regInfo_utf8Pairs()\fR returns the first utf8Pairs regInfo
in the given \fImsg\fR, if present.
.PP
\&\fBOSSL_CRMF_MSG_set1_regInfo_utf8Pairs()\fR adds a copy of the given \fIutf8pairs\fR
value as utf8Pairs regInfo to the given \fImsg\fR. See RFC 4211 section 7.1.
.PP
\&\fBOSSL_CRMF_MSG_get0_regInfo_certReq()\fR returns the first certReq regInfo
in the given \fImsg\fR, if present.
.PP
\&\fBOSSL_CRMF_MSG_set1_regInfo_certReq()\fR adds a copy of the given \fIcr\fR value
as certReq regInfo to the given \fImsg\fR. See RFC 4211 section 7.2.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All get0_*() functions return the respective pointer value, NULL if not present.
.PP
All set1_*() functions return 1 on success, 0 on error.
.SH NOTES
.IX Header "NOTES"
Calling the set1_*() functions multiple times
adds multiple instances of the respective
control to the regInfo structure of the given \fImsg\fR. While RFC 4211 expects
multiple utf8Pairs in one regInfo structure, it does not allow multiple certReq.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
RFC 4211
.SH HISTORY
.IX Header "HISTORY"
The OpenSSL CRMF support was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2007\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
