.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_HTTP_REQ_CTX 3ossl"
.TH OSSL_HTTP_REQ_CTX 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_HTTP_REQ_CTX,
OSSL_HTTP_REQ_CTX_new,
OSSL_HTTP_REQ_CTX_free,
OSSL_HTTP_REQ_CTX_set_request_line,
OSSL_HTTP_REQ_CTX_add1_header,
OSSL_HTTP_REQ_CTX_set_expected,
OSSL_HTTP_REQ_CTX_set1_req,
OSSL_HTTP_REQ_CTX_nbio,
OSSL_HTTP_REQ_CTX_nbio_d2i,
OSSL_HTTP_REQ_CTX_exchange,
OSSL_HTTP_REQ_CTX_get0_mem_bio,
OSSL_HTTP_REQ_CTX_get_resp_len,
OSSL_HTTP_REQ_CTX_set_max_response_length,
OSSL_HTTP_is_alive
\&\- HTTP client low\-level functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/http.h>
\&
\& typedef struct ossl_http_req_ctx_st OSSL_HTTP_REQ_CTX;
\&
\& OSSL_HTTP_REQ_CTX *OSSL_HTTP_REQ_CTX_new(BIO *wbio, BIO *rbio, int buf_size);
\& void OSSL_HTTP_REQ_CTX_free(OSSL_HTTP_REQ_CTX *rctx);
\&
\& int OSSL_HTTP_REQ_CTX_set_request_line(OSSL_HTTP_REQ_CTX *rctx, int method_POST,
\&                                        const char *server, const char *port,
\&                                        const char *path);
\& int OSSL_HTTP_REQ_CTX_add1_header(OSSL_HTTP_REQ_CTX *rctx,
\&                                   const char *name, const char *value);
\&
\& int OSSL_HTTP_REQ_CTX_set_expected(OSSL_HTTP_REQ_CTX *rctx,
\&                                    const char *content_type, int asn1,
\&                                    int timeout, int keep_alive);
\& int OSSL_HTTP_REQ_CTX_set1_req(OSSL_HTTP_REQ_CTX *rctx, const char *content_type,
\&                                const ASN1_ITEM *it, const ASN1_VALUE *req);
\& int OSSL_HTTP_REQ_CTX_nbio(OSSL_HTTP_REQ_CTX *rctx);
\& int OSSL_HTTP_REQ_CTX_nbio_d2i(OSSL_HTTP_REQ_CTX *rctx,
\&                                ASN1_VALUE **pval, const ASN1_ITEM *it);
\& BIO *OSSL_HTTP_REQ_CTX_exchange(OSSL_HTTP_REQ_CTX *rctx);
\&
\& BIO *OSSL_HTTP_REQ_CTX_get0_mem_bio(const OSSL_HTTP_REQ_CTX *rctx);
\& size_t OSSL_HTTP_REQ_CTX_get_resp_len(const OSSL_HTTP_REQ_CTX *rctx);
\& void OSSL_HTTP_REQ_CTX_set_max_response_length(OSSL_HTTP_REQ_CTX *rctx,
\&                                                unsigned long len);
\&
\& int OSSL_HTTP_is_alive(const OSSL_HTTP_REQ_CTX *rctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOSSL_HTTP_REQ_CTX\fR is a context structure for an HTTP request and response,
used to collect all the necessary data to perform that request.
.PP
This file documents low-level HTTP functions rarely used directly.  High-level
HTTP client functions like \fBOSSL_HTTP_get\fR\|(3) and \fBOSSL_HTTP_transfer\fR\|(3)
should be preferred.
.PP
\&\fBOSSL_HTTP_REQ_CTX_new()\fR allocates a new HTTP request context structure,
which gets populated with the \fBBIO\fR to write/send the request to (\fIwbio\fR),
the \fBBIO\fR to read/receive the response from (\fIrbio\fR, which may be equal to
\&\fIwbio\fR), and the maximum expected response header line length \fIbuf_size\fR.
A value <= 0 indicates that
the \fBOSSL_HTTP_DEFAULT_MAX_LINE_LEN\fR of 4KiB should be used.
\&\fIbuf_size\fR is also used as the number of content bytes that are read at a time.
The allocated context structure includes an internal memory \fBBIO\fR,
which collects the HTTP request header lines.
.PP
\&\fBOSSL_HTTP_REQ_CTX_free()\fR frees up the HTTP request context \fIrctx\fR.
The \fIrbio\fR is not free'd, \fIwbio\fR will be free'd if \fIfree_wbio\fR is set.
.PP
\&\fBOSSL_HTTP_REQ_CTX_set_request_line()\fR adds the 1st HTTP request line to \fIrctx\fR.
The HTTP method is determined by \fImethod_POST\fR,
which should be 1 to indicate \f(CW\*(C`POST\*(C'\fR or 0 to indicate \f(CW\*(C`GET\*(C'\fR.
\&\fIserver\fR and \fIport\fR may be set to give the server and the optional port that
an HTTP proxy shall forward the request to, otherwise they must be left NULL.
\&\fIpath\fR provides the HTTP request path; if left NULL, \f(CW\*(C`/\*(C'\fR is used.
For backward compatibility, \fIpath\fR may begin with \f(CW\*(C`http://\*(C'\fR and thus convey
an absoluteURI. In this case it indicates HTTP proxy use and provides also the
server (and optionally the port) that the proxy shall forward the request to.
In this case the \fIserver\fR and \fIport\fR arguments must be NULL.
.PP
\&\fBOSSL_HTTP_REQ_CTX_add1_header()\fR adds header \fIname\fR with value \fIvalue\fR to the
context \fIrctx\fR. It can be called more than once to add multiple header lines.
For example, to add a \f(CW\*(C`Host\*(C'\fR header for \f(CW\*(C`example.com\*(C'\fR you would call:
.PP
.Vb 1
\& OSSL_HTTP_REQ_CTX_add1_header(ctx, "Host", "example.com");
.Ve
.PP
\&\fBOSSL_HTTP_REQ_CTX_set_expected()\fR optionally sets in \fIrctx\fR some expectations
of the HTTP client on the response.
Due to the structure of an HTTP request, if the \fIkeep_alive\fR argument is
nonzero the function must be used before calling \fBOSSL_HTTP_REQ_CTX_set1_req()\fR.
If the \fIcontent_type\fR parameter
is not NULL then the client will check that the given content type string
is included in the HTTP header of the response and return an error if not.
If the \fIasn1\fR parameter is nonzero a structure in ASN.1 encoding will be
expected as the response content and input streaming is disabled.  This means
that an ASN.1 sequence header is required, its length field is checked, and
\&\fBOSSL_HTTP_REQ_CTX_get0_mem_bio()\fR should be used to get the buffered response.
Otherwise (by default) any input format is allowed without length checks.
In this case the BIO given as \fIrbio\fR argument to \fBOSSL_HTTP_REQ_CTX_new()\fR should
be used directly to read the response contents, which may support streaming.
If the \fItimeout\fR parameter is > 0 this indicates the maximum number of seconds
the subsequent HTTP transfer (sending the request and receiving a response)
is allowed to take.
\&\fItimeout\fR == 0 enables waiting indefinitely, i.e., no timeout can occur.
This is the default.
\&\fItimeout\fR < 0 takes over any value set via the \fIoverall_timeout\fR argument of
\&\fBOSSL_HTTP_open\fR\|(3) with the default being 0, which means no timeout.
If the \fIkeep_alive\fR parameter is 0, which is the default, the connection is not
kept open after receiving a response. This is the default behavior for HTTP 1.0.
If the value is 1 or 2 then a persistent connection is requested.
If the value is 2 then a persistent connection is required,
i.e., an error occurs in case the server does not grant it.
.PP
\&\fBOSSL_HTTP_REQ_CTX_set1_req()\fR finalizes the HTTP request context.
It is needed if the \fImethod_POST\fR parameter in the
\&\fBOSSL_HTTP_REQ_CTX_set_request_line()\fR call was 1
and an ASN.1\-encoded request should be sent.
It must also be used when requesting "keep-alive",
even if a GET request is going to be sent, in which case \fIreq\fR must be NULL.
Unless \fIreq\fR is NULL, the function adds the DER encoding of \fIreq\fR using
the ASN.1 template \fIit\fR to do the encoding (which does not support streaming).
The HTTP header \f(CW\*(C`Content\-Length\*(C'\fR is filled out with the length of the request.
\&\fIcontent_type\fR must be NULL if \fIreq\fR is NULL.
If \fIcontent_type\fR isn't NULL,
the HTTP header \f(CW\*(C`Content\-Type\*(C'\fR is also added with the given string value.
The header lines are added to the internal memory \fBBIO\fR for the request header.
.PP
\&\fBOSSL_HTTP_REQ_CTX_nbio()\fR attempts to send the request prepared in \fIrctx\fR
and to gather the response via HTTP, using the \fIwbio\fR and \fIrbio\fR
that were given when calling \fBOSSL_HTTP_REQ_CTX_new()\fR.
The function may need to be called again if its result is \-1, which indicates
\&\fBBIO_should_retry\fR\|(3).  In such a case it is advisable to sleep a little in
between, using \fBBIO_wait\fR\|(3) on the read BIO to prevent a busy loop.
.PP
\&\fBOSSL_HTTP_REQ_CTX_nbio_d2i()\fR is like \fBOSSL_HTTP_REQ_CTX_nbio()\fR but on success
in addition parses the response, which must be a DER-encoded ASN.1 structure,
using the ASN.1 template \fIit\fR and places the result in \fI*pval\fR.
.PP
\&\fBOSSL_HTTP_REQ_CTX_exchange()\fR calls \fBOSSL_HTTP_REQ_CTX_nbio()\fR as often as needed
in order to exchange a request and response or until a timeout is reached.
On success it returns a pointer to the BIO that can be used to read the result.
If an ASN.1\-encoded response was expected, this is the BIO
returned by \fBOSSL_HTTP_REQ_CTX_get0_mem_bio()\fR when called after the exchange.
This memory BIO does not support streaming.
Otherwise the returned BIO is the \fIrbio\fR given to \fBOSSL_HTTP_REQ_CTX_new()\fR,
which may support streaming.
When this BIO is returned, it has been read past the end of the response header,
such that the actual response body can be read from it.
The returned BIO pointer MUST NOT be freed by the caller.
.PP
\&\fBOSSL_HTTP_REQ_CTX_get0_mem_bio()\fR returns the internal memory \fBBIO\fR.
Before the HTTP request is sent, this could be used to adapt its header lines.
\&\fIUse with caution!\fR
After receiving a response via HTTP, the BIO represents the current state of
reading the response header. If the response was expected to be ASN.1 encoded,
its contents can be read via this BIO, which does not support streaming.
The returned BIO pointer must not be freed by the caller.
.PP
\&\fBOSSL_HTTP_REQ_CTX_get_resp_len()\fR returns the size of the response contents
in \fIrctx\fR if provided by the server as <Content\-Length> header field, else 0.
.PP
\&\fBOSSL_HTTP_REQ_CTX_set_max_response_length()\fR sets the maximum allowed
response content length for \fIrctx\fR to \fIlen\fR. If not set or \fIlen\fR is 0
then the \fBOSSL_HTTP_DEFAULT_MAX_RESP_LEN\fR is used, which currently is 100 KiB.
If the \f(CW\*(C`Content\-Length\*(C'\fR header is present and exceeds this value or
the content is an ASN.1 encoded structure with a length exceeding this value
or both length indications are present but disagree then an error occurs.
.PP
\&\fBOSSL_HTTP_is_alive()\fR can be used to query if the HTTP connection
given by \fIrctx\fR is still alive, i.e., has not been closed.
It returns 0 if \fIrctx\fR is NULL.
.PP
If the client application requested or required a persistent connection
and this was granted by the server, it can keep \fIrctx\fR as long as it wants
to send further requests and \fBOSSL_HTTP_is_alive()\fR returns nonzero,
else it should call \fIOSSL_HTTP_REQ_CTX_free(rctx)\fR or \fBOSSL_HTTP_close\fR\|(3).
In case the client application keeps \fIrctx\fR but the connection then dies
for any reason at the server side, it will notice this obtaining an
I/O error when trying to send the next request via \fIrctx\fR.
.SH WARNINGS
.IX Header "WARNINGS"
The server's response may be unexpected if the hostname that was used to
create the \fIwbio\fR, any \f(CW\*(C`Host\*(C'\fR header, and the host specified in the
request URL do not match.
.PP
Many of these functions must be called in a certain order.
.PP
First, the HTTP request context must be allocated:
\&\fBOSSL_HTTP_REQ_CTX_new()\fR.
.PP
Then, the HTTP request must be prepared with request data:
.IP 1. 4
Calling \fBOSSL_HTTP_REQ_CTX_set_request_line()\fR.
.IP 2. 4
Adding extra header lines with \fBOSSL_HTTP_REQ_CTX_add1_header()\fR.
This is optional and may be done multiple times with different names.
.IP 3. 4
Finalize the request using \fBOSSL_HTTP_REQ_CTX_set1_req()\fR.
This may be omitted if the GET method is used and "keep-alive" is not requested.
.PP
When the request context is fully prepared, the HTTP exchange may be performed
with \fBOSSL_HTTP_REQ_CTX_nbio()\fR or \fBOSSL_HTTP_REQ_CTX_exchange()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_HTTP_REQ_CTX_new()\fR returns a pointer to a \fBOSSL_HTTP_REQ_CTX\fR, or NULL
on error.
.PP
\&\fBOSSL_HTTP_REQ_CTX_free()\fR and \fBOSSL_HTTP_REQ_CTX_set_max_response_length()\fR
do not return values.
.PP
\&\fBOSSL_HTTP_REQ_CTX_set_request_line()\fR, \fBOSSL_HTTP_REQ_CTX_add1_header()\fR,
\&\fBOSSL_HTTP_REQ_CTX_set1_req()\fR, and \fBOSSL_HTTP_REQ_CTX_set_expected()\fR
return 1 for success and 0 for failure.
.PP
\&\fBOSSL_HTTP_REQ_CTX_nbio()\fR and \fBOSSL_HTTP_REQ_CTX_nbio_d2i()\fR
return 1 for success, 0 on error or redirection, \-1 if retry is needed.
.PP
\&\fBOSSL_HTTP_REQ_CTX_exchange()\fR and \fBOSSL_HTTP_REQ_CTX_get0_mem_bio()\fR
return a pointer to a \fBBIO\fR on success as described above or NULL on failure.
The returned BIO must not be freed by the caller.
.PP
\&\fBOSSL_HTTP_REQ_CTX_get_resp_len()\fR returns the size of the response contents
or 0 if not available or an error occurred.
.PP
\&\fBOSSL_HTTP_is_alive()\fR returns 1 if its argument is non-NULL
and the client requested a persistent connection
and the server did not disagree on keeping the connection open, else 0.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBBIO_should_retry\fR\|(3),
\&\fBBIO_wait\fR\|(3),
\&\fBASN1_item_d2i_bio\fR\|(3),
\&\fBASN1_item_i2d_mem_bio\fR\|(3),
\&\fBOSSL_HTTP_open\fR\|(3),
\&\fBOSSL_HTTP_get\fR\|(3),
\&\fBOSSL_HTTP_transfer\fR\|(3),
\&\fBOSSL_HTTP_close\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The functions described here were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
