.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_CTX_GET0_PKEY 3ossl"
.TH EVP_PKEY_CTX_GET0_PKEY 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_CTX_get0_pkey,
EVP_PKEY_CTX_get0_peerkey
\&\- functions for accessing the EVP_PKEY associated with an EVP_PKEY_CTX
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& EVP_PKEY *EVP_PKEY_CTX_get0_pkey(EVP_PKEY_CTX *ctx);
\& EVP_PKEY *EVP_PKEY_CTX_get0_peerkey(EVP_PKEY_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_PKEY_CTX_get0_pkey()\fR is used to access the \fBEVP_PKEY\fR
associated with the given \fBEVP_PKEY_CTX\fR \fIctx\fR.
The \fBEVP_PKEY\fR obtained is the one used for creating the \fBEVP_PKEY_CTX\fR
using either \fBEVP_PKEY_CTX_new\fR\|(3) or \fBEVP_PKEY_CTX_new_from_pkey\fR\|(3).
.PP
\&\fBEVP_PKEY_CTX_get0_peerkey()\fR is used to access the peer \fBEVP_PKEY\fR
associated with the given \fBEVP_PKEY_CTX\fR \fIctx\fR.
The peer \fBEVP_PKEY\fR obtained is the one set using
either \fBEVP_PKEY_derive_set_peer\fR\|(3) or \fBEVP_PKEY_derive_set_peer_ex\fR\|(3).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_CTX_get0_pkey()\fR returns the \fBEVP_PKEY\fR associated with the
EVP_PKEY_CTX or NULL if it is not set.
.PP
\&\fBEVP_PKEY_CTX_get0_peerkey()\fR returns the peer \fBEVP_PKEY\fR associated with the
EVP_PKEY_CTX or NULL if it is not set.
.PP
The returned EVP_PKEY objects are owned by the EVP_PKEY_CTX,
and therefore should not explicitly be freed by the caller.
.PP
These functions do not affect the EVP_PKEY reference count.
They merely act as getter functions, and should be treated as such.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new\fR\|(3), \fBEVP_PKEY_CTX_new_from_pkey\fR\|(3),
\&\fBEVP_PKEY_derive_set_peer\fR\|(3), \fBEVP_PKEY_derive_set_peer_ex\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2022\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").
You may not use this file except in compliance with the License.
You can obtain a copy in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
