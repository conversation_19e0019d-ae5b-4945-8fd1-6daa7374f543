.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_GET_GROUP_NAME 3ossl"
.TH EVP_PKEY_GET_GROUP_NAME 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_get_group_name \- get group name of a key
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_get_group_name(EVP_PKEY *pkey, char *gname, size_t gname_sz,
\&                             size_t *gname_len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_PKEY_get_group_name()\fR fills in the group name of the \fIpkey\fR into
\&\fIgname\fR, up to at most \fIgname_sz\fR bytes including the ending NUL byte
and assigns \fI*gname_len\fR the actual length of the name not including
the NUL byte, if \fIpkey\fR's key type supports it.
\&\fIgname\fR as well as \fIgname_len\fR may individually be NULL, and won't be
filled in or assigned in that case.
.SH NOTES
.IX Header "NOTES"
Among the standard OpenSSL key types, this is only supported for DH, EC and
SM2 keys.  Other providers may support this for additional key types.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_get_group_name()\fR returns 1 if the group name could be filled in,
otherwise 0.
.SH HISTORY
.IX Header "HISTORY"
This function was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
