.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL_SECURE_MALLOC 3ossl"
.TH OPENSSL_SECURE_MALLOC 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CRYPTO_secure_malloc_init, CRYPTO_secure_malloc_initialized,
CRYPTO_secure_malloc_done, OPENSSL_secure_malloc, CRYPTO_secure_malloc,
OPENSSL_secure_zalloc, CRYPTO_secure_zalloc, OPENSSL_secure_free,
CRYPTO_secure_free, OPENSSL_secure_clear_free,
CRYPTO_secure_clear_free, OPENSSL_secure_actual_size,
CRYPTO_secure_allocated,
CRYPTO_secure_used \- secure heap storage
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/crypto.h>
\&
\& int CRYPTO_secure_malloc_init(size_t size, size_t minsize);
\&
\& int CRYPTO_secure_malloc_initialized();
\&
\& int CRYPTO_secure_malloc_done();
\&
\& void *OPENSSL_secure_malloc(size_t num);
\& void *CRYPTO_secure_malloc(size_t num, const char *file, int line);
\&
\& void *OPENSSL_secure_zalloc(size_t num);
\& void *CRYPTO_secure_zalloc(size_t num, const char *file, int line);
\&
\& void OPENSSL_secure_free(void* ptr);
\& void CRYPTO_secure_free(void *ptr, const char *, int);
\&
\& void OPENSSL_secure_clear_free(void* ptr, size_t num);
\& void CRYPTO_secure_clear_free(void *ptr, size_t num, const char *, int);
\&
\& size_t OPENSSL_secure_actual_size(const void *ptr);
\&
\& int CRYPTO_secure_allocated(const void *ptr);
\& size_t CRYPTO_secure_used();
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
In order to help protect applications (particularly long-running servers)
from pointer overruns or underruns that could return arbitrary data from
the program's dynamic memory area, where keys and other sensitive
information might be stored, OpenSSL supports the concept of a "secure heap."
The level and type of security guarantees depend on the operating system.
It is a good idea to review the code and see if it addresses your
threat model and concerns.
.PP
If a secure heap is used, then private key \fBBIGNUM\fR values are stored there.
This protects long-term storage of private keys, but will not necessarily
put all intermediate values and computations there.
.PP
\&\fBCRYPTO_secure_malloc_init()\fR creates the secure heap, with the specified
\&\f(CW\*(C`size\*(C'\fR in bytes. The \f(CW\*(C`minsize\*(C'\fR parameter is the minimum size to
allocate from the heap or zero to use a reasonable default value.
Both \f(CW\*(C`size\*(C'\fR and, if specified, \f(CW\*(C`minsize\*(C'\fR must be a power of two and
\&\f(CW\*(C`minsize\*(C'\fR should generally be small, for example 16 or 32.
\&\f(CW\*(C`minsize\*(C'\fR must be less than a quarter of \f(CW\*(C`size\*(C'\fR in any case.
.PP
\&\fBCRYPTO_secure_malloc_initialized()\fR indicates whether or not the secure
heap as been initialized and is available.
.PP
\&\fBCRYPTO_secure_malloc_done()\fR releases the heap and makes the memory unavailable
to the process if all secure memory has been freed.
It can take noticeably long to complete.
.PP
\&\fBOPENSSL_secure_malloc()\fR allocates \f(CW\*(C`num\*(C'\fR bytes from the heap.
If \fBCRYPTO_secure_malloc_init()\fR is not called, this is equivalent to
calling \fBOPENSSL_malloc()\fR.
It is a macro that expands to
\&\fBCRYPTO_secure_malloc()\fR and adds the \f(CW\*(C`_\|_FILE_\|_\*(C'\fR and \f(CW\*(C`_\|_LINE_\|_\*(C'\fR parameters.
.PP
\&\fBOPENSSL_secure_zalloc()\fR and \fBCRYPTO_secure_zalloc()\fR are like
\&\fBOPENSSL_secure_malloc()\fR and \fBCRYPTO_secure_malloc()\fR, respectively,
except that they call \fBmemset()\fR to zero the memory before returning.
.PP
\&\fBOPENSSL_secure_free()\fR releases the memory at \f(CW\*(C`ptr\*(C'\fR back to the heap.
It must be called with a value previously obtained from
\&\fBOPENSSL_secure_malloc()\fR.
If \fBCRYPTO_secure_malloc_init()\fR is not called, this is equivalent to
calling \fBOPENSSL_free()\fR.
It exists for consistency with \fBOPENSSL_secure_malloc()\fR , and
is a macro that expands to \fBCRYPTO_secure_free()\fR and adds the \f(CW\*(C`_\|_FILE_\|_\*(C'\fR
and \f(CW\*(C`_\|_LINE_\|_\*(C'\fR parameters..
.PP
\&\fBOPENSSL_secure_clear_free()\fR is similar to \fBOPENSSL_secure_free()\fR except
that it has an additional \f(CW\*(C`num\*(C'\fR parameter which is used to clear
the memory if it was not allocated from the secure heap.
If \fBCRYPTO_secure_malloc_init()\fR is not called, this is equivalent to
calling \fBOPENSSL_clear_free()\fR.
.PP
\&\fBOPENSSL_secure_actual_size()\fR tells the actual size allocated to the
pointer; implementations may allocate more space than initially
requested, in order to "round up" and reduce secure heap fragmentation.
.PP
\&\fBOPENSSL_secure_allocated()\fR tells if a pointer is allocated in the secure heap.
.PP
\&\fBCRYPTO_secure_used()\fR returns the number of bytes allocated in the
secure heap.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCRYPTO_secure_malloc_init()\fR returns 0 on failure, 1 if successful,
and 2 if successful but the heap could not be protected by memory
mapping.
.PP
\&\fBCRYPTO_secure_malloc_initialized()\fR returns 1 if the secure heap is
available (that is, if \fBCRYPTO_secure_malloc_init()\fR has been called,
but \fBCRYPTO_secure_malloc_done()\fR has not been called or failed) or 0 if not.
.PP
\&\fBOPENSSL_secure_malloc()\fR and \fBOPENSSL_secure_zalloc()\fR return a pointer into
the secure heap of the requested size, or \f(CW\*(C`NULL\*(C'\fR if memory could not be
allocated.
.PP
\&\fBCRYPTO_secure_allocated()\fR returns 1 if the pointer is in the secure heap, or 0 if not.
.PP
\&\fBCRYPTO_secure_malloc_done()\fR returns 1 if the secure memory area is released, or 0 if not.
.PP
\&\fBOPENSSL_secure_free()\fR and \fBOPENSSL_secure_clear_free()\fR return no values.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOPENSSL_malloc\fR\|(3),
\&\fBBN_new\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBOPENSSL_secure_clear_free()\fR function was added in OpenSSL 1.1.0g.
.PP
The second argument to \fBCRYPTO_secure_malloc_init()\fR was changed from an \fBint\fR to
a \fBsize_t\fR in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
