.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ECDSA_SIGN 3ossl"
.TH ECDSA_SIGN 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ECDSA_size, ECDSA_sign, ECDSA_do_sign,
ECDSA_verify, ECDSA_do_verify, ECDSA_sign_setup, ECDSA_sign_ex,
ECDSA_do_sign_ex \- deprecated low\-level elliptic curve digital signature algorithm
(ECDSA) functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ecdsa.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& int ECDSA_size(const EC_KEY *eckey);
\&
\& int ECDSA_sign(int type, const unsigned char *dgst, int dgstlen,
\&                unsigned char *sig, unsigned int *siglen, EC_KEY *eckey);
\& ECDSA_SIG *ECDSA_do_sign(const unsigned char *dgst, int dgst_len,
\&                          EC_KEY *eckey);
\&
\& int ECDSA_verify(int type, const unsigned char *dgst, int dgstlen,
\&                  const unsigned char *sig, int siglen, EC_KEY *eckey);
\& int ECDSA_do_verify(const unsigned char *dgst, int dgst_len,
\&                     const ECDSA_SIG *sig, EC_KEY* eckey);
\&
\& ECDSA_SIG *ECDSA_do_sign_ex(const unsigned char *dgst, int dgstlen,
\&                             const BIGNUM *kinv, const BIGNUM *rp,
\&                             EC_KEY *eckey);
\& int ECDSA_sign_setup(EC_KEY *eckey, BN_CTX *ctx, BIGNUM **kinv, BIGNUM **rp);
\& int ECDSA_sign_ex(int type, const unsigned char *dgst, int dgstlen,
\&                   unsigned char *sig, unsigned int *siglen,
\&                   const BIGNUM *kinv, const BIGNUM *rp, EC_KEY *eckey);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
See \fBECDSA_SIG_new\fR\|(3) for a description of the \fBECDSA_SIG\fR object.
.PP
See \fBi2d_ECDSA_SIG\fR\|(3) and \fBd2i_ECDSA_SIG\fR\|(3) for information about encoding
and decoding ECDSA signatures to/from DER.
.PP
All of the functions described below are deprecated. Applications should
use the higher level \fBEVP\fR interface such as \fBEVP_DigestSignInit\fR\|(3)
or \fBEVP_DigestVerifyInit\fR\|(3) instead.
.PP
\&\fBECDSA_size()\fR returns the maximum length of a DER encoded ECDSA signature
created with the private EC key \fIeckey\fR. To obtain the actual signature
size use \fBEVP_PKEY_sign\fR\|(3) with a NULL \fIsig\fR parameter.
.PP
\&\fBECDSA_sign()\fR computes a digital signature of the \fIdgstlen\fR bytes hash value
\&\fIdgst\fR using the private EC key \fIeckey\fR. The DER encoded signatures is
stored in \fIsig\fR and its length is returned in \fIsig_len\fR. Note: \fIsig\fR must
point to ECDSA_size(eckey) bytes of memory. The parameter \fItype\fR is currently
ignored. \fBECDSA_sign()\fR is wrapper function for \fBECDSA_sign_ex()\fR with \fIkinv\fR
and \fIrp\fR set to NULL.
.PP
\&\fBECDSA_do_sign()\fR is similar to \fBECDSA_sign()\fR except the signature is returned
as a newly allocated \fBECDSA_SIG\fR structure (or NULL on error). \fBECDSA_do_sign()\fR
is a wrapper function for \fBECDSA_do_sign_ex()\fR with \fIkinv\fR and \fIrp\fR set to
NULL.
.PP
\&\fBECDSA_verify()\fR verifies that the signature in \fIsig\fR of size \fIsiglen\fR is a
valid ECDSA signature of the hash value \fIdgst\fR of size \fIdgstlen\fR using the
public key \fIeckey\fR.  The parameter \fItype\fR is ignored.
.PP
\&\fBECDSA_do_verify()\fR is similar to \fBECDSA_verify()\fR except the signature is
presented in the form of a pointer to an \fBECDSA_SIG\fR structure.
.PP
The remaining functions utilise the internal \fIkinv\fR and \fIr\fR values used
during signature computation. Most applications will never need to call these
and some external ECDSA ENGINE implementations may not support them at all if
either \fIkinv\fR or \fIr\fR is not NULL.
.PP
\&\fBECDSA_sign_setup()\fR may be used to precompute parts of the signing operation.
\&\fIeckey\fR is the private EC key and \fIctx\fR is a pointer to \fBBN_CTX\fR structure
(or NULL). The precomputed values or returned in \fIkinv\fR and \fIrp\fR and can be
used in a later call to \fBECDSA_sign_ex()\fR or \fBECDSA_do_sign_ex()\fR.
.PP
\&\fBECDSA_sign_ex()\fR computes a digital signature of the \fIdgstlen\fR bytes hash value
\&\fIdgst\fR using the private EC key \fIeckey\fR and the optional pre-computed values
\&\fIkinv\fR and \fIrp\fR. The DER encoded signature is stored in \fIsig\fR and its
length is returned in \fIsig_len\fR. Note: \fIsig\fR must point to ECDSA_size(eckey)
bytes of memory. The parameter \fItype\fR is ignored.
.PP
\&\fBECDSA_do_sign_ex()\fR is similar to \fBECDSA_sign_ex()\fR except the signature is
returned as a newly allocated \fBECDSA_SIG\fR structure (or NULL on error).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBECDSA_size()\fR returns the maximum length signature or 0 on error.
.PP
\&\fBECDSA_sign()\fR, \fBECDSA_sign_ex()\fR and \fBECDSA_sign_setup()\fR return 1 if successful
or 0 on error.
.PP
\&\fBECDSA_do_sign()\fR and \fBECDSA_do_sign_ex()\fR return a pointer to an allocated
\&\fBECDSA_SIG\fR structure or NULL on error.
.PP
\&\fBECDSA_verify()\fR and \fBECDSA_do_verify()\fR return 1 for a valid
signature, 0 for an invalid signature and \-1 on error.
The error codes can be obtained by \fBERR_get_error\fR\|(3).
.SH EXAMPLES
.IX Header "EXAMPLES"
Creating an ECDSA signature of a given SHA\-256 hash value using the
named curve prime256v1 (aka P\-256).
This example uses deprecated functionality. See "DESCRIPTION".
.PP
First step: create an EC_KEY object (note: this part is \fBnot\fR ECDSA
specific)
.PP
.Vb 3
\& int ret;
\& ECDSA_SIG *sig;
\& EC_KEY *eckey;
\&
\& eckey = EC_KEY_new_by_curve_name(NID_X9_62_prime256v1);
\& if (eckey == NULL)
\&     /* error */
\& if (EC_KEY_generate_key(eckey) == 0)
\&     /* error */
.Ve
.PP
Second step: compute the ECDSA signature of a SHA\-256 hash value
using \fBECDSA_do_sign()\fR:
.PP
.Vb 3
\& sig = ECDSA_do_sign(digest, 32, eckey);
\& if (sig == NULL)
\&     /* error */
.Ve
.PP
or using \fBECDSA_sign()\fR:
.PP
.Vb 2
\& unsigned char *buffer, *pp;
\& int buf_len;
\&
\& buf_len = ECDSA_size(eckey);
\& buffer = OPENSSL_malloc(buf_len);
\& pp = buffer;
\& if (ECDSA_sign(0, dgst, dgstlen, pp, &buf_len, eckey) == 0)
\&     /* error */
.Ve
.PP
Third step: verify the created ECDSA signature using \fBECDSA_do_verify()\fR:
.PP
.Vb 1
\& ret = ECDSA_do_verify(digest, 32, sig, eckey);
.Ve
.PP
or using \fBECDSA_verify()\fR:
.PP
.Vb 1
\& ret = ECDSA_verify(0, digest, 32, buffer, buf_len, eckey);
.Ve
.PP
and finally evaluate the return value:
.PP
.Vb 6
\& if (ret == 1)
\&     /* signature ok */
\& else if (ret == 0)
\&     /* incorrect signature */
\& else
\&     /* error */
.Ve
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
ANSI X9.62, US Federal Information Processing Standard FIPS186\-2
(Digital Signature Standard, DSS)
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEC_KEY_new\fR\|(3),
\&\fBEVP_DigestSignInit\fR\|(3),
\&\fBEVP_DigestVerifyInit\fR\|(3),
\&\fBEVP_PKEY_sign\fR\|(3)
\&\fBi2d_ECDSA_SIG\fR\|(3),
\&\fBd2i_ECDSA_SIG\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
All functionality described here was deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2004\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
