.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BF_ENCRYPT 3ossl"
.TH BF_ENCRYPT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BF_set_key, BF_encrypt, BF_decrypt, BF_ecb_encrypt, BF_cbc_encrypt,
BF_cfb64_encrypt, BF_ofb64_encrypt, BF_options \- Blowfish encryption
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/blowfish.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& void BF_set_key(BF_KEY *key, int len, const unsigned char *data);
\&
\& void BF_ecb_encrypt(const unsigned char *in, unsigned char *out,
\&                     BF_KEY *key, int enc);
\& void BF_cbc_encrypt(const unsigned char *in, unsigned char *out,
\&                     long length, BF_KEY *schedule,
\&                     unsigned char *ivec, int enc);
\& void BF_cfb64_encrypt(const unsigned char *in, unsigned char *out,
\&                       long length, BF_KEY *schedule,
\&                       unsigned char *ivec, int *num, int enc);
\& void BF_ofb64_encrypt(const unsigned char *in, unsigned char *out,
\&                       long length, BF_KEY *schedule,
\&                       unsigned char *ivec, int *num);
\& const char *BF_options(void);
\&
\& void BF_encrypt(BF_LONG *data, const BF_KEY *key);
\& void BF_decrypt(BF_LONG *data, const BF_KEY *key);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated. Applications should
instead use \fBEVP_EncryptInit_ex\fR\|(3), \fBEVP_EncryptUpdate\fR\|(3) and
\&\fBEVP_EncryptFinal_ex\fR\|(3) or the equivalently named decrypt functions.
.PP
This library implements the Blowfish cipher, which was invented and described
by Counterpane (see http://www.counterpane.com/blowfish.html ).
.PP
Blowfish is a block cipher that operates on 64 bit (8 byte) blocks of data.
It uses a variable size key, but typically, 128 bit (16 byte) keys are
considered good for strong encryption.  Blowfish can be used in the same
modes as DES (see \fBdes_modes\fR\|(7)).  Blowfish is currently one
of the faster block ciphers.  It is quite a bit faster than DES, and much
faster than IDEA or RC2.
.PP
Blowfish consists of a key setup phase and the actual encryption or decryption
phase.
.PP
\&\fBBF_set_key()\fR sets up the \fBBF_KEY\fR \fBkey\fR using the \fBlen\fR bytes long key
at \fBdata\fR.
.PP
\&\fBBF_ecb_encrypt()\fR is the basic Blowfish encryption and decryption function.
It encrypts or decrypts the first 64 bits of \fBin\fR using the key \fBkey\fR,
putting the result in \fBout\fR.  \fBenc\fR decides if encryption (\fBBF_ENCRYPT\fR)
or decryption (\fBBF_DECRYPT\fR) shall be performed.  The vector pointed at by
\&\fBin\fR and \fBout\fR must be 64 bits in length, no less.  If they are larger,
everything after the first 64 bits is ignored.
.PP
The mode functions \fBBF_cbc_encrypt()\fR, \fBBF_cfb64_encrypt()\fR and \fBBF_ofb64_encrypt()\fR
all operate on variable length data.  They all take an initialization vector
\&\fBivec\fR which needs to be passed along into the next call of the same function
for the same message.  \fBivec\fR may be initialized with anything, but the
recipient needs to know what it was initialized with, or it won't be able
to decrypt.  Some programs and protocols simplify this, like SSH, where
\&\fBivec\fR is simply initialized to zero.
\&\fBBF_cbc_encrypt()\fR operates on data that is a multiple of 8 bytes long, while
\&\fBBF_cfb64_encrypt()\fR and \fBBF_ofb64_encrypt()\fR are used to encrypt a variable
number of bytes (the amount does not have to be an exact multiple of 8).  The
purpose of the latter two is to simulate stream ciphers, and therefore, they
need the parameter \fBnum\fR, which is a pointer to an integer where the current
offset in \fBivec\fR is stored between calls.  This integer must be initialized
to zero when \fBivec\fR is initialized.
.PP
\&\fBBF_cbc_encrypt()\fR is the Cipher Block Chaining function for Blowfish.  It
encrypts or decrypts the 64 bits chunks of \fBin\fR using the key \fBschedule\fR,
putting the result in \fBout\fR.  \fBenc\fR decides if encryption (BF_ENCRYPT) or
decryption (BF_DECRYPT) shall be performed.  \fBivec\fR must point at an 8 byte
long initialization vector.
.PP
\&\fBBF_cfb64_encrypt()\fR is the CFB mode for Blowfish with 64 bit feedback.
It encrypts or decrypts the bytes in \fBin\fR using the key \fBschedule\fR,
putting the result in \fBout\fR.  \fBenc\fR decides if encryption (\fBBF_ENCRYPT\fR)
or decryption (\fBBF_DECRYPT\fR) shall be performed.  \fBivec\fR must point at an
8 byte long initialization vector. \fBnum\fR must point at an integer which must
be initially zero.
.PP
\&\fBBF_ofb64_encrypt()\fR is the OFB mode for Blowfish with 64 bit feedback.
It uses the same parameters as \fBBF_cfb64_encrypt()\fR, which must be initialized
the same way.
.PP
\&\fBBF_encrypt()\fR and \fBBF_decrypt()\fR are the lowest level functions for Blowfish
encryption.  They encrypt/decrypt the first 64 bits of the vector pointed by
\&\fBdata\fR, using the key \fBkey\fR.  These functions should not be used unless you
implement 'modes' of Blowfish.  The alternative is to use \fBBF_ecb_encrypt()\fR.
If you still want to use these functions, you should be aware that they take
each 32\-bit chunk in host-byte order, which is little-endian on little-endian
platforms and big-endian on big-endian ones.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
None of the functions presented here return any value.
.SH NOTE
.IX Header "NOTE"
Applications should use the higher level functions
\&\fBEVP_EncryptInit\fR\|(3) etc. instead of calling these
functions directly.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_EncryptInit\fR\|(3),
\&\fBdes_modes\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
All of these functions were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
