.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_CMP_EXEC_CERTREQ 3ossl"
.TH OSSL_CMP_EXEC_CERTREQ 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_CMP_exec_certreq,
OSSL_CMP_exec_IR_ses,
OSSL_CMP_exec_CR_ses,
OSSL_CMP_exec_P10CR_ses,
OSSL_CMP_exec_KUR_ses,
OSSL_CMP_IR,
OSSL_CMP_CR,
OSSL_CMP_P10CR,
OSSL_CMP_KUR,
OSSL_CMP_try_certreq,
OSSL_CMP_exec_RR_ses,
OSSL_CMP_exec_GENM_ses
\&\- functions implementing CMP client transactions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cmp.h>
\&
\& X509 *OSSL_CMP_exec_certreq(OSSL_CMP_CTX *ctx, int req_type,
\&                             const OSSL_CRMF_MSG *crm);
\& X509 *OSSL_CMP_exec_IR_ses(OSSL_CMP_CTX *ctx);
\& X509 *OSSL_CMP_exec_CR_ses(OSSL_CMP_CTX *ctx);
\& X509 *OSSL_CMP_exec_P10CR_ses(OSSL_CMP_CTX *ctx);
\& X509 *OSSL_CMP_exec_KUR_ses(OSSL_CMP_CTX *ctx);
\& #define OSSL_CMP_IR
\& #define OSSL_CMP_CR
\& #define OSSL_CMP_P10CR
\& #define OSSL_CMP_KUR
\& int OSSL_CMP_try_certreq(OSSL_CMP_CTX *ctx, int req_type,
\&                          const OSSL_CRMF_MSG *crm, int *checkAfter);
\& int OSSL_CMP_exec_RR_ses(OSSL_CMP_CTX *ctx);
\& STACK_OF(OSSL_CMP_ITAV) *OSSL_CMP_exec_GENM_ses(OSSL_CMP_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This is the OpenSSL API for doing CMP (Certificate Management Protocol)
client-server transactions, i.e., sequences of CMP requests and responses.
.PP
All functions take a populated OSSL_CMP_CTX structure as their first argument.
Usually the server name, port, and path ("CMP alias") need to be set, as well as
credentials the client can use for authenticating itself to the server.
In order to authenticate the server the client typically needs a trust store.
The functions return their respective main results directly, while there are
also accessor functions for retrieving various results and status information
from the \fIctx\fR. See \fBOSSL_CMP_CTX_new\fR\|(3) etc. for details.
.PP
The default conveying protocol is HTTP.
Timeout values may be given per request-response pair and per transaction.
See \fBOSSL_CMP_MSG_http_perform\fR\|(3) for details.
.PP
\&\fBOSSL_CMP_exec_IR_ses()\fR requests an initial certificate from the given PKI.
.PP
\&\fBOSSL_CMP_exec_CR_ses()\fR requests an additional certificate.
.PP
\&\fBOSSL_CMP_exec_P10CR_ses()\fR conveys a legacy PKCS#10 CSR requesting a certificate.
.PP
\&\fBOSSL_CMP_exec_KUR_ses()\fR obtains an updated certificate.
.PP
These four types of certificate enrollment are implemented as macros
calling \fBOSSL_CMP_exec_certreq()\fR.
.PP
\&\fBOSSL_CMP_exec_certreq()\fR performs a certificate request of the type specified
by the \fIreq_type\fR parameter, which may be IR, CR, P10CR, or KUR.
For IR, CR, and KUR, the certificate template to be used in the request
may be supplied via the \fIcrm\fR parameter pointing to a CRMF structure.
Typically \fIcrm\fR is NULL, then the template ingredients are taken from \fIctx\fR
and need to be filled in using \fBOSSL_CMP_CTX_set1_subjectName\fR\|(3),
\&\fBOSSL_CMP_CTX_set0_newPkey\fR\|(3), \fBOSSL_CMP_CTX_set1_oldCert\fR\|(3), etc.
For P10CR, \fBOSSL_CMP_CTX_set1_p10CSR\fR\|(3) needs to be used instead.
The enrollment session may be blocked by sleeping until the addressed
CA (or an intermediate PKI component) can fully process and answer the request.
.PP
\&\fBOSSL_CMP_try_certreq()\fR is an alternative to the above functions that is
more flexible regarding what to do after receiving a checkAfter value.
When called for the first time (with no certificate request in progress for
the given \fIctx\fR) it starts a new transaction by sending a certificate request
constructed as stated above using the \fIreq_type\fR and optional \fIcrm\fR parameter.
Otherwise (when according to \fIctx\fR a 'waiting' status has been received before)
it continues polling for the pending request
unless the \fIreq_type\fR argument is < 0, which aborts the request.
If the requested certificate is available the function returns 1 and the
caller can use \fBOSSL_CMP_CTX_get0_newCert\fR\|(3) to retrieve the new certificate.
If no error occurred but no certificate is available yet then
\&\fBOSSL_CMP_try_certreq()\fR remembers in the CMP context that it should be retried
and returns \-1 after assigning the received checkAfter value
via the output pointer argument (unless it is NULL).
The checkAfter value indicates the number of seconds the caller should let pass
before trying again. The caller is free to sleep for the given number of seconds
or for some other time and/or to do anything else before retrying by calling
\&\fBOSSL_CMP_try_certreq()\fR again with the same parameter values as before.
\&\fBOSSL_CMP_try_certreq()\fR then polls
to see whether meanwhile the requested certificate is available.
If the caller decides to abort the pending certificate request and provides
a negative value as the \fIreq_type\fR argument then \fBOSSL_CMP_try_certreq()\fR
aborts the CMP transaction by sending an error message to the server.
.PP
\&\fBOSSL_CMP_exec_RR_ses()\fR requests the revocation of the certificate
specified in the \fIctx\fR using \fBOSSL_CMP_CTX_set1_oldCert\fR\|(3).
RFC 4210 is vague in which PKIStatus should be returned by the server.
We take "accepted" and "grantedWithMods" as clear success and handle
"revocationWarning" and "revocationNotification" just as warnings because CAs
typically return them as an indication that the certificate was already revoked.
"rejection" is a clear error. The values "waiting" and "keyUpdateWarning"
make no sense for revocation and thus are treated as an error as well.
.PP
\&\fBOSSL_CMP_exec_GENM_ses()\fR sends a general message containing the sequence of
infoType and infoValue pairs (InfoTypeAndValue; short: \fBITAV\fR)
optionally provided in the \fIctx\fR using \fBOSSL_CMP_CTX_push0_genm_ITAV\fR\|(3).
On success it records in \fIctx\fR the status \fBOSSL_CMP_PKISTATUS_accepted\fR
and returns the list of \fBITAV\fRs received in the GENP message.
This can be used, for instance, to poll for CRLs or CA Key Updates.
See RFC 4210 section 5.3.19 and appendix E.5 for details.
.SH NOTES
.IX Header "NOTES"
CMP is defined in RFC 4210 (and CRMF in RFC 4211).
.PP
The CMP client implementation is limited to one request per CMP message
(and consequently to at most one response component per CMP message).
.PP
When a client obtains from a CMP server CA certificates that it is going to
trust, for instance via the caPubs field of a certificate response,
authentication of the CMP server is particularly critical.
So special care must be taken setting up server authentication in \fIctx\fR
using functions such as
\&\fBOSSL_CMP_CTX_set0_trustedStore\fR\|(3) (for certificate-based authentication) or
\&\fBOSSL_CMP_CTX_set1_secretValue\fR\|(3) (for MAC-based protection).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_CMP_exec_certreq()\fR, \fBOSSL_CMP_exec_IR_ses()\fR, \fBOSSL_CMP_exec_CR_ses()\fR,
\&\fBOSSL_CMP_exec_P10CR_ses()\fR, and \fBOSSL_CMP_exec_KUR_ses()\fR return a
pointer to the newly obtained X509 certificate on success, NULL on error.
This pointer will be freed implicitly by \fBOSSL_CMP_CTX_free()\fR or
\&\fBCSSL_CMP_CTX_reinit()\fR.
.PP
\&\fBOSSL_CMP_try_certreq()\fR returns 1 if the requested certificate is available
via \fBOSSL_CMP_CTX_get0_newCert\fR\|(3)
or on successfully aborting a pending certificate request, 0 on error, and \-1
in case a 'waiting' status has been received and checkAfter value is available.
In the latter case \fBOSSL_CMP_CTX_get0_newCert\fR\|(3) yields NULL
and the output parameter \fIcheckAfter\fR has been used to
assign the received value unless \fIcheckAfter\fR is NULL.
.PP
\&\fBOSSL_CMP_exec_RR_ses()\fR returns 1 on success, 0 on error.
.PP
\&\fBOSSL_CMP_exec_GENM_ses()\fR returns NULL on error,
otherwise a pointer to the sequence of \fBITAV\fR received, which may be empty.
This pointer must be freed by the caller.
.SH EXAMPLES
.IX Header "EXAMPLES"
See OSSL_CMP_CTX for examples on how to prepare the context for these
functions.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOSSL_CMP_CTX_new\fR\|(3), \fBOSSL_CMP_CTX_free\fR\|(3),
\&\fBOSSL_CMP_CTX_set1_subjectName\fR\|(3), \fBOSSL_CMP_CTX_set0_newPkey\fR\|(3),
\&\fBOSSL_CMP_CTX_set1_p10CSR\fR\|(3), \fBOSSL_CMP_CTX_set1_oldCert\fR\|(3),
\&\fBOSSL_CMP_CTX_get0_newCert\fR\|(3), \fBOSSL_CMP_CTX_push0_genm_ITAV\fR\|(3),
\&\fBOSSL_CMP_MSG_http_perform\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The OpenSSL CMP support was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2007\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
