.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL_LH_STATS 3ossl"
.TH OPENSSL_LH_STATS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OPENSSL_LH_stats, OPENSSL_LH_node_stats, OPENSSL_LH_node_usage_stats,
OPENSSL_LH_stats_bio,
OPENSSL_LH_node_stats_bio, OPENSSL_LH_node_usage_stats_bio \- LHASH statistics
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/lhash.h>
\&
\& void OPENSSL_LH_stats(LHASH *table, FILE *out);
\& void OPENSSL_LH_node_stats(LHASH *table, FILE *out);
\& void OPENSSL_LH_node_usage_stats(LHASH *table, FILE *out);
\&
\& void OPENSSL_LH_stats_bio(LHASH *table, BIO *out);
\& void OPENSSL_LH_node_stats_bio(LHASH *table, BIO *out);
\& void OPENSSL_LH_node_usage_stats_bio(LHASH *table, BIO *out);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBLHASH\fR structure records statistics about most aspects of
accessing the hash table.
.PP
\&\fBOPENSSL_LH_stats()\fR prints out statistics on the size of the hash table and how
many entries are in it. For historical reasons, this function also outputs a
number of additional statistics, but the tracking of these statistics is no
longer supported and these statistics are always reported as zero.
.PP
\&\fBOPENSSL_LH_node_stats()\fR prints the number of entries for each 'bucket' in the
hash table.
.PP
\&\fBOPENSSL_LH_node_usage_stats()\fR prints out a short summary of the state of the
hash table.  It prints the 'load' and the 'actual load'.  The load is
the average number of data items per 'bucket' in the hash table.  The
\&'actual load' is the average number of items per 'bucket', but only
for buckets which contain entries.  So the 'actual load' is the
average number of searches that will need to find an item in the hash
table, while the 'load' is the average number that will be done to
record a miss.
.PP
\&\fBOPENSSL_LH_stats_bio()\fR, \fBOPENSSL_LH_node_stats_bio()\fR and \fBOPENSSL_LH_node_usage_stats_bio()\fR
are the same as the above, except that the output goes to a \fBBIO\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
These functions do not return values.
.SH NOTE
.IX Header "NOTE"
These calls should be made under a read lock. Refer to
"NOTE" in \fBOPENSSL_LH_COMPFUNC\fR\|(3) for more details about the locks required
when using the LHASH data structure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBbio\fR\|(7), \fBOPENSSL_LH_COMPFUNC\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
