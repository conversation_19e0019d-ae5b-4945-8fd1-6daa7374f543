.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_IS_A 3ossl"
.TH EVP_PKEY_IS_A 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_is_a, EVP_PKEY_can_sign, EVP_PKEY_type_names_do_all,
EVP_PKEY_get0_type_name, EVP_PKEY_get0_description, EVP_PKEY_get0_provider
\&\- key type and capabilities functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_is_a(const EVP_PKEY *pkey, const char *name);
\& int EVP_PKEY_can_sign(const EVP_PKEY *pkey);
\& int EVP_PKEY_type_names_do_all(const EVP_PKEY *pkey,
\&                                void (*fn)(const char *name, void *data),
\&                                void *data);
\& const char *EVP_PKEY_get0_type_name(const EVP_PKEY *key);
\& const char *EVP_PKEY_get0_description(const EVP_PKEY *key);
\& const OSSL_PROVIDER *EVP_PKEY_get0_provider(const EVP_PKEY *key);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_PKEY_is_a()\fR checks if the key type of \fIpkey\fR is \fIname\fR.
.PP
\&\fBEVP_PKEY_can_sign()\fR checks if the functionality for the key type of
\&\fIpkey\fR supports signing.  No other check is done, such as whether
\&\fIpkey\fR contains a private key.
.PP
\&\fBEVP_PKEY_type_names_do_all()\fR traverses all names for \fIpkey\fR's key type, and
calls \fIfn\fR with each name and \fIdata\fR.  For example, an RSA \fBEVP_PKEY\fR may
be named both \f(CW\*(C`RSA\*(C'\fR and \f(CW\*(C`rsaEncryption\*(C'\fR.
The order of the names depends on the provider implementation that holds
the key.
.PP
\&\fBEVP_PKEY_get0_type_name()\fR returns the first key type name that is found
for the given \fIpkey\fR. Note that the \fIpkey\fR may have multiple synonyms
associated with it. In this case it depends on the provider implementation
that holds the key which one will be returned.
Ownership of the returned string is retained by the \fIpkey\fR object and should
not be freed by the caller.
.PP
\&\fBEVP_PKEY_get0_description()\fR returns a description of the type of \fBEVP_PKEY\fR,
meant for display and human consumption.  The description is at the
discretion of the key type implementation.
.PP
\&\fBEVP_PKEY_get0_provider()\fR returns the provider of the \fBEVP_PKEY\fR's
\&\fBEVP_KEYMGMT\fR\|(3).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_is_a()\fR returns 1 if \fIpkey\fR has the key type \fIname\fR,
otherwise 0.
.PP
\&\fBEVP_PKEY_can_sign()\fR returns 1 if the \fIpkey\fR key type functionality
supports signing, otherwise 0.
.PP
\&\fBEVP_PKEY_get0_type_name()\fR returns the name that is found or NULL on error.
.PP
\&\fBEVP_PKEY_get0_description()\fR returns the description if found or NULL if not.
.PP
\&\fBEVP_PKEY_get0_provider()\fR returns the provider if found or NULL if not.
.PP
\&\fBEVP_PKEY_type_names_do_all()\fR returns 1 if the callback was called for all
names. A return value of 0 means that the callback was not called for any
names.
.SH EXAMPLES
.IX Header "EXAMPLES"
.SS \fBEVP_PKEY_is_a()\fP
.IX Subsection "EVP_PKEY_is_a()"
The loaded providers and what key types they support will ultimately
determine what \fIname\fR is possible to use with \fBEVP_PKEY_is_a()\fR.  We do know
that the default provider supports RSA, DH, DSA and EC keys, so we can use
this as an crude example:
.PP
.Vb 1
\& #include <openssl/evp.h>
\&
\& ...
\&     /* |pkey| is an EVP_PKEY* */
\&     if (EVP_PKEY_is_a(pkey, "RSA")) {
\&         BIGNUM *modulus = NULL;
\&         if (EVP_PKEY_get_bn_param(pkey, "n", &modulus))
\&             /* do whatever with the modulus */
\&         BN_free(modulus);
\&     }
.Ve
.SS \fBEVP_PKEY_can_sign()\fP
.IX Subsection "EVP_PKEY_can_sign()"
.Vb 1
\& #include <openssl/evp.h>
\&
\& ...
\&     /* |pkey| is an EVP_PKEY* */
\&     if (!EVP_PKEY_can_sign(pkey)) {
\&         fprintf(stderr, "Not a signing key!");
\&         exit(1);
\&     }
\&     /* Sign something... */
.Ve
.SH HISTORY
.IX Header "HISTORY"
The functions described here were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
