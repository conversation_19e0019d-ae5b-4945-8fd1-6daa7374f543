.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_SHOULD_RETRY 3ossl"
.TH BIO_SHOULD_RETRY 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_should_read, BIO_should_write,
BIO_should_io_special, BIO_retry_type, BIO_should_retry,
BIO_get_retry_BIO, BIO_get_retry_reason, BIO_set_retry_reason \- BIO retry
functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& int BIO_should_read(BIO *b);
\& int BIO_should_write(BIO *b);
\& int BIO_should_io_special(iBIO *b);
\& int BIO_retry_type(BIO *b);
\& int BIO_should_retry(BIO *b);
\&
\& BIO *BIO_get_retry_BIO(BIO *bio, int *reason);
\& int BIO_get_retry_reason(BIO *bio);
\& void BIO_set_retry_reason(BIO *bio, int reason);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions determine why a BIO is not able to read or write data.
They will typically be called after a failed \fBBIO_read_ex()\fR or \fBBIO_write_ex()\fR
call.
.PP
\&\fBBIO_should_retry()\fR is true if the call that produced this condition
should then be retried at a later time.
.PP
If \fBBIO_should_retry()\fR is false then the cause is an error condition.
.PP
\&\fBBIO_should_read()\fR is true if the cause of the condition is that the BIO
has insufficient data to return. Check for readability and/or retry the
last operation.
.PP
\&\fBBIO_should_write()\fR is true if the cause of the condition is that the BIO
has pending data to write. Check for writability and/or retry the
last operation.
.PP
\&\fBBIO_should_io_special()\fR is true if some "special" condition, that is a
reason other than reading or writing is the cause of the condition.
.PP
\&\fBBIO_retry_type()\fR returns a mask of the cause of a retry condition
consisting of the values \fBBIO_FLAGS_READ\fR, \fBBIO_FLAGS_WRITE\fR,
\&\fBBIO_FLAGS_IO_SPECIAL\fR though current BIO types will only set one of
these.
.PP
\&\fBBIO_get_retry_BIO()\fR determines the precise reason for the special
condition, it returns the BIO that caused this condition and if
\&\fBreason\fR is not NULL it contains the reason code. The meaning of
the reason code and the action that should be taken depends on
the type of BIO that resulted in this condition.
.PP
\&\fBBIO_get_retry_reason()\fR returns the reason for a special condition if
passed the relevant BIO, for example as returned by \fBBIO_get_retry_BIO()\fR.
.PP
\&\fBBIO_set_retry_reason()\fR sets the retry reason for a special condition for a given
BIO. This would usually only be called by BIO implementations.
.SH NOTES
.IX Header "NOTES"
\&\fBBIO_should_read()\fR, \fBBIO_should_write()\fR, \fBBIO_should_io_special()\fR,
\&\fBBIO_retry_type()\fR, and \fBBIO_should_retry()\fR, are implemented as macros.
.PP
If \fBBIO_should_retry()\fR returns false then the precise "error condition"
depends on the BIO type that caused it and the return code of the BIO
operation. For example if a call to \fBBIO_read_ex()\fR on a socket BIO returns
0 and \fBBIO_should_retry()\fR is false then the cause will be that the
connection closed. A similar condition on a file BIO will mean that it
has reached EOF. Some BIO types may place additional information on
the error queue. For more details see the individual BIO type manual
pages.
.PP
If the underlying I/O structure is in a blocking mode almost all current
BIO types will not request a retry, because the underlying I/O
calls will not. If the application knows that the BIO type will never
signal a retry then it need not call \fBBIO_should_retry()\fR after a failed
BIO I/O call. This is typically done with file BIOs.
.PP
SSL BIOs are the only current exception to this rule: they can request a
retry even if the underlying I/O structure is blocking, if a handshake
occurs during a call to \fBBIO_read()\fR. An application can retry the failed
call immediately or avoid this situation by setting SSL_MODE_AUTO_RETRY
on the underlying SSL structure.
.PP
While an application may retry a failed non blocking call immediately
this is likely to be very inefficient because the call will fail
repeatedly until data can be processed or is available. An application
will normally wait until the necessary condition is satisfied. How
this is done depends on the underlying I/O structure.
.PP
For example if the cause is ultimately a socket and \fBBIO_should_read()\fR
is true then a call to \fBselect()\fR may be made to wait until data is
available and then retry the BIO operation. By combining the retry
conditions of several non blocking BIOs in a single \fBselect()\fR call
it is possible to service several BIOs in a single thread, though
the performance may be poor if SSL BIOs are present because long delays
can occur during the initial handshake process.
.PP
It is possible for a BIO to block indefinitely if the underlying I/O
structure cannot process or return any data. This depends on the behaviour of
the platforms I/O functions. This is often not desirable: one solution
is to use non blocking I/O and use a timeout on the \fBselect()\fR (or
equivalent) call.
.SH BUGS
.IX Header "BUGS"
The OpenSSL ASN1 functions cannot gracefully deal with non blocking I/O:
that is they cannot retry after a partial read or write. This is usually
worked around by only passing the relevant data to ASN1 functions when
the entire structure can be read or written.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_should_read()\fR, \fBBIO_should_write()\fR, \fBBIO_should_io_special()\fR, and
\&\fBBIO_should_retry()\fR return either 1 or 0 based on the actual conditions
of the \fBBIO\fR.
.PP
\&\fBBIO_retry_type()\fR returns a flag combination presenting the cause of a retry
condition or false if there is no retry condition.
.PP
\&\fBBIO_get_retry_BIO()\fR returns a valid \fBBIO\fR structure.
.PP
\&\fBBIO_get_retry_reason()\fR returns the reason for a special condition.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBbio\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The \fBBIO_get_retry_reason()\fR and \fBBIO_set_retry_reason()\fR functions were added in
OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
