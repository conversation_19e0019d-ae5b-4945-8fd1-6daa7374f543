.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_SOCKET_WAIT 3ossl"
.TH BIO_SOCKET_WAIT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_socket_wait,
BIO_wait,
BIO_do_connect_retry
\&\- BIO connection utility functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& #ifndef OPENSSL_NO_SOCK
\& int BIO_socket_wait(int fd, int for_read, time_t max_time);
\& #endif
\& int BIO_wait(BIO *bio, time_t max_time, unsigned int nap_milliseconds);
\& int BIO_do_connect_retry(BIO *bio, int timeout, int nap_milliseconds);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_socket_wait()\fR waits on the socket \fBfd\fR for reading if \fBfor_read\fR is not 0,
else for writing, at most until \fBmax_time\fR.
It succeeds immediately if \fBmax_time\fR == 0 (which means no timeout given).
.PP
\&\fBBIO_wait()\fR waits at most until \fBmax_time\fR on the given (typically socket-based)
\&\fBbio\fR, for reading if \fBbio\fR is supposed to read, else for writing.
It is used by \fBBIO_do_connect_retry()\fR and can be used together \fBBIO_read\fR\|(3).
It succeeds immediately if \fBmax_time\fR == 0 (which means no timeout given).
If sockets are not available it supports polling by succeeding after sleeping
at most the given \fBnap_milliseconds\fR in order to avoid a tight busy loop.
Via \fBnap_milliseconds\fR the caller determines the polling granularity.
.PP
\&\fBBIO_do_connect_retry()\fR connects via the given \fBbio\fR.
It retries \fBBIO_do_connect()\fR as far as needed to reach a definite outcome,
i.e., connection succeeded, timeout has been reached, or an error occurred.
For nonblocking and potentially even non-socket BIOs it polls
every \fBnap_milliseconds\fR and sleeps in between using \fBBIO_wait()\fR.
If \fBnap_milliseconds\fR is < 0 then a default value of 100 ms is used.
If the \fBtimeout\fR parameter is > 0 this indicates the maximum number of seconds
to wait until the connection is established or a definite error occurred.
A value of 0 enables waiting indefinitely (i.e, no timeout),
while a value < 0 means that \fBBIO_do_connect()\fR is tried only once.
The function may, directly or indirectly, invoke \fBERR_clear_error()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_socket_wait()\fR, \fBBIO_wait()\fR, and \fBBIO_do_connect_retry()\fR
return \-1 on error, 0 on timeout, and 1 on success.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBBIO_do_connect\fR\|(3), \fBBIO_read\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBBIO_socket_wait()\fR, \fBBIO_wait()\fR, and \fBBIO_do_connect_retry()\fR
were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
