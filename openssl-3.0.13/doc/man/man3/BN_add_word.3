.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BN_ADD_WORD 3ossl"
.TH BN_ADD_WORD 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BN_add_word, BN_sub_word, BN_mul_word, BN_div_word, BN_mod_word \- arithmetic
functions on BIGNUMs with integers
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bn.h>
\&
\& int BN_add_word(BIGNUM *a, BN_ULONG w);
\&
\& int BN_sub_word(BIGNUM *a, BN_ULONG w);
\&
\& int BN_mul_word(BIGNUM *a, BN_ULONG w);
\&
\& BN_ULONG BN_div_word(BIGNUM *a, BN_ULONG w);
\&
\& BN_ULONG BN_mod_word(const BIGNUM *a, BN_ULONG w);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions perform arithmetic operations on BIGNUMs with unsigned
integers. They are much more efficient than the normal BIGNUM
arithmetic operations.
.PP
\&\fBBN_add_word()\fR adds \fBw\fR to \fBa\fR (\f(CW\*(C`a+=w\*(C'\fR).
.PP
\&\fBBN_sub_word()\fR subtracts \fBw\fR from \fBa\fR (\f(CW\*(C`a\-=w\*(C'\fR).
.PP
\&\fBBN_mul_word()\fR multiplies \fBa\fR and \fBw\fR (\f(CW\*(C`a*=w\*(C'\fR).
.PP
\&\fBBN_div_word()\fR divides \fBa\fR by \fBw\fR (\f(CW\*(C`a/=w\*(C'\fR) and returns the remainder.
.PP
\&\fBBN_mod_word()\fR returns the remainder of \fBa\fR divided by \fBw\fR (\f(CW\*(C`a%w\*(C'\fR).
.PP
For \fBBN_div_word()\fR and \fBBN_mod_word()\fR, \fBw\fR must not be 0.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBN_add_word()\fR, \fBBN_sub_word()\fR and \fBBN_mul_word()\fR return 1 for success, 0
on error. The error codes can be obtained by \fBERR_get_error\fR\|(3).
.PP
\&\fBBN_mod_word()\fR and \fBBN_div_word()\fR return \fBa\fR%\fBw\fR on success and
\&\fB(BN_ULONG)\-1\fR if an error occurred.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBBN_add\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
