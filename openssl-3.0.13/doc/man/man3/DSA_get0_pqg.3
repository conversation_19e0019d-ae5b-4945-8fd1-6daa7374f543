.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DSA_GET0_PQG 3ossl"
.TH DSA_GET0_PQG 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
DSA_get0_pqg, DSA_set0_pqg, DSA_get0_key, DSA_set0_key,
DSA_get0_p, DSA_get0_q, DSA_get0_g,
DSA_get0_pub_key, DSA_get0_priv_key,
DSA_clear_flags, DSA_test_flags, DSA_set_flags,
DSA_get0_engine \- Routines for getting and
setting data in a DSA object
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/dsa.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 10
\& void DSA_get0_pqg(const DSA *d,
\&                   const BIGNUM **p, const BIGNUM **q, const BIGNUM **g);
\& int DSA_set0_pqg(DSA *d, BIGNUM *p, BIGNUM *q, BIGNUM *g);
\& void DSA_get0_key(const DSA *d,
\&                   const BIGNUM **pub_key, const BIGNUM **priv_key);
\& int DSA_set0_key(DSA *d, BIGNUM *pub_key, BIGNUM *priv_key);
\& const BIGNUM *DSA_get0_p(const DSA *d);
\& const BIGNUM *DSA_get0_q(const DSA *d);
\& const BIGNUM *DSA_get0_g(const DSA *d);
\& const BIGNUM *DSA_get0_pub_key(const DSA *d);
\& const BIGNUM *DSA_get0_priv_key(const DSA *d);
\& void DSA_clear_flags(DSA *d, int flags);
\& int DSA_test_flags(const DSA *d, int flags);
\& void DSA_set_flags(DSA *d, int flags);
\& ENGINE *DSA_get0_engine(DSA *d);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated.
Applications should instead use \fBEVP_PKEY_get_bn_param\fR\|(3).
.PP
A DSA object contains the parameters \fBp\fR, \fBq\fR and \fBg\fR. It also contains a
public key (\fBpub_key\fR) and (optionally) a private key (\fBpriv_key\fR).
.PP
The \fBp\fR, \fBq\fR and \fBg\fR parameters can be obtained by calling \fBDSA_get0_pqg()\fR.
If the parameters have not yet been set then \fB*p\fR, \fB*q\fR and \fB*g\fR will be set
to NULL. Otherwise they are set to pointers to their respective values. These
point directly to the internal representations of the values and therefore
should not be freed directly.
.PP
The \fBp\fR, \fBq\fR and \fBg\fR values can be set by calling \fBDSA_set0_pqg()\fR and passing
the new values for \fBp\fR, \fBq\fR and \fBg\fR as parameters to the function. Calling
this function transfers the memory management of the values to the DSA object,
and therefore the values that have been passed in should not be freed directly
after this function has been called.
.PP
To get the public and private key values use the \fBDSA_get0_key()\fR function. A
pointer to the public key will be stored in \fB*pub_key\fR, and a pointer to the
private key will be stored in \fB*priv_key\fR. Either may be NULL if they have not
been set yet, although if the private key has been set then the public key must
be. The values point to the internal representation of the public key and
private key values. This memory should not be freed directly.
.PP
The public and private key values can be set using \fBDSA_set0_key()\fR. The public
key must be non-NULL the first time this function is called on a given DSA
object. The private key may be NULL.  On subsequent calls, either may be NULL,
which means the corresponding DSA field is left untouched. As for \fBDSA_set0_pqg()\fR
this function transfers the memory management of the key values to the DSA
object, and therefore they should not be freed directly after this function has
been called.
.PP
Any of the values \fBp\fR, \fBq\fR, \fBg\fR, \fBpriv_key\fR, and \fBpub_key\fR can also be
retrieved separately by the corresponding function \fBDSA_get0_p()\fR, \fBDSA_get0_q()\fR,
\&\fBDSA_get0_g()\fR, \fBDSA_get0_priv_key()\fR, and \fBDSA_get0_pub_key()\fR, respectively.
.PP
\&\fBDSA_set_flags()\fR sets the flags in the \fBflags\fR parameter on the DSA object.
Multiple flags can be passed in one go (bitwise ORed together). Any flags that
are already set are left set. \fBDSA_test_flags()\fR tests to see whether the flags
passed in the \fBflags\fR parameter are currently set in the DSA object. Multiple
flags can be tested in one go. All flags that are currently set are returned, or
zero if none of the flags are set. \fBDSA_clear_flags()\fR clears the specified flags
within the DSA object.
.PP
\&\fBDSA_get0_engine()\fR returns a handle to the ENGINE that has been set for this DSA
object, or NULL if no such ENGINE has been set.
.SH NOTES
.IX Header "NOTES"
Values retrieved with \fBDSA_get0_key()\fR are owned by the DSA object used
in the call and may therefore \fInot\fR be passed to \fBDSA_set0_key()\fR.  If
needed, duplicate the received value using \fBBN_dup()\fR and pass the
duplicate.  The same applies to \fBDSA_get0_pqg()\fR and \fBDSA_set0_pqg()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBDSA_set0_pqg()\fR and \fBDSA_set0_key()\fR return 1 on success or 0 on failure.
.PP
\&\fBDSA_test_flags()\fR returns the current state of the flags in the DSA object.
.PP
\&\fBDSA_get0_engine()\fR returns the ENGINE set for the DSA object or NULL if no ENGINE
has been set.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_get_bn_param\fR\|(3),
\&\fBDSA_new\fR\|(3), \fBDSA_new\fR\|(3), \fBDSA_generate_parameters\fR\|(3), \fBDSA_generate_key\fR\|(3),
\&\fBDSA_dup_DH\fR\|(3), \fBDSA_do_sign\fR\|(3), \fBDSA_set_method\fR\|(3), \fBDSA_SIG_new\fR\|(3),
\&\fBDSA_sign\fR\|(3), \fBDSA_size\fR\|(3), \fBDSA_meth_new\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The functions described here were added in OpenSSL 1.1.0 and deprecated in
OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
