.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DEFINE_STACK_OF 3ossl"
.TH DEFINE_STACK_OF 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
DEFINE_STACK_OF, DEFINE_STACK_OF_CONST, DEFINE_SPECIAL_STACK_OF,
DEFINE_SPECIAL_STACK_OF_CONST,
sk_TYPE_num, sk_TYPE_value, sk_TYPE_new, sk_TYPE_new_null,
sk_TYPE_reserve, sk_TYPE_free, sk_TYPE_zero, sk_TYPE_delete,
sk_TYPE_delete_ptr, sk_TYPE_push, sk_TYPE_unshift, sk_TYPE_pop,
sk_TYPE_shift, sk_TYPE_pop_free, sk_TYPE_insert, sk_TYPE_set,
sk_TYPE_find, sk_TYPE_find_ex, sk_TYPE_find_all, sk_TYPE_sort,
sk_TYPE_is_sorted, sk_TYPE_dup, sk_TYPE_deep_copy, sk_TYPE_set_cmp_func,
sk_TYPE_new_reserve,
OPENSSL_sk_deep_copy, OPENSSL_sk_delete, OPENSSL_sk_delete_ptr,
OPENSSL_sk_dup, OPENSSL_sk_find, OPENSSL_sk_find_ex, OPENSSL_sk_find_all,
OPENSSL_sk_free, OPENSSL_sk_insert, OPENSSL_sk_is_sorted, OPENSSL_sk_new,
OPENSSL_sk_new_null, OPENSSL_sk_new_reserve, OPENSSL_sk_num, OPENSSL_sk_pop,
OPENSSL_sk_pop_free, OPENSSL_sk_push, OPENSSL_sk_reserve, OPENSSL_sk_set,
OPENSSL_sk_set_cmp_func, OPENSSL_sk_shift, OPENSSL_sk_sort,
OPENSSL_sk_unshift, OPENSSL_sk_value, OPENSSL_sk_zero
\&\- stack container
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/safestack.h>
\&
\& STACK_OF(TYPE)
\& DEFINE_STACK_OF(TYPE)
\& DEFINE_STACK_OF_CONST(TYPE)
\& DEFINE_SPECIAL_STACK_OF(FUNCTYPE, TYPE)
\& DEFINE_SPECIAL_STACK_OF_CONST(FUNCTYPE, TYPE)
\&
\& typedef int (*sk_TYPE_compfunc)(const TYPE *const *a, const TYPE *const *b);
\& typedef TYPE * (*sk_TYPE_copyfunc)(const TYPE *a);
\& typedef void (*sk_TYPE_freefunc)(TYPE *a);
\&
\& int sk_TYPE_num(const STACK_OF(TYPE) *sk);
\& TYPE *sk_TYPE_value(const STACK_OF(TYPE) *sk, int idx);
\& STACK_OF(TYPE) *sk_TYPE_new(sk_TYPE_compfunc compare);
\& STACK_OF(TYPE) *sk_TYPE_new_null(void);
\& int sk_TYPE_reserve(STACK_OF(TYPE) *sk, int n);
\& void sk_TYPE_free(const STACK_OF(TYPE) *sk);
\& void sk_TYPE_zero(const STACK_OF(TYPE) *sk);
\& TYPE *sk_TYPE_delete(STACK_OF(TYPE) *sk, int i);
\& TYPE *sk_TYPE_delete_ptr(STACK_OF(TYPE) *sk, TYPE *ptr);
\& int sk_TYPE_push(STACK_OF(TYPE) *sk, const TYPE *ptr);
\& int sk_TYPE_unshift(STACK_OF(TYPE) *sk, const TYPE *ptr);
\& TYPE *sk_TYPE_pop(STACK_OF(TYPE) *sk);
\& TYPE *sk_TYPE_shift(STACK_OF(TYPE) *sk);
\& void sk_TYPE_pop_free(STACK_OF(TYPE) *sk, sk_TYPE_freefunc freefunc);
\& int sk_TYPE_insert(STACK_OF(TYPE) *sk, TYPE *ptr, int idx);
\& TYPE *sk_TYPE_set(STACK_OF(TYPE) *sk, int idx, const TYPE *ptr);
\& int sk_TYPE_find(STACK_OF(TYPE) *sk, TYPE *ptr);
\& int sk_TYPE_find_ex(STACK_OF(TYPE) *sk, TYPE *ptr);
\& int sk_TYPE_find_all(STACK_OF(TYPE) *sk, TYPE *ptr, int *pnum);
\& void sk_TYPE_sort(const STACK_OF(TYPE) *sk);
\& int sk_TYPE_is_sorted(const STACK_OF(TYPE) *sk);
\& STACK_OF(TYPE) *sk_TYPE_dup(const STACK_OF(TYPE) *sk);
\& STACK_OF(TYPE) *sk_TYPE_deep_copy(const STACK_OF(TYPE) *sk,
\&                                   sk_TYPE_copyfunc copyfunc,
\&                                   sk_TYPE_freefunc freefunc);
\& sk_TYPE_compfunc (*sk_TYPE_set_cmp_func(STACK_OF(TYPE) *sk,
\&                                         sk_TYPE_compfunc compare));
\& STACK_OF(TYPE) *sk_TYPE_new_reserve(sk_TYPE_compfunc compare, int n);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Applications can create and use their own stacks by placing any of the macros
described below in a header file. These macros define typesafe inline
functions that wrap around the utility \fBOPENSSL_sk_\fR API.
In the description here, \fR\f(BITYPE\fR\fB\fR is used
as a placeholder for any of the OpenSSL datatypes, such as \fBX509\fR.
.PP
The \fBSTACK_OF()\fR macro returns the name for a stack of the specified \fR\f(BITYPE\fR\fB\fR.
This is an opaque pointer to a structure declaration.
This can be used in every header file that references the stack.
There are several \fBDEFINE...\fR macros that create static inline functions
for all of the functions described on this page.
This should normally be used in one source file, and the stack manipulation
is wrapped with application-specific functions.
.PP
\&\fBDEFINE_STACK_OF()\fR creates set of functions for a stack of \fR\f(BITYPE\fR\fB\fR elements.
The type is referenced by
\&\fBSTACK_OF\fR(\fB\fR\f(BITYPE\fR\fB\fR) and each function name begins with \fBsk_\fR\f(BITYPE\fR\fB_\fR.
\&\fBDEFINE_STACK_OF_CONST()\fR is identical to \fBDEFINE_STACK_OF()\fR except
each element is constant.
.PP
.Vb 4
\& /* DEFINE_STACK_OF(TYPE) */
\& TYPE *sk_TYPE_value(STACK_OF(TYPE) *sk, int idx);
\& /* DEFINE_STACK_OF_CONST(TYPE) */
\& const TYPE *sk_TYPE_value(STACK_OF(TYPE) *sk, int idx);
.Ve
.PP
\&\fBDEFINE_SPECIAL_STACK_OF()\fR and \fBDEFINE_SPECIAL_STACK_OF_CONST()\fR are similar
except \fBFUNCNAME\fR is used in the function names:
.PP
.Vb 4
\& /* DEFINE_SPECIAL_STACK_OF(TYPE, FUNCNAME) */
\& TYPE *sk_FUNCNAME_value(STACK_OF(TYPE) *sk, int idx);
\& /* DEFINE_SPECIAL_STACK_OF(TYPE, FUNCNAME) */
\& const TYPE *sk_FUNCNAME_value(STACK_OF(TYPE) *sk, int idx);
.Ve
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_num\fR() returns the number of elements in \fIsk\fR or \-1 if \fIsk\fR is
NULL.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_value\fR() returns element \fIidx\fR in \fIsk\fR, where \fIidx\fR starts at
zero. If \fIidx\fR is out of range then NULL is returned.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_new\fR() allocates a new empty stack using comparison function
\&\fIcompare\fR. If \fIcompare\fR is NULL then no comparison function is used. This
function is equivalent to \fBsk_\fR\f(BITYPE\fR\fB_new_reserve\fR(\fIcompare\fR, 0).
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_new_null\fR() allocates a new empty stack with no comparison
function. This function is equivalent to \fBsk_\fR\f(BITYPE\fR\fB_new_reserve\fR(NULL, 0).
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_reserve\fR() allocates additional memory in the \fIsk\fR structure
such that the next \fIn\fR calls to \fBsk_\fR\f(BITYPE\fR\fB_insert\fR(), \fBsk_\fR\f(BITYPE\fR\fB_push\fR()
or \fBsk_\fR\f(BITYPE\fR\fB_unshift\fR() will not fail or cause memory to be allocated
or reallocated. If \fIn\fR is zero, any excess space allocated in the
\&\fIsk\fR structure is freed. On error \fIsk\fR is unchanged.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_new_reserve\fR() allocates a new stack. The new stack will have
additional memory allocated to hold \fIn\fR elements if \fIn\fR is positive.
The next \fIn\fR calls to \fBsk_\fR\f(BITYPE\fR\fB_insert\fR(), \fBsk_\fR\f(BITYPE\fR\fB_push\fR() or
\&\fBsk_\fR\f(BITYPE\fR\fB_unshift\fR() will not fail or cause memory to be allocated or
reallocated. If \fIn\fR is zero or less than zero, no memory is allocated.
\&\fBsk_\fR\f(BITYPE\fR\fB_new_reserve\fR() also sets the comparison function \fIcompare\fR
to the newly created stack. If \fIcompare\fR is NULL then no comparison
function is used.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_set_cmp_func\fR() sets the comparison function of \fIsk\fR to
\&\fIcompare\fR. The previous comparison function is returned or NULL if there
was no previous comparison function.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_free\fR() frees up the \fIsk\fR structure. It does \fInot\fR free up any
elements of \fIsk\fR. After this call \fIsk\fR is no longer valid.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_zero\fR() sets the number of elements in \fIsk\fR to zero. It does not
free \fIsk\fR so after this call \fIsk\fR is still valid.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_pop_free\fR() frees up all elements of \fIsk\fR and \fIsk\fR itself. The
free function \fBfreefunc()\fR is called on each element to free it.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_delete\fR() deletes element \fIi\fR from \fIsk\fR. It returns the deleted
element or NULL if \fIi\fR is out of range.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_delete_ptr\fR() deletes element matching \fIptr\fR from \fIsk\fR. It
returns the deleted element or NULL if no element matching \fIptr\fR was found.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_insert\fR() inserts \fIptr\fR into \fIsk\fR at position \fIidx\fR. Any
existing elements at or after \fIidx\fR are moved downwards. If \fIidx\fR is out
of range the new element is appended to \fIsk\fR. \fBsk_\fR\f(BITYPE\fR\fB_insert\fR() either
returns the number of elements in \fIsk\fR after the new element is inserted or
zero if an error (such as memory allocation failure) occurred.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_push\fR() appends \fIptr\fR to \fIsk\fR it is equivalent to:
.PP
.Vb 1
\& sk_TYPE_insert(sk, ptr, \-1);
.Ve
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_unshift\fR() inserts \fIptr\fR at the start of \fIsk\fR it is equivalent
to:
.PP
.Vb 1
\& sk_TYPE_insert(sk, ptr, 0);
.Ve
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_pop\fR() returns and removes the last element from \fIsk\fR.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_shift\fR() returns and removes the first element from \fIsk\fR.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_set\fR() sets element \fIidx\fR of \fIsk\fR to \fIptr\fR replacing the current
element. The new element value is returned or NULL if an error occurred:
this will only happen if \fIsk\fR is NULL or \fIidx\fR is out of range.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_find\fR() searches \fIsk\fR for the element \fIptr\fR.  In the case
where no comparison function has been specified, the function performs
a linear search for a pointer equal to \fIptr\fR. The index of the first
matching element is returned or \fB\-1\fR if there is no match. In the case
where a comparison function has been specified, \fIsk\fR is sorted and
\&\fBsk_\fR\f(BITYPE\fR\fB_find\fR() returns the index of a matching element or \fB\-1\fR if there
is no match. Note that, in this case the comparison function will usually
compare the values pointed to rather than the pointers themselves and
the order of elements in \fIsk\fR can change. Note that because the stack may be
sorted as the result of a \fBsk_\fR\f(BITYPE\fR\fB_find\fR() call, if a lock is being used to
synchronise access to the stack across multiple threads, then that lock must be
a "write" lock.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_find_ex\fR() operates like \fBsk_\fR\f(BITYPE\fR\fB_find\fR() except when a
comparison function has been specified and no matching element is found.
Instead of returning \fB\-1\fR, \fBsk_\fR\f(BITYPE\fR\fB_find_ex\fR() returns the index of the
element either before or after the location where \fIptr\fR would be if it were
present in \fIsk\fR. The function also does not guarantee that the first matching
element in the sorted stack is returned.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_find_all\fR() operates like \fBsk_\fR\f(BITYPE\fR\fB_find\fR() but it also
sets the \fI*pnum\fR to number of matching elements in the stack. In case
no comparison function has been specified the \fI*pnum\fR will be always set
to 1 if matching element was found, 0 otherwise.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_sort\fR() sorts \fIsk\fR using the supplied comparison function.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_is_sorted\fR() returns \fB1\fR if \fIsk\fR is sorted and \fB0\fR otherwise.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_dup\fR() returns a shallow copy of \fIsk\fR
or an empty stack if the passed stack is NULL.
Note the pointers in the copy are identical to the original.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_deep_copy\fR() returns a new stack where each element has been
copied or an empty stack if the passed stack is NULL.
Copying is performed by the supplied \fBcopyfunc()\fR and freeing by \fBfreefunc()\fR.
The function \fBfreefunc()\fR is only called if an error occurs.
.SH NOTES
.IX Header "NOTES"
Care should be taken when accessing stacks in multi-threaded environments.
Any operation which increases the size of a stack such as \fBsk_\fR\f(BITYPE\fR\fB_insert\fR()
or \fBsk_\fR\f(BITYPE\fR\fB_push\fR() can "grow" the size of an internal array and cause race
conditions if the same stack is accessed in a different thread. Operations such
as \fBsk_\fR\f(BITYPE\fR\fB_find\fR() and \fBsk_\fR\f(BITYPE\fR\fB_sort\fR() can also reorder the stack.
.PP
Any comparison function supplied should use a metric suitable
for use in a binary search operation. That is it should return zero, a
positive or negative value if \fIa\fR is equal to, greater than
or less than \fIb\fR respectively.
.PP
Care should be taken when checking the return values of the functions
\&\fBsk_\fR\f(BITYPE\fR\fB_find\fR() and \fBsk_\fR\f(BITYPE\fR\fB_find_ex\fR(). They return an index to the
matching element. In particular \fB0\fR indicates a matching first element.
A failed search is indicated by a \fB\-1\fR return value.
.PP
\&\fBSTACK_OF()\fR, \fBDEFINE_STACK_OF()\fR, \fBDEFINE_STACK_OF_CONST()\fR, and
\&\fBDEFINE_SPECIAL_STACK_OF()\fR are implemented as macros.
.PP
It is not an error to call \fBsk_\fR\f(BITYPE\fR\fB_num\fR(), \fBsk_\fR\f(BITYPE\fR\fB_value\fR(),
\&\fBsk_\fR\f(BITYPE\fR\fB_free\fR(), \fBsk_\fR\f(BITYPE\fR\fB_zero\fR(), \fBsk_\fR\f(BITYPE\fR\fB_pop_free\fR(),
\&\fBsk_\fR\f(BITYPE\fR\fB_delete\fR(), \fBsk_\fR\f(BITYPE\fR\fB_delete_ptr\fR(), \fBsk_\fR\f(BITYPE\fR\fB_pop\fR(),
\&\fBsk_\fR\f(BITYPE\fR\fB_shift\fR(), \fBsk_\fR\f(BITYPE\fR\fB_find\fR(), \fBsk_\fR\f(BITYPE\fR\fB_find_ex\fR(),
and \fBsk_\fR\f(BITYPE\fR\fB_find_all\fR() on a NULL stack, empty stack, or with
an invalid index. An error is not raised in these conditions.
.PP
The underlying utility \fBOPENSSL_sk_\fR API should not be used directly.
It defines these functions: \fBOPENSSL_sk_deep_copy()\fR,
\&\fBOPENSSL_sk_delete()\fR, \fBOPENSSL_sk_delete_ptr()\fR, \fBOPENSSL_sk_dup()\fR,
\&\fBOPENSSL_sk_find()\fR, \fBOPENSSL_sk_find_ex()\fR, \fBOPENSSL_sk_find_all()\fR,
\&\fBOPENSSL_sk_free()\fR, \fBOPENSSL_sk_insert()\fR, \fBOPENSSL_sk_is_sorted()\fR,
\&\fBOPENSSL_sk_new()\fR, \fBOPENSSL_sk_new_null()\fR, \fBOPENSSL_sk_new_reserve()\fR,
\&\fBOPENSSL_sk_num()\fR, \fBOPENSSL_sk_pop()\fR, \fBOPENSSL_sk_pop_free()\fR, \fBOPENSSL_sk_push()\fR,
\&\fBOPENSSL_sk_reserve()\fR, \fBOPENSSL_sk_set()\fR, \fBOPENSSL_sk_set_cmp_func()\fR,
\&\fBOPENSSL_sk_shift()\fR, \fBOPENSSL_sk_sort()\fR, \fBOPENSSL_sk_unshift()\fR,
\&\fBOPENSSL_sk_value()\fR, \fBOPENSSL_sk_zero()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBsk_\fR\f(BITYPE\fR\fB_num\fR() returns the number of elements in the stack or \fB\-1\fR if the
passed stack is NULL.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_value\fR() returns a pointer to a stack element or NULL if the
index is out of range.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_new\fR(), \fBsk_\fR\f(BITYPE\fR\fB_new_null\fR() and \fBsk_\fR\f(BITYPE\fR\fB_new_reserve\fR()
return an empty stack or NULL if an error occurs.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_reserve\fR() returns \fB1\fR on successful allocation of the required
memory or \fB0\fR on error.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_set_cmp_func\fR() returns the old comparison function or NULL if
there was no old comparison function.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_free\fR(), \fBsk_\fR\f(BITYPE\fR\fB_zero\fR(), \fBsk_\fR\f(BITYPE\fR\fB_pop_free\fR() and
\&\fBsk_\fR\f(BITYPE\fR\fB_sort\fR() do not return values.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_pop\fR(), \fBsk_\fR\f(BITYPE\fR\fB_shift\fR(), \fBsk_\fR\f(BITYPE\fR\fB_delete\fR() and
\&\fBsk_\fR\f(BITYPE\fR\fB_delete_ptr\fR() return a pointer to the deleted element or NULL
on error.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_insert\fR(), \fBsk_\fR\f(BITYPE\fR\fB_push\fR() and \fBsk_\fR\f(BITYPE\fR\fB_unshift\fR() return
the total number of elements in the stack and 0 if an error occurred.
\&\fBsk_\fR\f(BITYPE\fR\fB_push\fR() further returns \-1 if \fIsk\fR is NULL.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_set\fR() returns a pointer to the replacement element or NULL on
error.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_find\fR() and \fBsk_\fR\f(BITYPE\fR\fB_find_ex\fR() return an index to the found
element or \fB\-1\fR on error.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_is_sorted\fR() returns \fB1\fR if the stack is sorted and \fB0\fR if it is
not.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_dup\fR() and \fBsk_\fR\f(BITYPE\fR\fB_deep_copy\fR() return a pointer to the copy
of the stack or NULL on error.
.SH HISTORY
.IX Header "HISTORY"
Before OpenSSL 1.1.0, this was implemented via macros and not inline functions
and was not a public API.
.PP
\&\fBsk_\fR\f(BITYPE\fR\fB_reserve\fR() and \fBsk_\fR\f(BITYPE\fR\fB_new_reserve\fR() were added in OpenSSL
1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
