.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PEM_X509_INFO_READ_BIO_EX 3ossl"
.TH PEM_X509_INFO_READ_BIO_EX 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PEM_X509_INFO_read_ex, PEM_X509_INFO_read, PEM_X509_INFO_read_bio_ex, PEM_X509_INFO_read_bio
\&\- read PEM\-encoded data structures into one or more X509_INFO objects
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pem.h>
\&
\& STACK_OF(X509_INFO) *PEM_X509_INFO_read_ex(FILE *fp, STACK_OF(X509_INFO) *sk,
\&                                            pem_password_cb *cb, void *u,
\&                                            OSSL_LIB_CTX *libctx,
\&                                            const char *propq);
\& STACK_OF(X509_INFO) *PEM_X509_INFO_read(FILE *fp, STACK_OF(X509_INFO) *sk,
\&                                         pem_password_cb *cb, void *u);
\& STACK_OF(X509_INFO) *PEM_X509_INFO_read_bio_ex(BIO *bio,
\&                                                STACK_OF(X509_INFO) *sk,
\&                                                pem_password_cb *cb, void *u,
\&                                                OSSL_LIB_CTX *libctx,
\&                                                const char *propq);
\& STACK_OF(X509_INFO) *PEM_X509_INFO_read_bio(BIO *bp, STACK_OF(X509_INFO) *sk,
\&                                             pem_password_cb *cb, void *u);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPEM_X509_INFO_read_ex()\fR loads the \fBX509_INFO\fR objects from a file \fIfp\fR.
.PP
\&\fBPEM_X509_INFO_read()\fR is similar to \fBPEM_X509_INFO_read_ex()\fR
but uses the default (NULL) library context \fIlibctx\fR
and empty property query \fIpropq\fR.
.PP
\&\fBPEM_X509_INFO_read_bio_ex()\fR loads the \fBX509_INFO\fR objects using a bio \fIbp\fR.
.PP
\&\fBPEM_X509_INFO_read_bio()\fR is similar to \fBPEM_X509_INFO_read_bio_ex()\fR
but uses the default (NULL) library context \fIlibctx\fR
and empty property query \fIpropq\fR.
.PP
Each of the loaded \fBX509_INFO\fR objects can contain a CRL, a certificate,
and/or a private key.
The elements are read sequentially, and as far as they are of different type than
the elements read before, they are combined into the same \fBX509_INFO\fR object.
The idea behind this is that if, for instance, a certificate is followed by
a private key, the private key is supposed to correspond to the certificate.
.PP
If the input stack \fIsk\fR is NULL a new stack is allocated,
else the given stack is extended.
.PP
The optional \fIcb\fR and \fIu\fR parameters can be used for providing a pass phrase
needed for decrypting encrypted PEM structures (normally only private keys).
See \fBPEM_read_bio_PrivateKey\fR\|(3) and \fBpassphrase\-encoding\fR\|(7) for details.
.PP
The library context \fIlibctx\fR and property query \fIpropq\fR are used for fetching
algorithms from providers.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPEM_X509_INFO_read_ex()\fR, \fBPEM_X509_INFO_read()\fR,
\&\fBPEM_X509_INFO_read_bio_ex()\fR and \fBPEM_X509_INFO_read_bio()\fR return
a stack of \fBX509_INFO\fR objects or NULL on failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBPEM_read_bio_ex\fR\|(3),
\&\fBPEM_read_bio_PrivateKey\fR\|(3),
\&\fBpassphrase\-encoding\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The functions \fBPEM_X509_INFO_read_ex()\fR and
\&\fBPEM_X509_INFO_read_bio_ex()\fR were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
