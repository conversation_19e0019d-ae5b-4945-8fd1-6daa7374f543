.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BN_ADD 3ossl"
.TH BN_ADD 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BN_add, BN_sub, BN_mul, BN_sqr, BN_div, BN_mod, BN_nnmod, BN_mod_add,
BN_mod_sub, BN_mod_mul, BN_mod_sqr, BN_mod_sqrt, BN_exp, BN_mod_exp, BN_gcd \-
arithmetic operations on BIGNUMs
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bn.h>
\&
\& int BN_add(BIGNUM *r, const BIGNUM *a, const BIGNUM *b);
\&
\& int BN_sub(BIGNUM *r, const BIGNUM *a, const BIGNUM *b);
\&
\& int BN_mul(BIGNUM *r, BIGNUM *a, BIGNUM *b, BN_CTX *ctx);
\&
\& int BN_sqr(BIGNUM *r, BIGNUM *a, BN_CTX *ctx);
\&
\& int BN_div(BIGNUM *dv, BIGNUM *rem, const BIGNUM *a, const BIGNUM *d,
\&            BN_CTX *ctx);
\&
\& int BN_mod(BIGNUM *rem, const BIGNUM *a, const BIGNUM *m, BN_CTX *ctx);
\&
\& int BN_nnmod(BIGNUM *r, const BIGNUM *a, const BIGNUM *m, BN_CTX *ctx);
\&
\& int BN_mod_add(BIGNUM *r, BIGNUM *a, BIGNUM *b, const BIGNUM *m,
\&                BN_CTX *ctx);
\&
\& int BN_mod_sub(BIGNUM *r, BIGNUM *a, BIGNUM *b, const BIGNUM *m,
\&                BN_CTX *ctx);
\&
\& int BN_mod_mul(BIGNUM *r, BIGNUM *a, BIGNUM *b, const BIGNUM *m,
\&                BN_CTX *ctx);
\&
\& int BN_mod_sqr(BIGNUM *r, BIGNUM *a, const BIGNUM *m, BN_CTX *ctx);
\&
\& BIGNUM *BN_mod_sqrt(BIGNUM *in, BIGNUM *a, const BIGNUM *p, BN_CTX *ctx);
\&
\& int BN_exp(BIGNUM *r, BIGNUM *a, BIGNUM *p, BN_CTX *ctx);
\&
\& int BN_mod_exp(BIGNUM *r, BIGNUM *a, const BIGNUM *p,
\&                const BIGNUM *m, BN_CTX *ctx);
\&
\& int BN_gcd(BIGNUM *r, BIGNUM *a, BIGNUM *b, BN_CTX *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBN_add()\fR adds \fIa\fR and \fIb\fR and places the result in \fIr\fR (\f(CW\*(C`r=a+b\*(C'\fR).
\&\fIr\fR may be the same \fBBIGNUM\fR as \fIa\fR or \fIb\fR.
.PP
\&\fBBN_sub()\fR subtracts \fIb\fR from \fIa\fR and places the result in \fIr\fR (\f(CW\*(C`r=a\-b\*(C'\fR).
\&\fIr\fR may be the same \fBBIGNUM\fR as \fIa\fR or \fIb\fR.
.PP
\&\fBBN_mul()\fR multiplies \fIa\fR and \fIb\fR and places the result in \fIr\fR (\f(CW\*(C`r=a*b\*(C'\fR).
\&\fIr\fR may be the same \fBBIGNUM\fR as \fIa\fR or \fIb\fR.
For multiplication by powers of 2, use \fBBN_lshift\fR\|(3).
.PP
\&\fBBN_sqr()\fR takes the square of \fIa\fR and places the result in \fIr\fR
(\f(CW\*(C`r=a^2\*(C'\fR). \fIr\fR and \fIa\fR may be the same \fBBIGNUM\fR.
This function is faster than BN_mul(r,a,a).
.PP
\&\fBBN_div()\fR divides \fIa\fR by \fId\fR and places the result in \fIdv\fR and the
remainder in \fIrem\fR (\f(CW\*(C`dv=a/d, rem=a%d\*(C'\fR). Either of \fIdv\fR and \fIrem\fR may
be \fBNULL\fR, in which case the respective value is not returned.
The result is rounded towards zero; thus if \fIa\fR is negative, the
remainder will be zero or negative.
For division by powers of 2, use \fBBN_rshift\fR\|(3).
.PP
\&\fBBN_mod()\fR corresponds to \fBBN_div()\fR with \fIdv\fR set to \fBNULL\fR.
.PP
\&\fBBN_nnmod()\fR reduces \fIa\fR modulo \fIm\fR and places the nonnegative
remainder in \fIr\fR.
.PP
\&\fBBN_mod_add()\fR adds \fIa\fR to \fIb\fR modulo \fIm\fR and places the nonnegative
result in \fIr\fR.
.PP
\&\fBBN_mod_sub()\fR subtracts \fIb\fR from \fIa\fR modulo \fIm\fR and places the
nonnegative result in \fIr\fR.
.PP
\&\fBBN_mod_mul()\fR multiplies \fIa\fR by \fIb\fR and finds the nonnegative
remainder respective to modulus \fIm\fR (\f(CW\*(C`r=(a*b) mod m\*(C'\fR). \fIr\fR may be
the same \fBBIGNUM\fR as \fIa\fR or \fIb\fR. For more efficient algorithms for
repeated computations using the same modulus, see
\&\fBBN_mod_mul_montgomery\fR\|(3) and
\&\fBBN_mod_mul_reciprocal\fR\|(3).
.PP
\&\fBBN_mod_sqr()\fR takes the square of \fIa\fR modulo \fBm\fR and places the
result in \fIr\fR.
.PP
\&\fBBN_mod_sqrt()\fR returns the modular square root of \fIa\fR such that
\&\f(CW\*(C`in^2 = a (mod p)\*(C'\fR. The modulus \fIp\fR must be a
prime, otherwise an error or an incorrect "result" will be returned.
The result is stored into \fIin\fR which can be NULL. The result will be
newly allocated in that case.
.PP
\&\fBBN_exp()\fR raises \fIa\fR to the \fIp\fR\-th power and places the result in \fIr\fR
(\f(CW\*(C`r=a^p\*(C'\fR). This function is faster than repeated applications of
\&\fBBN_mul()\fR.
.PP
\&\fBBN_mod_exp()\fR computes \fIa\fR to the \fIp\fR\-th power modulo \fIm\fR (\f(CW\*(C`r=a^p %
m\*(C'\fR). This function uses less time and space than \fBBN_exp()\fR. Do not call this
function when \fBm\fR is even and any of the parameters have the
\&\fBBN_FLG_CONSTTIME\fR flag set.
.PP
\&\fBBN_gcd()\fR computes the greatest common divisor of \fIa\fR and \fIb\fR and
places the result in \fIr\fR. \fIr\fR may be the same \fBBIGNUM\fR as \fIa\fR or
\&\fIb\fR.
.PP
For all functions, \fIctx\fR is a previously allocated \fBBN_CTX\fR used for
temporary variables; see \fBBN_CTX_new\fR\|(3).
.PP
Unless noted otherwise, the result \fBBIGNUM\fR must be different from
the arguments.
.SH NOTES
.IX Header "NOTES"
For modular operations such as \fBBN_nnmod()\fR or \fBBN_mod_exp()\fR it is an error
to use the same \fBBIGNUM\fR object for the modulus as for the output.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The \fBBN_mod_sqrt()\fR returns the result (possibly incorrect if \fIp\fR is
not a prime), or NULL.
.PP
For all remaining functions, 1 is returned for success, 0 on error. The return
value should always be checked (e.g., \f(CW\*(C`if (!BN_add(r,a,b)) goto err;\*(C'\fR).
The error codes can be obtained by \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBBN_CTX_new\fR\|(3),
\&\fBBN_add_word\fR\|(3), \fBBN_set_bit\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
