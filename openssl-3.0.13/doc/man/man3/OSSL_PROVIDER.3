.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_PROVIDER 3ossl"
.TH OSSL_PROVIDER 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_PROVIDER_set_default_search_path,
OSSL_PROVIDER, OSSL_PROVIDER_load, OSSL_PROVIDER_try_load, OSSL_PROVIDER_unload,
OSSL_PROVIDER_available, OSSL_PROVIDER_do_all,
OSSL_PROVIDER_gettable_params, OSSL_PROVIDER_get_params,
OSSL_PROVIDER_query_operation, OSSL_PROVIDER_unquery_operation,
OSSL_PROVIDER_get0_provider_ctx, OSSL_PROVIDER_get0_dispatch,
OSSL_PROVIDER_add_builtin, OSSL_PROVIDER_get0_name, OSSL_PROVIDER_get_capabilities,
OSSL_PROVIDER_self_test
\&\- provider routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/provider.h>
\&
\& typedef struct ossl_provider_st OSSL_PROVIDER;
\&
\& int OSSL_PROVIDER_set_default_search_path(OSSL_LIB_CTX *libctx,
\&                                           const char *path);
\&
\& OSSL_PROVIDER *OSSL_PROVIDER_load(OSSL_LIB_CTX *libctx, const char *name);
\& OSSL_PROVIDER *OSSL_PROVIDER_try_load(OSSL_LIB_CTX *libctx, const char *name,
\&                                       int retain_fallbacks);
\& int OSSL_PROVIDER_unload(OSSL_PROVIDER *prov);
\& int OSSL_PROVIDER_available(OSSL_LIB_CTX *libctx, const char *name);
\& int OSSL_PROVIDER_do_all(OSSL_LIB_CTX *ctx,
\&                          int (*cb)(OSSL_PROVIDER *provider, void *cbdata),
\&                          void *cbdata);
\&
\& const OSSL_PARAM *OSSL_PROVIDER_gettable_params(OSSL_PROVIDER *prov);
\& int OSSL_PROVIDER_get_params(OSSL_PROVIDER *prov, OSSL_PARAM params[]);
\&
\& const OSSL_ALGORITHM *OSSL_PROVIDER_query_operation(const OSSL_PROVIDER *prov,
\&                                                     int operation_id,
\&                                                     int *no_cache);
\& void OSSL_PROVIDER_unquery_operation(const OSSL_PROVIDER *prov,
\&                                      int operation_id,
\&                                      const OSSL_ALGORITHM *algs);
\& void *OSSL_PROVIDER_get0_provider_ctx(const OSSL_PROVIDER *prov);
\& const OSSL_DISPATCH *OSSL_PROVIDER_get0_dispatch(const OSSL_PROVIDER *prov);
\&
\& int OSSL_PROVIDER_add_builtin(OSSL_LIB_CTX *libctx, const char *name,
\&                               ossl_provider_init_fn *init_fn);
\&
\& const char *OSSL_PROVIDER_get0_name(const OSSL_PROVIDER *prov);
\&
\& int OSSL_PROVIDER_get_capabilities(const OSSL_PROVIDER *prov,
\&                                    const char *capability,
\&                                    OSSL_CALLBACK *cb,
\&                                    void *arg);
\& int OSSL_PROVIDER_self_test(const OSSL_PROVIDER *prov);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOSSL_PROVIDER\fR is a type that holds internal information about
implementation providers (see \fBprovider\fR\|(7) for information on what a
provider is).
A provider can be built in to the application or the OpenSSL
libraries, or can be a loadable module.
The functions described here handle both forms.
.PP
Some of these functions operate within a library context, please see
\&\fBOSSL_LIB_CTX\fR\|(3) for further details.
.SS Functions
.IX Subsection "Functions"
\&\fBOSSL_PROVIDER_set_default_search_path()\fR specifies the default search \fIpath\fR
that is to be used for looking for providers in the specified \fIlibctx\fR.
If left unspecified, an environment variable and a fall back default value will
be used instead.
.PP
\&\fBOSSL_PROVIDER_add_builtin()\fR is used to add a built in provider to
\&\fBOSSL_PROVIDER\fR store in the given library context, by associating a
provider name with a provider initialization function.
This name can then be used with \fBOSSL_PROVIDER_load()\fR.
.PP
\&\fBOSSL_PROVIDER_load()\fR loads and initializes a provider.
This may simply initialize a provider that was previously added with
\&\fBOSSL_PROVIDER_add_builtin()\fR and run its given initialization function,
or load a provider module with the given name and run its provider
entry point, \f(CW\*(C`OSSL_provider_init\*(C'\fR. The \fIname\fR can be a path
to a provider module, in that case the provider name as returned
by \fBOSSL_PROVIDER_get0_name()\fR will be the path. Interpretation
of relative paths is platform dependent and they are relative
to the configured "MODULESDIR" directory or the path set in
the environment variable OPENSSL_MODULES if set.
.PP
\&\fBOSSL_PROVIDER_try_load()\fR functions like \fBOSSL_PROVIDER_load()\fR, except that
it does not disable the fallback providers if the provider cannot be
loaded and initialized or if \fIretain_fallbacks\fR is nonzero.
If the provider loads successfully and \fIretain_fallbacks\fR is zero, the
fallback providers are disabled.
.PP
\&\fBOSSL_PROVIDER_unload()\fR unloads the given provider.
For a provider added with \fBOSSL_PROVIDER_add_builtin()\fR, this simply
runs its teardown function.
.PP
\&\fBOSSL_PROVIDER_available()\fR checks if a named provider is available
for use.
.PP
\&\fBOSSL_PROVIDER_do_all()\fR iterates over all loaded providers, calling
\&\fIcb\fR for each one, with the current provider in \fIprovider\fR and the
\&\fIcbdata\fR that comes from the caller. If no other provider has been loaded
before calling this function, the default provider is still available as
fallback.
See \fBOSSL_PROVIDER\-default\fR\|(7) for more information on this fallback
behaviour.
.PP
\&\fBOSSL_PROVIDER_gettable_params()\fR is used to get a provider parameter
descriptor set as a constant \fBOSSL_PARAM\fR\|(3) array.
.PP
\&\fBOSSL_PROVIDER_get_params()\fR is used to get provider parameter values.
The caller must prepare the \fBOSSL_PARAM\fR\|(3) array before calling this
function, and the variables acting as buffers for this parameter array
should be filled with data when it returns successfully.
.PP
\&\fBOSSL_PROVIDER_self_test()\fR is used to run a provider's self tests on demand.
If the self tests fail then the provider will fail to provide any further
services and algorithms. \fBOSSL_SELF_TEST_set_callback\fR\|(3) may be called
beforehand in order to display diagnostics for the running self tests.
.PP
\&\fBOSSL_PROVIDER_query_operation()\fR calls the provider's \fIquery_operation\fR
function (see \fBprovider\fR\|(7)), if the provider has one. It returns an
array of \fIOSSL_ALGORITHM\fR for the given \fIoperation_id\fR terminated by an all
NULL OSSL_ALGORITHM entry. This is considered a low-level function that most
applications should not need to call.
.PP
\&\fBOSSL_PROVIDER_unquery_operation()\fR calls the provider's \fIunquery_operation\fR
function (see \fBprovider\fR\|(7)), if the provider has one.  This is considered a
low-level function that most applications should not need to call.
.PP
\&\fBOSSL_PROVIDER_get0_provider_ctx()\fR returns the provider context for the given
provider. The provider context is an opaque handle set by the provider itself
and is passed back to the provider by libcrypto in various function calls.
.PP
\&\fBOSSL_PROVIDER_get0_dispatch()\fR returns the provider's dispatch table as it was
returned in the \fIout\fR parameter from the provider's init function. See
\&\fBprovider\-base\fR\|(7).
.PP
If it is permissible to cache references to this array then \fI*no_store\fR is set
to 0 or 1 otherwise. If the array is not cacheable then it is assumed to
have a short lifetime.
.PP
\&\fBOSSL_PROVIDER_get0_name()\fR returns the name of the given provider.
.PP
\&\fBOSSL_PROVIDER_get_capabilities()\fR provides information about the capabilities
supported by the provider specified in \fIprov\fR with the capability name
\&\fIcapability\fR. For each capability of that name supported by the provider it
will call the callback \fIcb\fR and supply a set of \fBOSSL_PARAM\fR\|(3)s describing the
capability. It will also pass back the argument \fIarg\fR. For more details about
capabilities and what they can be used for please see
"CAPABILTIIES" in \fBprovider\-base\fR\|(7).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_PROVIDER_set_default_search_path()\fR, \fBOSSL_PROVIDER_add()\fR,
\&\fBOSSL_PROVIDER_unload()\fR, \fBOSSL_PROVIDER_get_params()\fR and
\&\fBOSSL_PROVIDER_get_capabilities()\fR return 1 on success, or 0 on error.
.PP
\&\fBOSSL_PROVIDER_load()\fR and \fBOSSL_PROVIDER_try_load()\fR return a pointer to a
provider object on success, or NULL on error.
.PP
\&\fBOSSL_PROVIDER_do_all()\fR returns 1 if the callback \fIcb\fR returns 1 for every
provider it is called with, or 0 if any provider callback invocation returns 0;
callback processing stops at the first callback invocation on a provider
that returns 0.
.PP
\&\fBOSSL_PROVIDER_available()\fR returns 1 if the named provider is available,
otherwise 0.
.PP
\&\fBOSSL_PROVIDER_gettable_params()\fR returns a pointer to an array
of constant \fBOSSL_PARAM\fR\|(3), or NULL if none is provided.
.PP
\&\fBOSSL_PROVIDER_get_params()\fR and returns 1 on success, or 0 on error.
.PP
\&\fBOSSL_PROVIDER_query_operation()\fR returns an array of OSSL_ALGORITHM or NULL on
error.
.PP
\&\fBOSSL_PROVIDER_self_test()\fR returns 1 if the self tests pass, or 0 on error.
.SH EXAMPLES
.IX Header "EXAMPLES"
This demonstrates how to load the provider module "foo" and ask for
its build information.
.PP
.Vb 3
\& #include <openssl/params.h>
\& #include <openssl/provider.h>
\& #include <openssl/err.h>
\&
\& OSSL_PROVIDER *prov = NULL;
\& const char *build = NULL;
\& OSSL_PARAM request[] = {
\&     { "buildinfo", OSSL_PARAM_UTF8_PTR, &build, 0, 0 },
\&     { NULL, 0, NULL, 0, 0 }
\& };
\&
\& if ((prov = OSSL_PROVIDER_load(NULL, "foo")) != NULL
\&     && OSSL_PROVIDER_get_params(prov, request))
\&     printf("Provider \*(Aqfoo\*(Aq buildinfo: %s\en", build);
\& else
\&     ERR_print_errors_fp(stderr);
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\-core.h\fR\|(7), \fBOSSL_LIB_CTX\fR\|(3), \fBprovider\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The type and functions described here were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
