.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS7_SIGN_ADD_SIGNER 3ossl"
.TH PKCS7_SIGN_ADD_SIGNER 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS7_sign_add_signer,
PKCS7_add_certificate, PKCS7_add_crl \- add information to PKCS7 structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pkcs7.h>
\&
\& PKCS7_SIGNER_INFO *PKCS7_sign_add_signer(PKCS7 *p7, X509 *signcert,
\&                                          EVP_PKEY *pkey, const EVP_MD *md, int flags);
\& int PKCS7_add_certificate(PKCS7 *p7, X509 *cert);
\& int PKCS7_add_crl(PKCS7 *p7, X509_CRL *crl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPKCS7_sign_add_signer()\fR adds a signer with certificate \fIsigncert\fR and private
key \fIpkey\fR using message digest \fImd\fR to a PKCS7 signed data structure \fIp7\fR.
.PP
The \fBPKCS7\fR structure should be obtained from an initial call to \fBPKCS7_sign()\fR
with the flag \fBPKCS7_PARTIAL\fR set or in the case or re-signing a valid PKCS#7
signed data structure.
.PP
If the \fImd\fR parameter is NULL then the default digest for the public
key algorithm will be used.
.PP
Unless the \fBPKCS7_REUSE_DIGEST\fR flag is set the returned \fBPKCS7\fR structure
is not complete and must be finalized either by streaming (if applicable) or
a call to \fBPKCS7_final()\fR.
.SH NOTES
.IX Header "NOTES"
The main purpose of this function is to provide finer control over a PKCS#7
signed data structure where the simpler \fBPKCS7_sign()\fR function defaults are
not appropriate. For example if multiple signers or non default digest
algorithms are needed.
.PP
Any of the following flags (ored together) can be passed in the \fIflags\fR
parameter.
.PP
If \fBPKCS7_REUSE_DIGEST\fR is set then an attempt is made to copy the content
digest value from the \fBPKCS7\fR structure: to add a signer to an existing structure.
An error occurs if a matching digest value cannot be found to copy. The
returned \fBPKCS7\fR structure will be valid and finalized when this flag is set.
.PP
If \fBPKCS7_PARTIAL\fR is set in addition to \fBPKCS7_REUSE_DIGEST\fR then the
\&\fBPKCS7_SIGNER_INO\fR structure will not be finalized so additional attributes
can be added. In this case an explicit call to \fBPKCS7_SIGNER_INFO_sign()\fR is
needed to finalize it.
.PP
If \fBPKCS7_NOCERTS\fR is set the signer's certificate will not be included in the
\&\fBPKCS7\fR structure, the signer's certificate must still be supplied in the
\&\fIsigncert\fR parameter though. This can reduce the size of the signature if the
signers certificate can be obtained by other means: for example a previously
signed message.
.PP
The signedData structure includes several PKCS#7 authenticatedAttributes
including the signing time, the PKCS#7 content type and the supported list of
ciphers in an SMIMECapabilities attribute. If \fBPKCS7_NOATTR\fR is set then no
authenticatedAttributes will be used. If \fBPKCS7_NOSMIMECAP\fR is set then just
the SMIMECapabilities are omitted.
.PP
If present the SMIMECapabilities attribute indicates support for the following
algorithms: triple DES, 128 bit RC2, 64 bit RC2, DES and 40 bit RC2. If any of
these algorithms is disabled then it will not be included.
.PP
\&\fBPKCS7_sign_add_signers()\fR returns an internal pointer to the \fBPKCS7_SIGNER_INFO\fR
structure just added, which can be used to set additional attributes
before it is finalized.
.PP
\&\fBPKCS7_add_certificate()\fR adds to the \fBPKCS7\fR structure \fIp7\fR the certificate
\&\fIcert\fR, which may be an end-entity (signer) certificate
or a CA certificate useful for chain building.
This is done internally by \fBPKCS7_sign_ex\fR\|(3) and similar signing functions.
It may have to be used before calling \fBPKCS7_verify\fR\|(3)
in order to provide any missing certificate(s) needed for verification.
.PP
\&\fBPKCS7_add_crl()\fR adds the CRL \fIcrl\fR to the \fBPKCS7\fR structure \fIp7\fR.
This may be called to provide certificate status information
to be included when signing or to use when verifying the \fBPKCS7\fR structure.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPKCS7_sign_add_signers()\fR returns an internal pointer to the \fBPKCS7_SIGNER_INFO\fR
structure just added or NULL if an error occurs.
.PP
\&\fBPKCS7_add_certificate()\fR and \fBPKCS7_add_crl()\fR return 1 on success, 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3), \fBPKCS7_sign_ex\fR\|(3),
\&\fBPKCS7_final\fR\|(3), \fBPKCS7_verify\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBPPKCS7_sign_add_signer()\fR function was added in OpenSSL 1.0.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2007\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
