.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_KEYEXCH_FREE 3ossl"
.TH EVP_KEYEXCH_FREE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_KEYEXCH_fetch, EVP_KEYEXCH_free, EVP_KEYEXCH_up_ref,
EVP_KEYEXCH_get0_provider, EVP_KEYEXCH_is_a, EVP_KEYEXCH_do_all_provided,
EVP_KEYEXCH_names_do_all, EVP_KEYEXCH_get0_name, EVP_KEYEXCH_get0_description,
EVP_KEYEXCH_gettable_ctx_params, EVP_KEYEXCH_settable_ctx_params
\&\- Functions to manage EVP_KEYEXCH algorithm objects
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& EVP_KEYEXCH *EVP_KEYEXCH_fetch(OSSL_LIB_CTX *ctx, const char *algorithm,
\&                                const char *properties);
\& void EVP_KEYEXCH_free(EVP_KEYEXCH *exchange);
\& int EVP_KEYEXCH_up_ref(EVP_KEYEXCH *exchange);
\& OSSL_PROVIDER *EVP_KEYEXCH_get0_provider(const EVP_KEYEXCH *exchange);
\& int EVP_KEYEXCH_is_a(const EVP_KEYEXCH *exchange, const char *name);
\& const char *EVP_KEYEXCH_get0_name(const EVP_KEYEXCH *exchange);
\& void EVP_KEYEXCH_do_all_provided(OSSL_LIB_CTX *libctx,
\&                                  void (*fn)(EVP_KEYEXCH *exchange, void *arg),
\&                                  void *arg);
\& int EVP_KEYEXCH_names_do_all(const EVP_KEYEXCH *exchange,
\&                              void (*fn)(const char *name, void *data),
\&                              void *data);
\& const char *EVP_KEYEXCH_get0_description(const EVP_KEYEXCH *keyexch);
\& const OSSL_PARAM *EVP_KEYEXCH_gettable_ctx_params(const EVP_KEYEXCH *keyexch);
\& const OSSL_PARAM *EVP_KEYEXCH_settable_ctx_params(const EVP_KEYEXCH *keyexch);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_KEYEXCH_fetch()\fR fetches the key exchange implementation for the given
\&\fIalgorithm\fR from any provider offering it, within the criteria given
by the \fIproperties\fR.
See "ALGORITHM FETCHING" in \fBcrypto\fR\|(7) for further information.
.PP
The returned value must eventually be freed with \fBEVP_KEYEXCH_free()\fR.
.PP
\&\fBEVP_KEYEXCH_free()\fR decrements the reference count for the \fBEVP_KEYEXCH\fR
structure. Typically this structure will have been obtained from an earlier call
to \fBEVP_KEYEXCH_fetch()\fR. If the reference count drops to 0 then the
structure is freed.
.PP
\&\fBEVP_KEYEXCH_up_ref()\fR increments the reference count for an \fBEVP_KEYEXCH\fR
structure.
.PP
\&\fBEVP_KEYEXCH_get0_provider()\fR returns the provider that \fIexchange\fR was
fetched from.
.PP
\&\fBEVP_KEYEXCH_is_a()\fR checks if \fIexchange\fR is an implementation of an
algorithm that's identifiable with \fIname\fR.
.PP
\&\fBEVP_KEYEXCH_get0_name()\fR returns the algorithm name from the provided
implementation for the given \fIexchange\fR. Note that the \fIexchange\fR may have
multiple synonyms associated with it. In this case the first name from the
algorithm definition is returned. Ownership of the returned string is retained
by the \fIexchange\fR object and should not be freed by the caller.
.PP
\&\fBEVP_KEYEXCH_names_do_all()\fR traverses all names for the \fIexchange\fR, and
calls \fIfn\fR with each name and \fIdata\fR.
.PP
\&\fBEVP_KEYEXCH_get0_description()\fR returns a description of the \fIkeyexch\fR, meant
for display and human consumption.  The description is at the discretion of
the \fIkeyexch\fR implementation.
.PP
\&\fBEVP_KEYEXCH_do_all_provided()\fR traverses all key exchange implementations by
all activated providers in the library context \fIlibctx\fR, and for each
of the implementations, calls \fIfn\fR with the implementation method and
\&\fIdata\fR as arguments.
.PP
\&\fBEVP_KEYEXCH_gettable_ctx_params()\fR and \fBEVP_KEYEXCH_settable_ctx_params()\fR return
a constant \fBOSSL_PARAM\fR\|(3) array that describes the names and types of key
parameters that can be retrieved or set by a key exchange algorithm using
\&\fBEVP_PKEY_CTX_get_params\fR\|(3) and \fBEVP_PKEY_CTX_set_params\fR\|(3).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_KEYEXCH_fetch()\fR returns a pointer to a \fBEVP_KEYEXCH\fR for success
or NULL for failure.
.PP
\&\fBEVP_KEYEXCH_up_ref()\fR returns 1 for success or 0 otherwise.
.PP
\&\fBEVP_KEYEXCH_names_do_all()\fR returns 1 if the callback was called for all
names. A return value of 0 means that the callback was not called for any names.
.PP
\&\fBEVP_KEYEXCH_is_a()\fR returns 1 of \fIexchange\fR was identifiable,
otherwise 0.
.PP
\&\fBEVP_KEYEXCH_gettable_ctx_params()\fR and \fBEVP_KEYEXCH_settable_ctx_params()\fR return
a constant \fBOSSL_PARAM\fR\|(3) array or NULL on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
"ALGORITHM FETCHING" in \fBcrypto\fR\|(7), \fBOSSL_PROVIDER\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The functions described here were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
