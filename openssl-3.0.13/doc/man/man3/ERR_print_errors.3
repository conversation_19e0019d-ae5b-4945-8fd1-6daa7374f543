.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ERR_PRINT_ERRORS 3ossl"
.TH ERR_PRINT_ERRORS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ERR_print_errors, ERR_print_errors_fp, ERR_print_errors_cb
\&\- print error messages
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/err.h>
\&
\& void ERR_print_errors(BIO *bp);
\& void ERR_print_errors_fp(FILE *fp);
\& void ERR_print_errors_cb(int (*cb)(const char *str, size_t len, void *u),
\&                          void *u);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBERR_print_errors()\fR is a convenience function that prints the error
strings for all errors that OpenSSL has recorded to \fBbp\fR, thus
emptying the error queue.
.PP
\&\fBERR_print_errors_fp()\fR is the same, except that the output goes to a
\&\fBFILE\fR.
.PP
\&\fBERR_print_errors_cb()\fR is the same, except that the callback function,
\&\fBcb\fR, is called for each error line with the string, length, and userdata
\&\fBu\fR as the callback parameters.
.PP
The error strings will have the following format:
.PP
.Vb 1
\& [pid]:error:[error code]:[library name]:[function name]:[reason string]:[filename]:[line]:[optional text message]
.Ve
.PP
\&\fIerror code\fR is an 8 digit hexadecimal number. \fIlibrary name\fR,
\&\fIfunction name\fR and \fIreason string\fR are ASCII text, as is \fIoptional
text message\fR if one was set for the respective error code.
.PP
If there is no text string registered for the given error code,
the error string will contain the numeric code.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBERR_print_errors()\fR and \fBERR_print_errors_fp()\fR return no values.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_error_string\fR\|(3),
\&\fBERR_get_error\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
