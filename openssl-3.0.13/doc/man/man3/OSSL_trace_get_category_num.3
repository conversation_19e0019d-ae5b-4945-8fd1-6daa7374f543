.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_TRACE_GET_CATEGORY_NUM 3ossl"
.TH OSSL_TRACE_GET_CATEGORY_NUM 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_trace_get_category_num, OSSL_trace_get_category_name
\&\- OpenSSL tracing information functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/trace.h>
\&
\& int OSSL_trace_get_category_num(const char *name);
\& const char *OSSL_trace_get_category_name(int num);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOSSL_trace_get_category_num()\fR gives the category number corresponding
to the given \f(CW\*(C`name\*(C'\fR.
.PP
\&\fBOSSL_trace_get_category_name()\fR gives the category name corresponding
to the given \f(CW\*(C`num\*(C'\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_trace_get_category_num()\fR returns the category number if the given
\&\f(CW\*(C`name\*(C'\fR is a recognised category name, otherwise \-1.
.PP
\&\fBOSSL_trace_get_category_name()\fR returns the category name if the given
\&\f(CW\*(C`num\*(C'\fR is a recognised category number, otherwise NULL.
.SH HISTORY
.IX Header "HISTORY"
The OpenSSL Tracing API was added ino OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
