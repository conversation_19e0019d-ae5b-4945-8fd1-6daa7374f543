.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_F_SSL 3ossl"
.TH BIO_F_SSL 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_do_handshake,
BIO_f_ssl, BIO_set_ssl, BIO_get_ssl, BIO_set_ssl_mode,
BIO_set_ssl_renegotiate_bytes,
BIO_get_num_renegotiates, BIO_set_ssl_renegotiate_timeout, BIO_new_ssl,
BIO_new_ssl_connect, BIO_new_buffer_ssl_connect, BIO_ssl_copy_session_id,
BIO_ssl_shutdown \- SSL BIO
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 2
\& #include <openssl/bio.h>
\& #include <openssl/ssl.h>
\&
\& const BIO_METHOD *BIO_f_ssl(void);
\&
\& long BIO_set_ssl(BIO *b, SSL *ssl, long c);
\& long BIO_get_ssl(BIO *b, SSL **sslp);
\& long BIO_set_ssl_mode(BIO *b, long client);
\& long BIO_set_ssl_renegotiate_bytes(BIO *b, long num);
\& long BIO_set_ssl_renegotiate_timeout(BIO *b, long seconds);
\& long BIO_get_num_renegotiates(BIO *b);
\&
\& BIO *BIO_new_ssl(SSL_CTX *ctx, int client);
\& BIO *BIO_new_ssl_connect(SSL_CTX *ctx);
\& BIO *BIO_new_buffer_ssl_connect(SSL_CTX *ctx);
\& int BIO_ssl_copy_session_id(BIO *to, BIO *from);
\& void BIO_ssl_shutdown(BIO *bio);
\&
\& long BIO_do_handshake(BIO *b);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_f_ssl()\fR returns the SSL BIO method. This is a filter BIO which
is a wrapper round the OpenSSL SSL routines adding a BIO "flavour" to
SSL I/O.
.PP
I/O performed on an SSL BIO communicates using the SSL protocol with
the SSLs read and write BIOs. If an SSL connection is not established
then an attempt is made to establish one on the first I/O call.
.PP
If a BIO is appended to an SSL BIO using \fBBIO_push()\fR it is automatically
used as the SSL BIOs read and write BIOs.
.PP
Calling \fBBIO_reset()\fR on an SSL BIO closes down any current SSL connection
by calling \fBSSL_shutdown()\fR. \fBBIO_reset()\fR is then sent to the next BIO in
the chain: this will typically disconnect the underlying transport.
The SSL BIO is then reset to the initial accept or connect state.
.PP
If the close flag is set when an SSL BIO is freed then the internal
SSL structure is also freed using \fBSSL_free()\fR.
.PP
\&\fBBIO_set_ssl()\fR sets the internal SSL pointer of SSL BIO \fBb\fR to \fBssl\fR using
the close flag \fBc\fR.
.PP
\&\fBBIO_get_ssl()\fR retrieves the SSL pointer of SSL BIO \fBb\fR, it can then be
manipulated using the standard SSL library functions.
.PP
\&\fBBIO_set_ssl_mode()\fR sets the SSL BIO mode to \fBclient\fR. If \fBclient\fR
is 1 client mode is set. If \fBclient\fR is 0 server mode is set.
.PP
\&\fBBIO_set_ssl_renegotiate_bytes()\fR sets the renegotiate byte count of SSL BIO \fBb\fR
to \fBnum\fR. When set after every \fBnum\fR bytes of I/O (read and write)
the SSL session is automatically renegotiated. \fBnum\fR must be at
least 512 bytes.
.PP
\&\fBBIO_set_ssl_renegotiate_timeout()\fR sets the renegotiate timeout of SSL BIO \fBb\fR
to \fBseconds\fR.
When the renegotiate timeout elapses the session is automatically renegotiated.
.PP
\&\fBBIO_get_num_renegotiates()\fR returns the total number of session
renegotiations due to I/O or timeout of SSL BIO \fBb\fR.
.PP
\&\fBBIO_new_ssl()\fR allocates an SSL BIO using SSL_CTX \fBctx\fR and using
client mode if \fBclient\fR is non zero.
.PP
\&\fBBIO_new_ssl_connect()\fR creates a new BIO chain consisting of an
SSL BIO (using \fBctx\fR) followed by a connect BIO.
.PP
\&\fBBIO_new_buffer_ssl_connect()\fR creates a new BIO chain consisting
of a buffering BIO, an SSL BIO (using \fBctx\fR), and a connect BIO.
.PP
\&\fBBIO_ssl_copy_session_id()\fR copies an SSL session id between
BIO chains \fBfrom\fR and \fBto\fR. It does this by locating the
SSL BIOs in each chain and calling \fBSSL_copy_session_id()\fR on
the internal SSL pointer.
.PP
\&\fBBIO_ssl_shutdown()\fR closes down an SSL connection on BIO
chain \fBbio\fR. It does this by locating the SSL BIO in the
chain and calling \fBSSL_shutdown()\fR on its internal SSL
pointer.
.PP
\&\fBBIO_do_handshake()\fR attempts to complete an SSL handshake on the
supplied BIO and establish the SSL connection.
For non-SSL BIOs the connection is done typically at TCP level.
If domain name resolution yields multiple IP addresses all of them are tried
after \fBconnect()\fR failures.
The function returns 1 if the connection was established successfully.
A zero or negative value is returned if the connection could not be established.
The call \fBBIO_should_retry()\fR should be used for nonblocking connect BIOs
to determine if the call should be retried.
If a connection has already been established this call has no effect.
.SH NOTES
.IX Header "NOTES"
SSL BIOs are exceptional in that if the underlying transport
is non blocking they can still request a retry in exceptional
circumstances. Specifically this will happen if a session
renegotiation takes place during a \fBBIO_read_ex()\fR operation, one
case where this happens is when step up occurs.
.PP
The SSL flag SSL_AUTO_RETRY can be
set to disable this behaviour. That is when this flag is set
an SSL BIO using a blocking transport will never request a
retry.
.PP
Since unknown \fBBIO_ctrl()\fR operations are sent through filter
BIOs the servers name and port can be set using \fBBIO_set_host()\fR
on the BIO returned by \fBBIO_new_ssl_connect()\fR without having
to locate the connect BIO first.
.PP
Applications do not have to call \fBBIO_do_handshake()\fR but may wish
to do so to separate the handshake process from other I/O
processing.
.PP
\&\fBBIO_set_ssl()\fR, \fBBIO_get_ssl()\fR, \fBBIO_set_ssl_mode()\fR,
\&\fBBIO_set_ssl_renegotiate_bytes()\fR, \fBBIO_set_ssl_renegotiate_timeout()\fR,
\&\fBBIO_get_num_renegotiates()\fR, and \fBBIO_do_handshake()\fR are implemented as macros.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_f_ssl()\fR returns the SSL \fBBIO_METHOD\fR structure.
.PP
\&\fBBIO_set_ssl()\fR, \fBBIO_get_ssl()\fR, \fBBIO_set_ssl_mode()\fR, \fBBIO_set_ssl_renegotiate_bytes()\fR,
\&\fBBIO_set_ssl_renegotiate_timeout()\fR and \fBBIO_get_num_renegotiates()\fR return 1 on
success or a value which is less than or equal to 0 if an error occurred.
.PP
\&\fBBIO_new_ssl()\fR, \fBBIO_new_ssl_connect()\fR and \fBBIO_new_buffer_ssl_connect()\fR return
a valid \fBBIO\fR structure on success or \fBNULL\fR if an error occurred.
.PP
\&\fBBIO_ssl_copy_session_id()\fR returns 1 on success or 0 on error.
.PP
\&\fBBIO_do_handshake()\fR returns 1 if the connection was established successfully.
A zero or negative value is returned if the connection could not be established.
.SH EXAMPLES
.IX Header "EXAMPLES"
This SSL/TLS client example attempts to retrieve a page from an
SSL/TLS web server. The I/O routines are identical to those of the
unencrypted example in \fBBIO_s_connect\fR\|(3).
.PP
.Vb 5
\& BIO *sbio, *out;
\& int len;
\& char tmpbuf[1024];
\& SSL_CTX *ctx;
\& SSL *ssl;
\&
\& /* XXX Seed the PRNG if needed. */
\&
\& ctx = SSL_CTX_new(TLS_client_method());
\&
\& /* XXX Set verify paths and mode here. */
\&
\& sbio = BIO_new_ssl_connect(ctx);
\& BIO_get_ssl(sbio, &ssl);
\& if (ssl == NULL) {
\&     fprintf(stderr, "Can\*(Aqt locate SSL pointer\en");
\&     ERR_print_errors_fp(stderr);
\&     exit(1);
\& }
\&
\& /* XXX We might want to do other things with ssl here */
\&
\& /* An empty host part means the loopback address */
\& BIO_set_conn_hostname(sbio, ":https");
\&
\& out = BIO_new_fp(stdout, BIO_NOCLOSE);
\& if (BIO_do_connect(sbio) <= 0) {
\&     fprintf(stderr, "Error connecting to server\en");
\&     ERR_print_errors_fp(stderr);
\&     exit(1);
\& }
\&
\& /* XXX Could examine ssl here to get connection info */
\&
\& BIO_puts(sbio, "GET / HTTP/1.0\en\en");
\& for (;;) {
\&     len = BIO_read(sbio, tmpbuf, 1024);
\&     if (len <= 0)
\&         break;
\&     BIO_write(out, tmpbuf, len);
\& }
\& BIO_free_all(sbio);
\& BIO_free(out);
.Ve
.PP
Here is a simple server example. It makes use of a buffering
BIO to allow lines to be read from the SSL BIO using BIO_gets.
It creates a pseudo web page containing the actual request from
a client and also echoes the request to standard output.
.PP
.Vb 5
\& BIO *sbio, *bbio, *acpt, *out;
\& int len;
\& char tmpbuf[1024];
\& SSL_CTX *ctx;
\& SSL *ssl;
\&
\& /* XXX Seed the PRNG if needed. */
\&
\& ctx = SSL_CTX_new(TLS_server_method());
\& if (!SSL_CTX_use_certificate_file(ctx, "server.pem", SSL_FILETYPE_PEM)
\&         || !SSL_CTX_use_PrivateKey_file(ctx, "server.pem", SSL_FILETYPE_PEM)
\&         || !SSL_CTX_check_private_key(ctx)) {
\&     fprintf(stderr, "Error setting up SSL_CTX\en");
\&     ERR_print_errors_fp(stderr);
\&     exit(1);
\& }
\&
\& /* XXX Other things like set verify locations, EDH temp callbacks. */
\&
\& /* New SSL BIO setup as server */
\& sbio = BIO_new_ssl(ctx, 0);
\& BIO_get_ssl(sbio, &ssl);
\& if (ssl == NULL) {
\&     fprintf(stderr, "Can\*(Aqt locate SSL pointer\en");
\&     ERR_print_errors_fp(stderr);
\&     exit(1);
\& }
\&
\& bbio = BIO_new(BIO_f_buffer());
\& sbio = BIO_push(bbio, sbio);
\& acpt = BIO_new_accept("4433");
\&
\& /*
\&  * By doing this when a new connection is established
\&  * we automatically have sbio inserted into it. The
\&  * BIO chain is now \*(Aqswallowed\*(Aq by the accept BIO and
\&  * will be freed when the accept BIO is freed.
\&  */
\& BIO_set_accept_bios(acpt, sbio);
\& out = BIO_new_fp(stdout, BIO_NOCLOSE);
\&
\& /* First call to BIO_do_accept() sets up accept BIO */
\& if (BIO_do_accept(acpt) <= 0) {
\&     fprintf(stderr, "Error setting up accept BIO\en");
\&     ERR_print_errors_fp(stderr);
\&     exit(1);
\& }
.Ve
.PP
/* Second call to \fBBIO_do_accept()\fR waits for incoming connection */
 if (BIO_do_accept(acpt) <= 0) {
    fprintf(stderr, "Error accepting connection\en");
    ERR_print_errors_fp(stderr);
    \fBexit\fR\|(1);
 }
.PP
.Vb 3
\& /* We only want one connection so remove and free accept BIO */
\& sbio = BIO_pop(acpt);
\& BIO_free_all(acpt);
\&
\& if (BIO_do_handshake(sbio) <= 0) {
\&     fprintf(stderr, "Error in SSL handshake\en");
\&     ERR_print_errors_fp(stderr);
\&     exit(1);
\& }
\&
\& BIO_puts(sbio, "HTTP/1.0 200 OK\er\enContent\-type: text/plain\er\en\er\en");
\& BIO_puts(sbio, "\er\enConnection Established\er\enRequest headers:\er\en");
\& BIO_puts(sbio, "\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\er\en");
\&
\& for (;;) {
\&     len = BIO_gets(sbio, tmpbuf, 1024);
\&     if (len <= 0)
\&         break;
\&     BIO_write(sbio, tmpbuf, len);
\&     BIO_write(out, tmpbuf, len);
\&     /* Look for blank line signifying end of headers*/
\&     if (tmpbuf[0] == \*(Aq\er\*(Aq || tmpbuf[0] == \*(Aq\en\*(Aq)
\&         break;
\& }
\&
\& BIO_puts(sbio, "\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\-\er\en");
\& BIO_puts(sbio, "\er\en");
\& BIO_flush(sbio);
\& BIO_free_all(sbio);
.Ve
.SH HISTORY
.IX Header "HISTORY"
In OpenSSL before 1.0.0 the \fBBIO_pop()\fR call was handled incorrectly,
the I/O BIO reference count was incorrectly incremented (instead of
decremented) and dissociated with the SSL BIO even if the SSL BIO was not
explicitly being popped (e.g. a pop higher up the chain). Applications which
included workarounds for this bug (e.g. freeing BIOs more than once) should
be modified to handle this fix or they may free up an already freed BIO.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
