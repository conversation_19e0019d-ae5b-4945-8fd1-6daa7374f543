.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_F_MD 3ossl"
.TH BIO_F_MD 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_f_md, BIO_set_md, BIO_get_md, BIO_get_md_ctx \- message digest BIO filter
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 2
\& #include <openssl/bio.h>
\& #include <openssl/evp.h>
\&
\& const BIO_METHOD *BIO_f_md(void);
\& int BIO_set_md(BIO *b, EVP_MD *md);
\& int BIO_get_md(BIO *b, EVP_MD **mdp);
\& int BIO_get_md_ctx(BIO *b, EVP_MD_CTX **mdcp);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_f_md()\fR returns the message digest BIO method. This is a filter
BIO that digests any data passed through it.  It is a BIO wrapper
for the digest routines \fBEVP_DigestInit()\fR, \fBEVP_DigestUpdate()\fR
and \fBEVP_DigestFinal()\fR.
.PP
Any data written or read through a digest BIO using \fBBIO_read_ex()\fR and
\&\fBBIO_write_ex()\fR is digested.
.PP
\&\fBBIO_gets()\fR, if its \fBsize\fR parameter is large enough finishes the
digest calculation and returns the digest value. \fBBIO_puts()\fR is
not supported.
.PP
\&\fBBIO_reset()\fR reinitialises a digest BIO.
.PP
\&\fBBIO_set_md()\fR sets the message digest of BIO \fBb\fR to \fBmd\fR: this
must be called to initialize a digest BIO before any data is
passed through it. It is a \fBBIO_ctrl()\fR macro.
.PP
\&\fBBIO_get_md()\fR places a pointer to the digest BIOs digest method
in \fBmdp\fR.  It is a \fBBIO_ctrl()\fR macro.
.PP
\&\fBBIO_get_md_ctx()\fR returns the digest BIOs context into \fBmdcp\fR.
.SH NOTES
.IX Header "NOTES"
The context returned by \fBBIO_get_md_ctx()\fR can be used in calls
to \fBEVP_DigestFinal()\fR and also the signature routines \fBEVP_SignFinal()\fR
and \fBEVP_VerifyFinal()\fR.
.PP
The context returned by \fBBIO_get_md_ctx()\fR is an internal context
structure. Changes made to this context will affect the digest
BIO itself and the context pointer will become invalid when the digest
BIO is freed.
.PP
After the digest has been retrieved from a digest BIO it must be
reinitialized by calling \fBBIO_reset()\fR, or \fBBIO_set_md()\fR before any more
data is passed through it.
.PP
If an application needs to call \fBBIO_gets()\fR or \fBBIO_puts()\fR through
a chain containing digest BIOs then this can be done by prepending
a buffering BIO.
.PP
Calling \fBBIO_get_md_ctx()\fR will return the context and initialize the BIO
state. This allows applications to initialize the context externally
if the standard calls such as \fBBIO_set_md()\fR are not sufficiently flexible.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_f_md()\fR returns the digest BIO method.
.PP
\&\fBBIO_set_md()\fR, \fBBIO_get_md()\fR and \fBBIO_md_ctx()\fR return 1 for success and
<=0 for failure.
.SH EXAMPLES
.IX Header "EXAMPLES"
The following example creates a BIO chain containing an SHA1 and MD5
digest BIO and passes the string "Hello World" through it. Error
checking has been omitted for clarity.
.PP
.Vb 2
\& BIO *bio, *mdtmp;
\& char message[] = "Hello World";
\&
\& bio = BIO_new(BIO_s_null());
\& mdtmp = BIO_new(BIO_f_md());
\& BIO_set_md(mdtmp, EVP_sha1());
\& /*
\&  * For BIO_push() we want to append the sink BIO and keep a note of
\&  * the start of the chain.
\&  */
\& bio = BIO_push(mdtmp, bio);
\& mdtmp = BIO_new(BIO_f_md());
\& BIO_set_md(mdtmp, EVP_md5());
\& bio = BIO_push(mdtmp, bio);
\& /* Note: mdtmp can now be discarded */
\& BIO_write(bio, message, strlen(message));
.Ve
.PP
The next example digests data by reading through a chain instead:
.PP
.Vb 3
\& BIO *bio, *mdtmp;
\& char buf[1024];
\& int rdlen;
\&
\& bio = BIO_new_file(file, "rb");
\& mdtmp = BIO_new(BIO_f_md());
\& BIO_set_md(mdtmp, EVP_sha1());
\& bio = BIO_push(mdtmp, bio);
\& mdtmp = BIO_new(BIO_f_md());
\& BIO_set_md(mdtmp, EVP_md5());
\& bio = BIO_push(mdtmp, bio);
\& do {
\&     rdlen = BIO_read(bio, buf, sizeof(buf));
\&     /* Might want to do something with the data here */
\& } while (rdlen > 0);
.Ve
.PP
This next example retrieves the message digests from a BIO chain and
outputs them. This could be used with the examples above.
.PP
.Vb 4
\& BIO *mdtmp;
\& unsigned char mdbuf[EVP_MAX_MD_SIZE];
\& int mdlen;
\& int i;
\&
\& mdtmp = bio;   /* Assume bio has previously been set up */
\& do {
\&     EVP_MD *md;
\&
\&     mdtmp = BIO_find_type(mdtmp, BIO_TYPE_MD);
\&     if (!mdtmp)
\&         break;
\&     BIO_get_md(mdtmp, &md);
\&     printf("%s digest", OBJ_nid2sn(EVP_MD_get_type(md)));
\&     mdlen = BIO_gets(mdtmp, mdbuf, EVP_MAX_MD_SIZE);
\&     for (i = 0; i < mdlen; i++) printf(":%02X", mdbuf[i]);
\&     printf("\en");
\&     mdtmp = BIO_next(mdtmp);
\& } while (mdtmp);
\&
\& BIO_free_all(bio);
.Ve
.SH BUGS
.IX Header "BUGS"
The lack of support for \fBBIO_puts()\fR and the non standard behaviour of
\&\fBBIO_gets()\fR could be regarded as anomalous. It could be argued that \fBBIO_gets()\fR
and \fBBIO_puts()\fR should be passed to the next BIO in the chain and digest
the data passed through and that digests should be retrieved using a
separate \fBBIO_ctrl()\fR call.
.SH HISTORY
.IX Header "HISTORY"
Before OpenSSL 1.0.0., the call to \fBBIO_get_md_ctx()\fR would only work if the
BIO was initialized first.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
