.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_KDF 3ossl"
.TH EVP_KDF 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_KDF, EVP_KDF_fetch, EVP_KDF_free, EVP_KDF_up_ref,
EVP_KDF_CTX, EVP_KDF_CTX_new, EVP_KDF_CTX_free, EVP_KDF_CTX_dup,
EVP_KDF_CTX_reset, EVP_KDF_derive,
EVP_KDF_CTX_get_kdf_size,
EVP_KDF_get0_provider, EVP_KDF_CTX_kdf, EVP_KDF_is_a,
EVP_KDF_get0_name, EVP_KDF_names_do_all, EVP_KDF_get0_description,
EVP_KDF_CTX_get_params, EVP_KDF_CTX_set_params, EVP_KDF_do_all_provided,
EVP_KDF_get_params, EVP_KDF_gettable_params,
EVP_KDF_gettable_ctx_params, EVP_KDF_settable_ctx_params,
EVP_KDF_CTX_gettable_params, EVP_KDF_CTX_settable_params \- EVP KDF routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/kdf.h>
\&
\& typedef struct evp_kdf_st EVP_KDF;
\& typedef struct evp_kdf_ctx_st EVP_KDF_CTX;
\&
\& EVP_KDF_CTX *EVP_KDF_CTX_new(const EVP_KDF *kdf);
\& const EVP_KDF *EVP_KDF_CTX_kdf(EVP_KDF_CTX *ctx);
\& void EVP_KDF_CTX_free(EVP_KDF_CTX *ctx);
\& EVP_KDF_CTX *EVP_KDF_CTX_dup(const EVP_KDF_CTX *src);
\& void EVP_KDF_CTX_reset(EVP_KDF_CTX *ctx);
\& size_t EVP_KDF_CTX_get_kdf_size(EVP_KDF_CTX *ctx);
\& int EVP_KDF_derive(EVP_KDF_CTX *ctx, unsigned char *key, size_t keylen,
\&                    const OSSL_PARAM params[]);
\& int EVP_KDF_up_ref(EVP_KDF *kdf);
\& void EVP_KDF_free(EVP_KDF *kdf);
\& EVP_KDF *EVP_KDF_fetch(OSSL_LIB_CTX *libctx, const char *algorithm,
\&                        const char *properties);
\& int EVP_KDF_is_a(const EVP_KDF *kdf, const char *name);
\& const char *EVP_KDF_get0_name(const EVP_KDF *kdf);
\& const char *EVP_KDF_get0_description(const EVP_KDF *kdf);
\& const OSSL_PROVIDER *EVP_KDF_get0_provider(const EVP_KDF *kdf);
\& void EVP_KDF_do_all_provided(OSSL_LIB_CTX *libctx,
\&                              void (*fn)(EVP_KDF *kdf, void *arg),
\&                              void *arg);
\& int EVP_KDF_names_do_all(const EVP_KDF *kdf,
\&                          void (*fn)(const char *name, void *data),
\&                          void *data);
\& int EVP_KDF_get_params(EVP_KDF *kdf, OSSL_PARAM params[]);
\& int EVP_KDF_CTX_get_params(EVP_KDF_CTX *ctx, OSSL_PARAM params[]);
\& int EVP_KDF_CTX_set_params(EVP_KDF_CTX *ctx, const OSSL_PARAM params[]);
\& const OSSL_PARAM *EVP_KDF_gettable_params(const EVP_KDF *kdf);
\& const OSSL_PARAM *EVP_KDF_gettable_ctx_params(const EVP_KDF *kdf);
\& const OSSL_PARAM *EVP_KDF_settable_ctx_params(const EVP_KDF *kdf);
\& const OSSL_PARAM *EVP_KDF_CTX_gettable_params(const EVP_KDF *kdf);
\& const OSSL_PARAM *EVP_KDF_CTX_settable_params(const EVP_KDF *kdf);
\& const OSSL_PROVIDER *EVP_KDF_get0_provider(const EVP_KDF *kdf);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The EVP KDF routines are a high-level interface to Key Derivation Function
algorithms and should be used instead of algorithm-specific functions.
.PP
After creating a \fBEVP_KDF_CTX\fR for the required algorithm using
\&\fBEVP_KDF_CTX_new()\fR, inputs to the algorithm are supplied either by
passing them as part of the \fBEVP_KDF_derive()\fR call or using calls
to \fBEVP_KDF_CTX_set_params()\fR before calling \fBEVP_KDF_derive()\fR to derive
the key.
.SS Types
.IX Subsection "Types"
\&\fBEVP_KDF\fR is a type that holds the implementation of a KDF.
.PP
\&\fBEVP_KDF_CTX\fR is a context type that holds the algorithm inputs.
.SS "Algorithm implementation fetching"
.IX Subsection "Algorithm implementation fetching"
\&\fBEVP_KDF_fetch()\fR fetches an implementation of a KDF \fIalgorithm\fR, given
a library context \fIlibctx\fR and a set of \fIproperties\fR.
See "ALGORITHM FETCHING" in \fBcrypto\fR\|(7) for further information.
.PP
See "Key Derivation Function (KDF)" in \fBOSSL_PROVIDER\-default\fR\|(7) for the lists of
algorithms supported by the default provider.
.PP
The returned value must eventually be freed with
\&\fBEVP_KDF_free\fR\|(3).
.PP
\&\fBEVP_KDF_up_ref()\fR increments the reference count of an already fetched
KDF.
.PP
\&\fBEVP_KDF_free()\fR frees a fetched algorithm.
NULL is a valid parameter, for which this function is a no-op.
.SS "Context manipulation functions"
.IX Subsection "Context manipulation functions"
\&\fBEVP_KDF_CTX_new()\fR creates a new context for the KDF implementation \fIkdf\fR.
.PP
\&\fBEVP_KDF_CTX_free()\fR frees up the context \fIctx\fR.  If \fIctx\fR is NULL, nothing
is done.
.PP
\&\fBEVP_KDF_CTX_kdf()\fR returns the \fBEVP_KDF\fR associated with the context
\&\fIctx\fR.
.SS "Computing functions"
.IX Subsection "Computing functions"
\&\fBEVP_KDF_CTX_reset()\fR resets the context to the default state as if the context
had just been created.
.PP
\&\fBEVP_KDF_derive()\fR processes any parameters in \fIParams\fR and then derives
\&\fIkeylen\fR bytes of key material and places it in the \fIkey\fR buffer.
If the algorithm produces a fixed amount of output then an error will
occur unless the \fIkeylen\fR parameter is equal to that output size,
as returned by \fBEVP_KDF_CTX_get_kdf_size()\fR.
.PP
\&\fBEVP_KDF_get_params()\fR retrieves details about the implementation
\&\fIkdf\fR.
The set of parameters given with \fIparams\fR determine exactly what
parameters should be retrieved.
Note that a parameter that is unknown in the underlying context is
simply ignored.
.PP
\&\fBEVP_KDF_CTX_get_params()\fR retrieves chosen parameters, given the
context \fIctx\fR and its underlying context.
The set of parameters given with \fIparams\fR determine exactly what
parameters should be retrieved.
Note that a parameter that is unknown in the underlying context is
simply ignored.
.PP
\&\fBEVP_KDF_CTX_set_params()\fR passes chosen parameters to the underlying
context, given a context \fIctx\fR.
The set of parameters given with \fIparams\fR determine exactly what
parameters are passed down.
Note that a parameter that is unknown in the underlying context is
simply ignored.
Also, what happens when a needed parameter isn't passed down is
defined by the implementation.
.PP
\&\fBEVP_KDF_gettable_params()\fR returns an \fBOSSL_PARAM\fR\|(3) array that describes
the retrievable and settable parameters.  \fBEVP_KDF_gettable_params()\fR
returns parameters that can be used with \fBEVP_KDF_get_params()\fR.
.PP
\&\fBEVP_KDF_gettable_ctx_params()\fR and \fBEVP_KDF_CTX_gettable_params()\fR
return constant \fBOSSL_PARAM\fR\|(3) arrays that describe the retrievable
parameters that can be used with \fBEVP_KDF_CTX_get_params()\fR.
\&\fBEVP_KDF_gettable_ctx_params()\fR returns the parameters that can be retrieved
from the algorithm, whereas \fBEVP_KDF_CTX_gettable_params()\fR returns
the parameters that can be retrieved in the context's current state.
.PP
\&\fBEVP_KDF_settable_ctx_params()\fR and \fBEVP_KDF_CTX_settable_params()\fR return
constant \fBOSSL_PARAM\fR\|(3) arrays that describe the settable parameters that
can be used with \fBEVP_KDF_CTX_set_params()\fR.  \fBEVP_KDF_settable_ctx_params()\fR
returns the parameters that can be retrieved from the algorithm,
whereas \fBEVP_KDF_CTX_settable_params()\fR returns the parameters that can
be retrieved in the context's current state.
.SS "Information functions"
.IX Subsection "Information functions"
\&\fBEVP_KDF_CTX_get_kdf_size()\fR returns the output size if the algorithm produces a fixed amount
of output and \fBSIZE_MAX\fR otherwise.  If an error occurs then 0 is returned.
For some algorithms an error may result if input parameters necessary to
calculate a fixed output size have not yet been supplied.
.PP
\&\fBEVP_KDF_is_a()\fR returns 1 if \fIkdf\fR is an implementation of an
algorithm that's identifiable with \fIname\fR, otherwise 0.
.PP
\&\fBEVP_KDF_get0_provider()\fR returns the provider that holds the implementation
of the given \fIkdf\fR.
.PP
\&\fBEVP_KDF_do_all_provided()\fR traverses all KDF implemented by all activated
providers in the given library context \fIlibctx\fR, and for each of the
implementations, calls the given function \fIfn\fR with the implementation method
and the given \fIarg\fR as argument.
.PP
\&\fBEVP_KDF_get0_name()\fR return the name of the given KDF.  For fetched KDFs
with multiple names, only one of them is returned; it's
recommended to use \fBEVP_KDF_names_do_all()\fR instead.
.PP
\&\fBEVP_KDF_names_do_all()\fR traverses all names for \fIkdf\fR, and calls
\&\fIfn\fR with each name and \fIdata\fR.
.PP
\&\fBEVP_KDF_get0_description()\fR returns a description of the \fIkdf\fR, meant for
display and human consumption.  The description is at the discretion of
the \fIkdf\fR implementation.
.SH PARAMETERS
.IX Header "PARAMETERS"
The standard parameter names are:
.IP """pass"" (\fBOSSL_KDF_PARAM_PASSWORD\fR) <octet string>" 4
.IX Item """pass"" (OSSL_KDF_PARAM_PASSWORD) <octet string>"
Some KDF implementations require a password.
For those KDF implementations that support it, this parameter sets the password.
.IP """salt"" (\fBOSSL_KDF_PARAM_SALT\fR) <octet string>" 4
.IX Item """salt"" (OSSL_KDF_PARAM_SALT) <octet string>"
Some KDF implementations can take a non-secret unique cryptographic salt.
For those KDF implementations that support it, this parameter sets the salt.
.Sp
The default value, if any, is implementation dependent.
.IP """iter"" (\fBOSSL_KDF_PARAM_ITER\fR) <unsigned integer>" 4
.IX Item """iter"" (OSSL_KDF_PARAM_ITER) <unsigned integer>"
Some KDF implementations require an iteration count.
For those KDF implementations that support it, this parameter sets the
iteration count.
.Sp
The default value, if any, is implementation dependent.
.IP """properties"" (\fBOSSL_KDF_PARAM_PROPERTIES\fR) <UTF8 string>" 4
.IX Item """properties"" (OSSL_KDF_PARAM_PROPERTIES) <UTF8 string>"
.PD 0
.IP """mac"" (\fBOSSL_KDF_PARAM_MAC\fR) <UTF8 string>" 4
.IX Item """mac"" (OSSL_KDF_PARAM_MAC) <UTF8 string>"
.IP """digest"" (\fBOSSL_KDF_PARAM_DIGEST\fR) <UTF8 string>" 4
.IX Item """digest"" (OSSL_KDF_PARAM_DIGEST) <UTF8 string>"
.IP """cipher"" (\fBOSSL_KDF_PARAM_CIPHER\fR) <UTF8 string>" 4
.IX Item """cipher"" (OSSL_KDF_PARAM_CIPHER) <UTF8 string>"
.PD
For KDF implementations that use an underlying computation MAC, digest or
cipher, these parameters set what the algorithm should be.
.Sp
The value is always the name of the intended algorithm,
or the properties.
.Sp
Note that not all algorithms may support all possible underlying
implementations.
.IP """key"" (\fBOSSL_KDF_PARAM_KEY\fR) <octet string>" 4
.IX Item """key"" (OSSL_KDF_PARAM_KEY) <octet string>"
Some KDF implementations require a key.
For those KDF implementations that support it, this octet string parameter
sets the key.
.IP """info"" (\fBOSSL_KDF_PARAM_INFO\fR) <octet string>" 4
.IX Item """info"" (OSSL_KDF_PARAM_INFO) <octet string>"
Some KDF implementations, such as \fBEVP_KDF\-HKDF\fR\|(7), take an 'info' parameter
for binding the derived key material
to application\- and context-specific information.
This parameter sets the info, fixed info, other info or shared info argument.
You can specify this parameter multiple times, and each instance will
be concatenated to form the final value.
.IP """maclen"" (\fBOSSL_KDF_PARAM_MAC_SIZE\fR) <unsigned integer>" 4
.IX Item """maclen"" (OSSL_KDF_PARAM_MAC_SIZE) <unsigned integer>"
Used by implementations that use a MAC with a variable output size (KMAC).
For those KDF implementations that support it, this parameter
sets the MAC output size.
.Sp
The default value, if any, is implementation dependent.
The length must never exceed what can be given with a \fBsize_t\fR.
.IP """maxmem_bytes"" (\fBOSSL_KDF_PARAM_SCRYPT_MAXMEM\fR) <unsigned integer>" 4
.IX Item """maxmem_bytes"" (OSSL_KDF_PARAM_SCRYPT_MAXMEM) <unsigned integer>"
Memory-hard password-based KDF algorithms, such as scrypt, use an amount of
memory that depends on the load factors provided as input.
For those KDF implementations that support it, this \fBuint64_t\fR parameter sets
an upper limit on the amount of memory that may be consumed while performing
a key derivation.
If this memory usage limit is exceeded because the load factors are chosen
too high, the key derivation will fail.
.Sp
The default value is implementation dependent.
The memory size must never exceed what can be given with a \fBsize_t\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_KDF_fetch()\fR returns a pointer to a newly fetched \fBEVP_KDF\fR, or
NULL if allocation failed.
.PP
\&\fBEVP_KDF_get0_provider()\fR returns a pointer to the provider for the KDF, or
NULL on error.
.PP
\&\fBEVP_KDF_up_ref()\fR returns 1 on success, 0 on error.
.PP
\&\fBEVP_KDF_CTX_new()\fR returns either the newly allocated
\&\fBEVP_KDF_CTX\fR structure or NULL if an error occurred.
.PP
\&\fBEVP_KDF_CTX_free()\fR and \fBEVP_KDF_CTX_reset()\fR do not return a value.
.PP
\&\fBEVP_KDF_CTX_get_kdf_size()\fR returns the output size.  \fBSIZE_MAX\fR is returned to indicate
that the algorithm produces a variable amount of output; 0 to indicate failure.
.PP
\&\fBEVP_KDF_get0_name()\fR returns the name of the KDF, or NULL on error.
.PP
\&\fBEVP_KDF_names_do_all()\fR returns 1 if the callback was called for all names. A
return value of 0 means that the callback was not called for any names.
.PP
The remaining functions return 1 for success and 0 or a negative value for
failure.  In particular, a return value of \-2 indicates the operation is not
supported by the KDF algorithm.
.SH NOTES
.IX Header "NOTES"
The KDF life-cycle is described in \fBlife_cycle\-kdf\fR\|(7).  In the future,
the transitions described there will be enforced.  When this is done, it will
not be considered a breaking change to the API.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
"Key Derivation Function (KDF)" in \fBOSSL_PROVIDER\-default\fR\|(7),
\&\fBlife_cycle\-kdf\fR\|(7).
.SH HISTORY
.IX Header "HISTORY"
This functionality was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
