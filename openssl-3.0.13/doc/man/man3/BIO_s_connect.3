.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_S_CONNECT 3ossl"
.TH BIO_S_CONNECT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_s_connect, BIO_new_connect,
BIO_set_conn_hostname, BIO_set_conn_port,
BIO_set_conn_address, BIO_set_conn_ip_family,
BIO_get_conn_hostname, BIO_get_conn_port,
BIO_get_conn_address, BIO_get_conn_ip_family,
BIO_set_nbio, BIO_do_connect \- connect BIO
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& const BIO_METHOD *BIO_s_connect(void);
\&
\& BIO *BIO_new_connect(const char *name);
\&
\& long BIO_set_conn_hostname(BIO *b, char *name);
\& long BIO_set_conn_port(BIO *b, char *port);
\& long BIO_set_conn_address(BIO *b, BIO_ADDR *addr);
\& long BIO_set_conn_ip_family(BIO *b, long family);
\& const char *BIO_get_conn_hostname(BIO *b);
\& const char *BIO_get_conn_port(BIO *b);
\& const BIO_ADDR *BIO_get_conn_address(BIO *b);
\& const long BIO_get_conn_ip_family(BIO *b);
\&
\& long BIO_set_nbio(BIO *b, long n);
\&
\& long BIO_do_connect(BIO *b);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_s_connect()\fR returns the connect BIO method. This is a wrapper
round the platform's TCP/IP socket connection routines.
.PP
Using connect BIOs, TCP/IP connections can be made and data
transferred using only BIO routines. In this way any platform
specific operations are hidden by the BIO abstraction.
.PP
Read and write operations on a connect BIO will perform I/O
on the underlying connection. If no connection is established
and the port and hostname (see below) is set up properly then
a connection is established first.
.PP
Connect BIOs support \fBBIO_puts()\fR but not \fBBIO_gets()\fR.
.PP
If the close flag is set on a connect BIO then any active
connection is shutdown and the socket closed when the BIO
is freed.
.PP
Calling \fBBIO_reset()\fR on a connect BIO will close any active
connection and reset the BIO into a state where it can connect
to the same host again.
.PP
\&\fBBIO_new_connect()\fR combines \fBBIO_new()\fR and \fBBIO_set_conn_hostname()\fR into
a single call: that is it creates a new connect BIO with hostname \fBname\fR.
.PP
\&\fBBIO_set_conn_hostname()\fR uses the string \fBname\fR to set the hostname.
The hostname can be an IP address; if the address is an IPv6 one, it
must be enclosed with brackets \f(CW\*(C`[\*(C'\fR and \f(CW\*(C`]\*(C'\fR.
The hostname can also include the port in the form hostname:port;
see \fBBIO_parse_hostserv\fR\|(3) and \fBBIO_set_conn_port()\fR for details.
.PP
\&\fBBIO_set_conn_port()\fR sets the port to \fBport\fR. \fBport\fR can be the
numerical form or a service string such as "http", which
will be mapped to a port number using the system function \fBgetservbyname()\fR.
.PP
\&\fBBIO_set_conn_address()\fR sets the address and port information using
a \fBBIO_ADDR\fR\|(3ssl).
.PP
\&\fBBIO_set_conn_ip_family()\fR sets the IP family.
.PP
\&\fBBIO_get_conn_hostname()\fR returns the hostname of the connect BIO or
NULL if the BIO is initialized but no hostname is set.
This return value is an internal pointer which should not be modified.
.PP
\&\fBBIO_get_conn_port()\fR returns the port as a string.
This return value is an internal pointer which should not be modified.
.PP
\&\fBBIO_get_conn_address()\fR returns the address information as a BIO_ADDR.
This return value is an internal pointer which should not be modified.
.PP
\&\fBBIO_get_conn_ip_family()\fR returns the IP family of the connect BIO.
.PP
\&\fBBIO_set_nbio()\fR sets the non blocking I/O flag to \fBn\fR. If \fBn\fR is
zero then blocking I/O is set. If \fBn\fR is 1 then non blocking I/O
is set. Blocking I/O is the default. The call to \fBBIO_set_nbio()\fR
should be made before the connection is established because
non blocking I/O is set during the connect process.
.PP
\&\fBBIO_do_connect()\fR attempts to connect the supplied BIO.
This performs an SSL/TLS handshake as far as supported by the BIO.
For non-SSL BIOs the connection is done typically at TCP level.
If domain name resolution yields multiple IP addresses all of them are tried
after \fBconnect()\fR failures.
The function returns 1 if the connection was established successfully.
A zero or negative value is returned if the connection could not be established.
The call \fBBIO_should_retry()\fR should be used for non blocking connect BIOs
to determine if the call should be retried.
If a connection has already been established this call has no effect.
.SH NOTES
.IX Header "NOTES"
If blocking I/O is set then a non positive return value from any
I/O call is caused by an error condition, although a zero return
will normally mean that the connection was closed.
.PP
If the port name is supplied as part of the hostname then this will
override any value set with \fBBIO_set_conn_port()\fR. This may be undesirable
if the application does not wish to allow connection to arbitrary
ports. This can be avoided by checking for the presence of the ':'
character in the passed hostname and either indicating an error or
truncating the string at that point.
.PP
The values returned by \fBBIO_get_conn_hostname()\fR, \fBBIO_get_conn_address()\fR,
and \fBBIO_get_conn_port()\fR are updated when a connection attempt is made.
Before any connection attempt the values returned are those set by the
application itself.
.PP
Applications do not have to call \fBBIO_do_connect()\fR but may wish to do
so to separate the connection process from other I/O processing.
.PP
If non blocking I/O is set then retries will be requested as appropriate.
.PP
It addition to \fBBIO_should_read()\fR and \fBBIO_should_write()\fR it is also
possible for \fBBIO_should_io_special()\fR to be true during the initial
connection process with the reason BIO_RR_CONNECT. If this is returned
then this is an indication that a connection attempt would block,
the application should then take appropriate action to wait until
the underlying socket has connected and retry the call.
.PP
\&\fBBIO_set_conn_hostname()\fR, \fBBIO_set_conn_port()\fR, \fBBIO_get_conn_hostname()\fR,
\&\fBBIO_set_conn_address()\fR, \fBBIO_get_conn_port()\fR, \fBBIO_get_conn_address()\fR,
\&\fBBIO_set_conn_ip_family()\fR, \fBBIO_get_conn_ip_family()\fR,
\&\fBBIO_set_nbio()\fR, and \fBBIO_do_connect()\fR are macros.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_s_connect()\fR returns the connect BIO method.
.PP
\&\fBBIO_set_conn_address()\fR, \fBBIO_set_conn_port()\fR, and \fBBIO_set_conn_ip_family()\fR
return 1 or <=0 if an error occurs.
.PP
\&\fBBIO_set_conn_hostname()\fR returns 1 on success and <=0 on failure.
.PP
\&\fBBIO_get_conn_address()\fR returns the address information or NULL if none
was set.
.PP
\&\fBBIO_get_conn_hostname()\fR returns the connected hostname or NULL if
none was set.
.PP
\&\fBBIO_get_conn_ip_family()\fR returns the address family or \-1 if none was set.
.PP
\&\fBBIO_get_conn_port()\fR returns a string representing the connected
port or NULL if not set.
.PP
\&\fBBIO_set_nbio()\fR returns 1 or <=0 if an error occurs.
.PP
\&\fBBIO_do_connect()\fR returns 1 if the connection was successfully
established and <=0 if the connection failed.
.SH EXAMPLES
.IX Header "EXAMPLES"
This is example connects to a webserver on the local host and attempts
to retrieve a page and copy the result to standard output.
.PP
.Vb 3
\& BIO *cbio, *out;
\& int len;
\& char tmpbuf[1024];
\&
\& cbio = BIO_new_connect("localhost:http");
\& out = BIO_new_fp(stdout, BIO_NOCLOSE);
\& if (BIO_do_connect(cbio) <= 0) {
\&     fprintf(stderr, "Error connecting to server\en");
\&     ERR_print_errors_fp(stderr);
\&     exit(1);
\& }
\& BIO_puts(cbio, "GET / HTTP/1.0\en\en");
\& for (;;) {
\&     len = BIO_read(cbio, tmpbuf, 1024);
\&     if (len <= 0)
\&         break;
\&     BIO_write(out, tmpbuf, len);
\& }
\& BIO_free(cbio);
\& BIO_free(out);
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBBIO_ADDR\fR\|(3), \fBBIO_parse_hostserv\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBBIO_set_conn_int_port()\fR, \fBBIO_get_conn_int_port()\fR, \fBBIO_set_conn_ip()\fR, and \fBBIO_get_conn_ip()\fR
were removed in OpenSSL 1.1.0.
Use \fBBIO_set_conn_address()\fR and \fBBIO_get_conn_address()\fR instead.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
