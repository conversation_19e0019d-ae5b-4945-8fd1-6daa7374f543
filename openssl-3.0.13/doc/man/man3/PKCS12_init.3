.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS12_INIT 3ossl"
.TH PKCS12_INIT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS12_init, PKCS12_init_ex \- Create a new empty PKCS#12 structure
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pkcs12.h>
\&
\& PKCS12 *PKCS12_init(int mode);
\& PKCS12 *PKCS12_init_ex(int mode, OSSL_LIB_CTX *ctx, const char *propq);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPKCS12_init()\fR creates an empty PKCS#12 structure. Any PKCS#7 authSafes added
to this structure are enclosed first within a single PKCS#7 contentInfo
of type \fImode\fR. Currently the only supported type is \fBNID_pkcs7_data\fR.
.PP
\&\fBPKCS12_init_ex()\fR creates an empty PKCS#12 structure and assigns the supplied
\&\fIctx\fR and \fIpropq\fR to be used to select algorithm implementations for
operations performed on the \fBPKCS12\fR object.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPKCS12_init()\fR and \fBPKCS12_init_ex()\fR return a valid \fBPKCS12\fR structure or NULL
if an error occurred.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBd2i_PKCS12\fR\|(3),
\&\fBPKCS12_create\fR\|(3),
\&\fBpassphrase\-encoding\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
\&\fBPKCS12_init_ex()\fR was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
