.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_HTTP_PARSE_URL 3ossl"
.TH OSSL_HTTP_PARSE_URL 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_HTTP_adapt_proxy,
OSSL_parse_url,
OSSL_HTTP_parse_url,
OCSP_parse_url
\&\- http utility functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/http.h>
\&
\& const char *OSSL_HTTP_adapt_proxy(const char *proxy, const char *no_proxy,
\&                                   const char *server, int use_ssl);
\&
\& int OSSL_parse_url(const char *url, char **pscheme, char **puser, char **phost,
\&                    char **pport, int *pport_num,
\&                    char **ppath, char **pquery, char **pfrag);
\& int OSSL_HTTP_parse_url(const char *url,
\&                         int *pssl, char **puser, char **phost,
\&                         char **pport, int *pport_num,
\&                         char **ppath, char **pquery, char **pfrag);
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 2
\& int OCSP_parse_url(const char *url, char **phost, char **pport, char **ppath,
\&                    int *pssl);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOSSL_HTTP_adapt_proxy()\fR takes an optional proxy hostname \fIproxy\fR
and returns it transformed according to the optional \fIno_proxy\fR parameter,
\&\fIserver\fR, \fIuse_ssl\fR, and the applicable environment variable, as follows.
If \fIproxy\fR is NULL, take any default value from the \f(CW\*(C`http_proxy\*(C'\fR
environment variable, or from \f(CW\*(C`https_proxy\*(C'\fR if \fIuse_ssl\fR is nonzero.
If this still does not yield a proxy hostname,
take any further default value from the \f(CW\*(C`HTTP_PROXY\*(C'\fR
environment variable, or from \f(CW\*(C`HTTPS_PROXY\*(C'\fR if \fIuse_ssl\fR is nonzero.
If \fIno_proxy\fR is NULL, take any default exclusion value from the \f(CW\*(C`no_proxy\*(C'\fR
environment variable, or else from \f(CW\*(C`NO_PROXY\*(C'\fR.
Return the determined proxy hostname unless the exclusion contains \fIserver\fR.
Otherwise return NULL.
.PP
\&\fBOSSL_parse_url()\fR parses its input string \fIurl\fR as a URL of the form
\&\f(CW\*(C`[scheme://][userinfo@]host[:port][/path][?query][#fragment]\*(C'\fR and splits it up
into scheme, userinfo, host, port, path, query, and fragment components.
The host (or server) component may be a DNS name or an IP address
where IPv6 addresses should be enclosed in square brackets \f(CW\*(C`[\*(C'\fR and \f(CW\*(C`]\*(C'\fR.
The port component is optional and defaults to \f(CW0\fR.
If given, it must be in decimal form.  If the \fIpport_num\fR argument is not NULL
the integer value of the port number is assigned to \fI*pport_num\fR on success.
The path component is also optional and defaults to \f(CW\*(C`/\*(C'\fR.
Each non-NULL result pointer argument \fIpscheme\fR, \fIpuser\fR, \fIphost\fR, \fIpport\fR,
\&\fIppath\fR, \fIpquery\fR, and \fIpfrag\fR, is assigned the respective url component.
On success, they are guaranteed to contain non-NULL string pointers, else NULL.
It is the responsibility of the caller to free them using \fBOPENSSL_free\fR\|(3).
If \fIpquery\fR is NULL, any given query component is handled as part of the path.
A string returned via \fI*ppath\fR is guaranteed to begin with a \f(CW\*(C`/\*(C'\fR character.
For absent scheme, userinfo, port, query, and fragment components
an empty string is provided.
.PP
\&\fBOSSL_HTTP_parse_url()\fR is a special form of \fBOSSL_parse_url()\fR
where the scheme, if given, must be \f(CW\*(C`http\*(C'\fR or \f(CW\*(C`https\*(C'\fR.
If \fIpssl\fR is not NULL, \fI*pssl\fR is assigned 1 in case parsing was successful
and the scheme is \f(CW\*(C`https\*(C'\fR, else 0.
The port component is optional and defaults to \f(CW443\fR if the scheme is \f(CW\*(C`https\*(C'\fR,
else \f(CW80\fR.
Note that relative paths must be given with a leading \f(CW\*(C`/\*(C'\fR,
otherwise the first path element is interpreted as the hostname.
.PP
Calling the deprecated function OCSP_parse_url(url, host, port, path, ssl)
is equivalent to
OSSL_HTTP_parse_url(url, ssl, NULL, host, port, NULL, path, NULL, NULL).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_HTTP_adapt_proxy()\fR returns NULL if no proxy is to be used,
otherwise a constant proxy hostname string,
which is either the proxy name handed in or an environment variable value.
.PP
\&\fBOSSL_parse_url()\fR, \fBOSSL_HTTP_parse_url()\fR, and \fBOCSP_parse_url()\fR
return 1 on success, 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOSSL_HTTP_transfer\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBOSSL_HTTP_adapt_proxy()\fR,
\&\fBOSSL_parse_url()\fR and \fBOSSL_HTTP_parse_url()\fR were added in OpenSSL 3.0.
\&\fBOCSP_parse_url()\fR was deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
