.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_STORE_INFO 3ossl"
.TH OSSL_STORE_INFO 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_STORE_INFO, OSSL_STORE_INFO_get_type, OSSL_STORE_INFO_get0_NAME,
OSSL_STORE_INFO_get0_NAME_description,
OSSL_STORE_INFO_get0_PARAMS, OSSL_STORE_INFO_get0_PUBKEY,
OSSL_STORE_INFO_get0_PKEY, OSSL_STORE_INFO_get0_CERT, OSSL_STORE_INFO_get0_CRL,
OSSL_STORE_INFO_get1_NAME, OSSL_STORE_INFO_get1_NAME_description,
OSSL_STORE_INFO_get1_PARAMS, OSSL_STORE_INFO_get1_PUBKEY,
OSSL_STORE_INFO_get1_PKEY, OSSL_STORE_INFO_get1_CERT, OSSL_STORE_INFO_get1_CRL,
OSSL_STORE_INFO_type_string, OSSL_STORE_INFO_free,
OSSL_STORE_INFO_new_NAME, OSSL_STORE_INFO_set0_NAME_description,
OSSL_STORE_INFO_new_PARAMS, OSSL_STORE_INFO_new_PUBKEY,
OSSL_STORE_INFO_new_PKEY, OSSL_STORE_INFO_new_CERT, OSSL_STORE_INFO_new_CRL,
OSSL_STORE_INFO_new, OSSL_STORE_INFO_get0_data
\&\- Functions to manipulate OSSL_STORE_INFO objects
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/store.h>
\&
\& typedef struct ossl_store_info_st OSSL_STORE_INFO;
\&
\& int OSSL_STORE_INFO_get_type(const OSSL_STORE_INFO *store_info);
\& const char *OSSL_STORE_INFO_get0_NAME(const OSSL_STORE_INFO *store_info);
\& char *OSSL_STORE_INFO_get1_NAME(const OSSL_STORE_INFO *store_info);
\& const char *OSSL_STORE_INFO_get0_NAME_description(const OSSL_STORE_INFO
\&                                                   *store_info);
\& char *OSSL_STORE_INFO_get1_NAME_description(const OSSL_STORE_INFO *store_info);
\& EVP_PKEY *OSSL_STORE_INFO_get0_PARAMS(const OSSL_STORE_INFO *store_info);
\& EVP_PKEY *OSSL_STORE_INFO_get1_PARAMS(const OSSL_STORE_INFO *store_info);
\& EVP_PKEY *OSSL_STORE_INFO_get0_PUBKEY(const OSSL_STORE_INFO *info);
\& EVP_PKEY *OSSL_STORE_INFO_get1_PUBKEY(const OSSL_STORE_INFO *info);
\& EVP_PKEY *OSSL_STORE_INFO_get0_PKEY(const OSSL_STORE_INFO *store_info);
\& EVP_PKEY *OSSL_STORE_INFO_get1_PKEY(const OSSL_STORE_INFO *store_info);
\& X509 *OSSL_STORE_INFO_get0_CERT(const OSSL_STORE_INFO *store_info);
\& X509 *OSSL_STORE_INFO_get1_CERT(const OSSL_STORE_INFO *store_info);
\& X509_CRL *OSSL_STORE_INFO_get0_CRL(const OSSL_STORE_INFO *store_info);
\& X509_CRL *OSSL_STORE_INFO_get1_CRL(const OSSL_STORE_INFO *store_info);
\&
\& const char *OSSL_STORE_INFO_type_string(int type);
\&
\& void OSSL_STORE_INFO_free(OSSL_STORE_INFO *store_info);
\&
\& OSSL_STORE_INFO *OSSL_STORE_INFO_new_NAME(char *name);
\& int OSSL_STORE_INFO_set0_NAME_description(OSSL_STORE_INFO *info, char *desc);
\& OSSL_STORE_INFO *OSSL_STORE_INFO_new_PARAMS(DSA *dsa_params);
\& OSSL_STORE_INFO *OSSL_STORE_INFO_new_PUBKEY(EVP_PKEY *pubkey);
\& OSSL_STORE_INFO *OSSL_STORE_INFO_new_PKEY(EVP_PKEY *pkey);
\& OSSL_STORE_INFO *OSSL_STORE_INFO_new_CERT(X509 *x509);
\& OSSL_STORE_INFO *OSSL_STORE_INFO_new_CRL(X509_CRL *crl);
\&
\& OSSL_STORE_INFO *OSSL_STORE_INFO_new(int type, void *data);
\& void *OSSL_STORE_INFO_get0_data(int type, const OSSL_STORE_INFO *info);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions are primarily useful for applications to retrieve
supported objects from \fBOSSL_STORE_INFO\fR objects and for scheme specific
loaders to create \fBOSSL_STORE_INFO\fR holders.
.SS Types
.IX Subsection "Types"
\&\fBOSSL_STORE_INFO\fR is an opaque type that's just an intermediary holder for
the objects that have been retrieved by \fBOSSL_STORE_load()\fR and similar functions.
Supported OpenSSL type object can be extracted using one of
STORE_INFO_get0_<TYPE>() where <TYPE> can be NAME, PARAMS, PKEY, CERT, or CRL.
The life time of this extracted object is as long as the life time of
the \fBOSSL_STORE_INFO\fR it was extracted from, so care should be taken not
to free the latter too early.
As an alternative, STORE_INFO_get1_<TYPE>() extracts a duplicate (or the
same object with its reference count increased), which can be used
after the containing \fBOSSL_STORE_INFO\fR has been freed.
The object returned by STORE_INFO_get1_<TYPE>() must be freed separately
by the caller.
See "SUPPORTED OBJECTS" for more information on the types that are supported.
.SS Functions
.IX Subsection "Functions"
\&\fBOSSL_STORE_INFO_get_type()\fR takes a \fBOSSL_STORE_INFO\fR and returns the STORE
type number for the object inside.
.PP
\&\fBSTORE_INFO_get_type_string()\fR takes a STORE type number and returns a
short string describing it.
.PP
\&\fBOSSL_STORE_INFO_get0_NAME()\fR, \fBOSSL_STORE_INFO_get0_NAME_description()\fR,
\&\fBOSSL_STORE_INFO_get0_PARAMS()\fR, \fBOSSL_STORE_INFO_get0_PUBKEY()\fR,
\&\fBOSSL_STORE_INFO_get0_PKEY()\fR, \fBOSSL_STORE_INFO_get0_CERT()\fR,
\&\fBOSSL_STORE_INFO_get0_CRL()\fR
all take a \fBOSSL_STORE_INFO\fR and return the object it holds if the
\&\fBOSSL_STORE_INFO\fR type (as returned by \fBOSSL_STORE_INFO_get_type()\fR)
matches the function, otherwise NULL.
.PP
\&\fBOSSL_STORE_INFO_get1_NAME()\fR, \fBOSSL_STORE_INFO_get1_NAME_description()\fR,
\&\fBOSSL_STORE_INFO_get1_PARAMS()\fR, \fBOSSL_STORE_INFO_get1_PUBKEY()\fR,
\&\fBOSSL_STORE_INFO_get1_PKEY()\fR, \fBOSSL_STORE_INFO_get1_CERT()\fR and
\&\fBOSSL_STORE_INFO_get1_CRL()\fR
all take a \fBOSSL_STORE_INFO\fR and return a duplicate the object it
holds if the \fBOSSL_STORE_INFO\fR type (as returned by
\&\fBOSSL_STORE_INFO_get_type()\fR) matches the function, otherwise NULL.
.PP
\&\fBOSSL_STORE_INFO_free()\fR frees a \fBOSSL_STORE_INFO\fR and its contained type.
.PP
\&\fBOSSL_STORE_INFO_new_NAME()\fR , \fBOSSL_STORE_INFO_new_PARAMS()\fR,
, \fBOSSL_STORE_INFO_new_PUBKEY()\fR, \fBOSSL_STORE_INFO_new_PKEY()\fR,
\&\fBOSSL_STORE_INFO_new_CERT()\fR and \fBOSSL_STORE_INFO_new_CRL()\fR
create a \fBOSSL_STORE_INFO\fR object to hold the given input object.
On success the input object is consumed.
.PP
Additionally, for \fBOSSL_STORE_INFO_NAME\fR objects,
\&\fBOSSL_STORE_INFO_set0_NAME_description()\fR can be used to add an extra
description.
This description is meant to be human readable and should be used for
information printout.
.PP
\&\fBOSSL_STORE_INFO_new()\fR creates a \fBOSSL_STORE_INFO\fR with an arbitrary \fItype\fR
number and \fIdata\fR structure.  It's the responsibility of the caller to
define type numbers other than the ones defined by \fI<openssl/store.h>\fR,
and to handle freeing the associated data structure on their own.
\&\fIUsing type numbers that are defined by <openssl/store.h> may cause
undefined behaviours, including crashes\fR.
.PP
\&\fBOSSL_STORE_INFO_get0_data()\fR returns the data pointer that was passed to
\&\fBOSSL_STORE_INFO_new()\fR if \fItype\fR matches the type number in \fIinfo\fR.
.PP
\&\fBOSSL_STORE_INFO_new()\fR and \fBOSSL_STORE_INFO_get0_data()\fR may be useful for
applications that define their own STORE data, but must be used with care.
.SH "SUPPORTED OBJECTS"
.IX Header "SUPPORTED OBJECTS"
Currently supported object types are:
.IP OSSL_STORE_INFO_NAME 4
.IX Item "OSSL_STORE_INFO_NAME"
A name is exactly that, a name.
It's like a name in a directory, but formatted as a complete URI.
For example, the path in URI \f(CW\*(C`file:/foo/bar/\*(C'\fR could include a file
named \f(CW\*(C`cookie.pem\*(C'\fR, and in that case, the returned \fBOSSL_STORE_INFO_NAME\fR
object would have the URI \f(CW\*(C`file:/foo/bar/cookie.pem\*(C'\fR, which can be
used by the application to get the objects in that file.
This can be applied to all schemes that can somehow support a listing
of object URIs.
.Sp
For \f(CW\*(C`file:\*(C'\fR URIs that are used without the explicit scheme, the
returned name will be the path of each object, so if \f(CW\*(C`/foo/bar\*(C'\fR was
given and that path has the file \f(CW\*(C`cookie.pem\*(C'\fR, the name
\&\f(CW\*(C`/foo/bar/cookie.pem\*(C'\fR will be returned.
.Sp
The returned URI is considered canonical and must be unique and permanent
for the storage where the object (or collection of objects) resides.
Each loader is responsible for ensuring that it only returns canonical
URIs.
However, it's possible that certain schemes allow an object (or collection
thereof) to be reached with alternative URIs; just because one URI is
canonical doesn't mean that other variants can't be used.
.Sp
At the discretion of the loader that was used to get these names, an
extra description may be attached as well.
.IP OSSL_STORE_INFO_PARAMS 4
.IX Item "OSSL_STORE_INFO_PARAMS"
Key parameters.
.IP OSSL_STORE_INFO_PKEY 4
.IX Item "OSSL_STORE_INFO_PKEY"
A keypair or just a private key (possibly with key parameters).
.IP OSSL_STORE_INFO_PUBKEY 4
.IX Item "OSSL_STORE_INFO_PUBKEY"
A public key (possibly with key parameters).
.IP OSSL_STORE_INFO_CERT 4
.IX Item "OSSL_STORE_INFO_CERT"
An X.509 certificate.
.IP OSSL_STORE_INFO_CRL 4
.IX Item "OSSL_STORE_INFO_CRL"
A X.509 certificate revocation list.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_STORE_INFO_get_type()\fR returns the STORE type number of the given
\&\fBOSSL_STORE_INFO\fR.
There is no error value.
.PP
\&\fBOSSL_STORE_INFO_get0_NAME()\fR, \fBOSSL_STORE_INFO_get0_NAME_description()\fR,
\&\fBOSSL_STORE_INFO_get0_PARAMS()\fR, \fBOSSL_STORE_INFO_get0_PKEY()\fR,
\&\fBOSSL_STORE_INFO_get0_CERT()\fR and \fBOSSL_STORE_INFO_get0_CRL()\fR all return
a pointer to the OpenSSL object on success, NULL otherwise.
.PP
\&\fBOSSL_STORE_INFO_get1_NAME()\fR, \fBOSSL_STORE_INFO_get1_NAME_description()\fR,
\&\fBOSSL_STORE_INFO_get1_PARAMS()\fR, \fBOSSL_STORE_INFO_get1_PKEY()\fR,
\&\fBOSSL_STORE_INFO_get1_CERT()\fR and \fBOSSL_STORE_INFO_get1_CRL()\fR all return
a pointer to a duplicate of the OpenSSL object on success, NULL otherwise.
.PP
\&\fBOSSL_STORE_INFO_type_string()\fR returns a string on success, or NULL on
failure.
.PP
\&\fBOSSL_STORE_INFO_new_NAME()\fR, \fBOSSL_STORE_INFO_new_PARAMS()\fR,
\&\fBOSSL_STORE_INFO_new_PKEY()\fR, \fBOSSL_STORE_INFO_new_CERT()\fR and
\&\fBOSSL_STORE_INFO_new_CRL()\fR return a \fBOSSL_STORE_INFO\fR
pointer on success, or NULL on failure.
.PP
\&\fBOSSL_STORE_INFO_set0_NAME_description()\fR returns 1 on success, or 0 on
failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBossl_store\fR\|(7), \fBOSSL_STORE_open\fR\|(3), \fBOSSL_STORE_register_loader\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The OSSL_STORE API was added in OpenSSL 1.1.1.
.PP
The OSSL_STORE_INFO_PUBKEY object type was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
