.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_CMP_ITAV_SET0 3ossl"
.TH OSSL_CMP_ITAV_SET0 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_CMP_ITAV_create,
OSSL_CMP_ITAV_set0,
OSSL_CMP_ITAV_get0_type,
OSSL_CMP_ITAV_get0_value,
OSSL_CMP_ITAV_push0_stack_item
\&\- OSSL_CMP_ITAV utility functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 6
\&  #include <openssl/cmp.h>
\&  OSSL_CMP_ITAV *OSSL_CMP_ITAV_create(ASN1_OBJECT *type, ASN1_TYPE *value);
\&  void OSSL_CMP_ITAV_set0(OSSL_CMP_ITAV *itav, ASN1_OBJECT *type,
\&                          ASN1_TYPE *value);
\&  ASN1_OBJECT *OSSL_CMP_ITAV_get0_type(const OSSL_CMP_ITAV *itav);
\&  ASN1_TYPE *OSSL_CMP_ITAV_get0_value(const OSSL_CMP_ITAV *itav);
\&
\&  int OSSL_CMP_ITAV_push0_stack_item(STACK_OF(OSSL_CMP_ITAV) **itav_sk_p,
\&                                     OSSL_CMP_ITAV *itav);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Certificate Management Protocol (CMP, RFC 4210) extension to OpenSSL
.PP
ITAV is short for InfoTypeAndValue. This type is defined in RFC 4210
section 5.3.19 and Appendix F. It is used at various places in CMP messages,
e.g., in the generalInfo PKIHeader field, to hold a key-value pair.
.PP
\&\fBOSSL_CMP_ITAV_create()\fR creates a new \fBOSSL_CMP_ITAV\fR structure and fills it in.
It combines \fBOSSL_CMP_ITAV_new()\fR and \fBOSSL_CMP_ITAV_set0()\fR.
.PP
\&\fBOSSL_CMP_ITAV_set0()\fR sets the \fIitav\fR with an infoType of \fItype\fR and an
infoValue of \fIvalue\fR. This function uses the pointers \fItype\fR and \fIvalue\fR
internally, so they must \fBnot\fR be freed up after the call.
.PP
\&\fBOSSL_CMP_ITAV_get0_type()\fR returns a direct pointer to the infoType in the
\&\fIitav\fR.
.PP
\&\fBOSSL_CMP_ITAV_get0_value()\fR returns a direct pointer to the infoValue in
the \fIitav\fR as generic \fBASN1_TYPE\fR pointer.
.PP
\&\fBOSSL_CMP_ITAV_push0_stack_item()\fR pushes \fIitav\fR to the stack pointed to
by \fI*itav_sk_p\fR. It creates a new stack if \fI*itav_sk_p\fR points to NULL.
.SH NOTES
.IX Header "NOTES"
CMP is defined in RFC 4210 (and CRMF in RFC 4211).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_CMP_ITAV_create()\fR returns a pointer to the ITAV structure on success,
or NULL on error.
.PP
\&\fBOSSL_CMP_ITAV_set0()\fR does not return a value.
.PP
\&\fBOSSL_CMP_ITAV_get0_type()\fR and \fBOSSL_CMP_ITAV_get0_value()\fR
return the respective pointer or NULL if their input is NULL.
.PP
\&\fBOSSL_CMP_ITAV_push0_stack_item()\fR returns 1 on success, 0 on error.
.SH EXAMPLES
.IX Header "EXAMPLES"
The following code creates and sets a structure representing a generic
InfoTypeAndValue sequence, using an OID created from text as type, and an
integer as value. Afterwards, it is pushed to the \fBOSSL_CMP_CTX\fR to be later
included in the requests' PKIHeader's genInfo field.
.PP
.Vb 2
\&    ASN1_OBJECT *type = OBJ_txt2obj("1.2.3.4.5", 1);
\&    if (type == NULL) ...
\&
\&    ASN1_INTEGER *asn1int = ASN1_INTEGER_new();
\&    if (asn1int == NULL || !ASN1_INTEGER_set(asn1int, 12345)) ...
\&
\&    ASN1_TYPE *val = ASN1_TYPE_new();
\&    if (val == NULL) ...
\&    ASN1_TYPE_set(val, V_ASN1_INTEGER, asn1int);
\&
\&    OSSL_CMP_ITAV *itav = OSSL_CMP_ITAV_create(type, val);
\&    if (itav == NULL) ...
\&
\&    OSSL_CMP_CTX *ctx = OSSL_CMP_CTX_new();
\&    if (ctx == NULL || !OSSL_CMP_CTX_geninfo_push0_ITAV(ctx, itav)) {
\&        OSSL_CMP_ITAV_free(itav); /* also frees type and val */
\&        goto err;
\&    }
\&
\&    ...
\&
\&    OSSL_CMP_CTX_free(ctx); /* also frees itav */
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOSSL_CMP_CTX_new\fR\|(3), \fBOSSL_CMP_CTX_free\fR\|(3), \fBASN1_TYPE_set\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The OpenSSL CMP support was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2007\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
