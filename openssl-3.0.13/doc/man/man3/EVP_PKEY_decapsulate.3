.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_DECAPSULATE 3ossl"
.TH EVP_PKEY_DECAPSULATE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_decapsulate_init, EVP_PKEY_decapsulate
\&\- Key decapsulation using a KEM algorithm with a private key
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_decapsulate_init(EVP_PKEY_CTX *ctx, const OSSL_PARAM params[]);
\& int EVP_PKEY_decapsulate(EVP_PKEY_CTX *ctx,
\&                          unsigned char *unwrapped, size_t *unwrappedlen,
\&                          const unsigned char *wrapped, size_t wrappedlen);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBEVP_PKEY_decapsulate_init()\fR function initializes a private key algorithm
context \fIctx\fR for a decapsulation operation and then sets the \fIparams\fR
on the context in the same way as calling \fBEVP_PKEY_CTX_set_params\fR\|(3).
Note that \fIctx\fR usually is produced using \fBEVP_PKEY_CTX_new_from_pkey\fR\|(3),
specifying the private key to use.
.PP
The \fBEVP_PKEY_decapsulate()\fR function performs a private key decapsulation
operation using \fIctx\fR. The data to be decapsulated is specified using the
\&\fIwrapped\fR and \fIwrappedlen\fR parameters.
If \fIunwrapped\fR is NULL then the maximum size of the output secret buffer
is written to \fI*unwrappedlen\fR. If \fIunwrapped\fR is not NULL and the
call is successful then the decapsulated secret data is written to \fIunwrapped\fR
and the amount of data written to \fI*unwrappedlen\fR.
.SH NOTES
.IX Header "NOTES"
After the call to \fBEVP_PKEY_decapsulate_init()\fR algorithm-specific parameters
for the operation may be set or modified using \fBEVP_PKEY_CTX_set_params\fR\|(3).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_decapsulate_init()\fR and \fBEVP_PKEY_decapsulate()\fR return 1 for
success and 0 or a negative value for failure. In particular a return value of \-2
indicates the operation is not supported by the private key algorithm.
.SH EXAMPLES
.IX Header "EXAMPLES"
Decapsulate data using RSA:
.PP
.Vb 1
\& #include <openssl/evp.h>
\&
\& /*
\&  * NB: assumes rsa_priv_key is an RSA private key,
\&  * and that in, inlen are already set up to contain encapsulated data.
\&  */
\&
\& EVP_PKEY_CTX *ctx = NULL;
\& size_t secretlen = 0;
\& unsigned char *secret = NULL;;
\&
\& ctx = EVP_PKEY_CTX_new_from_pkey(libctx, rsa_priv_key, NULL);
\& if (ctx = NULL)
\&     /* Error */
\& if (EVP_PKEY_decapsulate_init(ctx, NULL) <= 0)
\&     /* Error */
\&
\& /* Set the mode \- only \*(AqRSASVE\*(Aq is currently supported */
\& if (EVP_PKEY_CTX_set_kem_op(ctx, "RSASVE") <= 0)
\&     /* Error */
\&
\& /* Determine buffer length */
\& if (EVP_PKEY_decapsulate(ctx, NULL, &secretlen, in, inlen) <= 0)
\&     /* Error */
\&
\& secret = OPENSSL_malloc(secretlen);
\& if (secret == NULL)
\&     /* malloc failure */
\&
\& /* Decapsulated secret data is secretlen bytes long */
\& if (EVP_PKEY_decapsulaterctx, secret, &secretlen, in, inlen) <= 0)
\&     /* Error */
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new_from_pkey\fR\|(3),
\&\fBEVP_PKEY_encapsulate\fR\|(3),
\&\fBEVP_KEM\-RSA\fR\|(7),
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
