.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS8_PKEY_ADD1_ATTR 3ossl"
.TH PKCS8_PKEY_ADD1_ATTR 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS8_pkey_get0_attrs, PKCS8_pkey_add1_attr, PKCS8_pkey_add1_attr_by_NID, PKCS8_pkey_add1_attr_by_OBJ \- PKCS8 attribute functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& const STACK_OF(X509_ATTRIBUTE) *
\& PKCS8_pkey_get0_attrs(const PKCS8_PRIV_KEY_INFO *p8);
\& int PKCS8_pkey_add1_attr(PKCS8_PRIV_KEY_INFO *p8, X509_ATTRIBUTE *attr);
\& int PKCS8_pkey_add1_attr_by_NID(PKCS8_PRIV_KEY_INFO *p8, int nid, int type,
\&                                 const unsigned char *bytes, int len);
\& int PKCS8_pkey_add1_attr_by_OBJ(PKCS8_PRIV_KEY_INFO *p8, const ASN1_OBJECT *obj,
\&                                int type, const unsigned char *bytes, int len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPKCS8_pkey_get0_attrs()\fR returns a const STACK of X509_ATTRIBUTE present in
the passed const PKCS8_PRIV_KEY_INFO structure \fBp8\fR.
.PP
\&\fBPKCS8_pkey_add1_attr()\fR adds a constructed X509_ATTRIBUTE \fBattr\fR to the
existing PKCS8_PRIV_KEY_INFO structure \fBp8\fR.
.PP
\&\fBPKCS8_pkey_add1_attr_by_NID()\fR and \fBPKCS8_pkey_add1_attr_by_OBJ()\fR construct a new
X509_ATTRIBUTE from the passed arguments and add it to the existing
PKCS8_PRIV_KEY_INFO structure \fBp8\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPKCS8_pkey_add1_attr()\fR, \fBPKCS8_pkey_add1_attr_by_NID()\fR, and
\&\fBPKCS8_pkey_add1_attr_by_OBJ()\fR return 1 for success and 0 for failure.
.SH NOTES
.IX Header "NOTES"
STACK of X509_ATTRIBUTE is present in many X509\-related structures and some of
them have the corresponding set of similar functions.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
