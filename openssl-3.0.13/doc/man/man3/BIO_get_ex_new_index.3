.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_GET_EX_NEW_INDEX 3ossl"
.TH BIO_GET_EX_NEW_INDEX 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_get_ex_new_index, BIO_set_ex_data, BIO_get_ex_data,
BIO_set_app_data, BIO_get_app_data,
DH_get_ex_new_index, DH_set_ex_data, DH_get_ex_data,
DSA_get_ex_new_index, DSA_set_ex_data, DSA_get_ex_data,
EC_KEY_get_ex_new_index, EC_KEY_set_ex_data, EC_KEY_get_ex_data,
ENGINE_get_ex_new_index, ENGINE_set_ex_data, ENGINE_get_ex_data,
EVP_PKEY_get_ex_new_index, EVP_PKEY_set_ex_data, EVP_PKEY_get_ex_data,
RSA_get_ex_new_index, RSA_set_ex_data, RSA_get_ex_data,
RSA_set_app_data, RSA_get_app_data,
SSL_get_ex_new_index, SSL_set_ex_data, SSL_get_ex_data,
SSL_set_app_data, SSL_get_app_data,
SSL_CTX_get_ex_new_index, SSL_CTX_set_ex_data, SSL_CTX_get_ex_data,
SSL_CTX_set_app_data, SSL_CTX_get_app_data,
SSL_SESSION_get_ex_new_index, SSL_SESSION_set_ex_data, SSL_SESSION_get_ex_data,
SSL_SESSION_set_app_data, SSL_SESSION_get_app_data,
UI_get_ex_new_index, UI_set_ex_data, UI_get_ex_data,
UI_set_app_data, UI_get_app_data,
X509_STORE_CTX_get_ex_new_index, X509_STORE_CTX_set_ex_data, X509_STORE_CTX_get_ex_data,
X509_STORE_CTX_set_app_data, X509_STORE_CTX_get_app_data,
X509_STORE_get_ex_new_index, X509_STORE_set_ex_data, X509_STORE_get_ex_data,
X509_get_ex_new_index, X509_set_ex_data, X509_get_ex_data
\&\- application\-specific data
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& int TYPE_get_ex_new_index(long argl, void *argp,
\&                           CRYPTO_EX_new *new_func,
\&                           CRYPTO_EX_dup *dup_func,
\&                           CRYPTO_EX_free *free_func);
\&
\& int TYPE_set_ex_data(TYPE *d, int idx, void *arg);
\&
\& void *TYPE_get_ex_data(const TYPE *d, int idx);
\&
\& #define TYPE_set_app_data(TYPE *d, void *arg)
\& #define TYPE_get_app_data(TYPE *d)
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 10
\& int DH_get_ex_new_index(long argl, void *argp, CRYPTO_EX_new *new_func,
\&                         CRYPTO_EX_dup *dup_func, CRYPTO_EX_free *free_func);
\& int DH_set_ex_data(DH *type, int idx, void *arg);
\& void *DH_get_ex_data(DH *type, int idx);
\& int DSA_get_ex_new_index(long argl, void *argp, CRYPTO_EX_new *new_func,
\&                          CRYPTO_EX_dup *dup_func, CRYPTO_EX_free *free_func);
\& int DSA_set_ex_data(DSA *type, int idx, void *arg);
\& void *DSA_get_ex_data(DSA *type, int idx);
\& int EC_KEY_get_ex_new_index(long argl, void *argp, CRYPTO_EX_new *new_func,
\&                             CRYPTO_EX_dup *dup_func, CRYPTO_EX_free *free_func);
\& int EC_KEY_set_ex_data(EC_KEY *type, int idx, void *arg);
\& void *EC_KEY_get_ex_data(EC_KEY *type, int idx);
\& int RSA_get_ex_new_index(long argl, void *argp, CRYPTO_EX_new *new_func,
\&                          CRYPTO_EX_dup *dup_func, CRYPTO_EX_free *free_func);
\& int RSA_set_ex_data(RSA *type, int idx, void *arg);
\& void *RSA_get_ex_data(RSA *type, int idx);
\& int RSA_set_app_data(RSA *type, void *arg);
\& void *RSA_get_app_data(RSA *type);
\& int ENGINE_get_ex_new_index(long argl, void *argp, CRYPTO_EX_new *new_func,
\&                             CRYPTO_EX_dup *dup_func, CRYPTO_EX_free *free_func);
\& int ENGINE_set_ex_data(ENGINE *type, int idx, void *arg);
\& void *ENGINE_get_ex_data(ENGINE *type, int idx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
In the description here, \fITYPE\fR is used a placeholder
for any of the OpenSSL datatypes listed in \fBCRYPTO_get_ex_new_index\fR\|(3).
.PP
All functions with a \fITYPE\fR of \fBDH\fR, \fBDSA\fR, \fBRSA\fR and \fBEC_KEY\fR are deprecated.
Applications should instead use \fBEVP_PKEY_set_ex_data()\fR,
\&\fBEVP_PKEY_get_ex_data()\fR and \fBEVP_PKEY_get_ex_new_index()\fR.
.PP
All functions with a \fITYPE\fR of \fBENGINE\fR are deprecated.
Applications using engines should be replaced by providers.
.PP
These functions handle application-specific data for OpenSSL data
structures.
.PP
\&\fBTYPE_get_ex_new_index()\fR is a macro that calls \fBCRYPTO_get_ex_new_index()\fR
with the correct \fBindex\fR value.
.PP
\&\fBTYPE_set_ex_data()\fR is a function that calls \fBCRYPTO_set_ex_data()\fR with
an offset into the opaque exdata part of the TYPE object.
.PP
\&\fBTYPE_get_ex_data()\fR is a function that calls \fBCRYPTO_get_ex_data()\fR with
an offset into the opaque exdata part of the TYPE object.
.PP
For compatibility with previous releases, the exdata index of zero is
reserved for "application data." There are two convenience functions for
this.
\&\fBTYPE_set_app_data()\fR is a macro that invokes \fBTYPE_set_ex_data()\fR with
\&\fBidx\fR set to zero.
\&\fBTYPE_get_app_data()\fR is a macro that invokes \fBTYPE_get_ex_data()\fR with
\&\fBidx\fR set to zero.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBTYPE_get_ex_new_index()\fR returns a new index on success or \-1 on error.
.PP
\&\fBTYPE_set_ex_data()\fR returns 1 on success or 0 on error.
.PP
\&\fBTYPE_get_ex_data()\fR returns the application data or NULL if an error occurred.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBCRYPTO_get_ex_new_index\fR\|(3).
.SH HISTORY
.IX Header "HISTORY"
The functions \fBDH_get_ex_new_index()\fR, \fBDH_set_ex_data()\fR, \fBDH_get_ex_data()\fR,
\&\fBDSA_get_ex_new_index()\fR, \fBDSA_set_ex_data()\fR, \fBDSA_get_ex_data()\fR,
\&\fBEC_KEY_get_ex_new_index()\fR, \fBEC_KEY_set_ex_data()\fR, \fBEC_KEY_get_ex_data()\fR,
\&\fBENGINE_get_ex_new_index()\fR, \fBENGINE_set_ex_data()\fR, \fBENGINE_get_ex_data()\fR,
\&\fBRSA_get_ex_new_index()\fR, \fBRSA_set_ex_data()\fR, \fBRSA_get_ex_data()\fR,
\&\fBRSA_set_app_data()\fR and \fBRSA_get_app_data()\fR were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
