.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_GETTABLE_PARAMS 3ossl"
.TH EVP_PKEY_GETTABLE_PARAMS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_gettable_params, EVP_PKEY_get_params,
EVP_PKEY_get_int_param, EVP_PKEY_get_size_t_param,
EVP_PKEY_get_bn_param, EVP_PKEY_get_utf8_string_param,
EVP_PKEY_get_octet_string_param
\&\- retrieve key parameters from a key
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& const OSSL_PARAM *EVP_PKEY_gettable_params(EVP_PKEY *pkey);
\& int EVP_PKEY_get_params(const EVP_PKEY *pkey, OSSL_PARAM params[]);
\& int EVP_PKEY_get_int_param(const EVP_PKEY *pkey, const char *key_name,
\&                            int *out);
\& int EVP_PKEY_get_size_t_param(const EVP_PKEY *pkey, const char *key_name,
\&                               size_t *out);
\& int EVP_PKEY_get_bn_param(const EVP_PKEY *pkey, const char *key_name,
\&                           BIGNUM **bn);
\& int EVP_PKEY_get_utf8_string_param(const EVP_PKEY *pkey, const char *key_name,
\&                                    char *str, size_t max_buf_sz,
\&                                    size_t *out_len);
\& int EVP_PKEY_get_octet_string_param(const EVP_PKEY *pkey, const char *key_name,
\&                                     unsigned char *buf, size_t max_buf_sz,
\&                                     size_t *out_len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
See \fBOSSL_PARAM\fR\|(3) for information about parameters.
.PP
\&\fBEVP_PKEY_get_params()\fR retrieves parameters from the key \fIpkey\fR, according to
the contents of \fIparams\fR.
.PP
\&\fBEVP_PKEY_gettable_params()\fR returns a constant list of \fIparams\fR indicating
the names and types of key parameters that can be retrieved.
.PP
An \fBOSSL_PARAM\fR\|(3) of type \fBOSSL_PARAM_INTEGER\fR or
\&\fBOSSL_PARAM_UNSIGNED_INTEGER\fR is of arbitrary length. Such a parameter can be
obtained using any of the functions \fBEVP_PKEY_get_int_param()\fR,
\&\fBEVP_PKEY_get_size_t_param()\fR or \fBEVP_PKEY_get_bn_param()\fR. Attempting to
obtain an integer value that does not fit into a native C \fBint\fR type will cause
\&\fBEVP_PKEY_get_int_param()\fR to fail. Similarly attempting to obtain an integer
value that is negative or does not fit into a native C \fBsize_t\fR type using
\&\fBEVP_PKEY_get_size_t_param()\fR will also fail.
.PP
\&\fBEVP_PKEY_get_int_param()\fR retrieves a key \fIpkey\fR integer value \fI*out\fR
associated with a name of \fIkey_name\fR if it fits into \f(CW\*(C`int\*(C'\fR type. For
parameters that do not fit into \f(CW\*(C`int\*(C'\fR use \fBEVP_PKEY_get_bn_param()\fR.
.PP
\&\fBEVP_PKEY_get_size_t_param()\fR retrieves a key \fIpkey\fR size_t value \fI*out\fR
associated with a name of \fIkey_name\fR if it fits into \f(CW\*(C`size_t\*(C'\fR type. For
parameters that do not fit into \f(CW\*(C`size_t\*(C'\fR use \fBEVP_PKEY_get_bn_param()\fR.
.PP
\&\fBEVP_PKEY_get_bn_param()\fR retrieves a key \fIpkey\fR BIGNUM value \fI**bn\fR
associated with a name of \fIkey_name\fR. If \fI*bn\fR is NULL then the BIGNUM
is allocated by the method.
.PP
\&\fBEVP_PKEY_get_utf8_string_param()\fR get a key \fIpkey\fR UTF8 string value into a
buffer \fIstr\fR of maximum size \fImax_buf_sz\fR associated with a name of
\&\fIkey_name\fR.  The maximum size must be large enough to accommodate the string
value including a terminating NUL byte, or this function will fail.
If \fIout_len\fR is not NULL, \fI*out_len\fR is set to the length of the string
not including the terminating NUL byte. The required buffer size not including
the terminating NUL byte can be obtained from \fI*out_len\fR by calling the
function with \fIstr\fR set to NULL.
.PP
\&\fBEVP_PKEY_get_octet_string_param()\fR get a key \fIpkey\fR's octet string value into a
buffer \fIbuf\fR of maximum size \fImax_buf_sz\fR associated with a name of \fIkey_name\fR.
If \fIout_len\fR is not NULL, \fI*out_len\fR is set to the length of the contents.
The required buffer size can be obtained from \fI*out_len\fR by calling the
function with \fIbuf\fR set to NULL.
.SH NOTES
.IX Header "NOTES"
These functions only work for \fBEVP_PKEY\fRs that contain a provider side key.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_gettable_params()\fR returns NULL on error or if it is not supported.
.PP
All other methods return 1 if a value associated with the key's \fIkey_name\fR was
successfully returned, or 0 if there was an error.
An error may be returned by methods \fBEVP_PKEY_get_utf8_string_param()\fR and
\&\fBEVP_PKEY_get_octet_string_param()\fR if \fImax_buf_sz\fR is not big enough to hold the
value.  If \fIout_len\fR is not NULL, \fI*out_len\fR will be assigned the required
buffer size to hold the value.
.SH EXAMPLES
.IX Header "EXAMPLES"
.Vb 1
\& #include <openssl/evp.h>
\&
\& char curve_name[64];
\& unsigned char pub[256];
\& BIGNUM *bn_priv = NULL;
\&
\& /*
\&  * NB: assumes \*(Aqkey\*(Aq is set up before the next step. In this example the key
\&  * is an EC key.
\&  */
\&
\& if (!EVP_PKEY_get_utf8_string_param(key, OSSL_PKEY_PARAM_GROUP_NAME,
\&                                     curve_name, sizeof(curve_name), &len)) {
\&   /* Error */
\& }
\& if (!EVP_PKEY_get_octet_string_param(key, OSSL_PKEY_PARAM_PUB_KEY,
\&                                      pub, sizeof(pub), &len)) {
\&     /* Error */
\& }
\& if (!EVP_PKEY_get_bn_param(key, OSSL_PKEY_PARAM_PRIV_KEY, &bn_priv)) {
\&     /* Error */
\& }
\&
\& BN_clear_free(bn_priv);
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new\fR\|(3), \fBprovider\-keymgmt\fR\|(7), \fBOSSL_PARAM\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
