.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CMS_ADD0_CERT 3ossl"
.TH CMS_ADD0_CERT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
CMS_add0_cert, CMS_add1_cert, CMS_get1_certs, CMS_add0_crl, CMS_add1_crl, CMS_get1_crls
\&\- CMS certificate and CRL utility functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/cms.h>
\&
\& int CMS_add0_cert(CMS_ContentInfo *cms, X509 *cert);
\& int CMS_add1_cert(CMS_ContentInfo *cms, X509 *cert);
\& STACK_OF(X509) *CMS_get1_certs(CMS_ContentInfo *cms);
\&
\& int CMS_add0_crl(CMS_ContentInfo *cms, X509_CRL *crl);
\& int CMS_add1_crl(CMS_ContentInfo *cms, X509_CRL *crl);
\& STACK_OF(X509_CRL) *CMS_get1_crls(CMS_ContentInfo *cms);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBCMS_add0_cert()\fR and \fBCMS_add1_cert()\fR add certificate \fIcert\fR to \fIcms\fR.
This is used by \fBCMS_sign_ex\fR\|(3) and \fBCMS_sign\fR\|(3) and may be used before
calling \fBCMS_verify\fR\|(3) to help chain building in certificate validation.
\&\fIcms\fR must be of type signed data or (authenticated) enveloped data.
For signed data, such a certificate can be used when signing or verifying
to fill in the signer certificate or to provide an extra CA certificate
that may be needed for chain building in certificate validation.
.PP
\&\fBCMS_get1_certs()\fR returns all certificates in \fIcms\fR.
.PP
\&\fBCMS_add0_crl()\fR and \fBCMS_add1_crl()\fR add CRL \fIcrl\fR to \fIcms\fR.
\&\fIcms\fR must be of type signed data or (authenticated) enveloped data.
For signed data, such a CRL may be used in certificate validation
with \fBCMS_verify\fR\|(3).
It may be given both for inclusion when signing a CMS message
and when verifying a signed CMS message.
.PP
\&\fBCMS_get1_crls()\fR returns all CRLs in \fIcms\fR.
.SH NOTES
.IX Header "NOTES"
The CMS_ContentInfo structure \fIcms\fR must be of type signed data or enveloped
data or an error will be returned.
.PP
For signed data certificates and CRLs are added to the \fIcertificates\fR and
\&\fIcrls\fR fields of SignedData structure. For enveloped data they are added to
\&\fBOriginatorInfo\fR.
.PP
As the \fI0\fR implies \fBCMS_add0_cert()\fR adds \fIcert\fR internally to \fIcms\fR and it
must not be freed up after the call as opposed to \fBCMS_add1_cert()\fR where \fIcert\fR
must be freed up.
.PP
The same certificate must not be added to the same cms structure more than once.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBCMS_add0_cert()\fR, \fBCMS_add1_cert()\fR and \fBCMS_add0_crl()\fR and \fBCMS_add1_crl()\fR return
1 for success and 0 for failure.
.PP
\&\fBCMS_get1_certs()\fR and \fBCMS_get1_crls()\fR return the STACK of certificates or CRLs
or NULL if there are none or an error occurs. The only error which will occur
in practice is if the \fIcms\fR type is invalid.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3),
\&\fBCMS_sign\fR\|(3), \fBCMS_sign_ex\fR\|(3), \fBCMS_verify\fR\|(3),
\&\fBCMS_encrypt\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2008\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
