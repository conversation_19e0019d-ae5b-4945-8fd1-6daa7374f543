.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_GET_DATA 3ossl"
.TH BIO_GET_DATA 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_set_data, BIO_get_data, BIO_set_init, BIO_get_init, BIO_set_shutdown,
BIO_get_shutdown \- functions for managing BIO state information
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& void BIO_set_data(BIO *a, void *ptr);
\& void *BIO_get_data(BIO *a);
\& void BIO_set_init(BIO *a, int init);
\& int BIO_get_init(BIO *a);
\& void BIO_set_shutdown(BIO *a, int shut);
\& int BIO_get_shutdown(BIO *a);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions are mainly useful when implementing a custom BIO.
.PP
The \fBBIO_set_data()\fR function associates the custom data pointed to by \fBptr\fR with
the BIO. This data can subsequently be retrieved via a call to \fBBIO_get_data()\fR.
This can be used by custom BIOs for storing implementation specific information.
.PP
The \fBBIO_set_init()\fR function sets the value of the BIO's "init" flag to indicate
whether initialisation has been completed for this BIO or not. A nonzero value
indicates that initialisation is complete, whilst zero indicates that it is not.
Often initialisation will complete during initial construction of the BIO. For
some BIOs however, initialisation may not complete until after additional steps
have occurred (for example through calling custom ctrls). The \fBBIO_get_init()\fR
function returns the value of the "init" flag.
.PP
The \fBBIO_set_shutdown()\fR and \fBBIO_get_shutdown()\fR functions set and get the state of
this BIO's shutdown (i.e. BIO_CLOSE) flag. If set then the underlying resource
is also closed when the BIO is freed.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_get_data()\fR returns a pointer to the implementation specific custom data
associated with this BIO, or NULL if none has been set.
.PP
\&\fBBIO_get_init()\fR returns the state of the BIO's init flag.
.PP
\&\fBBIO_get_shutdown()\fR returns the stat of the BIO's shutdown (i.e. BIO_CLOSE) flag.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBbio\fR\|(7), \fBBIO_meth_new\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The functions described here were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
