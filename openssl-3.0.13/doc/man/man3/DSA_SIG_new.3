.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "DSA_SIG_NEW 3ossl"
.TH DSA_SIG_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
DSA_SIG_get0, DSA_SIG_set0,
DSA_SIG_new, DSA_SIG_free \- allocate and free DSA signature objects
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/dsa.h>
\&
\& DSA_SIG *DSA_SIG_new(void);
\& void DSA_SIG_free(DSA_SIG *a);
\& void DSA_SIG_get0(const DSA_SIG *sig, const BIGNUM **pr, const BIGNUM **ps);
\& int DSA_SIG_set0(DSA_SIG *sig, BIGNUM *r, BIGNUM *s);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBDSA_SIG_new()\fR allocates an empty \fBDSA_SIG\fR structure.
.PP
\&\fBDSA_SIG_free()\fR frees the \fBDSA_SIG\fR structure and its components. The
values are erased before the memory is returned to the system.
.PP
\&\fBDSA_SIG_get0()\fR returns internal pointers to the \fBr\fR and \fBs\fR values contained
in \fBsig\fR.
.PP
The \fBr\fR and \fBs\fR values can be set by calling \fBDSA_SIG_set0()\fR and passing the
new values for \fBr\fR and \fBs\fR as parameters to the function. Calling this
function transfers the memory management of the values to the DSA_SIG object,
and therefore the values that have been passed in should not be freed directly
after this function has been called.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
If the allocation fails, \fBDSA_SIG_new()\fR returns \fBNULL\fR and sets an
error code that can be obtained by
\&\fBERR_get_error\fR\|(3). Otherwise it returns a pointer
to the newly allocated structure.
.PP
\&\fBDSA_SIG_free()\fR returns no value.
.PP
\&\fBDSA_SIG_set0()\fR returns 1 on success or 0 on failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_new\fR\|(3), \fBEVP_PKEY_free\fR\|(3), \fBEVP_PKEY_get_bn_param\fR\|(3),
\&\fBERR_get_error\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
