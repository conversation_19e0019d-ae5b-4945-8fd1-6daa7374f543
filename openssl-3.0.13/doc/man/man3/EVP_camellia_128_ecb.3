.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_CAMELLIA_128_ECB 3ossl"
.TH EVP_CAMELLIA_128_ECB 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_camellia_128_cbc,
EVP_camellia_192_cbc,
EVP_camellia_256_cbc,
EVP_camellia_128_cfb,
EVP_camellia_192_cfb,
EVP_camellia_256_cfb,
EVP_camellia_128_cfb1,
EVP_camellia_192_cfb1,
EVP_camellia_256_cfb1,
EVP_camellia_128_cfb8,
EVP_camellia_192_cfb8,
EVP_camellia_256_cfb8,
EVP_camellia_128_cfb128,
EVP_camellia_192_cfb128,
EVP_camellia_256_cfb128,
EVP_camellia_128_ctr,
EVP_camellia_192_ctr,
EVP_camellia_256_ctr,
EVP_camellia_128_ecb,
EVP_camellia_192_ecb,
EVP_camellia_256_ecb,
EVP_camellia_128_ofb,
EVP_camellia_192_ofb,
EVP_camellia_256_ofb
\&\- EVP Camellia cipher
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& const EVP_CIPHER *EVP_ciphername(void)
.Ve
.PP
\&\fIEVP_ciphername\fR is used a placeholder for any of the described cipher
functions, such as \fIEVP_camellia_128_cbc\fR.
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The Camellia encryption algorithm for EVP.
.IP "\fBEVP_camellia_128_cbc()\fR, \fBEVP_camellia_192_cbc()\fR, \fBEVP_camellia_256_cbc()\fR, \fBEVP_camellia_128_cfb()\fR, \fBEVP_camellia_192_cfb()\fR, \fBEVP_camellia_256_cfb()\fR, \fBEVP_camellia_128_cfb1()\fR, \fBEVP_camellia_192_cfb1()\fR, \fBEVP_camellia_256_cfb1()\fR, \fBEVP_camellia_128_cfb8()\fR, \fBEVP_camellia_192_cfb8()\fR, \fBEVP_camellia_256_cfb8()\fR, \fBEVP_camellia_128_cfb128()\fR, \fBEVP_camellia_192_cfb128()\fR, \fBEVP_camellia_256_cfb128()\fR, \fBEVP_camellia_128_ctr()\fR, \fBEVP_camellia_192_ctr()\fR, \fBEVP_camellia_256_ctr()\fR, \fBEVP_camellia_128_ecb()\fR, \fBEVP_camellia_192_ecb()\fR, \fBEVP_camellia_256_ecb()\fR, \fBEVP_camellia_128_ofb()\fR, \fBEVP_camellia_192_ofb()\fR, \fBEVP_camellia_256_ofb()\fR" 4
.IX Item "EVP_camellia_128_cbc(), EVP_camellia_192_cbc(), EVP_camellia_256_cbc(), EVP_camellia_128_cfb(), EVP_camellia_192_cfb(), EVP_camellia_256_cfb(), EVP_camellia_128_cfb1(), EVP_camellia_192_cfb1(), EVP_camellia_256_cfb1(), EVP_camellia_128_cfb8(), EVP_camellia_192_cfb8(), EVP_camellia_256_cfb8(), EVP_camellia_128_cfb128(), EVP_camellia_192_cfb128(), EVP_camellia_256_cfb128(), EVP_camellia_128_ctr(), EVP_camellia_192_ctr(), EVP_camellia_256_ctr(), EVP_camellia_128_ecb(), EVP_camellia_192_ecb(), EVP_camellia_256_ecb(), EVP_camellia_128_ofb(), EVP_camellia_192_ofb(), EVP_camellia_256_ofb()"
Camellia for 128, 192 and 256 bit keys in the following modes: CBC, CFB with
128\-bit shift, CFB with 1\-bit shift, CFB with 8\-bit shift, CTR, ECB and OFB.
.SH NOTES
.IX Header "NOTES"
Developers should be aware of the negative performance implications of
calling these functions multiple times and should consider using
\&\fBEVP_CIPHER_fetch\fR\|(3) with \fBEVP_CIPHER\-CAMELLIA\fR\|(7) instead.
See "Performance" in \fBcrypto\fR\|(7) for further information.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
These functions return an \fBEVP_CIPHER\fR structure that contains the
implementation of the symmetric cipher. See \fBEVP_CIPHER_meth_new\fR\|(3) for
details of the \fBEVP_CIPHER\fR structure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBevp\fR\|(7),
\&\fBEVP_EncryptInit\fR\|(3),
\&\fBEVP_CIPHER_meth_new\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
