.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "MD5 3ossl"
.TH MD5 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
MD2, MD4, MD5, MD2_Init, MD2_Update, MD2_Final, MD4_Init, MD4_Update,
MD4_Final, MD5_Init, MD5_Update, MD5_Final \- MD2, MD4, and MD5 hash functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/md2.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& unsigned char *MD2(const unsigned char *d, unsigned long n, unsigned char *md);
\&
\& int MD2_Init(MD2_CTX *c);
\& int MD2_Update(MD2_CTX *c, const unsigned char *data, unsigned long len);
\& int MD2_Final(unsigned char *md, MD2_CTX *c);
\&
\&
\& #include <openssl/md4.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& unsigned char *MD4(const unsigned char *d, unsigned long n, unsigned char *md);
\&
\& int MD4_Init(MD4_CTX *c);
\& int MD4_Update(MD4_CTX *c, const void *data, unsigned long len);
\& int MD4_Final(unsigned char *md, MD4_CTX *c);
\&
\&
\& #include <openssl/md5.h>
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& unsigned char *MD5(const unsigned char *d, unsigned long n, unsigned char *md);
\&
\& int MD5_Init(MD5_CTX *c);
\& int MD5_Update(MD5_CTX *c, const void *data, unsigned long len);
\& int MD5_Final(unsigned char *md, MD5_CTX *c);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All of the functions described on this page are deprecated.
Applications should instead use \fBEVP_DigestInit_ex\fR\|(3), \fBEVP_DigestUpdate\fR\|(3)
and \fBEVP_DigestFinal_ex\fR\|(3).
.PP
MD2, MD4, and MD5 are cryptographic hash functions with a 128 bit output.
.PP
\&\fBMD2()\fR, \fBMD4()\fR, and \fBMD5()\fR compute the MD2, MD4, and MD5 message digest
of the \fBn\fR bytes at \fBd\fR and place it in \fBmd\fR (which must have space
for MD2_DIGEST_LENGTH == MD4_DIGEST_LENGTH == MD5_DIGEST_LENGTH == 16
bytes of output). If \fBmd\fR is NULL, the digest is placed in a static
array.
.PP
The following functions may be used if the message is not completely
stored in memory:
.PP
\&\fBMD2_Init()\fR initializes a \fBMD2_CTX\fR structure.
.PP
\&\fBMD2_Update()\fR can be called repeatedly with chunks of the message to
be hashed (\fBlen\fR bytes at \fBdata\fR).
.PP
\&\fBMD2_Final()\fR places the message digest in \fBmd\fR, which must have space
for MD2_DIGEST_LENGTH == 16 bytes of output, and erases the \fBMD2_CTX\fR.
.PP
\&\fBMD4_Init()\fR, \fBMD4_Update()\fR, \fBMD4_Final()\fR, \fBMD5_Init()\fR, \fBMD5_Update()\fR, and
\&\fBMD5_Final()\fR are analogous using an \fBMD4_CTX\fR and \fBMD5_CTX\fR structure.
.PP
Applications should use the higher level functions
\&\fBEVP_DigestInit\fR\|(3)
etc. instead of calling the hash functions directly.
.SH NOTE
.IX Header "NOTE"
MD2, MD4, and MD5 are recommended only for compatibility with existing
applications. In new applications, hashes from the SHA\-2 or SHA\-3 family
should be preferred.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBMD2()\fR, \fBMD4()\fR, and \fBMD5()\fR return pointers to the hash value.
.PP
\&\fBMD2_Init()\fR, \fBMD2_Update()\fR, \fBMD2_Final()\fR, \fBMD4_Init()\fR, \fBMD4_Update()\fR,
\&\fBMD4_Final()\fR, \fBMD5_Init()\fR, \fBMD5_Update()\fR, and \fBMD5_Final()\fR return 1 for
success, 0 otherwise.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
RFC 1319, RFC 1320, RFC 1321
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_DigestInit\fR\|(3), \fBEVP_MD\-SHA2\fR\|(7), \fBEVP_MD\-SHA3\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
All of these functions were deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
