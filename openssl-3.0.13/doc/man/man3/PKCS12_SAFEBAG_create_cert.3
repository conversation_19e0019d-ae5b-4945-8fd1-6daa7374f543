.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS12_SAFEBAG_CREATE_CERT 3ossl"
.TH PKCS12_SAFEBAG_CREATE_CERT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS12_SAFEBAG_create_cert, PKCS12_SAFEBAG_create_crl,
PKCS12_SAFEBAG_create_secret, PKCS12_SAFEBAG_create0_p8inf,
PKCS12_SAFEBAG_create0_pkcs8, PKCS12_SAFEBAG_create_pkcs8_encrypt,
PKCS12_SAFEBAG_create_pkcs8_encrypt_ex \- Create PKCS#12 safeBag objects
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pkcs12.h>
\&
\& PKCS12_SAFEBAG *PKCS12_SAFEBAG_create_cert(X509 *x509);
\& PKCS12_SAFEBAG *PKCS12_SAFEBAG_create_crl(X509_CRL *crl);
\& PKCS12_SAFEBAG *PKCS12_SAFEBAG_create_secret(int type, int vtype,
\&                                              const unsigned char* value,
\&                                              int len);
\& PKCS12_SAFEBAG *PKCS12_SAFEBAG_create0_p8inf(PKCS8_PRIV_KEY_INFO *p8);
\& PKCS12_SAFEBAG *PKCS12_SAFEBAG_create0_pkcs8(X509_SIG *p8);
\& PKCS12_SAFEBAG *PKCS12_SAFEBAG_create_pkcs8_encrypt(int pbe_nid,
\&                                                     const char *pass,
\&                                                     int passlen,
\&                                                     unsigned char *salt,
\&                                                     int saltlen, int iter,
\&                                                     PKCS8_PRIV_KEY_INFO *p8inf);
\& PKCS12_SAFEBAG *PKCS12_SAFEBAG_create_pkcs8_encrypt_ex(int pbe_nid,
\&                                                        const char *pass,
\&                                                        int passlen,
\&                                                        unsigned char *salt,
\&                                                        int saltlen, int iter,
\&                                                        PKCS8_PRIV_KEY_INFO *p8inf,
\&                                                        OSSL_LIB_CTX *ctx,
\&                                                        const char *propq);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPKCS12_SAFEBAG_create_cert()\fR creates a new \fBPKCS12_SAFEBAG\fR of type \fBNID_certBag\fR
containing the supplied certificate.
.PP
\&\fBPKCS12_SAFEBAG_create_crl()\fR creates a new \fBPKCS12_SAFEBAG\fR of type \fBNID_crlBag\fR
containing the supplied crl.
.PP
\&\fBPKCS12_SAFEBAG_create_secret()\fR creates a new \fBPKCS12_SAFEBAG\fR of type
corresponding to a PKCS#12 \fBsecretBag\fR. The \fBsecretBag\fR contents are tagged as
\&\fItype\fR with an ASN1 value of type \fIvtype\fR constructed using the bytes in
\&\fIvalue\fR of length \fIlen\fR.
.PP
\&\fBPKCS12_SAFEBAG_create0_p8inf()\fR creates a new \fBPKCS12_SAFEBAG\fR of type \fBNID_keyBag\fR
containing the supplied PKCS8 structure.
.PP
\&\fBPKCS12_SAFEBAG_create0_pkcs8()\fR creates a new \fBPKCS12_SAFEBAG\fR of type
\&\fBNID_pkcs8ShroudedKeyBag\fR containing the supplied PKCS8 structure.
.PP
\&\fBPKCS12_SAFEBAG_create_pkcs8_encrypt()\fR creates a new \fBPKCS12_SAFEBAG\fR of type
\&\fBNID_pkcs8ShroudedKeyBag\fR by encrypting the supplied PKCS8 \fIp8inf\fR.
If \fIpbe_nid\fR is 0, a default encryption algorithm is used. \fIpass\fR is the
passphrase and \fIiter\fR is the iteration count. If \fIiter\fR is zero then a default
value of 2048 is used. If \fIsalt\fR is NULL then a salt is generated randomly.
.PP
\&\fBPKCS12_SAFEBAG_create_pkcs8_encrypt_ex()\fR is identical to \fBPKCS12_SAFEBAG_create_pkcs8_encrypt()\fR
but allows for a library context \fIctx\fR and property query \fIpropq\fR to be used to select
algorithm implementations.
.SH NOTES
.IX Header "NOTES"
\&\fBPKCS12_SAFEBAG_create_pkcs8_encrypt()\fR makes assumptions regarding the encoding of the given pass
phrase.
See \fBpassphrase\-encoding\fR\|(7) for more information.
.PP
\&\fBPKCS12_SAFEBAG_create_secret()\fR was added in OpenSSL 3.0.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
All of these functions return a valid \fBPKCS12_SAFEBAG\fR structure or NULL if an error occurred.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
IETF RFC 7292 (<https://tools.ietf.org/html/rfc7292>)
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBPKCS12_create\fR\|(3),
\&\fBPKCS12_add_safe\fR\|(3),
\&\fBPKCS12_add_safes\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBPKCS12_SAFEBAG_create_pkcs8_encrypt_ex()\fR was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
