.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_RC5_32_12_16_CBC 3ossl"
.TH EVP_RC5_32_12_16_CBC 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_rc5_32_12_16_cbc,
EVP_rc5_32_12_16_cfb,
EVP_rc5_32_12_16_cfb64,
EVP_rc5_32_12_16_ecb,
EVP_rc5_32_12_16_ofb
\&\- EVP RC5 cipher
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& const EVP_CIPHER *EVP_rc5_32_12_16_cbc(void);
\& const EVP_CIPHER *EVP_rc5_32_12_16_cfb(void);
\& const EVP_CIPHER *EVP_rc5_32_12_16_cfb64(void);
\& const EVP_CIPHER *EVP_rc5_32_12_16_ecb(void);
\& const EVP_CIPHER *EVP_rc5_32_12_16_ofb(void);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The RC5 encryption algorithm for EVP.
.IP "\fBEVP_rc5_32_12_16_cbc()\fR, \fBEVP_rc5_32_12_16_cfb()\fR, \fBEVP_rc5_32_12_16_cfb64()\fR, \fBEVP_rc5_32_12_16_ecb()\fR, \fBEVP_rc5_32_12_16_ofb()\fR" 4
.IX Item "EVP_rc5_32_12_16_cbc(), EVP_rc5_32_12_16_cfb(), EVP_rc5_32_12_16_cfb64(), EVP_rc5_32_12_16_ecb(), EVP_rc5_32_12_16_ofb()"
RC5 encryption algorithm in CBC, CFB, ECB and OFB modes respectively. This is a
variable key length cipher with an additional "number of rounds" parameter. By
default the key length is set to 128 bits and 12 rounds. Alternative key lengths
can be set using \fBEVP_CIPHER_CTX_set_key_length\fR\|(3). The maximum key length is
2040 bits.
.Sp
The following rc5 specific \fIctrl\fRs are supported (see
\&\fBEVP_CIPHER_CTX_ctrl\fR\|(3)).
.RS 4
.IP "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_SET_RC5_ROUNDS, rounds, NULL)" 4
.IX Item "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_SET_RC5_ROUNDS, rounds, NULL)"
Sets the number of rounds to \fBrounds\fR. This must be one of RC5_8_ROUNDS,
RC5_12_ROUNDS or RC5_16_ROUNDS.
.IP "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_GET_RC5_ROUNDS, 0, &rounds)" 4
.IX Item "EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_GET_RC5_ROUNDS, 0, &rounds)"
Stores the number of rounds currently configured in \fB*rounds\fR where \fB*rounds\fR
is an int.
.RE
.RS 4
.RE
.SH NOTES
.IX Header "NOTES"
Developers should be aware of the negative performance implications of
calling these functions multiple times and should consider using
\&\fBEVP_CIPHER_fetch\fR\|(3) with \fBEVP_CIPHER\-RC5\fR\|(7) instead.
See "Performance" in \fBcrypto\fR\|(7) for further information.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
These functions return an \fBEVP_CIPHER\fR structure that contains the
implementation of the symmetric cipher. See \fBEVP_CIPHER_meth_new\fR\|(3) for
details of the \fBEVP_CIPHER\fR structure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBevp\fR\|(7),
\&\fBEVP_EncryptInit\fR\|(3),
\&\fBEVP_CIPHER_meth_new\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
