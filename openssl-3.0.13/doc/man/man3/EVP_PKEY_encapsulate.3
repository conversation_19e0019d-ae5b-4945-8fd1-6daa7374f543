.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_ENCAPSULATE 3ossl"
.TH EVP_PKEY_ENCAPSULATE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_encapsulate_init, EVP_PKEY_encapsulate
\&\- Key encapsulation using a KEM algorithm with a public key
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_encapsulate_init(EVP_PKEY_CTX *ctx, const OSSL_PARAM params[]);
\& int EVP_PKEY_encapsulate(EVP_PKEY_CTX *ctx,
\&                          unsigned char *wrappedkey, size_t *wrappedkeylen,
\&                          unsigned char *genkey, size_t *genkeylen);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBEVP_PKEY_encapsulate_init()\fR function initializes a public key algorithm
context \fIctx\fR for an encapsulation operation and then sets the \fIparams\fR
on the context in the same way as calling \fBEVP_PKEY_CTX_set_params\fR\|(3).
Note that \fIctx\fR is usually is produced using \fBEVP_PKEY_CTX_new_from_pkey\fR\|(3),
specifying the public key to use.
.PP
The \fBEVP_PKEY_encapsulate()\fR function performs a public key encapsulation
operation using \fIctx\fR.
The symmetric secret generated in \fIgenkey\fR can be used as key material.
The ciphertext in \fIwrappedkey\fR is its encapsulated form, which can be sent
to another party, who can use \fBEVP_PKEY_decapsulate\fR\|(3) to retrieve it
using their private key.
If \fIwrappedkey\fR is NULL then the maximum size of the output buffer
is written to the \fI*wrappedkeylen\fR parameter unless \fIwrappedkeylen\fR is NULL
and the maximum size of the generated key buffer is written to \fI*genkeylen\fR
unless \fIgenkeylen\fR is NULL.
If \fIwrappedkey\fR is not NULL and the call is successful then the
internally generated key is written to \fIgenkey\fR and its size is written to
\&\fI*genkeylen\fR. The encapsulated version of the generated key is written to
\&\fIwrappedkey\fR and its size is written to \fI*wrappedkeylen\fR.
.SH NOTES
.IX Header "NOTES"
After the call to \fBEVP_PKEY_encapsulate_init()\fR algorithm-specific parameters
for the operation may be set or modified using \fBEVP_PKEY_CTX_set_params\fR\|(3).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_encapsulate_init()\fR and \fBEVP_PKEY_encapsulate()\fR return 1 for
success and 0 or a negative value for failure. In particular a return value of \-2
indicates the operation is not supported by the public key algorithm.
.SH EXAMPLES
.IX Header "EXAMPLES"
Encapsulate an RSASVE key (for RSA keys).
.PP
.Vb 1
\& #include <openssl/evp.h>
\&
\& /*
\&  * NB: assumes rsa_pub_key is an public key of another party.
\&  */
\&
\& EVP_PKEY_CTX *ctx = NULL;
\& size_t secretlen = 0, outlen = 0;
\& unsigned char *out = NULL, *secret = NULL;
\&
\& ctx = EVP_PKEY_CTX_new_from_pkey(libctx, rsa_pub_key, NULL);
\& if (ctx = NULL)
\&     /* Error */
\& if (EVP_PKEY_encapsulate_init(ctx, NULL) <= 0)
\&     /* Error */
\&
\& /* Set the mode \- only \*(AqRSASVE\*(Aq is currently supported */
\&  if (EVP_PKEY_CTX_set_kem_op(ctx, "RSASVE") <= 0)
\&     /* Error */
\& /* Determine buffer length */
\& if (EVP_PKEY_encapsulate(ctx, NULL, &outlen, NULL, &secretlen) <= 0)
\&     /* Error */
\&
\& out = OPENSSL_malloc(outlen);
\& secret = OPENSSL_malloc(secretlen);
\& if (out == NULL || secret == NULL)
\&     /* malloc failure */
\&
\& /*
\&  * The generated \*(Aqsecret\*(Aq can be used as key material.
\&  * The encapsulated \*(Aqout\*(Aq can be sent to another party who can
\&  * decapsulate it using their private key to retrieve the \*(Aqsecret\*(Aq.
\&  */
\& if (EVP_PKEY_encapsulate(ctx, out, &outlen, secret, &secretlen) <= 0)
\&     /* Error */
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_new_from_pkey\fR\|(3),
\&\fBEVP_PKEY_decapsulate\fR\|(3),
\&\fBEVP_KEM\-RSA\fR\|(7),
.SH HISTORY
.IX Header "HISTORY"
These functions were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
