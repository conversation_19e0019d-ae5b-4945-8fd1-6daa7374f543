.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BN_SET_BIT 3ossl"
.TH BN_SET_BIT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BN_set_bit, BN_clear_bit, BN_is_bit_set, BN_mask_bits, BN_lshift,
BN_lshift1, BN_rshift, BN_rshift1 \- bit operations on BIGNUMs
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bn.h>
\&
\& int BN_set_bit(BIGNUM *a, int n);
\& int BN_clear_bit(BIGNUM *a, int n);
\&
\& int BN_is_bit_set(const BIGNUM *a, int n);
\&
\& int BN_mask_bits(BIGNUM *a, int n);
\&
\& int BN_lshift(BIGNUM *r, const BIGNUM *a, int n);
\& int BN_lshift1(BIGNUM *r, BIGNUM *a);
\&
\& int BN_rshift(BIGNUM *r, BIGNUM *a, int n);
\& int BN_rshift1(BIGNUM *r, BIGNUM *a);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBN_set_bit()\fR sets bit \fBn\fR in \fBa\fR to 1 (\f(CW\*(C`a|=(1<<n)\*(C'\fR). The
number is expanded if necessary.
.PP
\&\fBBN_clear_bit()\fR sets bit \fBn\fR in \fBa\fR to 0 (\f(CW\*(C`a&=~(1<<n)\*(C'\fR). An
error occurs if \fBa\fR is shorter than \fBn\fR bits.
.PP
\&\fBBN_is_bit_set()\fR tests if bit \fBn\fR in \fBa\fR is set.
.PP
\&\fBBN_mask_bits()\fR truncates \fBa\fR to an \fBn\fR bit number
(\f(CW\*(C`a&=~((~0)<<n)\*(C'\fR).  An error occurs if \fBa\fR already is
shorter than \fBn\fR bits.
.PP
\&\fBBN_lshift()\fR shifts \fBa\fR left by \fBn\fR bits and places the result in
\&\fBr\fR (\f(CW\*(C`r=a*2^n\*(C'\fR). Note that \fBn\fR must be nonnegative. \fBBN_lshift1()\fR shifts
\&\fBa\fR left by one and places the result in \fBr\fR (\f(CW\*(C`r=2*a\*(C'\fR).
.PP
\&\fBBN_rshift()\fR shifts \fBa\fR right by \fBn\fR bits and places the result in
\&\fBr\fR (\f(CW\*(C`r=a/2^n\*(C'\fR). Note that \fBn\fR must be nonnegative. \fBBN_rshift1()\fR shifts
\&\fBa\fR right by one and places the result in \fBr\fR (\f(CW\*(C`r=a/2\*(C'\fR).
.PP
For the shift functions, \fBr\fR and \fBa\fR may be the same variable.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBN_is_bit_set()\fR returns 1 if the bit is set, 0 otherwise.
.PP
All other functions return 1 for success, 0 on error. The error codes
can be obtained by \fBERR_get_error\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBBN_num_bytes\fR\|(3), \fBBN_add\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
