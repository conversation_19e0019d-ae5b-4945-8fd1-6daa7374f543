.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ADMISSIONS 3ossl"
.TH ADMISSIONS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ADMISSIONS,
ADMISSIONS_get0_admissionAuthority,
ADMISSIONS_get0_namingAuthority,
ADMISSIONS_get0_professionInfos,
ADMISSIONS_set0_admissionAuthority,
ADMISSIONS_set0_namingAuthority,
ADMISSIONS_set0_professionInfos,
ADMISSION_SYNTAX,
ADMISSION_SYNTAX_get0_admissionAuthority,
ADMISSION_SYNTAX_get0_contentsOfAdmissions,
ADMISSION_SYNTAX_set0_admissionAuthority,
ADMISSION_SYNTAX_set0_contentsOfAdmissions,
NAMING_AUTHORITY,
NAMING_AUTHORITY_get0_authorityId,
NAMING_AUTHORITY_get0_authorityURL,
NAMING_AUTHORITY_get0_authorityText,
NAMING_AUTHORITY_set0_authorityId,
NAMING_AUTHORITY_set0_authorityURL,
NAMING_AUTHORITY_set0_authorityText,
PROFESSION_INFO,
PROFESSION_INFOS,
PROFESSION_INFO_get0_addProfessionInfo,
PROFESSION_INFO_get0_namingAuthority,
PROFESSION_INFO_get0_professionItems,
PROFESSION_INFO_get0_professionOIDs,
PROFESSION_INFO_get0_registrationNumber,
PROFESSION_INFO_set0_addProfessionInfo,
PROFESSION_INFO_set0_namingAuthority,
PROFESSION_INFO_set0_professionItems,
PROFESSION_INFO_set0_professionOIDs,
PROFESSION_INFO_set0_registrationNumber
\&\- Accessors and settors for ADMISSION_SYNTAX
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 5
\& typedef struct NamingAuthority_st NAMING_AUTHORITY;
\& typedef struct ProfessionInfo_st PROFESSION_INFO;
\& typedef STACK_OF(PROFESSION_INFO) PROFESSION_INFOS;
\& typedef struct Admissions_st ADMISSIONS;
\& typedef struct AdmissionSyntax_st ADMISSION_SYNTAX;
\&
\& const ASN1_OBJECT *NAMING_AUTHORITY_get0_authorityId(
\&     const NAMING_AUTHORITY *n);
\& void NAMING_AUTHORITY_set0_authorityId(NAMING_AUTHORITY *n,
\&     ASN1_OBJECT* namingAuthorityId);
\& const ASN1_IA5STRING *NAMING_AUTHORITY_get0_authorityURL(
\&     const NAMING_AUTHORITY *n);
\& void NAMING_AUTHORITY_set0_authorityURL(NAMING_AUTHORITY *n,
\&     ASN1_IA5STRING* namingAuthorityUrl);
\& const ASN1_STRING *NAMING_AUTHORITY_get0_authorityText(
\&     const NAMING_AUTHORITY *n);
\& void NAMING_AUTHORITY_set0_authorityText(NAMING_AUTHORITY *n,
\&     ASN1_STRING* namingAuthorityText);
\&
\& const GENERAL_NAME *ADMISSION_SYNTAX_get0_admissionAuthority(
\&     const ADMISSION_SYNTAX *as);
\& void ADMISSION_SYNTAX_set0_admissionAuthority(
\&     ADMISSION_SYNTAX *as, GENERAL_NAME *aa);
\& const STACK_OF(ADMISSIONS) *ADMISSION_SYNTAX_get0_contentsOfAdmissions(
\&     const ADMISSION_SYNTAX *as);
\& void ADMISSION_SYNTAX_set0_contentsOfAdmissions(
\&     ADMISSION_SYNTAX *as, STACK_OF(ADMISSIONS) *a);
\&
\& const GENERAL_NAME *ADMISSIONS_get0_admissionAuthority(const ADMISSIONS *a);
\& void ADMISSIONS_set0_admissionAuthority(ADMISSIONS *a, GENERAL_NAME *aa);
\& const NAMING_AUTHORITY *ADMISSIONS_get0_namingAuthority(const ADMISSIONS *a);
\& void ADMISSIONS_set0_namingAuthority(ADMISSIONS *a, NAMING_AUTHORITY *na);
\& const PROFESSION_INFOS *ADMISSIONS_get0_professionInfos(const ADMISSIONS *a);
\& void ADMISSIONS_set0_professionInfos(ADMISSIONS *a, PROFESSION_INFOS *pi);
\&
\& const ASN1_OCTET_STRING *PROFESSION_INFO_get0_addProfessionInfo(
\&     const PROFESSION_INFO *pi);
\& void PROFESSION_INFO_set0_addProfessionInfo(
\&     PROFESSION_INFO *pi, ASN1_OCTET_STRING *aos);
\& const NAMING_AUTHORITY *PROFESSION_INFO_get0_namingAuthority(
\&     const PROFESSION_INFO *pi);
\& void PROFESSION_INFO_set0_namingAuthority(
\&     PROFESSION_INFO *pi, NAMING_AUTHORITY *na);
\& const STACK_OF(ASN1_STRING) *PROFESSION_INFO_get0_professionItems(
\&     const PROFESSION_INFO *pi);
\& void PROFESSION_INFO_set0_professionItems(
\&     PROFESSION_INFO *pi, STACK_OF(ASN1_STRING) *as);
\& const STACK_OF(ASN1_OBJECT) *PROFESSION_INFO_get0_professionOIDs(
\&     const PROFESSION_INFO *pi);
\& void PROFESSION_INFO_set0_professionOIDs(
\&     PROFESSION_INFO *pi, STACK_OF(ASN1_OBJECT) *po);
\& const ASN1_PRINTABLESTRING *PROFESSION_INFO_get0_registrationNumber(
\&     const PROFESSION_INFO *pi);
\& void PROFESSION_INFO_set0_registrationNumber(
\&     PROFESSION_INFO *pi, ASN1_PRINTABLESTRING *rn);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBPROFESSION_INFOS\fR, \fBADMISSION_SYNTAX\fR, \fBADMISSIONS\fR, and
\&\fBPROFESSION_INFO\fR types are opaque structures representing the
analogous types defined in the Common PKI Specification published
by <https://www.t7ev.org>.
Knowledge of those structures and their semantics is assumed.
.PP
The conventional routines to convert between DER and the local format
are described in \fBd2i_X509\fR\|(3).
The conventional routines to allocate and free the types are defined
in \fBX509_dup\fR\|(3).
.PP
The \fBPROFESSION_INFOS\fR type is a stack of \fBPROFESSION_INFO\fR; see
\&\fBDEFINE_STACK_OF\fR\|(3) for details.
.PP
The \fBNAMING_AUTHORITY\fR type has an authority ID and URL, and text fields.
The \fBNAMING_AUTHORITY_get0_authorityId()\fR,
\&\fBNAMING_AUTHORITY_get0_get0_authorityURL()\fR, and
\&\fBNAMING_AUTHORITY_get0_get0_authorityText()\fR, functions return pointers
to those values within the object.
The \fBNAMING_AUTHORITY_set0_authorityId()\fR,
\&\fBNAMING_AUTHORITY_set0_get0_authorityURL()\fR, and
\&\fBNAMING_AUTHORITY_set0_get0_authorityText()\fR,
functions free any existing value and set the pointer to the specified value.
.PP
The \fBADMISSION_SYNTAX\fR type has an authority name and a stack of
\&\fBADMISSION\fR objects.
The \fBADMISSION_SYNTAX_get0_admissionAuthority()\fR
and \fBADMISSION_SYNTAX_get0_contentsOfAdmissions()\fR functions return pointers
to those values within the object.
The
\&\fBADMISSION_SYNTAX_set0_admissionAuthority()\fR and
\&\fBADMISSION_SYNTAX_set0_contentsOfAdmissions()\fR
functions free any existing value and set the pointer to the specified value.
.PP
The \fBADMISSION\fR type has an authority name, authority object, and a
stack of \fBPROFESSION_INFO\fR items.
The \fBADMISSIONS_get0_admissionAuthority()\fR, \fBADMISSIONS_get0_namingAuthority()\fR,
and \fBADMISSIONS_get0_professionInfos()\fR
functions return pointers to those values within the object.
The
\&\fBADMISSIONS_set0_admissionAuthority()\fR,
\&\fBADMISSIONS_set0_namingAuthority()\fR, and
\&\fBADMISSIONS_set0_professionInfos()\fR
functions free any existing value and set the pointer to the specified value.
.PP
The \fBPROFESSION_INFO\fR type has a name authority, stacks of
profession Items and OIDs, a registration number, and additional
profession info.
The functions \fBPROFESSION_INFO_get0_addProfessionInfo()\fR,
\&\fBPROFESSION_INFO_get0_namingAuthority()\fR, \fBPROFESSION_INFO_get0_professionItems()\fR,
\&\fBPROFESSION_INFO_get0_professionOIDs()\fR, and
\&\fBPROFESSION_INFO_get0_registrationNumber()\fR
functions return pointers to those values within the object.
The
\&\fBPROFESSION_INFO_set0_addProfessionInfo()\fR,
\&\fBPROFESSION_INFO_set0_namingAuthority()\fR,
\&\fBPROFESSION_INFO_set0_professionItems()\fR,
\&\fBPROFESSION_INFO_set0_professionOIDs()\fR, and
\&\fBPROFESSION_INFO_set0_registrationNumber()\fR
functions free any existing value and set the pointer to the specified value.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
Described above.
Note that all of the \fIget0\fR functions return a pointer to the internal data
structure and must not be freed.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_dup\fR\|(3),
\&\fBd2i_X509\fR\|(3),
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
