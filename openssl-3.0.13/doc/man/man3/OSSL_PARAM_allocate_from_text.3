.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_PARAM_ALLOCATE_FROM_TEXT 3ossl"
.TH OSSL_PARAM_ALLOCATE_FROM_TEXT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_PARAM_allocate_from_text
\&\- OSSL_PARAM construction utilities
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/params.h>
\&
\& int OSSL_PARAM_allocate_from_text(OSSL_PARAM *to,
\&                                   const OSSL_PARAM *paramdefs,
\&                                   const char *key, const char *value,
\&                                   size_t value_n,
\&                                   int *found);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
With OpenSSL before version 3.0, parameters were passed down to or
retrieved from algorithm implementations via control functions.
Some of these control functions existed in variants that took string
parameters, for example \fBEVP_PKEY_CTX_ctrl_str\fR\|(3).
.PP
OpenSSL 3.0 introduces a new mechanism to do the same thing with an
array of parameters that contain name, value, value type and value
size (see \fBOSSL_PARAM\fR\|(3) for more information).
.PP
\&\fBOSSL_PARAM_allocate_from_text()\fR uses \fIkey\fR to look up an item in
\&\fIparamdefs\fR.  If an item was found, it converts \fIvalue\fR to something
suitable for that item's \fIdata_type\fR, and stores the result in
\&\fIto\->data\fR as well as its size in \fIto\->data_size\fR.
\&\fIto\->key\fR and \fIto\->data_type\fR are assigned the corresponding
values from the item that was found, and \fIto\->return_size\fR is set
to zero.
.PP
\&\fIto\->data\fR is always allocated using \fBOPENSSL_zalloc\fR\|(3) and
needs to be freed by the caller when it's not useful any more, using
\&\fBOPENSSL_free\fR\|(3).
.PP
If \fIfound\fR is not NULL, \fI*found\fR is set to 1 if \fIkey\fR could be
located in \fIparamdefs\fR, and to 0 otherwise.
.SS "The use of \fIkey\fP and \fIvalue\fP in detail"
.IX Subsection "The use of key and value in detail"
\&\fBOSSL_PARAM_allocate_from_text()\fR takes note if \fIkey\fR starts with
"hex", and will only use the rest of \fIkey\fR to look up an item in
\&\fIparamdefs\fR in that case.  As an example, if \fIkey\fR is "hexid", "id"
will be looked up in \fIparamdefs\fR.
.PP
When an item in \fIparamdefs\fR has been found, \fIvalue\fR is converted
depending on that item's \fIdata_type\fR, as follows:
.IP "\fBOSSL_PARAM_INTEGER\fR and \fBOSSL_PARAM_UNSIGNED_INTEGER\fR" 4
.IX Item "OSSL_PARAM_INTEGER and OSSL_PARAM_UNSIGNED_INTEGER"
If \fIkey\fR didn't start with "hex", \fIvalue\fR is assumed to contain
\&\fIvalue_n\fR decimal characters, which are decoded, and the resulting
bytes become the number stored in the \fIto\->data\fR storage.
.Sp
If \fIvalue\fR starts with "0x", it is assumed to contain \fIvalue_n\fR
hexadecimal characters.
.Sp
If \fIkey\fR started with "hex", \fIvalue\fR is assumed to contain
\&\fIvalue_n\fR hexadecimal characters without the "0x" prefix.
.Sp
If \fIvalue\fR contains characters that couldn't be decoded as
hexadecimal or decimal characters, \fBOSSL_PARAM_allocate_from_text()\fR
considers that an error.
.IP \fBOSSL_PARAM_UTF8_STRING\fR 4
.IX Item "OSSL_PARAM_UTF8_STRING"
If \fIkey\fR started with "hex", \fBOSSL_PARAM_allocate_from_text()\fR
considers that an error.
.Sp
Otherwise, \fIvalue\fR is considered a C string and is copied to the
\&\fIto\->data\fR storage.
On systems where the native character encoding is EBCDIC, the bytes in
\&\fIto\->data\fR are converted to ASCII.
.IP \fBOSSL_PARAM_OCTET_STRING\fR 4
.IX Item "OSSL_PARAM_OCTET_STRING"
If \fIkey\fR started with "hex", \fIvalue\fR is assumed to contain
\&\fIvalue_n\fR hexadecimal characters, which are decoded, and the
resulting bytes are stored in the \fIto\->data\fR storage.
If \fIvalue\fR contains characters that couldn't be decoded as
hexadecimal or decimal characters, \fBOSSL_PARAM_allocate_from_text()\fR
considers that an error.
.Sp
If \fIkey\fR didn't start with "hex", \fIvalue_n\fR bytes from \fIvalue\fR are
copied to the \fIto\->data\fR storage.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_PARAM_allocate_from_text()\fR returns 1 if \fIkey\fR was found in
\&\fIparamdefs\fR and there was no other failure, otherwise 0.
.SH NOTES
.IX Header "NOTES"
The parameter descriptor array comes from functions dedicated to
return them.
The following \fBOSSL_PARAM\fR\|(3) attributes are used:
.IP \fIkey\fR 4
.IX Item "key"
.PD 0
.IP \fIdata_type\fR 4
.IX Item "data_type"
.IP \fIdata_size\fR 4
.IX Item "data_size"
.PD
.PP
All other attributes are ignored.
.PP
The \fIdata_size\fR attribute can be zero, meaning that the parameter it
describes expects arbitrary length data.
.SH EXAMPLES
.IX Header "EXAMPLES"
Code that looked like this:
.PP
.Vb 4
\&  int mac_ctrl_string(EVP_PKEY_CTX *ctx, const char *value)
\&  {
\&      int rv;
\&      char *stmp, *vtmp = NULL;
\&
\&      stmp = OPENSSL_strdup(value);
\&      if (stmp == NULL)
\&          return \-1;
\&      vtmp = strchr(stmp, \*(Aq:\*(Aq);
\&      if (vtmp != NULL)
\&          *vtmp++ = \*(Aq\e0\*(Aq;
\&      rv = EVP_MAC_ctrl_str(ctx, stmp, vtmp);
\&      OPENSSL_free(stmp);
\&      return rv;
\&  }
\&
\&  ...
\&
\&
\&  for (i = 0; i < sk_OPENSSL_STRING_num(macopts); i++) {
\&      char *macopt = sk_OPENSSL_STRING_value(macopts, i);
\&
\&      if (pkey_ctrl_string(mac_ctx, macopt) <= 0) {
\&          BIO_printf(bio_err,
\&                     "MAC parameter error \e"%s\e"\en", macopt);
\&          ERR_print_errors(bio_err);
\&          goto mac_end;
\&      }
\&  }
.Ve
.PP
Can be written like this instead:
.PP
.Vb 6
\&  OSSL_PARAM *params =
\&      OPENSSL_zalloc(sizeof(*params)
\&                     * (sk_OPENSSL_STRING_num(opts) + 1));
\&  const OSSL_PARAM *paramdefs = EVP_MAC_settable_ctx_params(mac);
\&  size_t params_n;
\&  char *opt = "<unknown>";
\&
\&  for (params_n = 0; params_n < (size_t)sk_OPENSSL_STRING_num(opts);
\&       params_n++) {
\&      char *stmp, *vtmp = NULL;
\&
\&      opt = sk_OPENSSL_STRING_value(opts, (int)params_n);
\&      if ((stmp = OPENSSL_strdup(opt)) == NULL
\&              || (vtmp = strchr(stmp, \*(Aq:\*(Aq)) == NULL)
\&          goto err;
\&
\&      *vtmp++ = \*(Aq\e0\*(Aq;
\&      if (!OSSL_PARAM_allocate_from_text(&params[params_n],
\&                                         paramdefs, stmp,
\&                                         vtmp, strlen(vtmp), NULL))
\&          goto err;
\&  }
\&  params[params_n] = OSSL_PARAM_construct_end();
\&  if (!EVP_MAC_CTX_set_params(ctx, params))
\&      goto err;
\&  while (params_n\-\- > 0)
\&      OPENSSL_free(params[params_n].data);
\&  OPENSSL_free(params);
\&  /* ... */
\&  return;
\&
\& err:
\&  BIO_printf(bio_err, "MAC parameter error \*(Aq%s\*(Aq\en", opt);
\&  ERR_print_errors(bio_err);
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOSSL_PARAM\fR\|(3), \fBOSSL_PARAM_int\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
