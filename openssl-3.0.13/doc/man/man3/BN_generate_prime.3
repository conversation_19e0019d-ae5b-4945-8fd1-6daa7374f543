.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BN_GENERATE_PRIME 3ossl"
.TH BN_GENERATE_PRIME 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BN_generate_prime_ex2, BN_generate_prime_ex, BN_is_prime_ex, BN_check_prime,
BN_is_prime_fasttest_ex, BN_GENCB_call, BN_GENCB_new, BN_GENCB_free,
BN_GENCB_set_old, BN_GENCB_set, BN_GENCB_get_arg, BN_generate_prime,
BN_is_prime, BN_is_prime_fasttest \- generate primes and test for primality
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bn.h>
\&
\& int BN_generate_prime_ex2(BIGNUM *ret, int bits, int safe,
\&                           const BIGNUM *add, const BIGNUM *rem, BN_GENCB *cb,
\&                           BN_CTX *ctx);
\&
\& int BN_generate_prime_ex(BIGNUM *ret, int bits, int safe, const BIGNUM *add,
\&                          const BIGNUM *rem, BN_GENCB *cb);
\&
\& int BN_check_prime(const BIGNUM *p, BN_CTX *ctx, BN_GENCB *cb);
\&
\& int BN_GENCB_call(BN_GENCB *cb, int a, int b);
\&
\& BN_GENCB *BN_GENCB_new(void);
\&
\& void BN_GENCB_free(BN_GENCB *cb);
\&
\& void BN_GENCB_set_old(BN_GENCB *gencb,
\&                       void (*callback)(int, int, void *), void *cb_arg);
\&
\& void BN_GENCB_set(BN_GENCB *gencb,
\&                   int (*callback)(int, int, BN_GENCB *), void *cb_arg);
\&
\& void *BN_GENCB_get_arg(BN_GENCB *cb);
.Ve
.PP
The following functions have been deprecated since OpenSSL 0.9.8, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 3
\& BIGNUM *BN_generate_prime(BIGNUM *ret, int num, int safe, BIGNUM *add,
\&                           BIGNUM *rem, void (*callback)(int, int, void *),
\&                           void *cb_arg);
\&
\& int BN_is_prime(const BIGNUM *p, int nchecks,
\&                 void (*callback)(int, int, void *), BN_CTX *ctx, void *cb_arg);
\&
\& int BN_is_prime_fasttest(const BIGNUM *p, int nchecks,
\&                          void (*callback)(int, int, void *), BN_CTX *ctx,
\&                          void *cb_arg, int do_trial_division);
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& int BN_is_prime_ex(const BIGNUM *p, int nchecks, BN_CTX *ctx, BN_GENCB *cb);
\&
\& int BN_is_prime_fasttest_ex(const BIGNUM *p, int nchecks, BN_CTX *ctx,
\&                             int do_trial_division, BN_GENCB *cb);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBN_generate_prime_ex2()\fR generates a pseudo-random prime number of
at least bit length \fBbits\fR using the BN_CTX provided in \fBctx\fR. The value of
\&\fBctx\fR must not be NULL.
.PP
The returned number is probably prime with a negligible error.
The maximum error rate is 2^\-128.
It's 2^\-287 for a 512 bit prime, 2^\-435 for a 1024 bit prime,
2^\-648 for a 2048 bit prime, and lower than 2^\-882 for primes larger
than 2048 bit.
.PP
If \fBadd\fR is \fBNULL\fR the returned prime number will have exact bit
length \fBbits\fR with the top most two bits set.
.PP
If \fBret\fR is not \fBNULL\fR, it will be used to store the number.
.PP
If \fBcb\fR is not \fBNULL\fR, it is used as follows:
.IP \(bu 2
\&\fBBN_GENCB_call(cb, 0, i)\fR is called after generating the i\-th
potential prime number.
.IP \(bu 2
While the number is being tested for primality,
\&\fBBN_GENCB_call(cb, 1, j)\fR is called as described below.
.IP \(bu 2
When a prime has been found, \fBBN_GENCB_call(cb, 2, i)\fR is called.
.IP \(bu 2
The callers of \fBBN_generate_prime_ex()\fR may call \fBBN_GENCB_call(cb, i, j)\fR with
other values as described in their respective man pages; see "SEE ALSO".
.PP
The prime may have to fulfill additional requirements for use in
Diffie-Hellman key exchange:
.PP
If \fBadd\fR is not \fBNULL\fR, the prime will fulfill the condition p % \fBadd\fR
== \fBrem\fR (p % \fBadd\fR == 1 if \fBrem\fR == \fBNULL\fR) in order to suit a given
generator.
.PP
If \fBsafe\fR is true, it will be a safe prime (i.e. a prime p so
that (p\-1)/2 is also prime). If \fBsafe\fR is true, and \fBrem\fR == \fBNULL\fR
the condition will be p % \fBadd\fR == 3.
It is recommended that \fBadd\fR is a multiple of 4.
.PP
The random generator must be seeded prior to calling \fBBN_generate_prime_ex()\fR.
If the automatic seeding or reseeding of the OpenSSL CSPRNG fails due to
external circumstances (see \fBRAND\fR\|(7)), the operation will fail.
The random number generator configured for the OSSL_LIB_CTX associated with
\&\fBctx\fR will be used.
.PP
\&\fBBN_generate_prime_ex()\fR is the same as \fBBN_generate_prime_ex2()\fR except that no
\&\fBctx\fR parameter is passed.
In this case the random number generator associated with the default OSSL_LIB_CTX
will be used.
.PP
\&\fBBN_check_prime()\fR, \fBBN_is_prime_ex()\fR, \fBBN_is_prime_fasttest_ex()\fR, \fBBN_is_prime()\fR
and \fBBN_is_prime_fasttest()\fR test if the number \fBp\fR is prime.
The functions tests until one of the tests shows that \fBp\fR is composite,
or all the tests passed.
If \fBp\fR passes all these tests, it is considered a probable prime.
.PP
The test performed on \fBp\fR are trial division by a number of small primes
and rounds of the of the Miller-Rabin probabilistic primality test.
.PP
The functions do at least 64 rounds of the Miller-Rabin test giving a maximum
false positive rate of 2^\-128.
If the size of \fBp\fR is more than 2048 bits, they do at least 128 rounds
giving a maximum false positive rate of 2^\-256.
.PP
If \fBnchecks\fR is larger than the minimum above (64 or 128), \fBnchecks\fR
rounds of the Miller-Rabin test will be done.
.PP
If \fBdo_trial_division\fR set to \fB0\fR, the trial division will be skipped.
\&\fBBN_is_prime_ex()\fR and \fBBN_is_prime()\fR always skip the trial division.
.PP
\&\fBBN_is_prime_ex()\fR, \fBBN_is_prime_fasttest_ex()\fR, \fBBN_is_prime()\fR
and \fBBN_is_prime_fasttest()\fR are deprecated.
.PP
\&\fBBN_is_prime_fasttest()\fR and \fBBN_is_prime()\fR behave just like
\&\fBBN_is_prime_fasttest_ex()\fR and \fBBN_is_prime_ex()\fR respectively, but with the old
style call back.
.PP
\&\fBctx\fR is a preallocated \fBBN_CTX\fR (to save the overhead of allocating and
freeing the structure in a loop), or \fBNULL\fR.
.PP
If the trial division is done, and no divisors are found and \fBcb\fR
is not \fBNULL\fR, \fBBN_GENCB_call(cb, 1, \-1)\fR is called.
.PP
After each round of the Miller-Rabin probabilistic primality test,
if \fBcb\fR is not \fBNULL\fR, \fBBN_GENCB_call(cb, 1, j)\fR is called
with \fBj\fR the iteration (j = 0, 1, ...).
.PP
\&\fBBN_GENCB_call()\fR calls the callback function held in the \fBBN_GENCB\fR structure
and passes the ints \fBa\fR and \fBb\fR as arguments. There are two types of
\&\fBBN_GENCB\fR structure that are supported: "new" style and "old" style. New
programs should prefer the "new" style, whilst the "old" style is provided
for backwards compatibility purposes.
.PP
A \fBBN_GENCB\fR structure should be created through a call to \fBBN_GENCB_new()\fR,
and freed through a call to \fBBN_GENCB_free()\fR.
.PP
For "new" style callbacks a BN_GENCB structure should be initialised with a
call to \fBBN_GENCB_set()\fR, where \fBgencb\fR is a \fBBN_GENCB *\fR, \fBcallback\fR is of
type \fBint (*callback)(int, int, BN_GENCB *)\fR and \fBcb_arg\fR is a \fBvoid *\fR.
"Old" style callbacks are the same except they are initialised with a call
to \fBBN_GENCB_set_old()\fR and \fBcallback\fR is of type
\&\fBvoid (*callback)(int, int, void *)\fR.
.PP
A callback is invoked through a call to \fBBN_GENCB_call\fR. This will check
the type of the callback and will invoke \fBcallback(a, b, gencb)\fR for new
style callbacks or \fBcallback(a, b, cb_arg)\fR for old style.
.PP
It is possible to obtain the argument associated with a BN_GENCB structure
(set via a call to BN_GENCB_set or BN_GENCB_set_old) using BN_GENCB_get_arg.
.PP
\&\fBBN_generate_prime()\fR (deprecated) works in the same way as
\&\fBBN_generate_prime_ex()\fR but expects an old-style callback function
directly in the \fBcallback\fR parameter, and an argument to pass to it in
the \fBcb_arg\fR. \fBBN_is_prime()\fR and \fBBN_is_prime_fasttest()\fR
can similarly be compared to \fBBN_is_prime_ex()\fR and
\&\fBBN_is_prime_fasttest_ex()\fR, respectively.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBN_generate_prime_ex()\fR return 1 on success or 0 on error.
.PP
\&\fBBN_is_prime_ex()\fR, \fBBN_is_prime_fasttest_ex()\fR, \fBBN_is_prime()\fR,
\&\fBBN_is_prime_fasttest()\fR and BN_check_prime return 0 if the number is composite,
1 if it is prime with an error probability of less than 0.25^\fBnchecks\fR, and
\&\-1 on error.
.PP
\&\fBBN_generate_prime()\fR returns the prime number on success, \fBNULL\fR otherwise.
.PP
BN_GENCB_new returns a pointer to a BN_GENCB structure on success, or \fBNULL\fR
otherwise.
.PP
BN_GENCB_get_arg returns the argument previously associated with a BN_GENCB
structure.
.PP
Callback functions should return 1 on success or 0 on error.
.PP
The error codes can be obtained by \fBERR_get_error\fR\|(3).
.SH "REMOVED FUNCTIONALITY"
.IX Header "REMOVED FUNCTIONALITY"
As of OpenSSL 1.1.0 it is no longer possible to create a BN_GENCB structure
directly, as in:
.PP
.Vb 1
\& BN_GENCB callback;
.Ve
.PP
Instead applications should create a BN_GENCB structure using BN_GENCB_new:
.PP
.Vb 6
\& BN_GENCB *callback;
\& callback = BN_GENCB_new();
\& if (!callback)
\&     /* error */
\& ...
\& BN_GENCB_free(callback);
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBDH_generate_parameters\fR\|(3), \fBDSA_generate_parameters\fR\|(3),
\&\fBRSA_generate_key\fR\|(3), \fBERR_get_error\fR\|(3), \fBRAND_bytes\fR\|(3),
\&\fBRAND\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The \fBBN_is_prime_ex()\fR and \fBBN_is_prime_fasttest_ex()\fR functions were
deprecated in OpenSSL 3.0.
.PP
The \fBBN_GENCB_new()\fR, \fBBN_GENCB_free()\fR,
and \fBBN_GENCB_get_arg()\fR functions were added in OpenSSL 1.1.0.
.PP
\&\fBBN_check_prime()\fR was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
