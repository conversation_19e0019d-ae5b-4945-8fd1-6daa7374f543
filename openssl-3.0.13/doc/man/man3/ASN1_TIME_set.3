.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ASN1_TIME_SET 3ossl"
.TH ASN1_TIME_SET 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ASN1_TIME_set, ASN1_UTCTIME_set, ASN1_GENERALIZEDTIME_set,
ASN1_TIME_adj, ASN1_UTCTIME_adj, ASN1_GENERALIZEDTIME_adj,
ASN1_TIME_check, ASN1_UTCTIME_check, ASN1_GENERALIZEDTIME_check,
ASN1_TIME_set_string, ASN1_UTCTIME_set_string, ASN1_GENERALIZEDTIME_set_string,
ASN1_TIME_set_string_X509,
ASN1_TIME_normalize,
ASN1_TIME_to_tm,
ASN1_TIME_print, ASN1_TIME_print_ex, ASN1_UTCTIME_print, ASN1_GENERALIZEDTIME_print,
ASN1_TIME_diff,
ASN1_TIME_cmp_time_t, ASN1_UTCTIME_cmp_time_t,
ASN1_TIME_compare,
ASN1_TIME_to_generalizedtime,
ASN1_TIME_dup, ASN1_UTCTIME_dup, ASN1_GENERALIZEDTIME_dup \- ASN.1 Time functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 4
\& ASN1_TIME *ASN1_TIME_set(ASN1_TIME *s, time_t t);
\& ASN1_UTCTIME *ASN1_UTCTIME_set(ASN1_UTCTIME *s, time_t t);
\& ASN1_GENERALIZEDTIME *ASN1_GENERALIZEDTIME_set(ASN1_GENERALIZEDTIME *s,
\&                                                time_t t);
\&
\& ASN1_TIME *ASN1_TIME_adj(ASN1_TIME *s, time_t t, int offset_day,
\&                          long offset_sec);
\& ASN1_UTCTIME *ASN1_UTCTIME_adj(ASN1_UTCTIME *s, time_t t,
\&                                int offset_day, long offset_sec);
\& ASN1_GENERALIZEDTIME *ASN1_GENERALIZEDTIME_adj(ASN1_GENERALIZEDTIME *s,
\&                                                time_t t, int offset_day,
\&                                                long offset_sec);
\&
\& int ASN1_TIME_set_string(ASN1_TIME *s, const char *str);
\& int ASN1_TIME_set_string_X509(ASN1_TIME *s, const char *str);
\& int ASN1_UTCTIME_set_string(ASN1_UTCTIME *s, const char *str);
\& int ASN1_GENERALIZEDTIME_set_string(ASN1_GENERALIZEDTIME *s,
\&                                     const char *str);
\&
\& int ASN1_TIME_normalize(ASN1_TIME *s);
\&
\& int ASN1_TIME_check(const ASN1_TIME *t);
\& int ASN1_UTCTIME_check(const ASN1_UTCTIME *t);
\& int ASN1_GENERALIZEDTIME_check(const ASN1_GENERALIZEDTIME *t);
\&
\& int ASN1_TIME_print(BIO *b, const ASN1_TIME *s);
\& int ASN1_TIME_print_ex(BIO *bp, const ASN1_TIME *tm, unsigned long flags);
\& int ASN1_UTCTIME_print(BIO *b, const ASN1_UTCTIME *s);
\& int ASN1_GENERALIZEDTIME_print(BIO *b, const ASN1_GENERALIZEDTIME *s);
\&
\& int ASN1_TIME_to_tm(const ASN1_TIME *s, struct tm *tm);
\& int ASN1_TIME_diff(int *pday, int *psec, const ASN1_TIME *from,
\&                    const ASN1_TIME *to);
\&
\& int ASN1_TIME_cmp_time_t(const ASN1_TIME *s, time_t t);
\& int ASN1_UTCTIME_cmp_time_t(const ASN1_UTCTIME *s, time_t t);
\&
\& int ASN1_TIME_compare(const ASN1_TIME *a, const ASN1_TIME *b);
\&
\& ASN1_GENERALIZEDTIME *ASN1_TIME_to_generalizedtime(ASN1_TIME *t,
\&                                                    ASN1_GENERALIZEDTIME **out);
\&
\& ASN1_TIME *ASN1_TIME_dup(const ASN1_TIME *t);
\& ASN1_UTCTIME *ASN1_UTCTIME_dup(const ASN1_UTCTIME *t);
\& ASN1_GENERALIZEDTIME *ASN1_GENERALIZEDTIME_dup(const ASN1_GENERALIZEDTIME *t);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBASN1_TIME_set()\fR, \fBASN1_UTCTIME_set()\fR and \fBASN1_GENERALIZEDTIME_set()\fR
functions set the structure \fIs\fR to the time represented by the time_t
value \fIt\fR. If \fIs\fR is NULL a new time structure is allocated and returned.
.PP
The \fBASN1_TIME_adj()\fR, \fBASN1_UTCTIME_adj()\fR and \fBASN1_GENERALIZEDTIME_adj()\fR
functions set the time structure \fIs\fR to the time represented
by the time \fIoffset_day\fR and \fIoffset_sec\fR after the time_t value \fIt\fR.
The values of \fIoffset_day\fR or \fIoffset_sec\fR can be negative to set a
time before \fIt\fR. The \fIoffset_sec\fR value can also exceed the number of
seconds in a day. If \fIs\fR is NULL a new structure is allocated
and returned.
.PP
The \fBASN1_TIME_set_string()\fR, \fBASN1_UTCTIME_set_string()\fR and
\&\fBASN1_GENERALIZEDTIME_set_string()\fR functions set the time structure \fIs\fR
to the time represented by string \fIstr\fR which must be in appropriate ASN.1
time format (for example YYMMDDHHMMSSZ or YYYYMMDDHHMMSSZ). If \fIs\fR is NULL
this function performs a format check on \fIstr\fR only. The string \fIstr\fR
is copied into \fIs\fR.
.PP
\&\fBASN1_TIME_set_string_X509()\fR sets \fBASN1_TIME\fR structure \fIs\fR to the time
represented by string \fIstr\fR which must be in appropriate time format
that RFC 5280 requires, which means it only allows YYMMDDHHMMSSZ and
YYYYMMDDHHMMSSZ (leap second is rejected), all other ASN.1 time format
are not allowed. If \fIs\fR is NULL this function performs a format check
on \fIstr\fR only.
.PP
The \fBASN1_TIME_normalize()\fR function converts an \fBASN1_GENERALIZEDTIME\fR or
\&\fBASN1_UTCTIME\fR into a time value that can be used in a certificate. It
should be used after the \fBASN1_TIME_set_string()\fR functions and before
\&\fBASN1_TIME_print()\fR functions to get consistent (i.e. GMT) results.
.PP
The \fBASN1_TIME_check()\fR, \fBASN1_UTCTIME_check()\fR and \fBASN1_GENERALIZEDTIME_check()\fR
functions check the syntax of the time structure \fIs\fR.
.PP
The \fBASN1_TIME_print()\fR, \fBASN1_UTCTIME_print()\fR and \fBASN1_GENERALIZEDTIME_print()\fR
functions print the time structure \fIs\fR to BIO \fIb\fR in human readable
format. It will be of the format MMM DD HH:MM:SS YYYY [GMT], for example
"Feb  3 00:55:52 2015 GMT", which does not include a newline.
If the time structure has invalid format it prints out "Bad time value" and
returns an error. The output for generalized time may include a fractional part
following the second.
.PP
\&\fBASN1_TIME_print_ex()\fR provides \fIflags\fR to specify the output format of the
datetime. This can be either \fBASN1_DTFLGS_RFC822\fR or \fBASN1_DTFLGS_ISO8601\fR.
.PP
\&\fBASN1_TIME_to_tm()\fR converts the time \fIs\fR to the standard \fItm\fR structure.
If \fIs\fR is NULL, then the current time is converted. The output time is GMT.
The \fItm_sec\fR, \fItm_min\fR, \fItm_hour\fR, \fItm_mday\fR, \fItm_wday\fR, \fItm_yday\fR,
\&\fItm_mon\fR and \fItm_year\fR fields of \fItm\fR structure are set to proper values,
whereas all other fields are set to 0. If \fItm\fR is NULL this function performs
a format check on \fIs\fR only. If \fIs\fR is in Generalized format with fractional
seconds, e.g. YYYYMMDDHHMMSS.SSSZ, the fractional seconds will be lost while
converting \fIs\fR to \fItm\fR structure.
.PP
\&\fBASN1_TIME_diff()\fR sets \fI*pday\fR and \fI*psec\fR to the time difference between
\&\fIfrom\fR and \fIto\fR. If \fIto\fR represents a time later than \fIfrom\fR then
one or both (depending on the time difference) of \fI*pday\fR and \fI*psec\fR
will be positive. If \fIto\fR represents a time earlier than \fIfrom\fR then
one or both of \fI*pday\fR and \fI*psec\fR will be negative. If \fIto\fR and \fIfrom\fR
represent the same time then \fI*pday\fR and \fI*psec\fR will both be zero.
If both \fI*pday\fR and \fI*psec\fR are nonzero they will always have the same
sign. The value of \fI*psec\fR will always be less than the number of seconds
in a day. If \fIfrom\fR or \fIto\fR is NULL the current time is used.
.PP
The \fBASN1_TIME_cmp_time_t()\fR and \fBASN1_UTCTIME_cmp_time_t()\fR functions compare
the two times represented by the time structure \fIs\fR and the time_t \fIt\fR.
.PP
The \fBASN1_TIME_compare()\fR function compares the two times represented by the
time structures \fIa\fR and \fIb\fR.
.PP
The \fBASN1_TIME_to_generalizedtime()\fR function converts an \fBASN1_TIME\fR to an
\&\fBASN1_GENERALIZEDTIME\fR, regardless of year. If either \fIout\fR or
\&\fI*out\fR are NULL, then a new object is allocated and must be freed after use.
.PP
The \fBASN1_TIME_dup()\fR, \fBASN1_UTCTIME_dup()\fR and \fBASN1_GENERALIZEDTIME_dup()\fR functions
duplicate the time structure \fIt\fR and return the duplicated result
correspondingly.
.SH NOTES
.IX Header "NOTES"
The \fBASN1_TIME\fR structure corresponds to the ASN.1 structure \fBTime\fR
defined in RFC5280 et al. The time setting functions obey the rules outlined
in RFC5280: if the date can be represented by UTCTime it is used, else
GeneralizedTime is used.
.PP
The \fBASN1_TIME\fR, \fBASN1_UTCTIME\fR and \fBASN1_GENERALIZEDTIME\fR structures are
represented as an \fBASN1_STRING\fR internally and can be freed up using
\&\fBASN1_STRING_free()\fR.
.PP
The \fBASN1_TIME\fR structure can represent years from 0000 to 9999 but no attempt
is made to correct ancient calendar changes (for example from Julian to
Gregorian calendars).
.PP
\&\fBASN1_UTCTIME\fR is limited to a year range of 1950 through 2049.
.PP
Some applications add offset times directly to a time_t value and pass the
results to \fBASN1_TIME_set()\fR (or equivalent). This can cause problems as the
time_t value can overflow on some systems resulting in unexpected results.
New applications should use \fBASN1_TIME_adj()\fR instead and pass the offset value
in the \fIoffset_sec\fR and \fIoffset_day\fR parameters instead of directly
manipulating a time_t value.
.PP
\&\fBASN1_TIME_adj()\fR may change the type from \fBASN1_GENERALIZEDTIME\fR to
\&\fBASN1_UTCTIME\fR, or vice versa, based on the resulting year.
\&\fBASN1_GENERALIZEDTIME_adj()\fR and \fBASN1_UTCTIME_adj()\fR will not modify the type
of the return structure.
.PP
It is recommended that functions starting with \fBASN1_TIME\fR be used instead of
those starting with \fBASN1_UTCTIME\fR or \fBASN1_GENERALIZEDTIME\fR. The functions
starting with \fBASN1_UTCTIME\fR and \fBASN1_GENERALIZEDTIME\fR act only on that
specific time format. The functions starting with \fBASN1_TIME\fR will operate on
either format.
.SH BUGS
.IX Header "BUGS"
\&\fBASN1_TIME_print()\fR, \fBASN1_UTCTIME_print()\fR and \fBASN1_GENERALIZEDTIME_print()\fR do
not print out the timezone: it either prints out "GMT" or nothing. But all
certificates complying with RFC5280 et al use GMT anyway.
.PP
\&\fBASN1_TIME_print()\fR, \fBASN1_TIME_print_ex()\fR, \fBASN1_UTCTIME_print()\fR and
\&\fBASN1_GENERALIZEDTIME_print()\fR do not distinguish if they fail because
of an I/O error or invalid time format.
.PP
Use the \fBASN1_TIME_normalize()\fR function to normalize the time value before
printing to get GMT results.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBASN1_TIME_set()\fR, \fBASN1_UTCTIME_set()\fR, \fBASN1_GENERALIZEDTIME_set()\fR,
\&\fBASN1_TIME_adj()\fR, \fBASN1_UTCTIME_adj()\fR and \fBASN1_GENERALIZEDTIME_set()\fR return
a pointer to a time structure or NULL if an error occurred.
.PP
\&\fBASN1_TIME_set_string()\fR, \fBASN1_UTCTIME_set_string()\fR,
\&\fBASN1_GENERALIZEDTIME_set_string()\fR and \fBASN1_TIME_set_string_X509()\fR return
1 if the time value is successfully set and 0 otherwise.
.PP
\&\fBASN1_TIME_normalize()\fR returns 1 on success, and 0 on error.
.PP
\&\fBASN1_TIME_check()\fR, ASN1_UTCTIME_check and \fBASN1_GENERALIZEDTIME_check()\fR return 1
if the structure is syntactically correct and 0 otherwise.
.PP
\&\fBASN1_TIME_print()\fR, \fBASN1_UTCTIME_print()\fR and \fBASN1_GENERALIZEDTIME_print()\fR
return 1 if the time is successfully printed out and
0 if an I/O error occurred an error occurred (I/O error or invalid time format).
.PP
\&\fBASN1_TIME_to_tm()\fR returns 1 if the time is successfully parsed and 0 if an
error occurred (invalid time format).
.PP
\&\fBASN1_TIME_diff()\fR returns 1 for success and 0 for failure. It can fail if the
passed-in time structure has invalid syntax, for example.
.PP
\&\fBASN1_TIME_cmp_time_t()\fR and \fBASN1_UTCTIME_cmp_time_t()\fR return \-1 if \fIs\fR is
before \fIt\fR, 0 if \fIs\fR equals \fIt\fR, or 1 if \fIs\fR is after \fIt\fR. \-2 is returned
on error.
.PP
\&\fBASN1_TIME_compare()\fR returns \-1 if \fIa\fR is before \fIb\fR, 0 if \fIa\fR equals \fIb\fR,
or 1 if \fIa\fR is after \fIb\fR. \-2 is returned on error.
.PP
\&\fBASN1_TIME_to_generalizedtime()\fR returns a pointer to the appropriate time
structure on success or NULL if an error occurred.
.PP
\&\fBASN1_TIME_dup()\fR, \fBASN1_UTCTIME_dup()\fR and \fBASN1_GENERALIZEDTIME_dup()\fR return a
pointer to a time structure or NULL if an error occurred.
.SH EXAMPLES
.IX Header "EXAMPLES"
Set a time structure to one hour after the current time and print it out:
.PP
.Vb 2
\& #include <time.h>
\& #include <openssl/asn1.h>
\&
\& ASN1_TIME *tm;
\& time_t t;
\& BIO *b;
\&
\& t = time(NULL);
\& tm = ASN1_TIME_adj(NULL, t, 0, 60 * 60);
\& b = BIO_new_fp(stdout, BIO_NOCLOSE);
\& ASN1_TIME_print(b, tm);
\& ASN1_STRING_free(tm);
\& BIO_free(b);
.Ve
.PP
Determine if one time is later or sooner than the current time:
.PP
.Vb 1
\& int day, sec;
\&
\& if (!ASN1_TIME_diff(&day, &sec, NULL, to))
\&     /* Invalid time format */
\&
\& if (day > 0 || sec > 0)
\&     printf("Later\en");
\& else if (day < 0 || sec < 0)
\&     printf("Sooner\en");
\& else
\&     printf("Same\en");
.Ve
.SH HISTORY
.IX Header "HISTORY"
The \fBASN1_TIME_to_tm()\fR function was added in OpenSSL 1.1.1.
The \fBASN1_TIME_set_string_X509()\fR function was added in OpenSSL 1.1.1.
The \fBASN1_TIME_normalize()\fR function was added in OpenSSL 1.1.1.
The \fBASN1_TIME_cmp_time_t()\fR function was added in OpenSSL 1.1.1.
The \fBASN1_TIME_compare()\fR function was added in OpenSSL 1.1.1.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
