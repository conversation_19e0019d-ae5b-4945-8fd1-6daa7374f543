.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_SET1_ENCODED_PUBLIC_KEY 3ossl"
.TH EVP_PKEY_SET1_ENCODED_PUBLIC_KEY 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_set1_encoded_public_key, EVP_PKEY_get1_encoded_public_key,
EVP_PKEY_set1_tls_encodedpoint, EVP_PKEY_get1_tls_encodedpoint
\&\- functions to set and get public key data within an EVP_PKEY
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_set1_encoded_public_key(EVP_PKEY *pkey,
\&                                      const unsigned char *pub, size_t publen);
\&
\& size_t EVP_PKEY_get1_encoded_public_key(EVP_PKEY *pkey, unsigned char **ppub);
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 2
\& int EVP_PKEY_set1_tls_encodedpoint(EVP_PKEY *pkey,
\&                                    const unsigned char *pt, size_t ptlen);
\&
\& size_t EVP_PKEY_get1_tls_encodedpoint(EVP_PKEY *pkey, unsigned char **ppt);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_PKEY_set1_encoded_public_key()\fR can be used to set the public key value
within an existing EVP_PKEY object. For the built-in OpenSSL algorithms this
currently only works for those that support key exchange. Parameters are not
set as part of this operation, so typically an application will create an
EVP_PKEY first, set the parameters on it, and then call this function.
For example setting the parameters might be done using
\&\fBEVP_PKEY_copy_parameters\fR\|(3).
.PP
The format for the encoded public key will depend on the algorithm in use. For
DH it should be encoded as a positive integer in big-endian form. For EC is
should be a point conforming to Sec. 2.3.4 of the SECG SEC 1 ("Elliptic
Curve Cryptography") standard. For X25519 and X448 it should be encoded in a
format as defined by RFC7748.
.PP
The key to be updated is supplied in \fBpkey\fR. The buffer containing the encoded
key is pointed to be \fBpub\fR. The length of the buffer is supplied in \fBpublen\fR.
.PP
\&\fBEVP_PKEY_get1_encoded_public_key()\fR does the equivalent operation except that
the encoded public key is returned to the application. The key containing the
public key data is supplied in \fBpkey\fR. A buffer containing the encoded key will
be allocated and stored in \fB*ppub\fR. The length of the encoded public key is
returned by the function. The application is responsible for freeing the
allocated buffer.
.PP
The macro \fBEVP_PKEY_set1_tls_encodedpoint()\fR is deprecated and simply calls
\&\fBEVP_PKEY_set1_encoded_public_key()\fR with all the same arguments. New applications
should use \fBEVP_PKEY_set1_encoded_public_key()\fR instead.
.PP
The macro \fBEVP_PKEY_get1_tls_encodedpoint()\fR is deprecated and simply calls
\&\fBEVP_PKEY_get1_encoded_public_key()\fR with all the same arguments. New applications
should use \fBEVP_PKEY_get1_encoded_public_key()\fR instead.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_set1_encoded_public_key()\fR returns 1 for success and 0 or a negative
value for failure.
.PP
\&\fBEVP_PKEY_get1_encoded_public_key()\fR returns the length of the encoded key or 0 for failure.
.SH EXAMPLES
.IX Header "EXAMPLES"
See \fBEVP_PKEY_derive_init\fR\|(3) and \fBEVP_PKEY_derive\fR\|(3) for information about
performing a key exchange operation.
.SS "Set up a peer's EVP_PKEY ready for a key exchange operation"
.IX Subsection "Set up a peer's EVP_PKEY ready for a key exchange operation"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int exchange(EVP_PKEY *ourkey, unsigned char *peer_pub, size_t peer_pub_len)
\& {
\&     EVP_PKEY *peerkey = EVP_PKEY_new();
\&
\&     if (peerkey == NULL || EVP_PKEY_copy_parameters(peerkey, ourkey) <= 0)
\&         return 0;
\&
\&     if (EVP_PKEY_set1_encoded_public_key(peerkey, peer_pub,
\&                                          peer_pub_len) <= 0)
\&         return 0;
\&
\&     /* Do the key exchange here */
\&
\&     EVP_PKEY_free(peerkey);
\&
\&     return 1;
\& }
.Ve
.SS "Get an encoded public key to send to a peer"
.IX Subsection "Get an encoded public key to send to a peer"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int get_encoded_pub_key(EVP_PKEY *ourkey)
\& {
\&     unsigned char *pubkey;
\&     size_t pubkey_len;
\&
\&    pubkey_len = EVP_PKEY_get1_encoded_public_key(ourkey, &pubkey);
\&    if (pubkey_len == 0)
\&        return 0;
\&
\&    /*
\&     * Send the encoded public key stored in the buffer at "pubkey" and of
\&     * length pubkey_len, to the peer.
\&     */
\&
\&    OPENSSL_free(pubkey);
\&    return 1;
\& }
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_new\fR\|(3), \fBEVP_PKEY_copy_parameters\fR\|(3),
\&\fBEVP_PKEY_derive_init\fR\|(3), \fBEVP_PKEY_derive\fR\|(3),
\&\fBEVP_PKEY\-DH\fR\|(7), \fBEVP_PKEY\-EC\fR\|(7), \fBEVP_PKEY\-X25519\fR\|(7), \fBEVP_PKEY\-X448\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
\&\fBEVP_PKEY_set1_encoded_public_key()\fR and \fBEVP_PKEY_get1_encoded_public_key()\fR were
added in OpenSSL 3.0.
.PP
\&\fBEVP_PKEY_set1_tls_encodedpoint()\fR and \fBEVP_PKEY_get1_tls_encodedpoint()\fR were
deprecated in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
