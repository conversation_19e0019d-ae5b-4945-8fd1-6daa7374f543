.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_DISPATCH 3ossl"
.TH OSSL_DISPATCH 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_DISPATCH \- OpenSSL Core type to define a dispatchable function table
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/core.h>
\&
\& typedef struct ossl_dispatch_st OSSL_DISPATCH;
\& struct ossl_dispatch_st {
\&     int function_id;
\&     void (*function)(void);
\& };
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This type is a tuple of function identity and function pointer.
Arrays of this type are passed between the OpenSSL libraries and the
providers to describe what functionality one side provides to the other.
.PP
Arrays of this type must be terminated with a tuple having function identity
zero and function pointer NULL.
.SS "\fBOSSL_DISPATCH\fP fields"
.IX Subsection "OSSL_DISPATCH fields"
.IP \fIfunction_id\fR 4
.IX Item "function_id"
OpenSSL defined function identity of the implemented function.
.IP \fIfunction\fR 4
.IX Item "function"
Pointer to the implemented function itself.  Despite the generic definition
of this field, the implemented function it points to must have a function
signature that corresponds to the \fIfunction_id\fR
.PP
Available function identities and corresponding function signatures are
defined in \fBopenssl\-core_dispatch.h\fR\|(7).
Furthermore, the chosen function identities and associated function
signature must be chosen specifically for the operation that it's intended
for, as determined by the intended \fBOSSL_ALGORITHM\fR\|(3) array.
.PP
Any function identity not recognised by the recipient of this type
will be ignored.
This ensures that providers built with one OpenSSL version in mind
will work together with any other OpenSSL version that supports this
mechanism.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7), \fBopenssl\-core_dispatch.h\fR\|(7), \fBOSSL_ALGORITHM\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBOSSL_DISPATCH\fR was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
