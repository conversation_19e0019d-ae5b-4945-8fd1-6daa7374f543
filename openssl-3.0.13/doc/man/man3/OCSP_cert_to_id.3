.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OCSP_CERT_TO_ID 3ossl"
.TH OCSP_CERT_TO_ID 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OCSP_cert_to_id, OCSP_cert_id_new, OCSP_CERTID_free, OCSP_id_issuer_cmp,
OCSP_id_cmp, OCSP_id_get0_info \- OCSP certificate ID utility functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ocsp.h>
\&
\& OCSP_CERTID *OCSP_cert_to_id(const EVP_MD *dgst,
\&                              X509 *subject, X509 *issuer);
\&
\& OCSP_CERTID *OCSP_cert_id_new(const EVP_MD *dgst,
\&                               X509_NAME *issuerName,
\&                               ASN1_BIT_STRING *issuerKey,
\&                               ASN1_INTEGER *serialNumber);
\&
\& void OCSP_CERTID_free(OCSP_CERTID *id);
\&
\& int OCSP_id_issuer_cmp(const OCSP_CERTID *a, const OCSP_CERTID *b);
\& int OCSP_id_cmp(const OCSP_CERTID *a, const OCSP_CERTID *b);
\&
\& int OCSP_id_get0_info(ASN1_OCTET_STRING **piNameHash, ASN1_OBJECT **pmd,
\&                       ASN1_OCTET_STRING **pikeyHash,
\&                       ASN1_INTEGER **pserial, OCSP_CERTID *cid);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBOCSP_cert_to_id()\fR creates and returns a new \fBOCSP_CERTID\fR structure using
message digest \fBdgst\fR for certificate \fBsubject\fR with issuer \fBissuer\fR. If
\&\fBdgst\fR is \fBNULL\fR then SHA1 is used.
.PP
\&\fBOCSP_cert_id_new()\fR creates and returns a new \fBOCSP_CERTID\fR using \fBdgst\fR and
issuer name \fBissuerName\fR, issuer key hash \fBissuerKey\fR and serial number
\&\fBserialNumber\fR.
.PP
\&\fBOCSP_CERTID_free()\fR frees up \fBid\fR.
.PP
\&\fBOCSP_id_cmp()\fR compares \fBOCSP_CERTID\fR \fBa\fR and \fBb\fR.
.PP
\&\fBOCSP_id_issuer_cmp()\fR compares only the issuer name of \fBOCSP_CERTID\fR \fBa\fR and \fBb\fR.
.PP
\&\fBOCSP_id_get0_info()\fR returns the issuer name hash, hash OID, issuer key hash and
serial number contained in \fBcid\fR. If any of the values are not required the
corresponding parameter can be set to \fBNULL\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOCSP_cert_to_id()\fR and \fBOCSP_cert_id_new()\fR return either a pointer to a valid
\&\fBOCSP_CERTID\fR structure or \fBNULL\fR if an error occurred.
.PP
\&\fBOCSP_id_cmp()\fR and \fBOCSP_id_issuer_cmp()\fR returns zero for a match and nonzero
otherwise.
.PP
\&\fBOCSP_CERTID_free()\fR does not return a value.
.PP
\&\fBOCSP_id_get0_info()\fR returns 1 for success and 0 for failure.
.SH NOTES
.IX Header "NOTES"
OCSP clients will typically only use \fBOCSP_cert_to_id()\fR or \fBOCSP_cert_id_new()\fR:
the other functions are used by responder applications.
.PP
The values returned by \fBOCSP_id_get0_info()\fR are internal pointers and \fBMUST
NOT\fR be freed up by an application: they will be freed when the corresponding
\&\fBOCSP_CERTID\fR structure is freed.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBcrypto\fR\|(7),
\&\fBOCSP_request_add1_nonce\fR\|(3),
\&\fBOCSP_REQUEST_new\fR\|(3),
\&\fBOCSP_resp_find_status\fR\|(3),
\&\fBOCSP_response_status\fR\|(3),
\&\fBOCSP_sendreq_new\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2015\-2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
