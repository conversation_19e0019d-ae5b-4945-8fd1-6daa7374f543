.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS12_SAFEBAG_GET0_ATTRS 3ossl"
.TH PKCS12_SAFEBAG_GET0_ATTRS 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS12_SAFEBAG_get0_attrs, PKCS12_get_attr_gen
\&\- Retrieve attributes from a PKCS#12 safeBag
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pkcs12.h>
\&
\& const STACK_OF(X509_ATTRIBUTE) *PKCS12_SAFEBAG_get0_attrs(const PKCS12_SAFEBAG *bag);
\&
\& ASN1_TYPE *PKCS12_get_attr_gen(const STACK_OF(X509_ATTRIBUTE) *attrs,
\&                                int attr_nid);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPKCS12_SAFEBAG_get0_attrs()\fR retrieves the stack of \fBX509_ATTRIBUTE\fRs from a
PKCS#12 safeBag. \fIbag\fR is the \fBPKCS12_SAFEBAG\fR to retrieve the attributes from.
.PP
\&\fBPKCS12_get_attr_gen()\fR retrieves an attribute by NID from a stack of
\&\fBX509_ATTRIBUTE\fRs. \fIattr_nid\fR is the NID of the attribute to retrieve.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBPKCS12_SAFEBAG_get0_attrs()\fR returns the stack of \fBX509_ATTRIBUTE\fRs from a
PKCS#12 safeBag, which could be empty.
.PP
\&\fBPKCS12_get_attr_gen()\fR returns an \fBASN1_TYPE\fR object containing the attribute,
or NULL if the attribute was either not present or an error occurred.
.PP
\&\fBPKCS12_get_attr_gen()\fR does not allocate a new attribute. The returned attribute
is still owned by the \fBPKCS12_SAFEBAG\fR in which it resides.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBPKCS12_get_friendlyname\fR\|(3),
\&\fBPKCS12_add_friendlyname_asc\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
