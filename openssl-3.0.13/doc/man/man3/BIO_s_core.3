.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "BIO_S_CORE 3ossl"
.TH BIO_S_CORE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
BIO_s_core, BIO_new_from_core_bio \- OSSL_CORE_BIO functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/bio.h>
\&
\& const BIO_METHOD *BIO_s_core(void);
\&
\& BIO *BIO_new_from_core_bio(OSSL_LIB_CTX *libctx, OSSL_CORE_BIO *corebio);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBBIO_s_core()\fR returns the core BIO method function.
.PP
A core BIO is treated as source/sink BIO which communicates to some external
BIO. This is primarily useful to provider authors. A number of calls from
libcrypto into a provider supply an OSSL_CORE_BIO parameter. This represents
a BIO within libcrypto, but cannot be used directly by a provider. Instead it
should be wrapped using a \fBBIO_s_core()\fR.
.PP
Once a BIO is constructed based on \fBBIO_s_core()\fR, the associated OSSL_CORE_BIO
object should be set on it using \fBBIO_set_data\fR\|(3). Note that the BIO will only
operate correctly if it is associated with a library context constructed using
\&\fBOSSL_LIB_CTX_new_from_dispatch\fR\|(3). To associate the BIO with a library context
construct it using \fBBIO_new_ex\fR\|(3).
.PP
\&\fBBIO_new_from_core_bio()\fR is a convenience function that constructs a new BIO
based on \fBBIO_s_core()\fR and that is associated with the given library context. It
then also sets the OSSL_CORE_BIO object on the BIO using \fBBIO_set_data\fR\|(3).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBBIO_s_core()\fR return a core BIO \fBBIO_METHOD\fR structure.
.PP
\&\fBBIO_new_from_core_bio()\fR returns a BIO structure on success or NULL on failure.
A failure will most commonly be because the library context was not constructed
using \fBOSSL_LIB_CTX_new_from_dispatch\fR\|(3).
.SH HISTORY
.IX Header "HISTORY"
\&\fBBIO_s_core()\fR and \fBBIO_new_from_core_bio()\fR were added in OpenSSL 3.0.
.SH EXAMPLES
.IX Header "EXAMPLES"
Create a core BIO and write some data to it:
.PP
.Vb 2
\& int some_function(OSSL_LIB_CTX *libctx, OSSL_CORE_BIO *corebio) {
\&     BIO *cbio = BIO_new_from_core_bio(libctx, corebio);
\&
\&     if (cbio == NULL)
\&         return 0;
\&
\&     BIO_puts(cbio, "Hello World\en");
\&
\&     BIO_free(cbio);
\&     return 1;
\& }
.Ve
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
