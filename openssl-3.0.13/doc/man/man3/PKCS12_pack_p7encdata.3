.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PKCS12_PACK_P7ENCDATA 3ossl"
.TH PKCS12_PACK_P7ENCDATA 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
PKCS12_pack_p7encdata, PKCS12_pack_p7encdata_ex \- Pack a set of PKCS#12 safeBags
into a PKCS#7 encrypted data object
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/pkcs12.h>
\&
\& PKCS7 *PKCS12_pack_p7encdata(int pbe_nid, const char *pass, int passlen,
\&                              unsigned char *salt, int saltlen, int iter,
\&                              STACK_OF(PKCS12_SAFEBAG) *bags);
\& PKCS7 *PKCS12_pack_p7encdata_ex(int pbe_nid, const char *pass, int passlen,
\&                                 unsigned char *salt, int saltlen, int iter,
\&                                 STACK_OF(PKCS12_SAFEBAG) *bags,
\&                                 OSSL_LIB_CTX *ctx, const char *propq);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBPKCS12_pack_p7encdata()\fR generates a PKCS#7 ContentInfo object of encrypted-data
type from the set of safeBags \fIbags\fR. The algorithm ID in \fIpbe_nid\fR can be
a PKCS#12 or PKCS#5 password based encryption algorithm, or a cipher algorithm.
If a cipher algorithm is passed, the PKCS#5 PBES2 algorithm will be used with
this cipher as a parameter.
The password \fIpass\fR of length \fIpasslen\fR, salt \fIsalt\fR of length \fIsaltlen\fR
and iteration count \fIiter\fR are inputs into the encryption operation.
.PP
\&\fBPKCS12_pack_p7encdata_ex()\fR operates similar to the above but allows for a
library context \fIctx\fR and property query \fIpropq\fR to be used to select the
algorithm implementation.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
A \fBPKCS7\fR object if successful, or NULL if an error occurred.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
IETF RFC 2315 (<https://tools.ietf.org/html/rfc2315>)
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBPKCS12_pbe_crypt_ex\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
\&\fBPKCS12_pack_p7encdata_ex()\fR was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
