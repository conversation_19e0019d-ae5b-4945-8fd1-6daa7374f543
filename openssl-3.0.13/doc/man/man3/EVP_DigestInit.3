.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_DIGESTINIT 3ossl"
.TH EVP_DIGESTINIT 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_MD_fetch, EVP_MD_up_ref, EVP_MD_free,
EVP_MD_get_params, EVP_MD_gettable_params,
EVP_MD_CTX_new, EVP_MD_CTX_reset, EVP_MD_CTX_free, EVP_MD_CTX_copy,
EVP_MD_CTX_copy_ex, EVP_MD_CTX_ctrl,
EVP_MD_CTX_set_params, EVP_MD_CTX_get_params,
EVP_MD_settable_ctx_params, EVP_MD_gettable_ctx_params,
EVP_MD_CTX_settable_params, EVP_MD_CTX_gettable_params,
EVP_MD_CTX_set_flags, EVP_MD_CTX_clear_flags, EVP_MD_CTX_test_flags,
EVP_Q_digest, EVP_Digest, EVP_DigestInit_ex2, EVP_DigestInit_ex, EVP_DigestInit,
EVP_DigestUpdate, EVP_DigestFinal_ex, EVP_DigestFinalXOF, EVP_DigestFinal,
EVP_MD_is_a, EVP_MD_get0_name, EVP_MD_get0_description,
EVP_MD_names_do_all, EVP_MD_get0_provider, EVP_MD_get_type,
EVP_MD_get_pkey_type, EVP_MD_get_size, EVP_MD_get_block_size, EVP_MD_get_flags,
EVP_MD_CTX_get0_name, EVP_MD_CTX_md, EVP_MD_CTX_get0_md, EVP_MD_CTX_get1_md,
EVP_MD_CTX_get_type, EVP_MD_CTX_get_size, EVP_MD_CTX_get_block_size,
EVP_MD_CTX_get0_md_data, EVP_MD_CTX_update_fn, EVP_MD_CTX_set_update_fn,
EVP_md_null,
EVP_get_digestbyname, EVP_get_digestbynid, EVP_get_digestbyobj,
EVP_MD_CTX_get_pkey_ctx, EVP_MD_CTX_set_pkey_ctx,
EVP_MD_do_all_provided,
EVP_MD_type, EVP_MD_nid, EVP_MD_name, EVP_MD_pkey_type, EVP_MD_size,
EVP_MD_block_size, EVP_MD_flags, EVP_MD_CTX_size, EVP_MD_CTX_block_size,
EVP_MD_CTX_type, EVP_MD_CTX_pkey_ctx, EVP_MD_CTX_md_data
\&\- EVP digest routines
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& EVP_MD *EVP_MD_fetch(OSSL_LIB_CTX *ctx, const char *algorithm,
\&                      const char *properties);
\& int EVP_MD_up_ref(EVP_MD *md);
\& void EVP_MD_free(EVP_MD *md);
\& int EVP_MD_get_params(const EVP_MD *digest, OSSL_PARAM params[]);
\& const OSSL_PARAM *EVP_MD_gettable_params(const EVP_MD *digest);
\& EVP_MD_CTX *EVP_MD_CTX_new(void);
\& int EVP_MD_CTX_reset(EVP_MD_CTX *ctx);
\& void EVP_MD_CTX_free(EVP_MD_CTX *ctx);
\& void EVP_MD_CTX_ctrl(EVP_MD_CTX *ctx, int cmd, int p1, void* p2);
\& int EVP_MD_CTX_get_params(EVP_MD_CTX *ctx, OSSL_PARAM params[]);
\& int EVP_MD_CTX_set_params(EVP_MD_CTX *ctx, const OSSL_PARAM params[]);
\& const OSSL_PARAM *EVP_MD_settable_ctx_params(const EVP_MD *md);
\& const OSSL_PARAM *EVP_MD_gettable_ctx_params(const EVP_MD *md);
\& const OSSL_PARAM *EVP_MD_CTX_settable_params(EVP_MD_CTX *ctx);
\& const OSSL_PARAM *EVP_MD_CTX_gettable_params(EVP_MD_CTX *ctx);
\& void EVP_MD_CTX_set_flags(EVP_MD_CTX *ctx, int flags);
\& void EVP_MD_CTX_clear_flags(EVP_MD_CTX *ctx, int flags);
\& int EVP_MD_CTX_test_flags(const EVP_MD_CTX *ctx, int flags);
\&
\& int EVP_Q_digest(OSSL_LIB_CTX *libctx, const char *name, const char *propq,
\&                  const void *data, size_t datalen,
\&                  unsigned char *md, size_t *mdlen);
\& int EVP_Digest(const void *data, size_t count, unsigned char *md,
\&                unsigned int *size, const EVP_MD *type, ENGINE *impl);
\& int EVP_DigestInit_ex2(EVP_MD_CTX *ctx, const EVP_MD *type,
\&                        const OSSL_PARAM params[]);
\& int EVP_DigestInit_ex(EVP_MD_CTX *ctx, const EVP_MD *type, ENGINE *impl);
\& int EVP_DigestUpdate(EVP_MD_CTX *ctx, const void *d, size_t cnt);
\& int EVP_DigestFinal_ex(EVP_MD_CTX *ctx, unsigned char *md, unsigned int *s);
\& int EVP_DigestFinalXOF(EVP_MD_CTX *ctx, unsigned char *md, size_t len);
\&
\& int EVP_MD_CTX_copy_ex(EVP_MD_CTX *out, const EVP_MD_CTX *in);
\&
\& int EVP_DigestInit(EVP_MD_CTX *ctx, const EVP_MD *type);
\& int EVP_DigestFinal(EVP_MD_CTX *ctx, unsigned char *md, unsigned int *s);
\&
\& int EVP_MD_CTX_copy(EVP_MD_CTX *out, EVP_MD_CTX *in);
\&
\& const char *EVP_MD_get0_name(const EVP_MD *md);
\& const char *EVP_MD_get0_description(const EVP_MD *md);
\& int EVP_MD_is_a(const EVP_MD *md, const char *name);
\& int EVP_MD_names_do_all(const EVP_MD *md,
\&                         void (*fn)(const char *name, void *data),
\&                         void *data);
\& const OSSL_PROVIDER *EVP_MD_get0_provider(const EVP_MD *md);
\& int EVP_MD_get_type(const EVP_MD *md);
\& int EVP_MD_get_pkey_type(const EVP_MD *md);
\& int EVP_MD_get_size(const EVP_MD *md);
\& int EVP_MD_get_block_size(const EVP_MD *md);
\& unsigned long EVP_MD_get_flags(const EVP_MD *md);
\&
\& const EVP_MD *EVP_MD_CTX_get0_md(const EVP_MD_CTX *ctx);
\& EVP_MD *EVP_MD_CTX_get1_md(EVP_MD_CTX *ctx);
\& const char *EVP_MD_CTX_get0_name(const EVP_MD_CTX *ctx);
\& int EVP_MD_CTX_get_size(const EVP_MD_CTX *ctx);
\& int EVP_MD_CTX_get_block_size(const EVP_MD_CTX *ctx);
\& int EVP_MD_CTX_get_type(const EVP_MD_CTX *ctx);
\& void *EVP_MD_CTX_get0_md_data(const EVP_MD_CTX *ctx);
\&
\& const EVP_MD *EVP_md_null(void);
\&
\& const EVP_MD *EVP_get_digestbyname(const char *name);
\& const EVP_MD *EVP_get_digestbynid(int type);
\& const EVP_MD *EVP_get_digestbyobj(const ASN1_OBJECT *o);
\&
\& EVP_PKEY_CTX *EVP_MD_CTX_get_pkey_ctx(const EVP_MD_CTX *ctx);
\& void EVP_MD_CTX_set_pkey_ctx(EVP_MD_CTX *ctx, EVP_PKEY_CTX *pctx);
\&
\& void EVP_MD_do_all_provided(OSSL_LIB_CTX *libctx,
\&                             void (*fn)(EVP_MD *mac, void *arg),
\&                             void *arg);
\&
\& #define EVP_MD_type EVP_MD_get_type
\& #define EVP_MD_nid EVP_MD_get_type
\& #define EVP_MD_name EVP_MD_get0_name
\& #define EVP_MD_pkey_type EVP_MD_get_pkey_type
\& #define EVP_MD_size EVP_MD_get_size
\& #define EVP_MD_block_size EVP_MD_get_block_size
\& #define EVP_MD_flags EVP_MD_get_flags
\& #define EVP_MD_CTX_size EVP_MD_CTX_get_size
\& #define EVP_MD_CTX_block_size EVP_MD_CTX_get_block_size
\& #define EVP_MD_CTX_type EVP_MD_CTX_get_type
\& #define EVP_MD_CTX_pkey_ctx EVP_MD_CTX_get_pkey_ctx
\& #define EVP_MD_CTX_md_data EVP_MD_CTX_get0_md_data
.Ve
.PP
The following functions have been deprecated since OpenSSL 3.0, and can be
hidden entirely by defining \fBOPENSSL_API_COMPAT\fR with a suitable version value,
see \fBopenssl_user_macros\fR\|(7):
.PP
.Vb 1
\& const EVP_MD *EVP_MD_CTX_md(const EVP_MD_CTX *ctx);
\&
\& int (*EVP_MD_CTX_update_fn(EVP_MD_CTX *ctx))(EVP_MD_CTX *ctx,
\&                                              const void *data, size_t count);
\&
\& void EVP_MD_CTX_set_update_fn(EVP_MD_CTX *ctx,
\&                               int (*update)(EVP_MD_CTX *ctx,
\&                                             const void *data, size_t count));
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The EVP digest routines are a high-level interface to message digests,
and should be used instead of the digest-specific functions.
.PP
The \fBEVP_MD\fR type is a structure for digest method implementation.
.IP \fBEVP_MD_fetch()\fR 4
.IX Item "EVP_MD_fetch()"
Fetches the digest implementation for the given \fIalgorithm\fR from any
provider offering it, within the criteria given by the \fIproperties\fR.
See "ALGORITHM FETCHING" in \fBcrypto\fR\|(7) for further information.
.Sp
The returned value must eventually be freed with \fBEVP_MD_free()\fR.
.Sp
Fetched \fBEVP_MD\fR structures are reference counted.
.IP \fBEVP_MD_up_ref()\fR 4
.IX Item "EVP_MD_up_ref()"
Increments the reference count for an \fBEVP_MD\fR structure.
.IP \fBEVP_MD_free()\fR 4
.IX Item "EVP_MD_free()"
Decrements the reference count for the fetched \fBEVP_MD\fR structure.
If the reference count drops to 0 then the structure is freed.
.IP \fBEVP_MD_CTX_new()\fR 4
.IX Item "EVP_MD_CTX_new()"
Allocates and returns a digest context.
.IP \fBEVP_MD_CTX_reset()\fR 4
.IX Item "EVP_MD_CTX_reset()"
Resets the digest context \fIctx\fR.  This can be used to reuse an already
existing context.
.IP \fBEVP_MD_CTX_free()\fR 4
.IX Item "EVP_MD_CTX_free()"
Cleans up digest context \fIctx\fR and frees up the space allocated to it.
.IP \fBEVP_MD_CTX_ctrl()\fR 4
.IX Item "EVP_MD_CTX_ctrl()"
\&\fIThis is a legacy method. \fR\f(BIEVP_MD_CTX_set_params()\fR\fI and \fR\f(BIEVP_MD_CTX_get_params()\fR\fI
is the mechanism that should be used to set and get parameters that are used by
providers.\fR
.Sp
Performs digest-specific control actions on context \fIctx\fR. The control command
is indicated in \fIcmd\fR and any additional arguments in \fIp1\fR and \fIp2\fR.
\&\fBEVP_MD_CTX_ctrl()\fR must be called after \fBEVP_DigestInit_ex2()\fR. Other restrictions
may apply depending on the control type and digest implementation.
.Sp
If this function happens to be used with a fetched \fBEVP_MD\fR, it will
translate the controls that are known to OpenSSL into \fBOSSL_PARAM\fR\|(3)
parameters with keys defined by OpenSSL and call \fBEVP_MD_CTX_get_params()\fR or
\&\fBEVP_MD_CTX_set_params()\fR as is appropriate for each control command.
.Sp
See "CONTROLS" below for more information, including what translations are
being done.
.IP \fBEVP_MD_get_params()\fR 4
.IX Item "EVP_MD_get_params()"
Retrieves the requested list of \fIparams\fR from a MD \fImd\fR.
See "PARAMETERS" below for more information.
.IP \fBEVP_MD_CTX_get_params()\fR 4
.IX Item "EVP_MD_CTX_get_params()"
Retrieves the requested list of \fIparams\fR from a MD context \fIctx\fR.
See "PARAMETERS" below for more information.
.IP \fBEVP_MD_CTX_set_params()\fR 4
.IX Item "EVP_MD_CTX_set_params()"
Sets the list of \fIparams\fR into a MD context \fIctx\fR.
See "PARAMETERS" below for more information.
.IP \fBEVP_MD_gettable_params()\fR 4
.IX Item "EVP_MD_gettable_params()"
Get a constant \fBOSSL_PARAM\fR\|(3) array that describes the retrievable parameters
that can be used with \fBEVP_MD_get_params()\fR.
.IP "\fBEVP_MD_gettable_ctx_params()\fR, \fBEVP_MD_CTX_gettable_params()\fR" 4
.IX Item "EVP_MD_gettable_ctx_params(), EVP_MD_CTX_gettable_params()"
Get a constant \fBOSSL_PARAM\fR\|(3) array that describes the retrievable parameters
that can be used with \fBEVP_MD_CTX_get_params()\fR.  \fBEVP_MD_gettable_ctx_params()\fR
returns the parameters that can be retrieved from the algorithm, whereas
\&\fBEVP_MD_CTX_gettable_params()\fR returns the parameters that can be retrieved
in the context's current state.
.IP "\fBEVP_MD_settable_ctx_params()\fR, \fBEVP_MD_CTX_settable_params()\fR" 4
.IX Item "EVP_MD_settable_ctx_params(), EVP_MD_CTX_settable_params()"
Get a constant \fBOSSL_PARAM\fR\|(3) array that describes the settable parameters
that can be used with \fBEVP_MD_CTX_set_params()\fR.  \fBEVP_MD_settable_ctx_params()\fR
returns the parameters that can be set from the algorithm, whereas
\&\fBEVP_MD_CTX_settable_params()\fR returns the parameters that can be set in the
context's current state.
.IP "\fBEVP_MD_CTX_set_flags()\fR, \fBEVP_MD_CTX_clear_flags()\fR, \fBEVP_MD_CTX_test_flags()\fR" 4
.IX Item "EVP_MD_CTX_set_flags(), EVP_MD_CTX_clear_flags(), EVP_MD_CTX_test_flags()"
Sets, clears and tests \fIctx\fR flags.  See "FLAGS" below for more information.
.IP "\fBEVP_Q_digest()\fR is a quick one-shot digest function." 4
.IX Item "EVP_Q_digest() is a quick one-shot digest function."
It hashes \fIdatalen\fR bytes of data at \fIdata\fR using the digest algorithm
\&\fIname\fR, which is fetched using the optional \fIlibctx\fR and \fIpropq\fR parameters.
The digest value is placed in \fImd\fR and its length is written at \fImdlen\fR
if the pointer is not NULL. At most \fBEVP_MAX_MD_SIZE\fR bytes will be written.
.IP \fBEVP_Digest()\fR 4
.IX Item "EVP_Digest()"
A wrapper around the Digest Init_ex, Update and Final_ex functions.
Hashes \fIcount\fR bytes of data at \fIdata\fR using a digest \fItype\fR from ENGINE
\&\fIimpl\fR. The digest value is placed in \fImd\fR and its length is written at \fIsize\fR
if the pointer is not NULL. At most \fBEVP_MAX_MD_SIZE\fR bytes will be written.
If \fIimpl\fR is NULL the default implementation of digest \fItype\fR is used.
.IP \fBEVP_DigestInit_ex2()\fR 4
.IX Item "EVP_DigestInit_ex2()"
Sets up digest context \fIctx\fR to use a digest \fItype\fR.
\&\fItype\fR is typically supplied by a function such as \fBEVP_sha1()\fR, or a
value explicitly fetched with \fBEVP_MD_fetch()\fR.
.Sp
The parameters \fBparams\fR are set on the context after initialisation.
.Sp
The \fItype\fR parameter can be NULL if \fIctx\fR has been already initialized
with another \fBEVP_DigestInit_ex()\fR call and has not been reset with
\&\fBEVP_MD_CTX_reset()\fR.
.IP \fBEVP_DigestInit_ex()\fR 4
.IX Item "EVP_DigestInit_ex()"
Sets up digest context \fIctx\fR to use a digest \fItype\fR.
\&\fItype\fR is typically supplied by a function such as \fBEVP_sha1()\fR, or a
value explicitly fetched with \fBEVP_MD_fetch()\fR.
.Sp
If \fIimpl\fR is non-NULL, its implementation of the digest \fItype\fR is used if
there is one, and if not, the default implementation is used.
.Sp
The \fItype\fR parameter can be NULL if \fIctx\fR has been already initialized
with another \fBEVP_DigestInit_ex()\fR call and has not been reset with
\&\fBEVP_MD_CTX_reset()\fR.
.IP \fBEVP_DigestUpdate()\fR 4
.IX Item "EVP_DigestUpdate()"
Hashes \fIcnt\fR bytes of data at \fId\fR into the digest context \fIctx\fR. This
function can be called several times on the same \fIctx\fR to hash additional
data.
.IP \fBEVP_DigestFinal_ex()\fR 4
.IX Item "EVP_DigestFinal_ex()"
Retrieves the digest value from \fIctx\fR and places it in \fImd\fR. If the \fIs\fR
parameter is not NULL then the number of bytes of data written (i.e. the
length of the digest) will be written to the integer at \fIs\fR, at most
\&\fBEVP_MAX_MD_SIZE\fR bytes will be written.  After calling \fBEVP_DigestFinal_ex()\fR
no additional calls to \fBEVP_DigestUpdate()\fR can be made, but
\&\fBEVP_DigestInit_ex2()\fR can be called to initialize a new digest operation.
.IP \fBEVP_DigestFinalXOF()\fR 4
.IX Item "EVP_DigestFinalXOF()"
Interfaces to extendable-output functions, XOFs, such as SHAKE128 and SHAKE256.
It retrieves the digest value from \fIctx\fR and places it in \fIlen\fR\-sized \fImd\fR.
After calling this function no additional calls to \fBEVP_DigestUpdate()\fR can be
made, but \fBEVP_DigestInit_ex2()\fR can be called to initialize a new operation.
.IP \fBEVP_MD_CTX_copy_ex()\fR 4
.IX Item "EVP_MD_CTX_copy_ex()"
Can be used to copy the message digest state from \fIin\fR to \fIout\fR. This is
useful if large amounts of data are to be hashed which only differ in the last
few bytes.
.IP \fBEVP_DigestInit()\fR 4
.IX Item "EVP_DigestInit()"
Behaves in the same way as \fBEVP_DigestInit_ex2()\fR except it doesn't set any
parameters and calls \fBEVP_MD_CTX_reset()\fR so it cannot be used with an \fItype\fR
of NULL.
.IP \fBEVP_DigestFinal()\fR 4
.IX Item "EVP_DigestFinal()"
Similar to \fBEVP_DigestFinal_ex()\fR except after computing the digest
the digest context \fIctx\fR is automatically cleaned up with \fBEVP_MD_CTX_reset()\fR.
.IP \fBEVP_MD_CTX_copy()\fR 4
.IX Item "EVP_MD_CTX_copy()"
Similar to \fBEVP_MD_CTX_copy_ex()\fR except the destination \fIout\fR does not have to
be initialized.
.IP \fBEVP_MD_is_a()\fR 4
.IX Item "EVP_MD_is_a()"
Returns 1 if \fImd\fR is an implementation of an algorithm that's
identifiable with \fIname\fR, otherwise 0.
.Sp
If \fImd\fR is a legacy digest (it's the return value from the likes of
\&\fBEVP_sha256()\fR rather than the result of an \fBEVP_MD_fetch()\fR), only cipher
names registered with the default library context (see
\&\fBOSSL_LIB_CTX\fR\|(3)) will be considered.
.IP "\fBEVP_MD_get0_name()\fR, \fBEVP_MD_CTX_get0_name()\fR" 4
.IX Item "EVP_MD_get0_name(), EVP_MD_CTX_get0_name()"
Return the name of the given message digest.  For fetched message
digests with multiple names, only one of them is returned; it's
recommended to use \fBEVP_MD_names_do_all()\fR instead.
.IP \fBEVP_MD_names_do_all()\fR 4
.IX Item "EVP_MD_names_do_all()"
Traverses all names for the \fImd\fR, and calls \fIfn\fR with each name and
\&\fIdata\fR.  This is only useful with fetched \fBEVP_MD\fRs.
.IP \fBEVP_MD_get0_description()\fR 4
.IX Item "EVP_MD_get0_description()"
Returns a description of the digest, meant for display and human consumption.
The description is at the discretion of the digest implementation.
.IP \fBEVP_MD_get0_provider()\fR 4
.IX Item "EVP_MD_get0_provider()"
Returns an \fBOSSL_PROVIDER\fR pointer to the provider that implements the given
\&\fBEVP_MD\fR.
.IP "\fBEVP_MD_get_size()\fR, \fBEVP_MD_CTX_get_size()\fR" 4
.IX Item "EVP_MD_get_size(), EVP_MD_CTX_get_size()"
Return the size of the message digest when passed an \fBEVP_MD\fR or an
\&\fBEVP_MD_CTX\fR structure, i.e. the size of the hash.
.IP "\fBEVP_MD_get_block_size()\fR, \fBEVP_MD_CTX_get_block_size()\fR" 4
.IX Item "EVP_MD_get_block_size(), EVP_MD_CTX_get_block_size()"
Return the block size of the message digest when passed an \fBEVP_MD\fR or an
\&\fBEVP_MD_CTX\fR structure.
.IP "\fBEVP_MD_get_type()\fR, \fBEVP_MD_CTX_get_type()\fR" 4
.IX Item "EVP_MD_get_type(), EVP_MD_CTX_get_type()"
Return the NID of the OBJECT IDENTIFIER representing the given message digest
when passed an \fBEVP_MD\fR structure.  For example, \f(CW\*(C`EVP_MD_get_type(EVP_sha1())\*(C'\fR
returns \fBNID_sha1\fR. This function is normally used when setting ASN1 OIDs.
.IP \fBEVP_MD_CTX_get0_md_data()\fR 4
.IX Item "EVP_MD_CTX_get0_md_data()"
Return the digest method private data for the passed \fBEVP_MD_CTX\fR.
The space is allocated by OpenSSL and has the size originally set with
\&\fBEVP_MD_meth_set_app_datasize()\fR.
.IP "\fBEVP_MD_CTX_get0_md()\fR, \fBEVP_MD_CTX_get1_md()\fR" 4
.IX Item "EVP_MD_CTX_get0_md(), EVP_MD_CTX_get1_md()"
\&\fBEVP_MD_CTX_get0_md()\fR returns
the \fBEVP_MD\fR structure corresponding to the passed \fBEVP_MD_CTX\fR. This
will be the same \fBEVP_MD\fR object originally passed to \fBEVP_DigestInit_ex2()\fR (or
other similar function) when the EVP_MD_CTX was first initialised. Note that
where explicit fetch is in use (see \fBEVP_MD_fetch\fR\|(3)) the value returned from
this function will not have its reference count incremented and therefore it
should not be used after the EVP_MD_CTX is freed.
\&\fBEVP_MD_CTX_get1_md()\fR is the same except the ownership is passed to the
caller and is from the passed \fBEVP_MD_CTX\fR.
.IP \fBEVP_MD_CTX_set_update_fn()\fR 4
.IX Item "EVP_MD_CTX_set_update_fn()"
Sets the update function for \fIctx\fR to \fIupdate\fR.
This is the function that is called by \fBEVP_DigestUpdate()\fR. If not set, the
update function from the \fBEVP_MD\fR type specified at initialization is used.
.IP \fBEVP_MD_CTX_update_fn()\fR 4
.IX Item "EVP_MD_CTX_update_fn()"
Returns the update function for \fIctx\fR.
.IP \fBEVP_MD_get_flags()\fR 4
.IX Item "EVP_MD_get_flags()"
Returns the \fImd\fR flags. Note that these are different from the \fBEVP_MD_CTX\fR
ones. See \fBEVP_MD_meth_set_flags\fR\|(3) for more information.
.IP \fBEVP_MD_get_pkey_type()\fR 4
.IX Item "EVP_MD_get_pkey_type()"
Returns the NID of the public key signing algorithm associated with this
digest. For example \fBEVP_sha1()\fR is associated with RSA so this will return
\&\fBNID_sha1WithRSAEncryption\fR. Since digests and signature algorithms are no
longer linked this function is only retained for compatibility reasons.
.IP \fBEVP_md_null()\fR 4
.IX Item "EVP_md_null()"
A "null" message digest that does nothing: i.e. the hash it returns is of zero
length.
.IP "\fBEVP_get_digestbyname()\fR, \fBEVP_get_digestbynid()\fR, \fBEVP_get_digestbyobj()\fR" 4
.IX Item "EVP_get_digestbyname(), EVP_get_digestbynid(), EVP_get_digestbyobj()"
Returns an \fBEVP_MD\fR structure when passed a digest name, a digest \fBNID\fR or an
\&\fBASN1_OBJECT\fR structure respectively.
.Sp
The \fBEVP_get_digestbyname()\fR function is present for backwards compatibility with
OpenSSL prior to version 3 and is different to the \fBEVP_MD_fetch()\fR function
since it does not attempt to "fetch" an implementation of the cipher.
Additionally, it only knows about digests that are built-in to OpenSSL and have
an associated NID. Similarly \fBEVP_get_digestbynid()\fR and \fBEVP_get_digestbyobj()\fR
also return objects without an associated implementation.
.Sp
When the digest objects returned by these functions are used (such as in a call
to \fBEVP_DigestInit_ex()\fR) an implementation of the digest will be implicitly
fetched from the loaded providers. This fetch could fail if no suitable
implementation is available. Use \fBEVP_MD_fetch()\fR instead to explicitly fetch
the algorithm and an associated implementation from a provider.
.Sp
See "ALGORITHM FETCHING" in \fBcrypto\fR\|(7) for more information about fetching.
.Sp
The digest objects returned from these functions do not need to be freed with
\&\fBEVP_MD_free()\fR.
.IP \fBEVP_MD_CTX_get_pkey_ctx()\fR 4
.IX Item "EVP_MD_CTX_get_pkey_ctx()"
Returns the \fBEVP_PKEY_CTX\fR assigned to \fIctx\fR. The returned pointer should not
be freed by the caller.
.IP \fBEVP_MD_CTX_set_pkey_ctx()\fR 4
.IX Item "EVP_MD_CTX_set_pkey_ctx()"
Assigns an \fBEVP_PKEY_CTX\fR to \fBEVP_MD_CTX\fR. This is usually used to provide
a customized \fBEVP_PKEY_CTX\fR to \fBEVP_DigestSignInit\fR\|(3) or
\&\fBEVP_DigestVerifyInit\fR\|(3). The \fIpctx\fR passed to this function should be freed
by the caller. A NULL \fIpctx\fR pointer is also allowed to clear the \fBEVP_PKEY_CTX\fR
assigned to \fIctx\fR. In such case, freeing the cleared \fBEVP_PKEY_CTX\fR or not
depends on how the \fBEVP_PKEY_CTX\fR is created.
.IP \fBEVP_MD_do_all_provided()\fR 4
.IX Item "EVP_MD_do_all_provided()"
Traverses all messages digests implemented by all activated providers
in the given library context \fIlibctx\fR, and for each of the implementations,
calls the given function \fIfn\fR with the implementation method and the given
\&\fIarg\fR as argument.
.SH PARAMETERS
.IX Header "PARAMETERS"
See \fBOSSL_PARAM\fR\|(3) for information about passing parameters.
.PP
\&\fBEVP_MD_CTX_set_params()\fR can be used with the following OSSL_PARAM keys:
.IP """xoflen"" (\fBOSSL_DIGEST_PARAM_XOFLEN\fR) <unsigned integer>" 4
.IX Item """xoflen"" (OSSL_DIGEST_PARAM_XOFLEN) <unsigned integer>"
Sets the digest length for extendable output functions.
It is used by the SHAKE algorithm and should not exceed what can be given
using a \fBsize_t\fR.
.IP """pad-type"" (\fBOSSL_DIGEST_PARAM_PAD_TYPE\fR) <unsigned integer>" 4
.IX Item """pad-type"" (OSSL_DIGEST_PARAM_PAD_TYPE) <unsigned integer>"
Sets the padding type.
It is used by the MDC2 algorithm.
.PP
\&\fBEVP_MD_CTX_get_params()\fR can be used with the following OSSL_PARAM keys:
.IP """micalg"" (\fBOSSL_PARAM_DIGEST_KEY_MICALG\fR) <UTF8 string>." 4
.IX Item """micalg"" (OSSL_PARAM_DIGEST_KEY_MICALG) <UTF8 string>."
Gets the digest Message Integrity Check algorithm string. This is used when
creating S/MIME multipart/signed messages, as specified in RFC 3851.
It may be used by external engines or providers.
.SH CONTROLS
.IX Header "CONTROLS"
\&\fBEVP_MD_CTX_ctrl()\fR can be used to send the following standard controls:
.IP EVP_MD_CTRL_MICALG 4
.IX Item "EVP_MD_CTRL_MICALG"
Gets the digest Message Integrity Check algorithm string. This is used when
creating S/MIME multipart/signed messages, as specified in RFC 3851.
The string value is written to \fIp2\fR.
.Sp
When used with a fetched \fBEVP_MD\fR, \fBEVP_MD_CTX_get_params()\fR gets called with
an \fBOSSL_PARAM\fR\|(3) item with the key "micalg" (\fBOSSL_DIGEST_PARAM_MICALG\fR).
.IP EVP_MD_CTRL_XOF_LEN 4
.IX Item "EVP_MD_CTRL_XOF_LEN"
This control sets the digest length for extendable output functions to \fIp1\fR.
Sending this control directly should not be necessary, the use of
\&\fBEVP_DigestFinalXOF()\fR is preferred.
Currently used by SHAKE.
.Sp
When used with a fetched \fBEVP_MD\fR, \fBEVP_MD_CTX_get_params()\fR gets called with
an \fBOSSL_PARAM\fR\|(3) item with the key "xoflen" (\fBOSSL_DIGEST_PARAM_XOFLEN\fR).
.SH FLAGS
.IX Header "FLAGS"
\&\fBEVP_MD_CTX_set_flags()\fR, \fBEVP_MD_CTX_clear_flags()\fR and \fBEVP_MD_CTX_test_flags()\fR
can be used the manipulate and test these \fBEVP_MD_CTX\fR flags:
.IP EVP_MD_CTX_FLAG_ONESHOT 4
.IX Item "EVP_MD_CTX_FLAG_ONESHOT"
This flag instructs the digest to optimize for one update only, if possible.
.IP EVP_MD_CTX_FLAG_NO_INIT 4
.IX Item "EVP_MD_CTX_FLAG_NO_INIT"
This flag instructs \fBEVP_DigestInit()\fR and similar not to initialise the
implementation specific data.
.IP EVP_MD_CTX_FLAG_FINALISE 4
.IX Item "EVP_MD_CTX_FLAG_FINALISE"
Some functions such as EVP_DigestSign only finalise copies of internal
contexts so additional data can be included after the finalisation call.
This is inefficient if this functionality is not required, and can be
disabled with this flag.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
.IP \fBEVP_MD_fetch()\fR 4
.IX Item "EVP_MD_fetch()"
Returns a pointer to a \fBEVP_MD\fR for success or NULL for failure.
.IP \fBEVP_MD_up_ref()\fR 4
.IX Item "EVP_MD_up_ref()"
Returns 1 for success or 0 for failure.
.IP "\fBEVP_Q_digest()\fR, \fBEVP_Digest()\fR, \fBEVP_DigestInit_ex2()\fR, \fBEVP_DigestInit_ex()\fR, \fBEVP_DigestInit()\fR, \fBEVP_DigestUpdate()\fR, \fBEVP_DigestFinal_ex()\fR, \fBEVP_DigestFinalXOF()\fR, and \fBEVP_DigestFinal()\fR" 4
.IX Item "EVP_Q_digest(), EVP_Digest(), EVP_DigestInit_ex2(), EVP_DigestInit_ex(), EVP_DigestInit(), EVP_DigestUpdate(), EVP_DigestFinal_ex(), EVP_DigestFinalXOF(), and EVP_DigestFinal()"
return 1 for
success and 0 for failure.
.IP \fBEVP_MD_CTX_ctrl()\fR 4
.IX Item "EVP_MD_CTX_ctrl()"
Returns 1 if successful or 0 for failure.
.IP "\fBEVP_MD_CTX_set_params()\fR, \fBEVP_MD_CTX_get_params()\fR" 4
.IX Item "EVP_MD_CTX_set_params(), EVP_MD_CTX_get_params()"
Returns 1 if successful or 0 for failure.
.IP "\fBEVP_MD_CTX_settable_params()\fR, \fBEVP_MD_CTX_gettable_params()\fR" 4
.IX Item "EVP_MD_CTX_settable_params(), EVP_MD_CTX_gettable_params()"
Return an array of constant \fBOSSL_PARAM\fR\|(3)s, or NULL if there is none
to get.
.IP \fBEVP_MD_CTX_copy_ex()\fR 4
.IX Item "EVP_MD_CTX_copy_ex()"
Returns 1 if successful or 0 for failure.
.IP "\fBEVP_MD_get_type()\fR, \fBEVP_MD_get_pkey_type()\fR" 4
.IX Item "EVP_MD_get_type(), EVP_MD_get_pkey_type()"
Returns the NID of the corresponding OBJECT IDENTIFIER or NID_undef if none
exists.
.IP "\fBEVP_MD_get_size()\fR, \fBEVP_MD_get_block_size()\fR, \fBEVP_MD_CTX_get_size()\fR, \fBEVP_MD_CTX_get_block_size()\fR" 4
.IX Item "EVP_MD_get_size(), EVP_MD_get_block_size(), EVP_MD_CTX_get_size(), EVP_MD_CTX_get_block_size()"
Returns the digest or block size in bytes or \-1 for failure.
.IP \fBEVP_md_null()\fR 4
.IX Item "EVP_md_null()"
Returns a pointer to the \fBEVP_MD\fR structure of the "null" message digest.
.IP "\fBEVP_get_digestbyname()\fR, \fBEVP_get_digestbynid()\fR, \fBEVP_get_digestbyobj()\fR" 4
.IX Item "EVP_get_digestbyname(), EVP_get_digestbynid(), EVP_get_digestbyobj()"
Returns either an \fBEVP_MD\fR structure or NULL if an error occurs.
.IP \fBEVP_MD_CTX_set_pkey_ctx()\fR 4
.IX Item "EVP_MD_CTX_set_pkey_ctx()"
This function has no return value.
.IP \fBEVP_MD_names_do_all()\fR 4
.IX Item "EVP_MD_names_do_all()"
Returns 1 if the callback was called for all names. A return value of 0 means
that the callback was not called for any names.
.SH NOTES
.IX Header "NOTES"
The \fBEVP\fR interface to message digests should almost always be used in
preference to the low-level interfaces. This is because the code then becomes
transparent to the digest used and much more flexible.
.PP
New applications should use the SHA\-2 (such as \fBEVP_sha256\fR\|(3)) or the SHA\-3
digest algorithms (such as \fBEVP_sha3_512\fR\|(3)). The other digest algorithms
are still in common use.
.PP
For most applications the \fIimpl\fR parameter to \fBEVP_DigestInit_ex()\fR will be
set to NULL to use the default digest implementation.
.PP
Ignoring failure returns of \fBEVP_DigestInit_ex()\fR, \fBEVP_DigestInit_ex2()\fR, or
\&\fBEVP_DigestInit()\fR can lead to undefined behavior on subsequent calls
updating or finalizing the \fBEVP_MD_CTX\fR such as the \fBEVP_DigestUpdate()\fR or
\&\fBEVP_DigestFinal()\fR functions. The only valid calls on the \fBEVP_MD_CTX\fR
when initialization fails are calls that attempt another initialization of
the context or release the context.
.PP
The functions \fBEVP_DigestInit()\fR, \fBEVP_DigestFinal()\fR and \fBEVP_MD_CTX_copy()\fR are
obsolete but are retained to maintain compatibility with existing code. New
applications should use \fBEVP_DigestInit_ex()\fR, \fBEVP_DigestFinal_ex()\fR and
\&\fBEVP_MD_CTX_copy_ex()\fR because they can efficiently reuse a digest context
instead of initializing and cleaning it up on each call and allow non default
implementations of digests to be specified.
.PP
If digest contexts are not cleaned up after use,
memory leaks will occur.
.PP
\&\fBEVP_MD_CTX_get0_name()\fR, \fBEVP_MD_CTX_get_size()\fR, \fBEVP_MD_CTX_get_block_size()\fR,
\&\fBEVP_MD_CTX_get_type()\fR, \fBEVP_get_digestbynid()\fR and \fBEVP_get_digestbyobj()\fR are
defined as macros.
.PP
\&\fBEVP_MD_CTX_ctrl()\fR sends commands to message digests for additional configuration
or control.
.SH EXAMPLES
.IX Header "EXAMPLES"
This example digests the data "Test Message\en" and "Hello World\en", using the
digest name passed on the command line.
.PP
.Vb 3
\& #include <stdio.h>
\& #include <string.h>
\& #include <openssl/evp.h>
\&
\& int main(int argc, char *argv[])
\& {
\&     EVP_MD_CTX *mdctx;
\&     const EVP_MD *md;
\&     char mess1[] = "Test Message\en";
\&     char mess2[] = "Hello World\en";
\&     unsigned char md_value[EVP_MAX_MD_SIZE];
\&     unsigned int md_len, i;
\&
\&     if (argv[1] == NULL) {
\&         printf("Usage: mdtest digestname\en");
\&         exit(1);
\&     }
\&
\&     md = EVP_get_digestbyname(argv[1]);
\&     if (md == NULL) {
\&         printf("Unknown message digest %s\en", argv[1]);
\&         exit(1);
\&     }
\&
\&     mdctx = EVP_MD_CTX_new();
\&     if (!EVP_DigestInit_ex2(mdctx, md, NULL)) {
\&         printf("Message digest initialization failed.\en");
\&         EVP_MD_CTX_free(mdctx);
\&         exit(1);
\&     }
\&     if (!EVP_DigestUpdate(mdctx, mess1, strlen(mess1))) {
\&         printf("Message digest update failed.\en");
\&         EVP_MD_CTX_free(mdctx);
\&         exit(1);
\&     }
\&     if (!EVP_DigestUpdate(mdctx, mess2, strlen(mess2))) {
\&         printf("Message digest update failed.\en");
\&         EVP_MD_CTX_free(mdctx);
\&         exit(1);
\&     }
\&     if (!EVP_DigestFinal_ex(mdctx, md_value, &md_len)) {
\&         printf("Message digest finalization failed.\en");
\&         EVP_MD_CTX_free(mdctx);
\&         exit(1);
\&     }
\&     EVP_MD_CTX_free(mdctx);
\&
\&     printf("Digest is: ");
\&     for (i = 0; i < md_len; i++)
\&         printf("%02x", md_value[i]);
\&     printf("\en");
\&
\&     exit(0);
\& }
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_MD_meth_new\fR\|(3),
\&\fBopenssl\-dgst\fR\|(1),
\&\fBevp\fR\|(7),
\&\fBOSSL_PROVIDER\fR\|(3),
\&\fBOSSL_PARAM\fR\|(3),
\&\fBproperty\fR\|(7),
"ALGORITHM FETCHING" in \fBcrypto\fR\|(7),
\&\fBprovider\-digest\fR\|(7),
\&\fBlife_cycle\-digest\fR\|(7)
.PP
The full list of digest algorithms are provided below.
.PP
\&\fBEVP_blake2b512\fR\|(3),
\&\fBEVP_md2\fR\|(3),
\&\fBEVP_md4\fR\|(3),
\&\fBEVP_md5\fR\|(3),
\&\fBEVP_mdc2\fR\|(3),
\&\fBEVP_ripemd160\fR\|(3),
\&\fBEVP_sha1\fR\|(3),
\&\fBEVP_sha224\fR\|(3),
\&\fBEVP_sha3_224\fR\|(3),
\&\fBEVP_sm3\fR\|(3),
\&\fBEVP_whirlpool\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBEVP_MD_CTX_create()\fR and \fBEVP_MD_CTX_destroy()\fR functions were renamed to
\&\fBEVP_MD_CTX_new()\fR and \fBEVP_MD_CTX_free()\fR in OpenSSL 1.1.0, respectively.
.PP
The link between digests and signing algorithms was fixed in OpenSSL 1.0 and
later, so now \fBEVP_sha1()\fR can be used with RSA and DSA.
.PP
The \fBEVP_dss1()\fR function was removed in OpenSSL 1.1.0.
.PP
The \fBEVP_MD_CTX_set_pkey_ctx()\fR function was added in OpenSSL 1.1.1.
.PP
The \fBEVP_Q_digest()\fR, \fBEVP_DigestInit_ex2()\fR,
\&\fBEVP_MD_fetch()\fR, \fBEVP_MD_free()\fR, \fBEVP_MD_up_ref()\fR,
\&\fBEVP_MD_get_params()\fR, \fBEVP_MD_CTX_set_params()\fR, \fBEVP_MD_CTX_get_params()\fR,
\&\fBEVP_MD_gettable_params()\fR, \fBEVP_MD_gettable_ctx_params()\fR,
\&\fBEVP_MD_settable_ctx_params()\fR, \fBEVP_MD_CTX_settable_params()\fR and
\&\fBEVP_MD_CTX_gettable_params()\fR functions were added in OpenSSL 3.0.
.PP
The \fBEVP_MD_type()\fR, \fBEVP_MD_nid()\fR, \fBEVP_MD_name()\fR, \fBEVP_MD_pkey_type()\fR,
\&\fBEVP_MD_size()\fR, \fBEVP_MD_block_size()\fR, \fBEVP_MD_flags()\fR, \fBEVP_MD_CTX_size()\fR,
\&\fBEVP_MD_CTX_block_size()\fR, \fBEVP_MD_CTX_type()\fR, and \fBEVP_MD_CTX_md_data()\fR
functions were renamed to include \f(CW\*(C`get\*(C'\fR or \f(CW\*(C`get0\*(C'\fR in their names in
OpenSSL 3.0, respectively. The old names are kept as non-deprecated
alias macros.
.PP
The \fBEVP_MD_CTX_md()\fR function was deprecated in OpenSSL 3.0; use
\&\fBEVP_MD_CTX_get0_md()\fR instead.
\&\fBEVP_MD_CTX_update_fn()\fR and \fBEVP_MD_CTX_set_update_fn()\fR were deprecated
in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
