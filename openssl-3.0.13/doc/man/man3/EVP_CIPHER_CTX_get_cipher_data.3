.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_CIPHER_CTX_GET_CIPHER_DATA 3ossl"
.TH EVP_CIPHER_CTX_GET_CIPHER_DATA 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_CIPHER_CTX_get_cipher_data, EVP_CIPHER_CTX_set_cipher_data \- Routines to
inspect and modify EVP_CIPHER_CTX objects
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& void *EVP_CIPHER_CTX_get_cipher_data(const EVP_CIPHER_CTX *ctx);
\& void *EVP_CIPHER_CTX_set_cipher_data(EVP_CIPHER_CTX *ctx, void *cipher_data);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBEVP_CIPHER_CTX_get_cipher_data()\fR function returns a pointer to the cipher
data relevant to EVP_CIPHER_CTX. The contents of this data is specific to the
particular implementation of the cipher. For example this data can be used by
engines to store engine specific information. The data is automatically
allocated and freed by OpenSSL, so applications and engines should not normally
free this directly (but see below).
.PP
The \fBEVP_CIPHER_CTX_set_cipher_data()\fR function allows an application or engine to
replace the cipher data with new data. A pointer to any existing cipher data is
returned from this function. If the old data is no longer required then it
should be freed through a call to \fBOPENSSL_free()\fR.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
The \fBEVP_CIPHER_CTX_get_cipher_data()\fR function returns a pointer to the current
cipher data for the EVP_CIPHER_CTX.
.PP
The \fBEVP_CIPHER_CTX_set_cipher_data()\fR function returns a pointer to the old
cipher data for the EVP_CIPHER_CTX.
.SH HISTORY
.IX Header "HISTORY"
The \fBEVP_CIPHER_CTX_get_cipher_data()\fR and \fBEVP_CIPHER_CTX_set_cipher_data()\fR
functions were added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
