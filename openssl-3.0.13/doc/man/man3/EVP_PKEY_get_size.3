.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_GET_SIZE 3ossl"
.TH EVP_PKEY_GET_SIZE 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_get_size, EVP_PKEY_get_bits, EVP_PKEY_get_security_bits,
EVP_PKEY_bits, EVP_PKEY_security_bits, EVP_PKEY_size
\&\- EVP_PKEY information functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
\&
\& int EVP_PKEY_get_size(const EVP_PKEY *pkey);
\& int EVP_PKEY_get_bits(const EVP_PKEY *pkey);
\& int EVP_PKEY_get_security_bits(const EVP_PKEY *pkey);
\&
\& #define EVP_PKEY_bits EVP_PKEY_get_bits
\& #define EVP_PKEY_security_bits EVP_PKEY_get_security_bits
\& #define EVP_PKEY_size EVP_PKEY_get_size
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBEVP_PKEY_get_size()\fR returns the maximum suitable size for the output
buffers for almost all operations that can be done with \fIpkey\fR.
The primary documented use is with \fBEVP_SignFinal\fR\|(3) and
\&\fBEVP_SealInit\fR\|(3), but it isn't limited there.  The returned size is
also large enough for the output buffer of \fBEVP_PKEY_sign\fR\|(3),
\&\fBEVP_PKEY_encrypt\fR\|(3), \fBEVP_PKEY_decrypt\fR\|(3), \fBEVP_PKEY_derive\fR\|(3).
.PP
It must be stressed that, unless the documentation for the operation
that's being performed says otherwise, the size returned by
\&\fBEVP_PKEY_get_size()\fR is only preliminary and not exact, so the final
contents of the target buffer may be smaller.  It is therefore crucial
to take note of the size given back by the function that performs the
operation, such as \fBEVP_PKEY_sign\fR\|(3) (the \fIsiglen\fR argument will
receive that length), to avoid bugs.
.PP
\&\fBEVP_PKEY_get_bits()\fR returns the cryptographic length of the cryptosystem
to which the key in \fIpkey\fR belongs, in bits.  Note that the definition
of cryptographic length is specific to the key cryptosystem.
.PP
\&\fBEVP_PKEY_get_security_bits()\fR returns the number of security bits of the given
\&\fIpkey\fR, bits of security is defined in NIST SP800\-57.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_get_size()\fR, \fBEVP_PKEY_get_bits()\fR and \fBEVP_PKEY_get_security_bits()\fR
return a positive number, or 0 if this size isn't available.
.SH NOTES
.IX Header "NOTES"
Most functions that have an output buffer and are mentioned with
\&\fBEVP_PKEY_get_size()\fR have a functionality where you can pass NULL for the
buffer and still pass a pointer to an integer and get the exact size
that this function call delivers in the context that it's called in.
This allows those functions to be called twice, once to find out the
exact buffer size, then allocate the buffer in between, and call that
function again actually output the data.  For those functions, it
isn't strictly necessary to call \fBEVP_PKEY_get_size()\fR to find out the
buffer size, but may be useful in cases where it's desirable to know
the upper limit in advance.
.PP
It should also be especially noted that \fBEVP_PKEY_get_size()\fR shouldn't be
used to get the output size for \fBEVP_DigestSignFinal()\fR, according to
"NOTES" in \fBEVP_DigestSignFinal\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_SignFinal\fR\|(3),
\&\fBEVP_SealInit\fR\|(3),
\&\fBEVP_PKEY_sign\fR\|(3),
\&\fBEVP_PKEY_encrypt\fR\|(3),
\&\fBEVP_PKEY_decrypt\fR\|(3),
\&\fBEVP_PKEY_derive\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The \fBEVP_PKEY_bits()\fR, \fBEVP_PKEY_security_bits()\fR, and \fBEVP_PKEY_size()\fR functions
were renamed to include \f(CW\*(C`get\*(C'\fR in their names in OpenSSL 3.0, respectively.
The old names are kept as non-deprecated alias macros.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
