.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_TRACE_ENABLED 3ossl"
.TH OSSL_TRACE_ENABLED 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_trace_enabled, OSSL_trace_begin, OSSL_trace_end,
OSSL_TRACE_BEGIN, OSSL_TRACE_END, OSSL_TRACE_CANCEL,
OSSL_TRACE, OSSL_TRACE1, OSSL_TRACE2, OSSL_TRACE3, OSSL_TRACE4,
OSSL_TRACE5, OSSL_TRACE6, OSSL_TRACE7, OSSL_TRACE8, OSSL_TRACE9,
OSSL_TRACEV,
OSSL_TRACE_ENABLED
\&\- OpenSSL Tracing API
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/trace.h>
\&
\& int OSSL_trace_enabled(int category);
\&
\& BIO *OSSL_trace_begin(int category);
\& void OSSL_trace_end(int category, BIO *channel);
\&
\& /* trace group macros */
\& OSSL_TRACE_BEGIN(category) {
\&     ...
\&     if (some_error) {
\&         /* Leave trace group prematurely in case of an error */
\&         OSSL_TRACE_CANCEL(category);
\&         goto err;
\&     }
\&     ...
\& } OSSL_TRACE_END(category);
\&
\& /* one\-shot trace macros */
\& OSSL_TRACE1(category, format, arg1)
\& OSSL_TRACE2(category, format, arg1, arg2)
\& ...
\& OSSL_TRACE9(category, format, arg1, ..., arg9)
\&
\& /* check whether a trace category is enabled */
\& if (OSSL_TRACE_ENABLED(category)) {
\&     ...
\& }
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The functions described here are mainly interesting for those who provide
OpenSSL functionality, either in OpenSSL itself or in engine modules
or similar.
.PP
If tracing is enabled (see "NOTES" below), these functions are used to
generate free text tracing output.
.PP
The tracing output is divided into types which are enabled
individually by the application.
The tracing types are described in detail in
"Trace types" in \fBOSSL_trace_set_callback\fR\|(3).
The fallback type \fBOSSL_TRACE_CATEGORY_ALL\fR should \fInot\fR be used
with the functions described here.
.PP
Tracing for a specific category is enabled if a so called
\&\fItrace channel\fR is attached to it. A trace channel is simply a
BIO object to which the application can write its trace output.
.PP
The application has two different ways of registering a trace channel,
either by directly providing a BIO object using \fBOSSL_trace_set_channel()\fR,
or by providing a callback routine using \fBOSSL_trace_set_callback()\fR.
The latter is wrapped internally by a dedicated BIO object, so for the
tracing code both channel types are effectively indistinguishable.
We call them a \fIsimple trace channel\fR and a \fIcallback trace channel\fR,
respectively.
.PP
To produce trace output, it is necessary to obtain a pointer to the
trace channel (i.e., the BIO object) using \fBOSSL_trace_begin()\fR, write
to it using arbitrary BIO output routines, and finally releases the
channel using \fBOSSL_trace_end()\fR. The \fBOSSL_trace_begin()\fR/\fBOSSL_trace_end()\fR
calls surrounding the trace output create a group, which acts as a
critical section (guarded by a mutex) to ensure that the trace output
of different threads does not get mixed up.
.PP
The tracing code normally does not call OSSL_trace_{begin,end}() directly,
but rather uses a set of convenience macros, see the "Macros" section below.
.SS Functions
.IX Subsection "Functions"
\&\fBOSSL_trace_enabled()\fR can be used to check if tracing for the given
\&\fIcategory\fR is enabled.
.PP
\&\fBOSSL_trace_begin()\fR is used to starts a tracing section, and get the
channel for the given \fIcategory\fR in form of a BIO.
This BIO can only be used for output.
.PP
\&\fBOSSL_trace_end()\fR is used to end a tracing section.
.PP
Using \fBOSSL_trace_begin()\fR and \fBOSSL_trace_end()\fR to wrap tracing sections
is \fImandatory\fR.
The result of trying to produce tracing output outside of such
sections is undefined.
.SS Macros
.IX Subsection "Macros"
There are a number of convenience macros defined, to make tracing
easy and consistent.
.PP
\&\fBOSSL_TRACE_BEGIN()\fR and \fBOSSL_TRACE_END()\fR reserve the \fBBIO\fR \f(CW\*(C`trc_out\*(C'\fR and are
used as follows to wrap a trace section:
.PP
.Vb 1
\& OSSL_TRACE_BEGIN(TLS) {
\&
\&     BIO_fprintf(trc_out, ... );
\&
\& } OSSL_TRACE_END(TLS);
.Ve
.PP
This will normally expand to:
.PP
.Vb 8
\& do {
\&     BIO *trc_out = OSSL_trace_begin(OSSL_TRACE_CATEGORY_TLS);
\&     if (trc_out != NULL) {
\&         ...
\&         BIO_fprintf(trc_out, ...);
\&     }
\&     OSSL_trace_end(OSSL_TRACE_CATEGORY_TLS, trc_out);
\& } while (0);
.Ve
.PP
\&\fBOSSL_TRACE_CANCEL()\fR must be used before returning from or jumping out of a
trace section:
.PP
.Vb 1
\& OSSL_TRACE_BEGIN(TLS) {
\&
\&     if (some_error) {
\&         OSSL_TRACE_CANCEL(TLS);
\&         goto err;
\&     }
\&     BIO_fprintf(trc_out, ... );
\&
\& } OSSL_TRACE_END(TLS);
.Ve
.PP
This will normally expand to:
.PP
.Vb 11
\& do {
\&     BIO *trc_out = OSSL_trace_begin(OSSL_TRACE_CATEGORY_TLS);
\&     if (trc_out != NULL) {
\&         if (some_error) {
\&             OSSL_trace_end(OSSL_TRACE_CATEGORY_TLS, trc_out);
\&             goto err;
\&         }
\&         BIO_fprintf(trc_out, ... );
\&     }
\&     OSSL_trace_end(OSSL_TRACE_CATEGORY_TLS, trc_out);
\& } while (0);
.Ve
.PP
\&\fBOSSL_TRACE()\fR and \fBOSSL_TRACE1()\fR, \fBOSSL_TRACE2()\fR, ... \fBOSSL_TRACE9()\fR are
so-called one-shot macros:
.PP
The macro call \f(CW\*(C`OSSL_TRACE(category, text)\*(C'\fR, produces literal text trace output.
.PP
The macro call \f(CW\*(C`OSSL_TRACEn(category, format, arg1, ..., argn)\*(C'\fR produces
printf-style trace output with n format field arguments (n=1,...,9).
It expands to:
.PP
.Vb 3
\& OSSL_TRACE_BEGIN(category) {
\&     BIO_printf(trc_out, format, arg1, ..., argN)
\& } OSSL_TRACE_END(category)
.Ve
.PP
Internally, all one-shot macros are implemented using a generic \fBOSSL_TRACEV()\fR
macro, since C90 does not support variadic macros. This helper macro has a rather
weird synopsis and should not be used directly.
.PP
The \fBOSSL_TRACE_ENABLED()\fR macro can be used to conditionally execute some code
only if a specific trace category is enabled.
In some situations this is simpler than entering a trace section using
\&\fBOSSL_TRACE_BEGIN()\fR and \fBOSSL_TRACE_END()\fR.
For example, the code
.PP
.Vb 3
\& if (OSSL_TRACE_ENABLED(TLS)) {
\&     ...
\& }
.Ve
.PP
expands to
.PP
.Vb 3
\& if (OSSL_trace_enabled(OSSL_TRACE_CATEGORY_TLS) {
\&     ...
\& }
.Ve
.SH NOTES
.IX Header "NOTES"
If producing the trace output requires carrying out auxiliary calculations,
this auxiliary code should be placed inside a conditional block which is
executed only if the trace category is enabled.
.PP
The most natural way to do this is to place the code inside the trace section
itself because it already introduces such a conditional block.
.PP
.Vb 2
\& OSSL_TRACE_BEGIN(TLS) {
\&     int var = do_some_auxiliary_calculation();
\&
\&     BIO_printf(trc_out, "var = %d\en", var);
\&
\& } OSSL_TRACE_END(TLS);
.Ve
.PP
In some cases it is more advantageous to use a simple conditional group instead
of a trace section. This is the case if calculations and tracing happen in
different locations of the code, or if the calculations are so time consuming
that placing them inside a (critical) trace section would create too much
contention.
.PP
.Vb 2
\& if (OSSL_TRACE_ENABLED(TLS)) {
\&     int var = do_some_auxiliary_calculation();
\&
\&     OSSL_TRACE1("var = %d\en", var);
\& }
.Ve
.PP
Note however that premature optimization of tracing code is in general futile
and it's better to keep the tracing code as simple as possible.
Because most often the limiting factor for the application's speed is the time
it takes to print the trace output, not to calculate it.
.SS "Configure Tracing"
.IX Subsection "Configure Tracing"
By default, the OpenSSL library is built with tracing disabled. To
use the tracing functionality documented here, it is therefore
necessary to configure and build OpenSSL with the 'enable\-trace' option.
.PP
When the library is built with tracing disabled:
.IP \(bu 4
The macro \fBOPENSSL_NO_TRACE\fR is defined in \fI<openssl/opensslconf.h>\fR.
.IP \(bu 4
all functions are still present, but \fBOSSL_trace_enabled()\fR will always
report the categories as disabled, and all other functions will do
nothing.
.IP \(bu 4
the convenience macros are defined to produce dead code.
For example, take this example from "Macros" section above:
.Sp
.Vb 1
\& OSSL_TRACE_BEGIN(TLS) {
\&
\&     if (condition) {
\&         OSSL_TRACE_CANCEL(TLS);
\&         goto err;
\&     }
\&     BIO_fprintf(trc_out, ... );
\&
\& } OSSL_TRACE_END(TLS);
.Ve
.Sp
When the tracing API isn't operational, that will expand to:
.Sp
.Vb 10
\& do {
\&     BIO *trc_out = NULL;
\&     if (0) {
\&         if (condition) {
\&             ((void)0);
\&             goto err;
\&         }
\&         BIO_fprintf(trc_out, ... );
\&     }
\& } while (0);
.Ve
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_trace_enabled()\fR returns 1 if tracing for the given \fItype\fR is
operational and enabled, otherwise 0.
.PP
\&\fBOSSL_trace_begin()\fR returns a \fBBIO\fR pointer if the given \fItype\fR is enabled,
otherwise NULL.
.SH HISTORY
.IX Header "HISTORY"
The OpenSSL Tracing API was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
