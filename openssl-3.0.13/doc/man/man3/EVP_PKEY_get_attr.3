.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY_GET_ATTR 3ossl"
.TH EVP_PKEY_GET_ATTR 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY_get_attr,
EVP_PKEY_get_attr_count,
EVP_PKEY_get_attr_by_NID, EVP_PKEY_get_attr_by_OBJ,
EVP_PKEY_delete_attr,
EVP_PKEY_add1_attr,
EVP_PKEY_add1_attr_by_OBJ, EVP_PKEY_add1_attr_by_NID, EVP_PKEY_add1_attr_by_txt
\&\- EVP_PKEY X509_ATTRIBUTE functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/x509.h>
\&
\& int EVP_PKEY_get_attr_count(const EVP_PKEY *key);
\& int EVP_PKEY_get_attr_by_NID(const EVP_PKEY *key, int nid, int lastpos);
\& int EVP_PKEY_get_attr_by_OBJ(const EVP_PKEY *key, const ASN1_OBJECT *obj,
\&                              int lastpos);
\& X509_ATTRIBUTE *EVP_PKEY_get_attr(const EVP_PKEY *key, int loc);
\& X509_ATTRIBUTE *EVP_PKEY_delete_attr(EVP_PKEY *key, int loc);
\& int EVP_PKEY_add1_attr(EVP_PKEY *key, X509_ATTRIBUTE *attr);
\& int EVP_PKEY_add1_attr_by_OBJ(EVP_PKEY *key,
\&                               const ASN1_OBJECT *obj, int type,
\&                               const unsigned char *bytes, int len);
\& int EVP_PKEY_add1_attr_by_NID(EVP_PKEY *key,
\&                               int nid, int type,
\&                               const unsigned char *bytes, int len);
\& int EVP_PKEY_add1_attr_by_txt(EVP_PKEY *key,
\&                               const char *attrname, int type,
\&                               const unsigned char *bytes, int len);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
These functions are used by \fBPKCS12\fR.
.PP
\&\fBEVP_PKEY_get_attr_by_OBJ()\fR finds the location of the first matching object \fIobj\fR
in the \fIkey\fR attribute list. The search starts at the position after \fIlastpos\fR.
If the returned value is positive then it can be used on the next call to
\&\fBEVP_PKEY_get_attr_by_OBJ()\fR as the value of \fIlastpos\fR in order to iterate through
the remaining attributes. \fIlastpos\fR can be set to any negative value on the
first call, in order to start searching from the start of the attribute list.
.PP
\&\fBEVP_PKEY_get_attr_by_NID()\fR is similar to \fBEVP_PKEY_get_attr_by_OBJ()\fR except that
it passes the numerical identifier (NID) \fInid\fR associated with the object.
See <openssl/obj_mac.h> for a list of NID_*.
.PP
\&\fBEVP_PKEY_get_attr()\fR returns the \fBX509_ATTRIBUTE\fR object at index \fIloc\fR in the
\&\fIkey\fR attribute list. \fIloc\fR should be in the range from 0 to
\&\fBEVP_PKEY_get_attr_count()\fR \- 1.
.PP
\&\fBEVP_PKEY_delete_attr()\fR removes the \fBX509_ATTRIBUTE\fR object at index \fIloc\fR in
the \fIkey\fR attribute list.
.PP
\&\fBEVP_PKEY_add1_attr()\fR pushes a copy of the passed in \fBX509_ATTRIBUTE\fR object
to the \fIkey\fR attribute list. A new \fIkey\fR attribute list is created if required.
An error occurs if either \fIattr\fR is NULL, or the attribute already exists.
.PP
\&\fBEVP_PKEY_add1_attr_by_OBJ()\fR creates a new \fBX509_ATTRIBUTE\fR using
\&\fBX509_ATTRIBUTE_set1_object()\fR and \fBX509_ATTRIBUTE_set1_data()\fR to assign a new
\&\fIobj\fR with type \fItype\fR and data \fIbytes\fR of length \fIlen\fR and then pushes it
to the \fIkey\fR object's attribute list. If \fIobj\fR already exists in the attribute
list then an error occurs.
.PP
\&\fBEVP_PKEY_add1_attr_by_NID()\fR is similar to \fBEVP_PKEY_add1_attr_by_OBJ()\fR except
that it passes the numerical identifier (NID) \fInid\fR associated with the object.
See <openssl/obj_mac.h> for a list of NID_*.
.PP
\&\fBEVP_PKEY_add1_attr_by_txt()\fR is similar to \fBEVP_PKEY_add1_attr_by_OBJ()\fR except
that it passes a name \fIattrname\fR associated with the object.
See <openssl/obj_mac.h> for a list of SN_* names.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBEVP_PKEY_get_attr_count()\fR returns the number of attributes in the \fIkey\fR object
attribute list or \-1 if the attribute list is NULL.
.PP
\&\fBEVP_PKEY_get_attr_by_OBJ()\fR returns \-1 if either the list is empty OR the object
is not found, otherwise it returns the location of the object in the list.
.PP
\&\fBEVP_PKEY_get_attr_by_NID()\fR is similar to \fBEVP_PKEY_get_attr_by_OBJ()\fR, except that
it returns \-2 if the \fInid\fR is not known by OpenSSL.
.PP
\&\fBEVP_PKEY_get_attr()\fR returns either a \fBX509_ATTRIBUTE\fR or NULL if there is a
error.
.PP
\&\fBEVP_PKEY_delete_attr()\fR returns either the removed \fBX509_ATTRIBUTE\fR or NULL if
there is a error.
.PP
\&\fBEVP_PKEY_add1_attr()\fR, \fBEVP_PKEY_add1_attr_by_OBJ()\fR, \fBEVP_PKEY_add1_attr_by_NID()\fR
and \fBEVP_PKEY_add1_attr_by_txt()\fR return 1 on success or 0 otherwise.
.SH NOTES
.IX Header "NOTES"
A \fBEVP_PKEY\fR object's attribute list is initially NULL. All the above functions
listed will return an error unless \fBEVP_PKEY_add1_attr()\fR is called.
All functions listed assume that the \fIkey\fR is not NULL.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBX509_ATTRIBUTE\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2023\-2024 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
