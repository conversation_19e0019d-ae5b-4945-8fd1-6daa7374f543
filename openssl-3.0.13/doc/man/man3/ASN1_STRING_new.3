.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "ASN1_STRING_NEW 3ossl"
.TH ASN1_STRING_NEW 3ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ASN1_STRING_new, ASN1_STRING_type_new, ASN1_STRING_free \-
ASN1_STRING allocation functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/asn1.h>
\&
\& ASN1_STRING *ASN1_STRING_new(void);
\& ASN1_STRING *ASN1_STRING_type_new(int type);
\& void ASN1_STRING_free(ASN1_STRING *a);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fBASN1_STRING_new()\fR returns an allocated \fBASN1_STRING\fR structure. Its type
is undefined.
.PP
\&\fBASN1_STRING_type_new()\fR returns an allocated \fBASN1_STRING\fR structure of
type \fItype\fR.
.PP
\&\fBASN1_STRING_free()\fR frees up \fIa\fR.
If \fIa\fR is NULL nothing is done.
.SH NOTES
.IX Header "NOTES"
Other string types call the \fBASN1_STRING\fR functions. For example
\&\fBASN1_OCTET_STRING_new()\fR calls ASN1_STRING_type_new(V_ASN1_OCTET_STRING).
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBASN1_STRING_new()\fR and \fBASN1_STRING_type_new()\fR return a valid
\&\fBASN1_STRING\fR structure or NULL if an error occurred.
.PP
\&\fBASN1_STRING_free()\fR does not return a value.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBERR_get_error\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2002\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
