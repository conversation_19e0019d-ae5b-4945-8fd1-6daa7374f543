.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_CIPHER-AES 7ossl"
.TH EVP_CIPHER-AES 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_CIPHER\-AES \- The AES EVP_CIPHER implementations
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Support for AES symmetric encryption using the \fBEVP_CIPHER\fR API.
.SS "Algorithm Names"
.IX Subsection "Algorithm Names"
The following algorithms are available in the FIPS provider as well as the
default provider:
.IP """AES\-128\-CBC"", ""AES\-192\-CBC"" and  ""AES\-256\-CBC""" 4
.IX Item """AES-128-CBC"", ""AES-192-CBC"" and ""AES-256-CBC"""
.PD 0
.IP """AES\-128\-CBC\-CTS"", ""AES\-192\-CBC\-CTS"" and ""AES\-256\-CBC\-CTS""" 4
.IX Item """AES-128-CBC-CTS"", ""AES-192-CBC-CTS"" and ""AES-256-CBC-CTS"""
.IP """AES\-128\-CFB"", ""AES\-192\-CFB"", ""AES\-256\-CFB"", ""AES\-128\-CFB1"", ""AES\-192\-CFB1"", ""AES\-256\-CFB1"", ""AES\-128\-CFB8"", ""AES\-192\-CFB8"" and ""AES\-256\-CFB8""" 4
.IX Item """AES-128-CFB"", ""AES-192-CFB"", ""AES-256-CFB"", ""AES-128-CFB1"", ""AES-192-CFB1"", ""AES-256-CFB1"", ""AES-128-CFB8"", ""AES-192-CFB8"" and ""AES-256-CFB8"""
.IP """AES\-128\-CTR"", ""AES\-192\-CTR"" and ""AES\-256\-CTR""" 4
.IX Item """AES-128-CTR"", ""AES-192-CTR"" and ""AES-256-CTR"""
.IP """AES\-128\-ECB"", ""AES\-192\-ECB"" and ""AES\-256\-ECB""" 4
.IX Item """AES-128-ECB"", ""AES-192-ECB"" and ""AES-256-ECB"""
.IP """AES\-192\-OFB"", ""AES\-128\-OFB"" and ""AES\-256\-OFB""" 4
.IX Item """AES-192-OFB"", ""AES-128-OFB"" and ""AES-256-OFB"""
.IP """AES\-128\-XTS"" and ""AES\-256\-XTS""" 4
.IX Item """AES-128-XTS"" and ""AES-256-XTS"""
.IP """AES\-128\-CCM"", ""AES\-192\-CCM"" and ""AES\-256\-CCM""" 4
.IX Item """AES-128-CCM"", ""AES-192-CCM"" and ""AES-256-CCM"""
.IP """AES\-128\-GCM"", ""AES\-192\-GCM"" and ""AES\-256\-GCM""" 4
.IX Item """AES-128-GCM"", ""AES-192-GCM"" and ""AES-256-GCM"""
.IP """AES\-128\-WRAP"", ""AES\-192\-WRAP"", ""AES\-256\-WRAP"", ""AES\-128\-WRAP\-PAD"", ""AES\-192\-WRAP\-PAD"", ""AES\-256\-WRAP\-PAD"", ""AES\-128\-WRAP\-INV"", ""AES\-192\-WRAP\-INV"", ""AES\-256\-WRAP\-INV"", ""AES\-128\-WRAP\-PAD\-INV"", ""AES\-192\-WRAP\-PAD\-INV"" and ""AES\-256\-WRAP\-PAD\-INV""" 4
.IX Item """AES-128-WRAP"", ""AES-192-WRAP"", ""AES-256-WRAP"", ""AES-128-WRAP-PAD"", ""AES-192-WRAP-PAD"", ""AES-256-WRAP-PAD"", ""AES-128-WRAP-INV"", ""AES-192-WRAP-INV"", ""AES-256-WRAP-INV"", ""AES-128-WRAP-PAD-INV"", ""AES-192-WRAP-PAD-INV"" and ""AES-256-WRAP-PAD-INV"""
.IP """AES\-128\-CBC\-HMAC\-SHA1"", ""AES\-256\-CBC\-HMAC\-SHA1"", ""AES\-128\-CBC\-HMAC\-SHA256"" and ""AES\-256\-CBC\-HMAC\-SHA256""" 4
.IX Item """AES-128-CBC-HMAC-SHA1"", ""AES-256-CBC-HMAC-SHA1"", ""AES-128-CBC-HMAC-SHA256"" and ""AES-256-CBC-HMAC-SHA256"""
.PD
.PP
The following algorithms are available in the default provider, but not the
FIPS provider:
.IP """AES\-128\-OCB"", ""AES\-192\-OCB"" and ""AES\-256\-OCB""" 4
.IX Item """AES-128-OCB"", ""AES-192-OCB"" and ""AES-256-OCB"""
.PD 0
.IP """AES\-128\-SIV"", ""AES\-192\-SIV"" and ""AES\-256\-SIV""" 4
.IX Item """AES-128-SIV"", ""AES-192-SIV"" and ""AES-256-SIV"""
.PD
.SS Parameters
.IX Subsection "Parameters"
This implementation supports the parameters described in
"PARAMETERS" in \fBEVP_EncryptInit\fR\|(3).
.SH NOTES
.IX Header "NOTES"
The AES-SIV and AES-WRAP mode implementations do not support streaming. That
means to obtain correct results there can be only one \fBEVP_EncryptUpdate\fR\|(3)
or \fBEVP_DecryptUpdate\fR\|(3) call after the initialization of the context.
.PP
The AES-XTS implementations allow streaming to be performed, but each
\&\fBEVP_EncryptUpdate\fR\|(3) or \fBEVP_DecryptUpdate\fR\|(3) call requires each input
to be a multiple of the blocksize. Only the final \fBEVP_EncryptUpdate()\fR or
\&\fBEVP_DecryptUpdate()\fR call can optionally have an input that is not a multiple
of the blocksize but is larger than one block. In that case ciphertext
stealing (CTS) is used to fill the block.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\-cipher\fR\|(7), \fBOSSL_PROVIDER\-FIPS\fR\|(7), \fBOSSL_PROVIDER\-default\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
