.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "RAND 7ossl"
.TH RAND 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
RAND
\&\- the OpenSSL random generator
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Random numbers are a vital part of cryptography, they are needed to provide
unpredictability for tasks like key generation, creating salts, and many more.
Software-based generators must be seeded with external randomness before they
can be used as a cryptographically-secure pseudo-random number generator
(CSPRNG).
The availability of common hardware with special instructions and
modern operating systems, which may use items such as interrupt jitter
and network packet timings, can be reasonable sources of seeding material.
.PP
OpenSSL comes with a default implementation of the RAND API which is based on
the deterministic random bit generator (DRBG) model as described in
[NIST SP 800\-90A Rev. 1]. The default random generator will initialize
automatically on first use and will be fully functional without having
to be initialized ('seeded') explicitly.
It seeds and reseeds itself automatically using trusted random sources
provided by the operating system.
.PP
As a normal application developer, you do not have to worry about any details,
just use \fBRAND_bytes\fR\|(3) to obtain random data.
Having said that, there is one important rule to obey: Always check the error
return value of \fBRAND_bytes\fR\|(3) and do not take randomness for granted.
Although (re\-)seeding is automatic, it can fail because no trusted random source
is available or the trusted source(s) temporarily fail to provide sufficient
random seed material.
In this case the CSPRNG enters an error state and ceases to provide output,
until it is able to recover from the error by reseeding itself.
For more details on reseeding and error recovery, see \fBEVP_RAND\fR\|(7).
.PP
For values that should remain secret, you can use \fBRAND_priv_bytes\fR\|(3)
instead.
This method does not provide 'better' randomness, it uses the same type of
CSPRNG.
The intention behind using a dedicated CSPRNG exclusively for private
values is that none of its output should be visible to an attacker (e.g.,
used as salt value), in order to reveal as little information as
possible about its internal state, and that a compromise of the "public"
CSPRNG instance will not affect the secrecy of these private values.
.PP
In the rare case where the default implementation does not satisfy your special
requirements, the default RAND internals can be replaced by your own
\&\fBEVP_RAND\fR\|(3) objects.
.PP
Changing the default random generator should be necessary
only in exceptional cases and is not recommended, unless you have a profound
knowledge of cryptographic principles and understand the implications of your
changes.
.SH "DEFAULT SETUP"
.IX Header "DEFAULT SETUP"
The default OpenSSL RAND method is based on the EVP_RAND deterministic random
bit generator (DRBG) classes.
A DRBG is a certain type of cryptographically-secure pseudo-random
number generator (CSPRNG), which is described in [NIST SP 800\-90A Rev. 1].
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBRAND_bytes\fR\|(3),
\&\fBRAND_priv_bytes\fR\|(3),
\&\fBEVP_RAND\fR\|(3),
\&\fBRAND_get0_primary\fR\|(3),
\&\fBEVP_RAND\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
