.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PROVIDER-SIGNATURE 7ossl"
.TH PROVIDER-SIGNATURE 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
provider\-signature \- The signature library <\-> provider functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 2
\& #include <openssl/core_dispatch.h>
\& #include <openssl/core_names.h>
\&
\& /*
\&  * None of these are actual functions, but are displayed like this for
\&  * the function signatures for functions that are offered as function
\&  * pointers in OSSL_DISPATCH arrays.
\&  */
\&
\& /* Context management */
\& void *OSSL_FUNC_signature_newctx(void *provctx, const char *propq);
\& void OSSL_FUNC_signature_freectx(void *ctx);
\& void *OSSL_FUNC_signature_dupctx(void *ctx);
\&
\& /* Signing */
\& int OSSL_FUNC_signature_sign_init(void *ctx, void *provkey,
\&                                   const OSSL_PARAM params[]);
\& int OSSL_FUNC_signature_sign(void *ctx, unsigned char *sig, size_t *siglen,
\&                              size_t sigsize, const unsigned char *tbs, size_t tbslen);
\&
\& /* Verifying */
\& int OSSL_FUNC_signature_verify_init(void *ctx, void *provkey,
\&                                     const OSSL_PARAM params[]);
\& int OSSL_FUNC_signature_verify(void *ctx, const unsigned char *sig, size_t siglen,
\&                                const unsigned char *tbs, size_t tbslen);
\&
\& /* Verify Recover */
\& int OSSL_FUNC_signature_verify_recover_init(void *ctx, void *provkey,
\&                                             const OSSL_PARAM params[]);
\& int OSSL_FUNC_signature_verify_recover(void *ctx, unsigned char *rout,
\&                                        size_t *routlen, size_t routsize,
\&                                        const unsigned char *sig, size_t siglen);
\&
\& /* Digest Sign */
\& int OSSL_FUNC_signature_digest_sign_init(void *ctx, const char *mdname,
\&                                          void *provkey,
\&                                          const OSSL_PARAM params[]);
\& int OSSL_FUNC_signature_digest_sign_update(void *ctx, const unsigned char *data,
\&                                     size_t datalen);
\& int OSSL_FUNC_signature_digest_sign_final(void *ctx, unsigned char *sig,
\&                                           size_t *siglen, size_t sigsize);
\& int OSSL_FUNC_signature_digest_sign(void *ctx,
\&                              unsigned char *sigret, size_t *siglen,
\&                              size_t sigsize, const unsigned char *tbs,
\&                              size_t tbslen);
\&
\& /* Digest Verify */
\& int OSSL_FUNC_signature_digest_verify_init(void *ctx, const char *mdname,
\&                                            void *provkey,
\&                                            const OSSL_PARAM params[]);
\& int OSSL_FUNC_signature_digest_verify_update(void *ctx,
\&                                              const unsigned char *data,
\&                                              size_t datalen);
\& int OSSL_FUNC_signature_digest_verify_final(void *ctx, const unsigned char *sig,
\&                                      size_t siglen);
\& int OSSL_FUNC_signature_digest_verify(void *ctx, const unsigned char *sig,
\&                                size_t siglen, const unsigned char *tbs,
\&                                size_t tbslen);
\&
\& /* Signature parameters */
\& int OSSL_FUNC_signature_get_ctx_params(void *ctx, OSSL_PARAM params[]);
\& const OSSL_PARAM *OSSL_FUNC_signature_gettable_ctx_params(void *ctx,
\&                                                           void *provctx);
\& int OSSL_FUNC_signature_set_ctx_params(void *ctx, const OSSL_PARAM params[]);
\& const OSSL_PARAM *OSSL_FUNC_signature_settable_ctx_params(void *ctx,
\&                                                           void *provctx);
\& /* MD parameters */
\& int OSSL_FUNC_signature_get_ctx_md_params(void *ctx, OSSL_PARAM params[]);
\& const OSSL_PARAM * OSSL_FUNC_signature_gettable_ctx_md_params(void *ctx);
\& int OSSL_FUNC_signature_set_ctx_md_params(void *ctx, const OSSL_PARAM params[]);
\& const OSSL_PARAM * OSSL_FUNC_signature_settable_ctx_md_params(void *ctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This documentation is primarily aimed at provider authors. See \fBprovider\fR\|(7)
for further information.
.PP
The signature (OSSL_OP_SIGNATURE) operation enables providers to implement
signature algorithms and make them available to applications via the API
functions \fBEVP_PKEY_sign\fR\|(3),
\&\fBEVP_PKEY_verify\fR\|(3),
and \fBEVP_PKEY_verify_recover\fR\|(3) (as well
as other related functions).
.PP
All "functions" mentioned here are passed as function pointers between
\&\fIlibcrypto\fR and the provider in \fBOSSL_DISPATCH\fR\|(3) arrays via
\&\fBOSSL_ALGORITHM\fR\|(3) arrays that are returned by the provider's
\&\fBprovider_query_operation()\fR function
(see "Provider Functions" in \fBprovider\-base\fR\|(7)).
.PP
All these "functions" have a corresponding function type definition
named \fBOSSL_FUNC_{name}_fn\fR, and a helper function to retrieve the
function pointer from an \fBOSSL_DISPATCH\fR\|(3) element named
\&\fBOSSL_FUNC_{name}\fR.
For example, the "function" \fBOSSL_FUNC_signature_newctx()\fR has these:
.PP
.Vb 3
\& typedef void *(OSSL_FUNC_signature_newctx_fn)(void *provctx, const char *propq);
\& static ossl_inline OSSL_FUNC_signature_newctx_fn
\&     OSSL_FUNC_signature_newctx(const OSSL_DISPATCH *opf);
.Ve
.PP
\&\fBOSSL_DISPATCH\fR\|(3) arrays are indexed by numbers that are provided as
macros in \fBopenssl\-core_dispatch.h\fR\|(7), as follows:
.PP
.Vb 3
\& OSSL_FUNC_signature_newctx                 OSSL_FUNC_SIGNATURE_NEWCTX
\& OSSL_FUNC_signature_freectx                OSSL_FUNC_SIGNATURE_FREECTX
\& OSSL_FUNC_signature_dupctx                 OSSL_FUNC_SIGNATURE_DUPCTX
\&
\& OSSL_FUNC_signature_sign_init              OSSL_FUNC_SIGNATURE_SIGN_INIT
\& OSSL_FUNC_signature_sign                   OSSL_FUNC_SIGNATURE_SIGN
\&
\& OSSL_FUNC_signature_verify_init            OSSL_FUNC_SIGNATURE_VERIFY_INIT
\& OSSL_FUNC_signature_verify                 OSSL_FUNC_SIGNATURE_VERIFY
\&
\& OSSL_FUNC_signature_verify_recover_init    OSSL_FUNC_SIGNATURE_VERIFY_RECOVER_INIT
\& OSSL_FUNC_signature_verify_recover         OSSL_FUNC_SIGNATURE_VERIFY_RECOVER
\&
\& OSSL_FUNC_signature_digest_sign_init       OSSL_FUNC_SIGNATURE_DIGEST_SIGN_INIT
\& OSSL_FUNC_signature_digest_sign_update     OSSL_FUNC_SIGNATURE_DIGEST_SIGN_UPDATE
\& OSSL_FUNC_signature_digest_sign_final      OSSL_FUNC_SIGNATURE_DIGEST_SIGN_FINAL
\& OSSL_FUNC_signature_digest_sign            OSSL_FUNC_SIGNATURE_DIGEST_SIGN
\&
\& OSSL_FUNC_signature_digest_verify_init     OSSL_FUNC_SIGNATURE_DIGEST_VERIFY_INIT
\& OSSL_FUNC_signature_digest_verify_update   OSSL_FUNC_SIGNATURE_DIGEST_VERIFY_UPDATE
\& OSSL_FUNC_signature_digest_verify_final    OSSL_FUNC_SIGNATURE_DIGEST_VERIFY_FINAL
\& OSSL_FUNC_signature_digest_verify          OSSL_FUNC_SIGNATURE_DIGEST_VERIFY
\&
\& OSSL_FUNC_signature_get_ctx_params         OSSL_FUNC_SIGNATURE_GET_CTX_PARAMS
\& OSSL_FUNC_signature_gettable_ctx_params    OSSL_FUNC_SIGNATURE_GETTABLE_CTX_PARAMS
\& OSSL_FUNC_signature_set_ctx_params         OSSL_FUNC_SIGNATURE_SET_CTX_PARAMS
\& OSSL_FUNC_signature_settable_ctx_params    OSSL_FUNC_SIGNATURE_SETTABLE_CTX_PARAMS
\&
\& OSSL_FUNC_signature_get_ctx_md_params      OSSL_FUNC_SIGNATURE_GET_CTX_MD_PARAMS
\& OSSL_FUNC_signature_gettable_ctx_md_params OSSL_FUNC_SIGNATURE_GETTABLE_CTX_MD_PARAMS
\& OSSL_FUNC_signature_set_ctx_md_params      OSSL_FUNC_SIGNATURE_SET_CTX_MD_PARAMS
\& OSSL_FUNC_signature_settable_ctx_md_params OSSL_FUNC_SIGNATURE_SETTABLE_CTX_MD_PARAMS
.Ve
.PP
A signature algorithm implementation may not implement all of these functions.
In order to be a consistent set of functions we must have at least a set of
context functions (OSSL_FUNC_signature_newctx and OSSL_FUNC_signature_freectx) as well as a
set of "signature" functions, i.e. at least one of:
.IP "OSSL_FUNC_signature_sign_init and OSSL_FUNC_signature_sign" 4
.IX Item "OSSL_FUNC_signature_sign_init and OSSL_FUNC_signature_sign"
.PD 0
.IP "OSSL_FUNC_signature_verify_init and OSSL_FUNC_signature_verify" 4
.IX Item "OSSL_FUNC_signature_verify_init and OSSL_FUNC_signature_verify"
.IP "OSSL_FUNC_signature_verify_recover_init and OSSL_FUNC_signature_verify_recover" 4
.IX Item "OSSL_FUNC_signature_verify_recover_init and OSSL_FUNC_signature_verify_recover"
.IP "OSSL_FUNC_signature_digest_sign_init, OSSL_FUNC_signature_digest_sign_update and OSSL_FUNC_signature_digest_sign_final" 4
.IX Item "OSSL_FUNC_signature_digest_sign_init, OSSL_FUNC_signature_digest_sign_update and OSSL_FUNC_signature_digest_sign_final"
.IP "OSSL_FUNC_signature_digest_verify_init, OSSL_FUNC_signature_digest_verify_update and OSSL_FUNC_signature_digest_verify_final" 4
.IX Item "OSSL_FUNC_signature_digest_verify_init, OSSL_FUNC_signature_digest_verify_update and OSSL_FUNC_signature_digest_verify_final"
.IP "OSSL_FUNC_signature_digest_sign_init and OSSL_FUNC_signature_digest_sign" 4
.IX Item "OSSL_FUNC_signature_digest_sign_init and OSSL_FUNC_signature_digest_sign"
.IP "OSSL_FUNC_signature_digest_verify_init and OSSL_FUNC_signature_digest_verify" 4
.IX Item "OSSL_FUNC_signature_digest_verify_init and OSSL_FUNC_signature_digest_verify"
.PD
.PP
OSSL_FUNC_signature_set_ctx_params and OSSL_FUNC_signature_settable_ctx_params are optional,
but if one of them is present then the other one must also be present. The same
applies to OSSL_FUNC_signature_get_ctx_params and OSSL_FUNC_signature_gettable_ctx_params, as
well as the "md_params" functions. The OSSL_FUNC_signature_dupctx function is optional.
.PP
A signature algorithm must also implement some mechanism for generating,
loading or importing keys via the key management (OSSL_OP_KEYMGMT) operation.
See \fBprovider\-keymgmt\fR\|(7) for further details.
.SS "Context Management Functions"
.IX Subsection "Context Management Functions"
\&\fBOSSL_FUNC_signature_newctx()\fR should create and return a pointer to a provider side
structure for holding context information during a signature operation.
A pointer to this context will be passed back in a number of the other signature
operation function calls.
The parameter \fIprovctx\fR is the provider context generated during provider
initialisation (see \fBprovider\fR\|(7)). The \fIpropq\fR parameter is a property query
string that may be (optionally) used by the provider during any "fetches" that
it may perform (if it performs any).
.PP
\&\fBOSSL_FUNC_signature_freectx()\fR is passed a pointer to the provider side signature
context in the \fIctx\fR parameter.
This function should free any resources associated with that context.
.PP
\&\fBOSSL_FUNC_signature_dupctx()\fR should duplicate the provider side signature context in
the \fIctx\fR parameter and return the duplicate copy.
.SS "Signing Functions"
.IX Subsection "Signing Functions"
\&\fBOSSL_FUNC_signature_sign_init()\fR initialises a context for signing given a provider side
signature context in the \fIctx\fR parameter, and a pointer to a provider key object
in the \fIprovkey\fR parameter.
The \fIparams\fR, if not NULL, should be set on the context in a manner similar to
using \fBOSSL_FUNC_signature_set_ctx_params()\fR.
The key object should have been previously generated, loaded or imported into
the provider using the key management (OSSL_OP_KEYMGMT) operation (see
\&\fBprovider\-keymgmt\fR\|(7)>.
.PP
\&\fBOSSL_FUNC_signature_sign()\fR performs the actual signing itself.
A previously initialised signature context is passed in the \fIctx\fR
parameter.
The data to be signed is pointed to be the \fItbs\fR parameter which is \fItbslen\fR
bytes long.
Unless \fIsig\fR is NULL, the signature should be written to the location pointed
to by the \fIsig\fR parameter and it should not exceed \fIsigsize\fR bytes in length.
The length of the signature should be written to \fI*siglen\fR.
If \fIsig\fR is NULL then the maximum length of the signature should be written to
\&\fI*siglen\fR.
.SS "Verify Functions"
.IX Subsection "Verify Functions"
\&\fBOSSL_FUNC_signature_verify_init()\fR initialises a context for verifying a signature given
a provider side signature context in the \fIctx\fR parameter, and a pointer to a
provider key object in the \fIprovkey\fR parameter.
The \fIparams\fR, if not NULL, should be set on the context in a manner similar to
using \fBOSSL_FUNC_signature_set_ctx_params()\fR.
The key object should have been previously generated, loaded or imported into
the provider using the key management (OSSL_OP_KEYMGMT) operation (see
\&\fBprovider\-keymgmt\fR\|(7)>.
.PP
\&\fBOSSL_FUNC_signature_verify()\fR performs the actual verification itself.
A previously initialised signature context is passed in the \fIctx\fR parameter.
The data that the signature covers is pointed to be the \fItbs\fR parameter which
is \fItbslen\fR bytes long.
The signature is pointed to by the \fIsig\fR parameter which is \fIsiglen\fR bytes
long.
.SS "Verify Recover Functions"
.IX Subsection "Verify Recover Functions"
\&\fBOSSL_FUNC_signature_verify_recover_init()\fR initialises a context for recovering the
signed data given a provider side signature context in the \fIctx\fR parameter, and
a pointer to a provider key object in the \fIprovkey\fR parameter.
The \fIparams\fR, if not NULL, should be set on the context in a manner similar to
using \fBOSSL_FUNC_signature_set_ctx_params()\fR.
The key object should have been previously generated, loaded or imported into
the provider using the key management (OSSL_OP_KEYMGMT) operation (see
\&\fBprovider\-keymgmt\fR\|(7)>.
.PP
\&\fBOSSL_FUNC_signature_verify_recover()\fR performs the actual verify recover itself.
A previously initialised signature context is passed in the \fIctx\fR parameter.
The signature is pointed to by the \fIsig\fR parameter which is \fIsiglen\fR bytes
long.
Unless \fIrout\fR is NULL, the recovered data should be written to the location
pointed to by \fIrout\fR which should not exceed \fIroutsize\fR bytes in length.
The length of the recovered data should be written to \fI*routlen\fR.
If \fIrout\fR is NULL then the maximum size of the output buffer is written to
the \fIroutlen\fR parameter.
.SS "Digest Sign Functions"
.IX Subsection "Digest Sign Functions"
\&\fBOSSL_FUNC_signature_digeset_sign_init()\fR initialises a context for signing given a
provider side signature context in the \fIctx\fR parameter, and a pointer to a
provider key object in the \fIprovkey\fR parameter.
The \fIparams\fR, if not NULL, should be set on the context in a manner similar to
using \fBOSSL_FUNC_signature_set_ctx_params()\fR and
\&\fBOSSL_FUNC_signature_set_ctx_md_params()\fR.
The key object should have been
previously generated, loaded or imported into the provider using the
key management (OSSL_OP_KEYMGMT) operation (see \fBprovider\-keymgmt\fR\|(7)>.
The name of the digest to be used will be in the \fImdname\fR parameter.
.PP
\&\fBOSSL_FUNC_signature_digest_sign_update()\fR provides data to be signed in the \fIdata\fR
parameter which should be of length \fIdatalen\fR. A previously initialised
signature context is passed in the \fIctx\fR parameter. This function may be called
multiple times to cumulatively add data to be signed.
.PP
\&\fBOSSL_FUNC_signature_digest_sign_final()\fR finalises a signature operation previously
started through \fBOSSL_FUNC_signature_digest_sign_init()\fR and
\&\fBOSSL_FUNC_signature_digest_sign_update()\fR calls. Once finalised no more data will be
added through \fBOSSL_FUNC_signature_digest_sign_update()\fR. A previously initialised
signature context is passed in the \fIctx\fR parameter. Unless \fIsig\fR is NULL, the
signature should be written to the location pointed to by the \fIsig\fR parameter
and it should not exceed \fIsigsize\fR bytes in length. The length of the signature
should be written to \fI*siglen\fR. If \fIsig\fR is NULL then the maximum length of
the signature should be written to \fI*siglen\fR.
.PP
\&\fBOSSL_FUNC_signature_digest_sign()\fR implements a "one shot" digest sign operation
previously started through \fBOSSL_FUNC_signature_digeset_sign_init()\fR. A previously
initialised signature context is passed in the \fIctx\fR parameter. The data to be
signed is in \fItbs\fR which should be \fItbslen\fR bytes long. Unless \fIsig\fR is NULL,
the signature should be written to the location pointed to by the \fIsig\fR
parameter and it should not exceed \fIsigsize\fR bytes in length. The length of the
signature should be written to \fI*siglen\fR. If \fIsig\fR is NULL then the maximum
length of the signature should be written to \fI*siglen\fR.
.SS "Digest Verify Functions"
.IX Subsection "Digest Verify Functions"
\&\fBOSSL_FUNC_signature_digeset_verify_init()\fR initialises a context for verifying given a
provider side verification context in the \fIctx\fR parameter, and a pointer to a
provider key object in the \fIprovkey\fR parameter.
The \fIparams\fR, if not NULL, should be set on the context in a manner similar to
\&\fBOSSL_FUNC_signature_set_ctx_params()\fR and
\&\fBOSSL_FUNC_signature_set_ctx_md_params()\fR.
The key object should have been
previously generated, loaded or imported into the provider using the
key management (OSSL_OP_KEYMGMT) operation (see \fBprovider\-keymgmt\fR\|(7)>.
The name of the digest to be used will be in the \fImdname\fR parameter.
.PP
\&\fBOSSL_FUNC_signature_digest_verify_update()\fR provides data to be verified in the \fIdata\fR
parameter which should be of length \fIdatalen\fR. A previously initialised
verification context is passed in the \fIctx\fR parameter. This function may be
called multiple times to cumulatively add data to be verified.
.PP
\&\fBOSSL_FUNC_signature_digest_verify_final()\fR finalises a verification operation previously
started through \fBOSSL_FUNC_signature_digest_verify_init()\fR and
\&\fBOSSL_FUNC_signature_digest_verify_update()\fR calls. Once finalised no more data will be
added through \fBOSSL_FUNC_signature_digest_verify_update()\fR. A previously initialised
verification context is passed in the \fIctx\fR parameter. The signature to be
verified is in \fIsig\fR which is \fIsiglen\fR bytes long.
.PP
\&\fBOSSL_FUNC_signature_digest_verify()\fR implements a "one shot" digest verify operation
previously started through \fBOSSL_FUNC_signature_digeset_verify_init()\fR. A previously
initialised verification context is passed in the \fIctx\fR parameter. The data to be
verified is in \fItbs\fR which should be \fItbslen\fR bytes long. The signature to be
verified is in \fIsig\fR which is \fIsiglen\fR bytes long.
.SS "Signature parameters"
.IX Subsection "Signature parameters"
See \fBOSSL_PARAM\fR\|(3) for further details on the parameters structure used by
the \fBOSSL_FUNC_signature_get_ctx_params()\fR and \fBOSSL_FUNC_signature_set_ctx_params()\fR functions.
.PP
\&\fBOSSL_FUNC_signature_get_ctx_params()\fR gets signature parameters associated with the
given provider side signature context \fIctx\fR and stored them in \fIparams\fR.
Passing NULL for \fIparams\fR should return true.
.PP
\&\fBOSSL_FUNC_signature_set_ctx_params()\fR sets the signature parameters associated with the
given provider side signature context \fIctx\fR to \fIparams\fR.
Any parameter settings are additional to any that were previously set.
Passing NULL for \fIparams\fR should return true.
.PP
Common parameters currently recognised by built-in signature algorithms are as
follows.
.IP """digest"" (\fBOSSL_SIGNATURE_PARAM_DIGEST\fR) <UTF8 string>" 4
.IX Item """digest"" (OSSL_SIGNATURE_PARAM_DIGEST) <UTF8 string>"
Get or sets the name of the digest algorithm used for the input to the
signature functions. It is required in order to calculate the "algorithm-id".
.IP """properties"" (\fBOSSL_SIGNATURE_PARAM_PROPERTIES\fR) <UTF8 string>" 4
.IX Item """properties"" (OSSL_SIGNATURE_PARAM_PROPERTIES) <UTF8 string>"
Sets the name of the property query associated with the "digest" algorithm.
NULL is used if this optional value is not set.
.IP """digest-size"" (\fBOSSL_SIGNATURE_PARAM_DIGEST_SIZE\fR) <unsigned integer>" 4
.IX Item """digest-size"" (OSSL_SIGNATURE_PARAM_DIGEST_SIZE) <unsigned integer>"
Gets or sets the output size of the digest algorithm used for the input to the
signature functions.
The length of the "digest-size" parameter should not exceed that of a \fBsize_t\fR.
.IP """algorithm-id"" (\fBOSSL_SIGNATURE_PARAM_ALGORITHM_ID\fR) <octet string>" 4
.IX Item """algorithm-id"" (OSSL_SIGNATURE_PARAM_ALGORITHM_ID) <octet string>"
Gets the DER encoded AlgorithmIdentifier that corresponds to the combination of
signature algorithm and digest algorithm for the signature operation.
.IP """kat"" (\fBOSSL_SIGNATURE_PARAM_KAT\fR) <unsigned integer>" 4
.IX Item """kat"" (OSSL_SIGNATURE_PARAM_KAT) <unsigned integer>"
Sets a flag to modify the sign operation to return an error if the initial
calculated signature is invalid.
In the normal mode of operation \- new random values are chosen until the
signature operation succeeds.
By default it retries until a signature is calculated.
Setting the value to 0 causes the sign operation to retry,
otherwise the sign operation is only tried once and returns whether or not it
was successful.
Known answer tests can be performed if the random generator is overridden to
supply known values that either pass or fail.
.PP
\&\fBOSSL_FUNC_signature_gettable_ctx_params()\fR and \fBOSSL_FUNC_signature_settable_ctx_params()\fR get a
constant \fBOSSL_PARAM\fR\|(3) array that describes the gettable and settable parameters,
i.e. parameters that can be used with \fBOSSL_FUNC_signature_get_ctx_params()\fR and
\&\fBOSSL_FUNC_signature_set_ctx_params()\fR respectively.
.SS "MD parameters"
.IX Subsection "MD parameters"
See \fBOSSL_PARAM\fR\|(3) for further details on the parameters structure used by
the \fBOSSL_FUNC_signature_get_md_ctx_params()\fR and \fBOSSL_FUNC_signature_set_md_ctx_params()\fR
functions.
.PP
\&\fBOSSL_FUNC_signature_get_md_ctx_params()\fR gets digest parameters associated with the
given provider side digest signature context \fIctx\fR and stores them in \fIparams\fR.
Passing NULL for \fIparams\fR should return true.
.PP
\&\fBOSSL_FUNC_signature_set_ms_ctx_params()\fR sets the digest parameters associated with the
given provider side digest signature context \fIctx\fR to \fIparams\fR.
Any parameter settings are additional to any that were previously set.
Passing NULL for \fIparams\fR should return true.
.PP
Parameters currently recognised by built-in signature algorithms are the same
as those for built-in digest algorithms. See
"Digest Parameters" in \fBprovider\-digest\fR\|(7) for further information.
.PP
\&\fBOSSL_FUNC_signature_gettable_md_ctx_params()\fR and \fBOSSL_FUNC_signature_settable_md_ctx_params()\fR
get a constant \fBOSSL_PARAM\fR\|(3) array that describes the gettable and settable
digest parameters, i.e. parameters that can be used with
\&\fBOSSL_FUNC_signature_get_md_ctx_params()\fR and \fBOSSL_FUNC_signature_set_md_ctx_params()\fR
respectively.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_FUNC_signature_newctx()\fR and \fBOSSL_FUNC_signature_dupctx()\fR should return the newly created
provider side signature context, or NULL on failure.
.PP
\&\fBOSSL_FUNC_signature_gettable_ctx_params()\fR, \fBOSSL_FUNC_signature_settable_ctx_params()\fR,
\&\fBOSSL_FUNC_signature_gettable_md_ctx_params()\fR and \fBOSSL_FUNC_signature_settable_md_ctx_params()\fR,
return the gettable or settable parameters in a constant \fBOSSL_PARAM\fR\|(3) array.
.PP
All other functions should return 1 for success or 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The provider SIGNATURE interface was introduced in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
