.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_CIPHER-ARIA 7ossl"
.TH EVP_CIPHER-ARIA 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_CIPHER\-ARIA \- The ARIA EVP_CIPHER implementations
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Support for ARIA symmetric encryption using the \fBEVP_CIPHER\fR API.
.SS "Algorithm Names"
.IX Subsection "Algorithm Names"
The following algorithms are available in the default provider:
.IP """ARIA\-128\-CBC"", ""ARIA\-192\-CBC"" and  ""ARIA\-256\-CBC""" 4
.IX Item """ARIA-128-CBC"", ""ARIA-192-CBC"" and ""ARIA-256-CBC"""
.PD 0
.IP """ARIA\-128\-CFB"", ""ARIA\-192\-CFB"", ""ARIA\-256\-CFB"", ""ARIA\-128\-CFB1"", ""ARIA\-192\-CFB1"", ""ARIA\-256\-CFB1"", ""ARIA\-128\-CFB8"", ""ARIA\-192\-CFB8"" and ""ARIA\-256\-CFB8""" 4
.IX Item """ARIA-128-CFB"", ""ARIA-192-CFB"", ""ARIA-256-CFB"", ""ARIA-128-CFB1"", ""ARIA-192-CFB1"", ""ARIA-256-CFB1"", ""ARIA-128-CFB8"", ""ARIA-192-CFB8"" and ""ARIA-256-CFB8"""
.IP """ARIA\-128\-CTR"", ""ARIA\-192\-CTR"" and ""ARIA\-256\-CTR""" 4
.IX Item """ARIA-128-CTR"", ""ARIA-192-CTR"" and ""ARIA-256-CTR"""
.IP """ARIA\-128\-ECB"", ""ARIA\-192\-ECB"" and ""ARIA\-256\-ECB""" 4
.IX Item """ARIA-128-ECB"", ""ARIA-192-ECB"" and ""ARIA-256-ECB"""
.IP """AES\-192\-OCB"", ""AES\-128\-OCB"" and ""AES\-256\-OCB""" 4
.IX Item """AES-192-OCB"", ""AES-128-OCB"" and ""AES-256-OCB"""
.IP """ARIA\-128\-OFB"", ""ARIA\-192\-OFB"" and ""ARIA\-256\-OFB""" 4
.IX Item """ARIA-128-OFB"", ""ARIA-192-OFB"" and ""ARIA-256-OFB"""
.IP """ARIA\-128\-CCM"", ""ARIA\-192\-CCM"" and ""ARIA\-256\-CCM""" 4
.IX Item """ARIA-128-CCM"", ""ARIA-192-CCM"" and ""ARIA-256-CCM"""
.IP """ARIA\-128\-GCM"", ""ARIA\-192\-GCM"" and ""ARIA\-256\-GCM""" 4
.IX Item """ARIA-128-GCM"", ""ARIA-192-GCM"" and ""ARIA-256-GCM"""
.PD
.SS Parameters
.IX Subsection "Parameters"
This implementation supports the parameters described in
"PARAMETERS" in \fBEVP_EncryptInit\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\-cipher\fR\|(7), \fBOSSL_PROVIDER\-default\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
