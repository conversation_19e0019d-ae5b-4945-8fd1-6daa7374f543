.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_MD-SHAKE 7ossl"
.TH EVP_MD-SHAKE 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_MD\-SHAKE, EVP_MD\-KECCAK\-KMAC
\&\- The SHAKE / KECCAK family EVP_MD implementations
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Support for computing SHAKE or KECCAK-KMAC digests through the
\&\fBEVP_MD\fR API.
.PP
KECCAK-KMAC is an Extendable Output Function (XOF), with a definition
similar to SHAKE, used by the KMAC EVP_MAC implementation (see
\&\fBEVP_MAC\-KMAC\fR\|(7)).
.SS Identities
.IX Subsection "Identities"
This implementation is available in the FIPS provider as well as the default
provider, and includes the following varieties:
.IP KECCAK\-KMAC\-128 4
.IX Item "KECCAK-KMAC-128"
Known names are "KECCAK\-KMAC\-128" and "KECCAK\-KMAC128".  This is used
by \fBEVP_MAC\-KMAC128\fR\|(7).  Using the notation from NIST FIPS 202
(Section 6.2), we have KECCAK\-KMAC\-128(M,\ d) = KECCAK[256](M\ ||\ 00,\ d)
(see the description of KMAC128 in Appendix A of NIST SP 800\-185).
.IP KECCAK\-KMAC\-256 4
.IX Item "KECCAK-KMAC-256"
Known names are "KECCAK\-KMAC\-256" and "KECCAK\-KMAC256".  This is used
by \fBEVP_MAC\-KMAC256\fR\|(7).  Using the notation from NIST FIPS 202
(Section 6.2), we have KECCAK\-KMAC\-256(M,\ d) = KECCAK[512](M\ ||\ 00,\ d)
(see the description of KMAC256 in Appendix A of NIST SP 800\-185).
.IP SHAKE\-128 4
.IX Item "SHAKE-128"
Known names are "SHAKE\-128" and "SHAKE128".
.IP SHAKE\-256 4
.IX Item "SHAKE-256"
Known names are "SHAKE\-256" and "SHAKE256".
.SS "Gettable Parameters"
.IX Subsection "Gettable Parameters"
This implementation supports the common gettable parameters described
in \fBEVP_MD\-common\fR\|(7).
.SS "Settable Context Parameters"
.IX Subsection "Settable Context Parameters"
These implementations support the following \fBOSSL_PARAM\fR\|(3) entries,
settable for an \fBEVP_MD_CTX\fR with \fBEVP_MD_CTX_set_params\fR\|(3):
.IP """xoflen"" (\fBOSSL_DIGEST_PARAM_XOFLEN\fR) <unsigned integer>" 4
.IX Item """xoflen"" (OSSL_DIGEST_PARAM_XOFLEN) <unsigned integer>"
Sets the digest length for extendable output functions.
The length of the "xoflen" parameter should not exceed that of a \fBsize_t\fR.
.Sp
For backwards compatibility reasons the default xoflen length for SHAKE\-128 is
16 (bytes) which results in a security strength of only 64 bits. To ensure the
maximum security strength of 128 bits, the xoflen should be set to at least 32.
.Sp
For backwards compatibility reasons the default xoflen length for SHAKE\-256 is
32 (bytes) which results in a security strength of only 128 bits. To ensure the
maximum security strength of 256 bits, the xoflen should be set to at least 64.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_MD_CTX_set_params\fR\|(3), \fBprovider\-digest\fR\|(7), \fBOSSL_PROVIDER\-default\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
