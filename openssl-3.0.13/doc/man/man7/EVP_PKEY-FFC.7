.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY-FFC 7ossl"
.TH EVP_PKEY-FFC 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY\-FFC \- EVP_PKEY DSA and DH/DHX shared FFC parameters.
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Finite field cryptography (FFC) is a method of implementing discrete logarithm
cryptography using finite field mathematics. DSA is an example of FFC and
Diffie-Hellman key establishment algorithms specified in SP800\-56A can also be
implemented as FFC.
.PP
The \fBDSA\fR, \fBDH\fR and \fBDHX\fR keytypes are implemented in OpenSSL's default and
FIPS providers.
The implementations support the basic DSA, DH and DHX keys, containing the public
and private keys \fIpub\fR and \fIpriv\fR as well as the three main domain parameters
\&\fIp\fR, \fIq\fR and \fIg\fR.
.PP
For \fBDSA\fR (and \fBDH\fR that is not a named group) the FIPS186\-4 standard
specifies that the values used for FFC parameter generation are also required
for parameter validation.
This means that optional FFC domain parameter values for \fIseed\fR, \fIpcounter\fR
and \fIgindex\fR may need to be stored for validation purposes.
For \fBDH\fR the \fIseed\fR and \fIpcounter\fR can be stored in ASN1 data
(but the \fIgindex\fR is not). For \fBDSA\fR however, these fields are not stored in
the ASN1 data so they need to be stored externally if validation is required.
.PP
The \fBDH\fR key type uses PKCS#3 format which saves p and g, but not the 'q' value.
The \fBDHX\fR key type uses X9.42 format which saves the value of 'q' and this
must be used for FIPS186\-4.
.SS "FFC parameters"
.IX Subsection "FFC parameters"
In addition to the common parameters that all keytypes should support (see
"Common parameters" in \fBprovider\-keymgmt\fR\|(7)), the \fBDSA\fR, \fBDH\fR and \fBDHX\fR keytype
implementations support the following.
.IP """pub"" (\fBOSSL_PKEY_PARAM_PUB_KEY\fR) <unsigned integer>" 4
.IX Item """pub"" (OSSL_PKEY_PARAM_PUB_KEY) <unsigned integer>"
The public key value.
.IP """priv"" (\fBOSSL_PKEY_PARAM_PRIV_KEY\fR) <unsigned integer>" 4
.IX Item """priv"" (OSSL_PKEY_PARAM_PRIV_KEY) <unsigned integer>"
The private key value.
.SS "FFC DSA, DH and DHX domain parameters"
.IX Subsection "FFC DSA, DH and DHX domain parameters"
.IP """p"" (\fBOSSL_PKEY_PARAM_FFC_P\fR) <unsigned integer>" 4
.IX Item """p"" (OSSL_PKEY_PARAM_FFC_P) <unsigned integer>"
A DSA or Diffie-Hellman prime "p" value.
.IP """g"" (\fBOSSL_PKEY_PARAM_FFC_G\fR) <unsigned integer>" 4
.IX Item """g"" (OSSL_PKEY_PARAM_FFC_G) <unsigned integer>"
A DSA or Diffie-Hellman generator "g" value.
.SS "FFC DSA and DHX domain parameters"
.IX Subsection "FFC DSA and DHX domain parameters"
.IP """q"" (\fBOSSL_PKEY_PARAM_FFC_Q\fR) <unsigned integer>" 4
.IX Item """q"" (OSSL_PKEY_PARAM_FFC_Q) <unsigned integer>"
A DSA or Diffie-Hellman prime "q" value.
.IP """seed"" (\fBOSSL_PKEY_PARAM_FFC_SEED\fR) <octet string>" 4
.IX Item """seed"" (OSSL_PKEY_PARAM_FFC_SEED) <octet string>"
An optional domain parameter \fIseed\fR value used during generation and validation
of \fIp\fR, \fIq\fR and canonical \fIg\fR.
For validation this needs to set the \fIseed\fR that was produced during generation.
.IP """gindex"" (\fBOSSL_PKEY_PARAM_FFC_GINDEX\fR) <integer>" 4
.IX Item """gindex"" (OSSL_PKEY_PARAM_FFC_GINDEX) <integer>"
Sets the index to use for canonical generation and verification of the generator
\&\fIg\fR.
Set this to a positive value from 0..FF to use this mode. This \fIgindex\fR can
then be reused during key validation to verify the value of \fIg\fR. If this value
is not set or is \-1 then unverifiable generation of the generator \fIg\fR will be
used.
.IP """pcounter"" (\fBOSSL_PKEY_PARAM_FFC_PCOUNTER\fR) <integer>" 4
.IX Item """pcounter"" (OSSL_PKEY_PARAM_FFC_PCOUNTER) <integer>"
An optional domain parameter \fIcounter\fR value that is output during generation
of \fIp\fR. This value must be saved if domain parameter validation is required.
.IP """hindex"" (\fBOSSL_PKEY_PARAM_FFC_H\fR) <integer>" 4
.IX Item """hindex"" (OSSL_PKEY_PARAM_FFC_H) <integer>"
For unverifiable generation of the generator \fIg\fR this value is output during
generation of \fIg\fR. Its value is the first integer larger than one that
satisfies g = h^j mod p (where g != 1 and "j" is the cofactor).
.IP """j"" (\fBOSSL_PKEY_PARAM_FFC_COFACTOR\fR) <unsigned integer>" 4
.IX Item """j"" (OSSL_PKEY_PARAM_FFC_COFACTOR) <unsigned integer>"
An optional informational cofactor parameter that should equal to (p \- 1) / q.
.IP """validate-pq"" (\fBOSSL_PKEY_PARAM_FFC_VALIDATE_PQ\fR) <unsigned integer>" 4
.IX Item """validate-pq"" (OSSL_PKEY_PARAM_FFC_VALIDATE_PQ) <unsigned integer>"
.PD 0
.IP """validate-g"" (\fBOSSL_PKEY_PARAM_FFC_VALIDATE_G\fR) <unsigned integer>" 4
.IX Item """validate-g"" (OSSL_PKEY_PARAM_FFC_VALIDATE_G) <unsigned integer>"
.PD
These boolean values are used during FIPS186\-4 or FIPS186\-2 key validation checks
(See \fBEVP_PKEY_param_check\fR\|(3)) to select validation options. By default
\&\fIvalidate-pq\fR and \fIvalidate-g\fR are both set to 1 to check that p,q and g are
valid. Either of these may be set to 0 to skip a test, which is mainly useful
for testing purposes.
.IP """validate-legacy"" (\fBOSSL_PKEY_PARAM_FFC_VALIDATE_LEGACY\fR) <unsigned integer>" 4
.IX Item """validate-legacy"" (OSSL_PKEY_PARAM_FFC_VALIDATE_LEGACY) <unsigned integer>"
This boolean value is used during key validation checks
(See \fBEVP_PKEY_param_check\fR\|(3)) to select the validation type. The default
value of 0 selects FIPS186\-4 validation. Setting this value to 1 selects
FIPS186\-2 validation.
.SS "FFC key generation parameters"
.IX Subsection "FFC key generation parameters"
The following key generation types are available for DSA and DHX algorithms:
.IP """type"" (\fBOSSL_PKEY_PARAM_FFC_TYPE\fR) <UTF8 string>" 4
.IX Item """type"" (OSSL_PKEY_PARAM_FFC_TYPE) <UTF8 string>"
Sets the type of parameter generation. The shared valid values are:
.RS 4
.IP """fips186_4""" 4
.IX Item """fips186_4"""
The current standard.
.IP """fips186_2""" 4
.IX Item """fips186_2"""
The old standard that should only be used for legacy purposes.
.IP """default""" 4
.IX Item """default"""
This can choose one of "fips186_4" or "fips186_2" depending on other
parameters set for parameter generation.
.RE
.RS 4
.RE
.IP """pbits"" (\fBOSSL_PKEY_PARAM_FFC_PBITS\fR) <unsigned integer>" 4
.IX Item """pbits"" (OSSL_PKEY_PARAM_FFC_PBITS) <unsigned integer>"
Sets the size (in bits) of the prime 'p'.
.IP """qbits"" (\fBOSSL_PKEY_PARAM_FFC_QBITS\fR) <unsigned integer>" 4
.IX Item """qbits"" (OSSL_PKEY_PARAM_FFC_QBITS) <unsigned integer>"
Sets the size (in bits) of the prime 'q'.
.Sp
For "fips186_4" this can be either 224 or 256.
For "fips186_2" this has a size of 160.
.IP """digest"" (\fBOSSL_PKEY_PARAM_FFC_DIGEST\fR)  <UTF8 string>" 4
.IX Item """digest"" (OSSL_PKEY_PARAM_FFC_DIGEST) <UTF8 string>"
Sets the Digest algorithm to be used as part of the Key Generation Function
associated with the given Key Generation \fIctx\fR.
This must also be set for key validation.
.IP """properties"" (\fBOSSL_PKEY_PARAM_FFC_DIGEST_PROPS\fR) <UTF8 string>" 4
.IX Item """properties"" (OSSL_PKEY_PARAM_FFC_DIGEST_PROPS) <UTF8 string>"
Sets properties to be used upon look up of the implementation for the selected
Digest algorithm for the Key Generation Function associated with the given key
generation \fIctx\fR. This may also be set for key validation.
.IP """seed"" (\fBOSSL_PKEY_PARAM_FFC_SEED\fR) <octet string>" 4
.IX Item """seed"" (OSSL_PKEY_PARAM_FFC_SEED) <octet string>"
For "fips186_4" or "fips186_2" generation this sets the \fIseed\fR data to use
instead of generating a random seed internally. This should be used for
testing purposes only. This will either produce fixed values for the generated
parameters OR it will fail if the seed did not generate valid primes.
.IP """gindex"" (\fBOSSL_PKEY_PARAM_FFC_GINDEX\fR) <integer>" 4
.IX Item """gindex"" (OSSL_PKEY_PARAM_FFC_GINDEX) <integer>"
.PD 0
.IP """pcounter"" (\fBOSSL_PKEY_PARAM_FFC_PCOUNTER\fR) <integer>" 4
.IX Item """pcounter"" (OSSL_PKEY_PARAM_FFC_PCOUNTER) <integer>"
.IP """hindex"" (\fBOSSL_PKEY_PARAM_FFC_H\fR) <integer>" 4
.IX Item """hindex"" (OSSL_PKEY_PARAM_FFC_H) <integer>"
.PD
These types are described above.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
The following sections of SP800\-56Ar3:
.IP "******* FFC Domain Parameter Selection/Generation" 4
.IX Item "******* FFC Domain Parameter Selection/Generation"
.PP
The following sections of FIPS186\-4:
.IP "A.1.1.2 Generation of Probable Primes p and q Using an Approved Hash Function." 4
.IX Item "A.1.1.2 Generation of Probable Primes p and q Using an Approved Hash Function."
.PD 0
.IP "A.2.3 Generation of canonical generator g." 4
.IX Item "A.2.3 Generation of canonical generator g."
.IP "A.2.1 Unverifiable Generation of the Generator g." 4
.IX Item "A.2.1 Unverifiable Generation of the Generator g."
.PD
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY\-DSA\fR\|(7),
\&\fBEVP_PKEY\-DH\fR\|(7),
\&\fBEVP_SIGNATURE\-DSA\fR\|(7),
\&\fBEVP_KEYEXCH\-DH\fR\|(7)
\&\fBEVP_KEYMGMT\fR\|(3),
\&\fBEVP_PKEY\fR\|(3),
\&\fBprovider\-keymgmt\fR\|(7),
\&\fBOSSL_PROVIDER\-default\fR\|(7),
\&\fBOSSL_PROVIDER\-FIPS\fR\|(7),
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
