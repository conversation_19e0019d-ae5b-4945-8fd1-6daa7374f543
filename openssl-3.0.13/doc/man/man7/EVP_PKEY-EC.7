.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY-EC 7ossl"
.TH EVP_PKEY-EC 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY\-EC,
EVP_KEYMGMT\-EC
\&\- EVP_PKEY EC keytype and algorithm support
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBEC\fR keytype is implemented in OpenSSL's default provider.
.SS "Common EC parameters"
.IX Subsection "Common EC parameters"
The normal way of specifying domain parameters for an EC curve is via the
curve name "group". For curves with no curve name, explicit parameters can be
used that specify "field-type", "p", "a", "b", "generator" and "order".
Explicit parameters are supported for backwards compatibility reasons, but they
are not compliant with multiple standards (including RFC5915) which only allow
named curves.
.PP
The following KeyGen/Gettable/Import/Export types are available for the
built-in EC algorithm:
.IP """group"" (\fBOSSL_PKEY_PARAM_GROUP_NAME\fR) <UTF8 string>" 4
.IX Item """group"" (OSSL_PKEY_PARAM_GROUP_NAME) <UTF8 string>"
The curve name.
.IP """field-type"" (\fBOSSL_PKEY_PARAM_EC_FIELD_TYPE\fR) <UTF8 string>" 4
.IX Item """field-type"" (OSSL_PKEY_PARAM_EC_FIELD_TYPE) <UTF8 string>"
The value should be either "prime-field" or "characteristic-two-field",
which correspond to prime field Fp and binary field F2^m.
.IP """p"" (\fBOSSL_PKEY_PARAM_EC_P\fR) <unsigned integer>" 4
.IX Item """p"" (OSSL_PKEY_PARAM_EC_P) <unsigned integer>"
For a curve over Fp \fIp\fR is the prime for the field. For a curve over F2^m \fIp\fR
represents the irreducible polynomial \- each bit represents a term in the
polynomial. Therefore, there will either be three or five bits set dependent on
whether the polynomial is a trinomial or a pentanomial.
.IP """a"" (\fBOSSL_PKEY_PARAM_EC_A\fR) <unsigned integer>" 4
.IX Item """a"" (OSSL_PKEY_PARAM_EC_A) <unsigned integer>"
.PD 0
.IP """b"" (\fBOSSL_PKEY_PARAM_EC_B\fR) <unsigned integer>" 4
.IX Item """b"" (OSSL_PKEY_PARAM_EC_B) <unsigned integer>"
.IP """seed"" (\fBOSSL_PKEY_PARAM_EC_SEED\fR) <octet string>" 4
.IX Item """seed"" (OSSL_PKEY_PARAM_EC_SEED) <octet string>"
.PD
\&\fIa\fR and \fIb\fR represents the coefficients of the curve
For Fp:   y^2 mod p = x^3 +ax + b mod p OR
For F2^m: y^2 + xy = x^3 + ax^2 + b
.Sp
\&\fIseed\fR is an optional value that is for information purposes only.
It represents the random number seed used to generate the coefficient \fIb\fR from a
random number.
.IP """generator"" (\fBOSSL_PKEY_PARAM_EC_GENERATOR\fR) <octet string>" 4
.IX Item """generator"" (OSSL_PKEY_PARAM_EC_GENERATOR) <octet string>"
.PD 0
.IP """order"" (\fBOSSL_PKEY_PARAM_EC_ORDER\fR) <unsigned integer>" 4
.IX Item """order"" (OSSL_PKEY_PARAM_EC_ORDER) <unsigned integer>"
.IP """cofactor"" (\fBOSSL_PKEY_PARAM_EC_COFACTOR\fR) <unsigned integer>" 4
.IX Item """cofactor"" (OSSL_PKEY_PARAM_EC_COFACTOR) <unsigned integer>"
.PD
The \fIgenerator\fR is a well defined point on the curve chosen for cryptographic
operations. The encoding conforms with Sec. 2.3.3 of the SECG SEC 1 ("Elliptic Curve
Cryptography") standard. See \fBEC_POINT_oct2point()\fR.
Integers used for point multiplications will be between 0 and
\&\fIorder\fR \- 1.
\&\fIcofactor\fR is an optional value.
\&\fIorder\fR multiplied by the \fIcofactor\fR gives the number of points on the curve.
.IP """decoded-from-explicit"" (\fBOSSL_PKEY_PARAM_EC_DECODED_FROM_EXPLICIT_PARAMS\fR) <integer>" 4
.IX Item """decoded-from-explicit"" (OSSL_PKEY_PARAM_EC_DECODED_FROM_EXPLICIT_PARAMS) <integer>"
Gets a flag indicating whether the key or parameters were decoded from explicit
curve parameters. Set to 1 if so or 0 if a named curve was used.
.IP """use-cofactor-flag"" (\fBOSSL_PKEY_PARAM_USE_COFACTOR_ECDH\fR) <integer>" 4
.IX Item """use-cofactor-flag"" (OSSL_PKEY_PARAM_USE_COFACTOR_ECDH) <integer>"
Enable Cofactor DH (ECC CDH) if this value is 1, otherwise it uses normal EC DH
if the value is zero. The cofactor variant multiplies the shared secret by the
EC curve's cofactor (note for some curves the cofactor is 1).
.Sp
See also \fBEVP_KEYEXCH\-ECDH\fR\|(7) for the related
\&\fBOSSL_EXCHANGE_PARAM_EC_ECDH_COFACTOR_MODE\fR parameter that can be set on a
per-operation basis.
.IP """encoding"" (\fBOSSL_PKEY_PARAM_EC_ENCODING\fR) <UTF8 string>" 4
.IX Item """encoding"" (OSSL_PKEY_PARAM_EC_ENCODING) <UTF8 string>"
Set the format used for serializing the EC group parameters.
Valid values are "explicit" or "named_curve". The default value is "named_curve".
.IP """point-format"" (\fBOSSL_PKEY_PARAM_EC_POINT_CONVERSION_FORMAT\fR) <UTF8 string>" 4
.IX Item """point-format"" (OSSL_PKEY_PARAM_EC_POINT_CONVERSION_FORMAT) <UTF8 string>"
Sets or gets the point_conversion_form for the \fIkey\fR. For a description of
point_conversion_forms please see \fBEC_POINT_new\fR\|(3). Valid values are
"uncompressed" or "compressed". The default value is "uncompressed".
.IP """group-check"" (\fBOSSL_PKEY_PARAM_EC_GROUP_CHECK_TYPE\fR) <UTF8 string>" 4
.IX Item """group-check"" (OSSL_PKEY_PARAM_EC_GROUP_CHECK_TYPE) <UTF8 string>"
Sets or Gets the type of group check done when \fBEVP_PKEY_param_check()\fR is called.
Valid values are "default", "named" and "named-nist".
The "named" type checks that the domain parameters match the inbuilt curve parameters,
"named-nist" is similar but also checks that the named curve is a nist curve.
The "default" type does domain parameter validation for the OpenSSL default provider,
but is equivalent to "named-nist" for the OpenSSL FIPS provider.
.IP """include-public"" (\fBOSSL_PKEY_PARAM_EC_INCLUDE_PUBLIC\fR) <integer>" 4
.IX Item """include-public"" (OSSL_PKEY_PARAM_EC_INCLUDE_PUBLIC) <integer>"
Setting this value to 0 indicates that the public key should not be included when
encoding the private key. The default value of 1 will include the public key.
.IP """pub"" (\fBOSSL_PKEY_PARAM_PUB_KEY\fR) <octet string>" 4
.IX Item """pub"" (OSSL_PKEY_PARAM_PUB_KEY) <octet string>"
The public key value in encoded EC point format conforming to Sec. 2.3.3 and
2.3.4 of the SECG SEC 1 ("Elliptic Curve Cryptography") standard.
This parameter is used when importing or exporting the public key value with the
\&\fBEVP_PKEY_fromdata()\fR and \fBEVP_PKEY_todata()\fR functions.
.Sp
Note, in particular, that the choice of point compression format used for
encoding the exported value via \fBEVP_PKEY_todata()\fR depends on the underlying
provider implementation.
Before OpenSSL 3.0.8, the implementation of providers included with OpenSSL always
opted for an encoding in compressed format, unconditionally.
Since OpenSSL 3.0.8, the implementation has been changed to honor the
\&\fBOSSL_PKEY_PARAM_EC_POINT_CONVERSION_FORMAT\fR parameter, if set, or to default
to uncompressed format.
.IP """priv"" (\fBOSSL_PKEY_PARAM_PRIV_KEY\fR) <unsigned integer>" 4
.IX Item """priv"" (OSSL_PKEY_PARAM_PRIV_KEY) <unsigned integer>"
The private key value.
.IP """encoded-pub-key"" (\fBOSSL_PKEY_PARAM_ENCODED_PUBLIC_KEY\fR) <octet string>" 4
.IX Item """encoded-pub-key"" (OSSL_PKEY_PARAM_ENCODED_PUBLIC_KEY) <octet string>"
Used for getting and setting the encoding of an EC public key. The public key
is expected to be a point conforming to Sec. 2.3.4 of the SECG SEC 1 ("Elliptic
Curve Cryptography") standard.
.IP """qx"" (\fBOSSL_PKEY_PARAM_EC_PUB_X\fR) <unsigned integer>" 4
.IX Item """qx"" (OSSL_PKEY_PARAM_EC_PUB_X) <unsigned integer>"
Used for getting the EC public key X component.
.IP """qy"" (\fBOSSL_PKEY_PARAM_EC_PUB_Y\fR) <unsigned integer>" 4
.IX Item """qy"" (OSSL_PKEY_PARAM_EC_PUB_Y) <unsigned integer>"
Used for getting the EC public key Y component.
.IP """default-digest"" (\fBOSSL_PKEY_PARAM_DEFAULT_DIGEST\fR) <UTF8 string>" 4
.IX Item """default-digest"" (OSSL_PKEY_PARAM_DEFAULT_DIGEST) <UTF8 string>"
Getter that returns the default digest name.
(Currently returns "SHA256" as of OpenSSL 3.0).
.PP
The following Gettable types are also available for the built-in EC algorithm:
.IP """basis-type"" (\fBOSSL_PKEY_PARAM_EC_CHAR2_TYPE\fR) <UTF8 string>" 4
.IX Item """basis-type"" (OSSL_PKEY_PARAM_EC_CHAR2_TYPE) <UTF8 string>"
Supports the values "tpBasis" for a trinomial or "ppBasis" for a pentanomial.
This field is only used for a binary field F2^m.
.IP """m"" (\fBOSSL_PKEY_PARAM_EC_CHAR2_M\fR) <integer>" 4
.IX Item """m"" (OSSL_PKEY_PARAM_EC_CHAR2_M) <integer>"
.PD 0
.IP """tp"" (\fBOSSL_PKEY_PARAM_EC_CHAR2_TP_BASIS\fR) <integer>" 4
.IX Item """tp"" (OSSL_PKEY_PARAM_EC_CHAR2_TP_BASIS) <integer>"
.IP """k1"" (\fBOSSL_PKEY_PARAM_EC_CHAR2_PP_K1\fR) <integer>" 4
.IX Item """k1"" (OSSL_PKEY_PARAM_EC_CHAR2_PP_K1) <integer>"
.IP """k2"" (\fBOSSL_PKEY_PARAM_EC_CHAR2_PP_K2\fR) <integer>" 4
.IX Item """k2"" (OSSL_PKEY_PARAM_EC_CHAR2_PP_K2) <integer>"
.IP """k3"" (\fBOSSL_PKEY_PARAM_EC_CHAR2_PP_K3\fR) <integer>" 4
.IX Item """k3"" (OSSL_PKEY_PARAM_EC_CHAR2_PP_K3) <integer>"
.PD
These fields are only used for a binary field F2^m.
\&\fIm\fR is the degree of the binary field.
.Sp
\&\fItp\fR is the middle bit of a trinomial so its value must be in the
range m > tp > 0.
.Sp
\&\fIk1\fR, \fIk2\fR and \fIk3\fR are used to get the middle bits of a pentanomial such
that m > k3 > k2 > k1 > 0
.SS "EC key validation"
.IX Subsection "EC key validation"
For EC keys, \fBEVP_PKEY_param_check\fR\|(3) behaves in the following way:
For the OpenSSL default provider it uses either
\&\fBEC_GROUP_check\fR\|(3) or \fBEC_GROUP_check_named_curve\fR\|(3) depending on the flag
EC_FLAG_CHECK_NAMED_GROUP.
The OpenSSL FIPS provider uses \fBEC_GROUP_check_named_curve\fR\|(3) in order to
conform to SP800\-56Ar3 \fIAssurances of Domain-Parameter Validity\fR.
.PP
For EC keys, \fBEVP_PKEY_param_check_quick\fR\|(3) is equivalent to
\&\fBEVP_PKEY_param_check\fR\|(3).
.PP
For EC keys, \fBEVP_PKEY_public_check\fR\|(3) and \fBEVP_PKEY_public_check_quick\fR\|(3)
conform to SP800\-56Ar3 \fIECC Full Public-Key Validation\fR and
\&\fIECC Partial Public-Key Validation\fR respectively.
.PP
For EC Keys, \fBEVP_PKEY_private_check\fR\|(3) and \fBEVP_PKEY_pairwise_check\fR\|(3)
conform to SP800\-56Ar3 \fIPrivate key validity\fR and
\&\fIOwner Assurance of Pair-wise Consistency\fR respectively.
.SH EXAMPLES
.IX Header "EXAMPLES"
An \fBEVP_PKEY\fR context can be obtained by calling:
.PP
.Vb 2
\&    EVP_PKEY_CTX *pctx =
\&        EVP_PKEY_CTX_new_from_name(NULL, "EC", NULL);
.Ve
.PP
An \fBEVP_PKEY\fR ECDSA or ECDH key can be generated with a "P\-256" named group by
calling:
.PP
.Vb 1
\&    pkey = EVP_EC_gen("P\-256");
.Ve
.PP
or like this:
.PP
.Vb 4
\&    EVP_PKEY *key = NULL;
\&    OSSL_PARAM params[2];
\&    EVP_PKEY_CTX *gctx =
\&        EVP_PKEY_CTX_new_from_name(NULL, "EC", NULL);
\&
\&    EVP_PKEY_keygen_init(gctx);
\&
\&    params[0] = OSSL_PARAM_construct_utf8_string(OSSL_PKEY_PARAM_GROUP_NAME,
\&                                                 "P\-256", 0);
\&    params[1] = OSSL_PARAM_construct_end();
\&    EVP_PKEY_CTX_set_params(gctx, params);
\&
\&    EVP_PKEY_generate(gctx, &key);
\&
\&    EVP_PKEY_print_private(bio_out, key, 0, NULL);
\&    ...
\&    EVP_PKEY_free(key);
\&    EVP_PKEY_CTX_free(gctx);
.Ve
.PP
An \fBEVP_PKEY\fR EC CDH (Cofactor Diffie-Hellman) key can be generated with a
"K\-571" named group by calling:
.PP
.Vb 5
\&    int use_cdh = 1;
\&    EVP_PKEY *key = NULL;
\&    OSSL_PARAM params[3];
\&    EVP_PKEY_CTX *gctx =
\&        EVP_PKEY_CTX_new_from_name(NULL, "EC", NULL);
\&
\&    EVP_PKEY_keygen_init(gctx);
\&
\&    params[0] = OSSL_PARAM_construct_utf8_string(OSSL_PKEY_PARAM_GROUP_NAME,
\&                                                 "K\-571", 0);
\&    /*
\&     * This curve has a cofactor that is not 1 \- so setting CDH mode changes
\&     * the behaviour. For many curves the cofactor is 1 \- so setting this has
\&     * no effect.
\&     */
\&    params[1] = OSSL_PARAM_construct_int(OSSL_PKEY_PARAM_USE_COFACTOR_ECDH,
\&                                         &use_cdh);
\&    params[2] = OSSL_PARAM_construct_end();
\&    EVP_PKEY_CTX_set_params(gctx, params);
\&
\&    EVP_PKEY_generate(gctx, &key);
\&    EVP_PKEY_print_private(bio_out, key, 0, NULL);
\&    ...
\&    EVP_PKEY_free(key);
\&    EVP_PKEY_CTX_free(gctx);
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_EC_gen\fR\|(3),
\&\fBEVP_KEYMGMT\fR\|(3),
\&\fBEVP_PKEY\fR\|(3),
\&\fBprovider\-keymgmt\fR\|(7),
\&\fBEVP_SIGNATURE\-ECDSA\fR\|(7),
\&\fBEVP_KEYEXCH\-ECDH\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
