.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_KDF-X942-ASN1 7ossl"
.TH EVP_KDF-X942-ASN1 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_KDF\-X942\-ASN1 \- The X9.42\-2003 asn1 EVP_KDF implementation
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The EVP_KDF\-X942\-ASN1 algorithm implements the key derivation function
X942KDF\-ASN1. It is used by DH KeyAgreement, to derive a key using input such as
a shared secret key and other info. The other info is DER encoded data that
contains a 32 bit counter as well as optional fields for "partyu-info",
"partyv-info", "supp-pubinfo" and "supp-privinfo".
This kdf is used by Cryptographic Message Syntax (CMS).
.SS Identity
.IX Subsection "Identity"
"X942KDF\-ASN1" or "X942KDF" is the name for this implementation; it
can be used with the \fBEVP_KDF_fetch()\fR function.
.SS "Supported parameters"
.IX Subsection "Supported parameters"
The supported parameters are:
.IP """properties"" (\fBOSSL_KDF_PARAM_PROPERTIES\fR) <UTF8 string>" 4
.IX Item """properties"" (OSSL_KDF_PARAM_PROPERTIES) <UTF8 string>"
.PD 0
.IP """digest"" (\fBOSSL_KDF_PARAM_DIGEST\fR) <UTF8 string>" 4
.IX Item """digest"" (OSSL_KDF_PARAM_DIGEST) <UTF8 string>"
.PD
These parameters work as described in "PARAMETERS" in \fBEVP_KDF\fR\|(3).
.IP """secret"" (\fBOSSL_KDF_PARAM_SECRET\fR) <octet string>" 4
.IX Item """secret"" (OSSL_KDF_PARAM_SECRET) <octet string>"
The shared secret used for key derivation.  This parameter sets the secret.
.IP """acvp-info"" (\fBOSSL_KDF_PARAM_X942_ACVPINFO\fR) <octet string>" 4
.IX Item """acvp-info"" (OSSL_KDF_PARAM_X942_ACVPINFO) <octet string>"
This value should not be used in production and should only be used for ACVP
testing. It is an optional octet string containing a combined DER encoded blob
of any of the optional fields related to "partyu-info", "partyv-info",
"supp-pubinfo" and "supp-privinfo". If it is specified then none of these other
fields should be used.
.IP """partyu-info"" (\fBOSSL_KDF_PARAM_X942_PARTYUINFO\fR) <octet string>" 4
.IX Item """partyu-info"" (OSSL_KDF_PARAM_X942_PARTYUINFO) <octet string>"
An optional octet string containing public info contributed by the initiator.
.IP """ukm"" (\fBOSSL_KDF_PARAM_UKM\fR) <octet string>" 4
.IX Item """ukm"" (OSSL_KDF_PARAM_UKM) <octet string>"
An alias for "partyu-info".
In CMS this is the user keying material.
.IP """partyv-info"" (\fBOSSL_KDF_PARAM_X942_PARTYVINFO\fR) <octet string>" 4
.IX Item """partyv-info"" (OSSL_KDF_PARAM_X942_PARTYVINFO) <octet string>"
An optional octet string containing public info contributed by the responder.
.IP """supp-pubinfo"" (\fBOSSL_KDF_PARAM_X942_SUPP_PUBINFO\fR) <octet string>" 4
.IX Item """supp-pubinfo"" (OSSL_KDF_PARAM_X942_SUPP_PUBINFO) <octet string>"
An optional octet string containing some additional, mutually-known public
information. Setting this value also sets "use-keybits" to 0.
.IP """use-keybits"" (\fBOSSL_KDF_PARAM_X942_USE_KEYBITS\fR) <integer>" 4
.IX Item """use-keybits"" (OSSL_KDF_PARAM_X942_USE_KEYBITS) <integer>"
The default value of 1 will use the KEK key length (in bits) as the
"supp-pubinfo". A value of 0 disables setting the "supp-pubinfo".
.IP """supp-privinfo"" (\fBOSSL_KDF_PARAM_X942_SUPP_PRIVINFO\fR) <octet string>" 4
.IX Item """supp-privinfo"" (OSSL_KDF_PARAM_X942_SUPP_PRIVINFO) <octet string>"
An optional octet string containing some additional, mutually-known private
information.
.IP """cekalg"" (\fBOSSL_KDF_PARAM_CEK_ALG\fR) <UTF8 string>" 4
.IX Item """cekalg"" (OSSL_KDF_PARAM_CEK_ALG) <UTF8 string>"
This parameter sets the CEK wrapping algorithm name.
Valid values are "AES\-128\-WRAP", "AES\-192\-WRAP", "AES\-256\-WRAP" and "DES3\-WRAP".
.SH NOTES
.IX Header "NOTES"
A context for X942KDF can be obtained by calling:
.PP
.Vb 2
\& EVP_KDF *kdf = EVP_KDF_fetch(NULL, "X942KDF", NULL);
\& EVP_KDF_CTX *kctx = EVP_KDF_CTX_new(kdf);
.Ve
.PP
The output length of an X942KDF is specified via the \fIkeylen\fR
parameter to the \fBEVP_KDF_derive\fR\|(3) function.
.SH EXAMPLES
.IX Header "EXAMPLES"
This example derives 24 bytes, with the secret key "secret" and random user
keying material:
.PP
.Vb 5
\&  EVP_KDF_CTX *kctx;
\&  EVP_KDF_CTX *kctx;
\&  unsigned char out[192/8];
\&  unsignred char ukm[64];
\&  OSSL_PARAM params[5], *p = params;
\&
\&  if (RAND_bytes(ukm, sizeof(ukm)) <= 0)
\&      error("RAND_bytes");
\&
\&  kdf = EVP_KDF_fetch(NULL, "X942KDF", NULL);
\&  if (kctx == NULL)
\&      error("EVP_KDF_fetch");
\&  kctx = EVP_KDF_CTX_new(kdf);
\&  EVP_KDF_free(kdf);
\&  if (kctx == NULL)
\&      error("EVP_KDF_CTX_new");
\&
\&  *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_DIGEST, "SHA256", 0);
\&  *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SECRET,
\&                                           "secret", (size_t)6);
\&  *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_UKM, ukm, sizeof(ukm));
\&  *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_CEK_ALG, "AES\-256\-WRAP, 0);
\&  *p = OSSL_PARAM_construct_end();
\&  if (EVP_KDF_derive(kctx, out, sizeof(out), params) <= 0)
\&      error("EVP_KDF_derive");
\&
\&  EVP_KDF_CTX_free(kctx);
.Ve
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
ANS1 X9.42\-2003
RFC 2631
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_KDF\fR\|(3),
\&\fBEVP_KDF_CTX_new\fR\|(3),
\&\fBEVP_KDF_CTX_free\fR\|(3),
\&\fBEVP_KDF_CTX_set_params\fR\|(3),
\&\fBEVP_KDF_CTX_get_kdf_size\fR\|(3),
\&\fBEVP_KDF_derive\fR\|(3),
"PARAMETERS" in \fBEVP_KDF\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
This functionality was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
