.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PROVIDER-DIGEST 7ossl"
.TH PROVIDER-DIGEST 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
provider\-digest \- The digest library <\-> provider functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 2
\& #include <openssl/core_dispatch.h>
\& #include <openssl/core_names.h>
\&
\& /*
\&  * Digests support the following function signatures in OSSL_DISPATCH arrays.
\&  * (The function signatures are not actual functions).
\&  */
\&
\& /* Context management */
\& void *OSSL_FUNC_digest_newctx(void *provctx);
\& void OSSL_FUNC_digest_freectx(void *dctx);
\& void *OSSL_FUNC_digest_dupctx(void *dctx);
\&
\& /* Digest generation */
\& int OSSL_FUNC_digest_init(void *dctx, const OSSL_PARAM params[]);
\& int OSSL_FUNC_digest_update(void *dctx, const unsigned char *in, size_t inl);
\& int OSSL_FUNC_digest_final(void *dctx, unsigned char *out, size_t *outl,
\&                            size_t outsz);
\& int OSSL_FUNC_digest_digest(void *provctx, const unsigned char *in, size_t inl,
\&                             unsigned char *out, size_t *outl, size_t outsz);
\&
\& /* Digest parameter descriptors */
\& const OSSL_PARAM *OSSL_FUNC_digest_gettable_params(void *provctx);
\&
\& /* Digest operation parameter descriptors */
\& const OSSL_PARAM *OSSL_FUNC_digest_gettable_ctx_params(void *dctx,
\&                                                        void *provctx);
\& const OSSL_PARAM *OSSL_FUNC_digest_settable_ctx_params(void *dctx,
\&                                                        void *provctx);
\&
\& /* Digest parameters */
\& int OSSL_FUNC_digest_get_params(OSSL_PARAM params[]);
\&
\& /* Digest operation parameters */
\& int OSSL_FUNC_digest_set_ctx_params(void *dctx, const OSSL_PARAM params[]);
\& int OSSL_FUNC_digest_get_ctx_params(void *dctx, OSSL_PARAM params[]);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This documentation is primarily aimed at provider authors. See \fBprovider\fR\|(7)
for further information.
.PP
The DIGEST operation enables providers to implement digest algorithms and make
them available to applications via the API functions \fBEVP_DigestInit_ex\fR\|(3),
\&\fBEVP_DigestUpdate\fR\|(3) and \fBEVP_DigestFinal\fR\|(3) (and other related functions).
.PP
All "functions" mentioned here are passed as function pointers between
\&\fIlibcrypto\fR and the provider in \fBOSSL_DISPATCH\fR\|(3) arrays via
\&\fBOSSL_ALGORITHM\fR\|(3) arrays that are returned by the provider's
\&\fBprovider_query_operation()\fR function
(see "Provider Functions" in \fBprovider\-base\fR\|(7)).
.PP
All these "functions" have a corresponding function type definition
named \fBOSSL_FUNC_{name}_fn\fR, and a helper function to retrieve the
function pointer from an \fBOSSL_DISPATCH\fR\|(3) element named
\&\fBOSSL_FUNC_{name}\fR.
For example, the "function" \fBOSSL_FUNC_digest_newctx()\fR has these:
.PP
.Vb 3
\& typedef void *(OSSL_FUNC_digest_newctx_fn)(void *provctx);
\& static ossl_inline OSSL_FUNC_digest_newctx_fn
\&     OSSL_FUNC_digest_newctx(const OSSL_DISPATCH *opf);
.Ve
.PP
\&\fBOSSL_DISPATCH\fR\|(3) arrays are indexed by numbers that are provided as
macros in \fBopenssl\-core_dispatch.h\fR\|(7), as follows:
.PP
.Vb 3
\& OSSL_FUNC_digest_newctx               OSSL_FUNC_DIGEST_NEWCTX
\& OSSL_FUNC_digest_freectx              OSSL_FUNC_DIGEST_FREECTX
\& OSSL_FUNC_digest_dupctx               OSSL_FUNC_DIGEST_DUPCTX
\&
\& OSSL_FUNC_digest_init                 OSSL_FUNC_DIGEST_INIT
\& OSSL_FUNC_digest_update               OSSL_FUNC_DIGEST_UPDATE
\& OSSL_FUNC_digest_final                OSSL_FUNC_DIGEST_FINAL
\& OSSL_FUNC_digest_digest               OSSL_FUNC_DIGEST_DIGEST
\&
\& OSSL_FUNC_digest_get_params           OSSL_FUNC_DIGEST_GET_PARAMS
\& OSSL_FUNC_digest_get_ctx_params       OSSL_FUNC_DIGEST_GET_CTX_PARAMS
\& OSSL_FUNC_digest_set_ctx_params       OSSL_FUNC_DIGEST_SET_CTX_PARAMS
\&
\& OSSL_FUNC_digest_gettable_params      OSSL_FUNC_DIGEST_GETTABLE_PARAMS
\& OSSL_FUNC_digest_gettable_ctx_params  OSSL_FUNC_DIGEST_GETTABLE_CTX_PARAMS
\& OSSL_FUNC_digest_settable_ctx_params  OSSL_FUNC_DIGEST_SETTABLE_CTX_PARAMS
.Ve
.PP
A digest algorithm implementation may not implement all of these functions.
In order to be usable all or none of OSSL_FUNC_digest_newctx, OSSL_FUNC_digest_freectx,
OSSL_FUNC_digest_init, OSSL_FUNC_digest_update and OSSL_FUNC_digest_final should be implemented.
All other functions are optional.
.SS "Context Management Functions"
.IX Subsection "Context Management Functions"
\&\fBOSSL_FUNC_digest_newctx()\fR should create and return a pointer to a provider side
structure for holding context information during a digest operation.
A pointer to this context will be passed back in a number of the other digest
operation function calls.
The parameter \fIprovctx\fR is the provider context generated during provider
initialisation (see \fBprovider\fR\|(7)).
.PP
\&\fBOSSL_FUNC_digest_freectx()\fR is passed a pointer to the provider side digest context in
the \fIdctx\fR parameter.
This function should free any resources associated with that context.
.PP
\&\fBOSSL_FUNC_digest_dupctx()\fR should duplicate the provider side digest context in the
\&\fIdctx\fR parameter and return the duplicate copy.
.SS "Digest Generation Functions"
.IX Subsection "Digest Generation Functions"
\&\fBOSSL_FUNC_digest_init()\fR initialises a digest operation given a newly created
provider side digest context in the \fIdctx\fR parameter.
The \fIparams\fR, if not NULL, should be set on the context in a manner similar to
using \fBOSSL_FUNC_digest_set_ctx_params()\fR.
.PP
\&\fBOSSL_FUNC_digest_update()\fR is called to supply data to be digested as part of a
previously initialised digest operation.
The \fIdctx\fR parameter contains a pointer to a previously initialised provider
side context.
\&\fBOSSL_FUNC_digest_update()\fR should digest \fIinl\fR bytes of data at the location pointed to
by \fIin\fR.
\&\fBOSSL_FUNC_digest_update()\fR may be called multiple times for a single digest operation.
.PP
\&\fBOSSL_FUNC_digest_final()\fR generates a digest started through previous \fBOSSL_FUNC_digest_init()\fR
and \fBOSSL_FUNC_digest_update()\fR calls.
The \fIdctx\fR parameter contains a pointer to the provider side context.
The digest should be written to \fI*out\fR and the length of the digest to
\&\fI*outl\fR.
The digest should not exceed \fIoutsz\fR bytes.
.PP
\&\fBOSSL_FUNC_digest_digest()\fR is a "oneshot" digest function.
No provider side digest context is used.
Instead the provider context that was created during provider initialisation is
passed in the \fIprovctx\fR parameter (see \fBprovider\fR\|(7)).
\&\fIinl\fR bytes at \fIin\fR should be digested and the result should be stored at
\&\fIout\fR. The length of the digest should be stored in \fI*outl\fR which should not
exceed \fIoutsz\fR bytes.
.SS "Digest Parameters"
.IX Subsection "Digest Parameters"
See \fBOSSL_PARAM\fR\|(3) for further details on the parameters structure used by
these functions.
.PP
\&\fBOSSL_FUNC_digest_get_params()\fR gets details of the algorithm implementation
and stores them in \fIparams\fR.
.PP
\&\fBOSSL_FUNC_digest_set_ctx_params()\fR sets digest operation parameters for the
provider side digest context \fIdctx\fR to \fIparams\fR.
Any parameter settings are additional to any that were previously set.
Passing NULL for \fIparams\fR should return true.
.PP
\&\fBOSSL_FUNC_digest_get_ctx_params()\fR gets digest operation details details from
the given provider side digest context \fIdctx\fR and stores them in \fIparams\fR.
Passing NULL for \fIparams\fR should return true.
.PP
\&\fBOSSL_FUNC_digest_gettable_params()\fR returns a constant \fBOSSL_PARAM\fR\|(3) array
containing descriptors of the parameters that \fBOSSL_FUNC_digest_get_params()\fR
can handle.
.PP
\&\fBOSSL_FUNC_digest_gettable_ctx_params()\fR and
\&\fBOSSL_FUNC_digest_settable_ctx_params()\fR both return constant
\&\fBOSSL_PARAM\fR\|(3) arrays as descriptors of the parameters that
\&\fBOSSL_FUNC_digest_get_ctx_params()\fR and \fBOSSL_FUNC_digest_set_ctx_params()\fR
can handle, respectively.  The array is based on the current state of
the provider side context if \fIdctx\fR is not NULL and on the provider
side algorithm \fIprovctx\fR otherwise.
.PP
Parameters currently recognised by built-in digests with this function
are as follows. Not all parameters are relevant to, or are understood
by all digests:
.IP """blocksize"" (\fBOSSL_DIGEST_PARAM_BLOCK_SIZE\fR) <unsigned integer>" 4
.IX Item """blocksize"" (OSSL_DIGEST_PARAM_BLOCK_SIZE) <unsigned integer>"
The digest block size.
The length of the "blocksize" parameter should not exceed that of a \fBsize_t\fR.
.IP """size"" (\fBOSSL_DIGEST_PARAM_SIZE\fR) <unsigned integer>" 4
.IX Item """size"" (OSSL_DIGEST_PARAM_SIZE) <unsigned integer>"
The digest output size.
The length of the "size" parameter should not exceed that of a \fBsize_t\fR.
.IP """flags"" (\fBOSSL_DIGEST_PARAM_FLAGS\fR) <unsigned integer>" 4
.IX Item """flags"" (OSSL_DIGEST_PARAM_FLAGS) <unsigned integer>"
Diverse flags that describe exceptional behaviour for the digest:
.RS 4
.IP \fBEVP_MD_FLAG_ONESHOT\fR 4
.IX Item "EVP_MD_FLAG_ONESHOT"
This digest method can only handle one block of input.
.IP \fBEVP_MD_FLAG_XOF\fR 4
.IX Item "EVP_MD_FLAG_XOF"
This digest method is an extensible-output function (XOF) and supports
setting the \fBOSSL_DIGEST_PARAM_XOFLEN\fR parameter.
.IP \fBEVP_MD_FLAG_DIGALGID_NULL\fR 4
.IX Item "EVP_MD_FLAG_DIGALGID_NULL"
When setting up a DigestAlgorithmIdentifier, this flag will have the
parameter set to NULL by default.  Use this for PKCS#1.  \fINote: if
combined with EVP_MD_FLAG_DIGALGID_ABSENT, the latter will override.\fR
.IP \fBEVP_MD_FLAG_DIGALGID_ABSENT\fR 4
.IX Item "EVP_MD_FLAG_DIGALGID_ABSENT"
When setting up a DigestAlgorithmIdentifier, this flag will have the
parameter be left absent by default.  \fINote: if combined with
EVP_MD_FLAG_DIGALGID_NULL, the latter will be overridden.\fR
.IP \fBEVP_MD_FLAG_DIGALGID_CUSTOM\fR 4
.IX Item "EVP_MD_FLAG_DIGALGID_CUSTOM"
Custom DigestAlgorithmIdentifier handling via ctrl, with
\&\fBEVP_MD_FLAG_DIGALGID_ABSENT\fR as default.  \fINote: if combined with
EVP_MD_FLAG_DIGALGID_NULL, the latter will be overridden.\fR
Currently unused.
.RE
.RS 4
.Sp
The length of the "flags" parameter should equal that of an
\&\fBunsigned long int\fR.
.RE
.SS "Digest Context Parameters"
.IX Subsection "Digest Context Parameters"
\&\fBOSSL_FUNC_digest_set_ctx_params()\fR sets digest parameters associated with the
given provider side digest context \fIdctx\fR to \fIparams\fR.
Any parameter settings are additional to any that were previously set.
See \fBOSSL_PARAM\fR\|(3) for further details on the parameters structure.
.PP
\&\fBOSSL_FUNC_digest_get_ctx_params()\fR gets details of currently set parameters
values associated with the give provider side digest context \fIdctx\fR
and stores them in \fIparams\fR.
See \fBOSSL_PARAM\fR\|(3) for further details on the parameters structure.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_FUNC_digest_newctx()\fR and \fBOSSL_FUNC_digest_dupctx()\fR should return the newly created
provider side digest context, or NULL on failure.
.PP
\&\fBOSSL_FUNC_digest_init()\fR, \fBOSSL_FUNC_digest_update()\fR, \fBOSSL_FUNC_digest_final()\fR, \fBOSSL_FUNC_digest_digest()\fR,
\&\fBOSSL_FUNC_digest_set_params()\fR and \fBOSSL_FUNC_digest_get_params()\fR should return 1 for success or
0 on error.
.PP
\&\fBOSSL_FUNC_digest_size()\fR should return the digest size.
.PP
\&\fBOSSL_FUNC_digest_block_size()\fR should return the block size of the underlying digest
algorithm.
.SH BUGS
.IX Header "BUGS"
The \fBEVP_Q_digest()\fR, \fBEVP_Digest()\fR and \fBEVP_DigestFinal_ex()\fR API calls do not
expect the digest size to be larger than EVP_MAX_MD_SIZE. Any algorithm which
produces larger digests is unusable with those API calls.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\fR\|(7), \fBOSSL_PROVIDER\-FIPS\fR\|(7), \fBOSSL_PROVIDER\-default\fR\|(7),
\&\fBOSSL_PROVIDER\-legacy\fR\|(7),
\&\fBEVP_MD\-common\fR\|(7), \fBEVP_MD\-BLAKE2\fR\|(7), \fBEVP_MD\-MD2\fR\|(7),
\&\fBEVP_MD\-MD4\fR\|(7), \fBEVP_MD\-MD5\fR\|(7), \fBEVP_MD\-MD5\-SHA1\fR\|(7),
\&\fBEVP_MD\-MDC2\fR\|(7), \fBEVP_MD\-RIPEMD160\fR\|(7), \fBEVP_MD\-SHA1\fR\|(7),
\&\fBEVP_MD\-SHA2\fR\|(7), \fBEVP_MD\-SHA3\fR\|(7), \fBEVP_MD\-SHAKE\fR\|(7),
\&\fBEVP_MD\-SM3\fR\|(7), \fBEVP_MD\-WHIRLPOOL\fR\|(7),
\&\fBEVP_MD\-NULL\fR\|(7),
\&\fBlife_cycle\-digest\fR\|(7), \fBEVP_DigestInit\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The provider DIGEST interface was introduced in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
