.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PROVIDER-ENCODER 7ossl"
.TH PROVIDER-ENCODER 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
provider\-encoder \- The OSSL_ENCODER library <\-> provider functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/core_dispatch.h>
\&
\& /*
\&  * None of these are actual functions, but are displayed like this for
\&  * the function signatures for functions that are offered as function
\&  * pointers in OSSL_DISPATCH arrays.
\&  */
\&
\& /* Encoder parameter accessor and descriptor */
\& const OSSL_PARAM *OSSL_FUNC_encoder_gettable_params(void *provctx);
\& int OSSL_FUNC_encoder_get_params(OSSL_PARAM params[]);
\&
\& /* Functions to construct / destruct / manipulate the encoder context */
\& void *OSSL_FUNC_encoder_newctx(void *provctx);
\& void OSSL_FUNC_encoder_freectx(void *ctx);
\& int OSSL_FUNC_encoder_set_ctx_params(void *ctx, const OSSL_PARAM params[]);
\& const OSSL_PARAM *OSSL_FUNC_encoder_settable_ctx_params(void *provctx);
\&
\& /* Functions to check selection support */
\& int OSSL_FUNC_encoder_does_selection(void *provctx, int selection);
\&
\& /* Functions to encode object data */
\& int OSSL_FUNC_encoder_encode(void *ctx, OSSL_CORE_BIO *out,
\&                              const void *obj_raw,
\&                              const OSSL_PARAM obj_abstract[],
\&                              int selection,
\&                              OSSL_PASSPHRASE_CALLBACK *cb,
\&                              void *cbarg);
\&
\& /* Functions to import and free a temporary object to be encoded */
\& void *OSSL_FUNC_encoder_import_object(void *ctx, int selection,
\&                                       const OSSL_PARAM params[]);
\& void OSSL_FUNC_encoder_free_object(void *obj);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
\&\fIWe use the wide term "encode" in this manual.  This includes but is
not limited to serialization.\fR
.PP
The ENCODER operation is a generic method to encode a provider-native
object (\fIobj_raw\fR) or an object abstraction (\fIobject_abstract\fR, see
\&\fBprovider\-object\fR\|(7)) into an encoded form, and write the result to
the given OSSL_CORE_BIO.  If the caller wants to get the encoded
stream to memory, it should provide a \fBBIO_s_mem\fR\|(3) \fBBIO\fR.
.PP
The encoder doesn't need to know more about the \fBOSSL_CORE_BIO\fR
pointer than being able to pass it to the appropriate BIO upcalls (see
"Core functions" in \fBprovider\-base\fR\|(7)).
.PP
The ENCODER implementation may be part of a chain, where data is
passed from one to the next.  For example, there may be an
implementation to encode an object to DER (that object is assumed to
be provider-native and thereby passed via \fIobj_raw\fR), and another one
that encodes DER to PEM (that one would receive the DER encoding via
\&\fIobj_abstract\fR).
.PP
The encoding using the \fBOSSL_PARAM\fR\|(3) array form allows a
encoder to be used for data that's been exported from another
provider, and thereby allow them to exist independently of each
other.
.PP
The encoding using a provider side object can only be safely used
with provider data coming from the same provider, for example keys
with the KEYMGMT provider.
.PP
All "functions" mentioned here are passed as function pointers between
\&\fIlibcrypto\fR and the provider in \fBOSSL_DISPATCH\fR\|(3) arrays via
\&\fBOSSL_ALGORITHM\fR\|(3) arrays that are returned by the provider's
\&\fBprovider_query_operation()\fR function
(see "Provider Functions" in \fBprovider\-base\fR\|(7)).
.PP
All these "functions" have a corresponding function type definition
named \fBOSSL_FUNC_{name}_fn\fR, and a helper function to retrieve the
function pointer from an \fBOSSL_DISPATCH\fR\|(3) element named
\&\fBOSSL_FUNC_{name}\fR.
For example, the "function" \fBOSSL_FUNC_encoder_encode()\fR has these:
.PP
.Vb 8
\& typedef int
\&     (OSSL_FUNC_encoder_encode_fn)(void *ctx, OSSL_CORE_BIO *out,
\&                                   const void *obj_raw,
\&                                   const OSSL_PARAM obj_abstract[],
\&                                   int selection,
\&                                   OSSL_PASSPHRASE_CALLBACK *cb, void *cbarg);
\& static ossl_inline OSSL_FUNC_encoder_encode_fn
\&     OSSL_FUNC_encoder_encode(const OSSL_DISPATCH *opf);
.Ve
.PP
\&\fBOSSL_DISPATCH\fR\|(3) arrays are indexed by numbers that are provided as
macros in \fBopenssl\-core_dispatch.h\fR\|(7), as follows:
.PP
.Vb 2
\& OSSL_FUNC_encoder_get_params          OSSL_FUNC_ENCODER_GET_PARAMS
\& OSSL_FUNC_encoder_gettable_params     OSSL_FUNC_ENCODER_GETTABLE_PARAMS
\&
\& OSSL_FUNC_encoder_newctx              OSSL_FUNC_ENCODER_NEWCTX
\& OSSL_FUNC_encoder_freectx             OSSL_FUNC_ENCODER_FREECTX
\& OSSL_FUNC_encoder_set_ctx_params      OSSL_FUNC_ENCODER_SET_CTX_PARAMS
\& OSSL_FUNC_encoder_settable_ctx_params OSSL_FUNC_ENCODER_SETTABLE_CTX_PARAMS
\&
\& OSSL_FUNC_encoder_does_selection      OSSL_FUNC_ENCODER_DOES_SELECTION
\&
\& OSSL_FUNC_encoder_encode              OSSL_FUNC_ENCODER_ENCODE
\&
\& OSSL_FUNC_encoder_import_object       OSSL_FUNC_ENCODER_IMPORT_OBJECT
\& OSSL_FUNC_encoder_free_object         OSSL_FUNC_ENCODER_FREE_OBJECT
.Ve
.SS "Names and properties"
.IX Subsection "Names and properties"
The name of an implementation should match the type of object it handles.
For example, an implementation that encodes an RSA key should be named "RSA".
Likewise, an implementation that further encodes DER should be named "DER".
.PP
Properties can be used to further specify details about an implementation:
.IP output 4
.IX Item "output"
This property is used to specify what type of output the implementation
produces.
.Sp
This property is \fImandatory\fR.
.Sp
OpenSSL providers recognize the following output types:
.RS 4
.IP text 4
.IX Item "text"
An implementation with that output type outputs human readable text, making
that implementation suitable for \f(CW\*(C`\-text\*(C'\fR output in diverse \fBopenssl\fR\|(1)
commands.
.IP pem 4
.IX Item "pem"
An implementation with that output type outputs PEM formatted data.
.IP der 4
.IX Item "der"
An implementation with that output type outputs DER formatted data.
.IP msblob 4
.IX Item "msblob"
An implementation with that output type outputs MSBLOB formatted data.
.IP pvk 4
.IX Item "pvk"
An implementation with that output type outputs PVK formatted data.
.RE
.RS 4
.RE
.IP structure 4
.IX Item "structure"
This property is used to specify the structure that is used for the encoded
object.  An example could be \f(CW\*(C`pkcs8\*(C'\fR, to specify explicitly that an object
(presumably an asymmetric key pair, in this case) will be wrapped in a
PKCS#8 structure as part of the encoding.
.Sp
This property is \fIoptional\fR.
.PP
The possible values of both these properties is open ended.  A provider may
very well specify output types and structures that libcrypto doesn't know
anything about.
.SS "Subset selections"
.IX Subsection "Subset selections"
Sometimes, an object has more than one subset of data that is interesting to
treat separately or together.  It's possible to specify what subsets are to
be encoded, with a set of bits \fIselection\fR that are passed in an \fBint\fR.
.PP
This set of bits depend entirely on what kind of provider-side object is
passed.  For example, those bits are assumed to be the same as those used
with \fBprovider\-keymgmt\fR\|(7) (see "Key Objects" in \fBprovider\-keymgmt\fR\|(7)) when
the object is an asymmetric keypair.
.PP
ENCODER implementations are free to regard the \fIselection\fR as a set of
hints, but must do so with care.  In the end, the output must make sense,
and if there's a corresponding decoder, the resulting decoded object must
match the original object that was encoded.
.PP
\&\fBOSSL_FUNC_encoder_does_selection()\fR should tell if a particular implementation
supports any of the combinations given by \fIselection\fR.
.SS "Context functions"
.IX Subsection "Context functions"
\&\fBOSSL_FUNC_encoder_newctx()\fR returns a context to be used with the rest of
the functions.
.PP
\&\fBOSSL_FUNC_encoder_freectx()\fR frees the given \fIctx\fR, if it was created by
\&\fBOSSL_FUNC_encoder_newctx()\fR.
.PP
\&\fBOSSL_FUNC_encoder_set_ctx_params()\fR sets context data according to parameters
from \fIparams\fR that it recognises.  Unrecognised parameters should be
ignored.
Passing NULL for \fIparams\fR should return true.
.PP
\&\fBOSSL_FUNC_encoder_settable_ctx_params()\fR returns a constant \fBOSSL_PARAM\fR\|(3)
array describing the parameters that \fBOSSL_FUNC_encoder_set_ctx_params()\fR
can handle.
.PP
See \fBOSSL_PARAM\fR\|(3) for further details on the parameters structure used by
\&\fBOSSL_FUNC_encoder_set_ctx_params()\fR and \fBOSSL_FUNC_encoder_settable_ctx_params()\fR.
.SS "Import functions"
.IX Subsection "Import functions"
A provider-native object may be associated with a foreign provider, and may
therefore be unsuitable for direct use with a given ENCODER implementation.
Provided that the foreign provider's implementation to handle the object has
a function to export that object in \fBOSSL_PARAM\fR\|(3) array form, the ENCODER
implementation should be able to import that array and create a suitable
object to be passed to \fBOSSL_FUNC_encoder_encode()\fR's \fIobj_raw\fR.
.PP
\&\fBOSSL_FUNC_encoder_import_object()\fR should import the subset of \fIparams\fR
given with \fIselection\fR to create a provider-native object that can be
passed as \fIobj_raw\fR to \fBOSSL_FUNC_encoder_encode()\fR.
.PP
\&\fBOSSL_FUNC_encoder_free_object()\fR should free the object that was created with
\&\fBOSSL_FUNC_encoder_import_object()\fR.
.SS "Encoding functions"
.IX Subsection "Encoding functions"
\&\fBOSSL_FUNC_encoder_encode()\fR should take a provider-native object (in
\&\fIobj_raw\fR) or an object abstraction (in \fIobj_abstract\fR), and should output
the object in encoded form to the \fBOSSL_CORE_BIO\fR.  The \fIselection\fR bits,
if relevant, should determine in greater detail what will be output.
The encoding functions also take an \fBOSSL_PASSPHRASE_CALLBACK\fR\|(3) function
pointer along with a pointer to application data \fIcbarg\fR, which should be
used when a pass phrase prompt is needed.
.SS "Encoder operation parameters"
.IX Subsection "Encoder operation parameters"
Operation parameters currently recognised by built-in encoders are as
follows:
.IP """cipher"" (\fBOSSL_ENCODER_PARAM_CIPHER\fR) <UTF8 string>" 4
.IX Item """cipher"" (OSSL_ENCODER_PARAM_CIPHER) <UTF8 string>"
The name of the encryption cipher to be used when generating encrypted
encoding.  This is used when encoding private keys, as well as
other objects that need protection.
.Sp
If this name is invalid for the encoding implementation, the
implementation should refuse to perform the encoding, i.e.
\&\fBOSSL_FUNC_encoder_encode_data()\fR and \fBOSSL_FUNC_encoder_encode_object()\fR
should return an error.
.IP """properties"" (\fBOSSL_ENCODER_PARAM_PROPERTIES\fR) <UTF8 string>" 4
.IX Item """properties"" (OSSL_ENCODER_PARAM_PROPERTIES) <UTF8 string>"
The properties to be queried when trying to fetch the algorithm given
with the "cipher" parameter.
This must be given together with the "cipher" parameter to be
considered valid.
.Sp
The encoding implementation isn't obligated to use this value.
However, it is recommended that implementations that do not handle
property strings return an error on receiving this parameter unless
its value NULL or the empty string.
.IP """save-parameters"" (\fBOSSL_ENCODER_PARAM_SAVE_PARAMETERS\fR) <integer>" 4
.IX Item """save-parameters"" (OSSL_ENCODER_PARAM_SAVE_PARAMETERS) <integer>"
If set to 0 disables saving of key domain parameters. Default is 1.
It currently has an effect only on DSA keys.
.PP
Parameters currently recognised by the built-in pass phrase callback:
.IP """info"" (\fBOSSL_PASSPHRASE_PARAM_INFO\fR) <UTF8 string>" 4
.IX Item """info"" (OSSL_PASSPHRASE_PARAM_INFO) <UTF8 string>"
A string of information that will become part of the pass phrase
prompt.  This could be used to give the user information on what kind
of object it's being prompted for.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_FUNC_encoder_newctx()\fR returns a pointer to a context, or NULL on
failure.
.PP
\&\fBOSSL_FUNC_encoder_set_ctx_params()\fR returns 1, unless a recognised
parameter was invalid or caused an error, for which 0 is returned.
.PP
\&\fBOSSL_FUNC_encoder_settable_ctx_params()\fR returns a pointer to an array of
constant \fBOSSL_PARAM\fR\|(3) elements.
.PP
\&\fBOSSL_FUNC_encoder_does_selection()\fR returns 1 if the encoder implementation
supports any of the \fIselection\fR bits, otherwise 0.
.PP
\&\fBOSSL_FUNC_encoder_encode()\fR returns 1 on success, or 0 on failure.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The ENCODER interface was introduced in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
