.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CT 7ossl"
.TH CT 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ct \- Certificate Transparency
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/ct.h>
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This library implements Certificate Transparency (CT) verification for TLS
clients, as defined in RFC 6962. This verification can provide some confidence
that a certificate has been publicly logged in a set of CT logs.
.PP
By default, these checks are disabled. They can be enabled using
\&\fBSSL_CTX_enable_ct\fR\|(3) or \fBSSL_enable_ct\fR\|(3).
.PP
This library can also be used to parse and examine CT data structures, such as
Signed Certificate Timestamps (SCTs), or to read a list of CT logs. There are
functions for:
\&\- decoding and encoding SCTs in DER and TLS wire format.
\&\- printing SCTs.
\&\- verifying the authenticity of SCTs.
\&\- loading a CT log list from a CONF file.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBd2i_SCT_LIST\fR\|(3),
\&\fBCTLOG_STORE_new\fR\|(3),
\&\fBCTLOG_STORE_get0_log_by_id\fR\|(3),
\&\fBSCT_new\fR\|(3),
\&\fBSCT_print\fR\|(3),
\&\fBSCT_validate\fR\|(3),
\&\fBSCT_validate\fR\|(3),
\&\fBCT_POLICY_EVAL_CTX_new\fR\|(3),
\&\fBSSL_CTX_set_ct_validation_callback\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The ct library was added in OpenSSL 1.1.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2017 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
