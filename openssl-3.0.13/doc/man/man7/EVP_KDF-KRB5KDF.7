.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_KDF-KRB5KDF 7ossl"
.TH EVP_KDF-KRB5KDF 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_KDF\-KRB5KDF \- The RFC3961 Krb5 KDF EVP_KDF implementation
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Support for computing the \fBKRB5KDF\fR KDF through the \fBEVP_KDF\fR API.
.PP
The EVP_KDF\-KRB5KDF algorithm implements the key derivation function defined
in RFC 3961, section 5.1 and is used by Krb5 to derive session keys.
Three inputs are required to perform key derivation: a cipher, (for example
AES\-128\-CBC), the initial key, and a constant.
.SS Identity
.IX Subsection "Identity"
"KRB5KDF" is the name for this implementation;
it can be used with the \fBEVP_KDF_fetch()\fR function.
.SS "Supported parameters"
.IX Subsection "Supported parameters"
The supported parameters are:
.IP """properties"" (\fBOSSL_KDF_PARAM_PROPERTIES\fR) <UTF8 string>" 4
.IX Item """properties"" (OSSL_KDF_PARAM_PROPERTIES) <UTF8 string>"
.PD 0
.IP """cipher"" (\fBOSSL_KDF_PARAM_CIPHER\fR) <UTF8 string>" 4
.IX Item """cipher"" (OSSL_KDF_PARAM_CIPHER) <UTF8 string>"
.IP """key"" (\fBOSSL_KDF_PARAM_KEY\fR) <octet string>" 4
.IX Item """key"" (OSSL_KDF_PARAM_KEY) <octet string>"
.PD
These parameters work as described in "PARAMETERS" in \fBEVP_KDF\fR\|(3).
.IP """constant"" (\fBOSSL_KDF_PARAM_CONSTANT\fR) <octet string>" 4
.IX Item """constant"" (OSSL_KDF_PARAM_CONSTANT) <octet string>"
This parameter sets the constant value for the KDF.
If a value is already set, the contents are replaced.
.SH NOTES
.IX Header "NOTES"
A context for KRB5KDF can be obtained by calling:
.PP
.Vb 2
\& EVP_KDF *kdf = EVP_KDF_fetch(NULL, "KRB5KDF", NULL);
\& EVP_KDF_CTX *kctx = EVP_KDF_CTX_new(kdf);
.Ve
.PP
The output length of the KRB5KDF derivation is specified via the \fIkeylen\fR
parameter to the \fBEVP_KDF_derive\fR\|(3) function, and MUST match the key
length for the chosen cipher or an error is returned. Moreover, the
constant's length must not exceed the block size of the cipher.
Since the KRB5KDF output length depends on the chosen cipher, calling
\&\fBEVP_KDF_CTX_get_kdf_size\fR\|(3) to obtain the requisite length returns the correct length
only after the cipher is set. Prior to that \fBEVP_MAX_KEY_LENGTH\fR is returned.
The caller must allocate a buffer of the correct length for the chosen
cipher, and pass that buffer to the \fBEVP_KDF_derive\fR\|(3) function along
with that length.
.SH EXAMPLES
.IX Header "EXAMPLES"
This example derives a key using the AES\-128\-CBC cipher:
.PP
.Vb 7
\& EVP_KDF *kdf;
\& EVP_KDF_CTX *kctx;
\& unsigned char key[16] = "01234...";
\& unsigned char constant[] = "I\*(Aqm a constant";
\& unsigned char out[16];
\& size_t outlen = sizeof(out);
\& OSSL_PARAM params[4], *p = params;
\&
\& kdf = EVP_KDF_fetch(NULL, "KRB5KDF", NULL);
\& kctx = EVP_KDF_CTX_new(kdf);
\& EVP_KDF_free(kdf);
\&
\& *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_CIPHER,
\&                                         SN_aes_128_cbc,
\&                                         strlen(SN_aes_128_cbc));
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_KEY,
\&                                          key, (size_t)16);
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_CONSTANT,
\&                                          constant, strlen(constant));
\& *p = OSSL_PARAM_construct_end();
\& if (EVP_KDF_derive(kctx, out, outlen, params) <= 0)
\&     /* Error */
\&
\& EVP_KDF_CTX_free(kctx);
.Ve
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
RFC 3961
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_KDF\fR\|(3),
\&\fBEVP_KDF_CTX_free\fR\|(3),
\&\fBEVP_KDF_CTX_get_kdf_size\fR\|(3),
\&\fBEVP_KDF_derive\fR\|(3),
"PARAMETERS" in \fBEVP_KDF\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
This functionality was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
