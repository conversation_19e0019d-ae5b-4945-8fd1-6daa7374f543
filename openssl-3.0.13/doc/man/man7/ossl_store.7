.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_STORE 7ossl"
.TH OSSL_STORE 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ossl_store \- Store retrieval functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
#include <openssl/store.h>
.SH DESCRIPTION
.IX Header "DESCRIPTION"
.SS General
.IX Subsection "General"
A STORE is a layer of functionality to retrieve a number of supported
objects from a repository of any kind, addressable as a filename or
as a URI.
.PP
The functionality supports the pattern "open a channel to the
repository", "loop and retrieve one object at a time", and "finish up
by closing the channel".
.PP
The retrieved objects are returned as a wrapper type \fBOSSL_STORE_INFO\fR,
from which an OpenSSL type can be retrieved.
.SS "URI schemes and loaders"
.IX Subsection "URI schemes and loaders"
Support for a URI scheme is called a STORE "loader", and can be added
dynamically from the calling application or from a loadable engine.
.PP
Support for the 'file' scheme is built into \f(CW\*(C`libcrypto\*(C'\fR.
See \fBossl_store\-file\fR\|(7) for more information.
.SS "UI_METHOD and pass phrases"
.IX Subsection "UI_METHOD and pass phrases"
The \fBOSS_STORE\fR API does nothing to enforce any specific format or
encoding on the pass phrase that the \fBUI_METHOD\fR provides.  However,
the pass phrase is expected to be UTF\-8 encoded.  The result of any
other encoding is undefined.
.SH EXAMPLES
.IX Header "EXAMPLES"
.SS "A generic call"
.IX Subsection "A generic call"
.Vb 1
\& OSSL_STORE_CTX *ctx = OSSL_STORE_open("file:/foo/bar/data.pem");
\&
\& /*
\&  * OSSL_STORE_eof() simulates file semantics for any repository to signal
\&  * that no more data can be expected
\&  */
\& while (!OSSL_STORE_eof(ctx)) {
\&     OSSL_STORE_INFO *info = OSSL_STORE_load(ctx);
\&
\&     /*
\&      * Do whatever is necessary with the OSSL_STORE_INFO,
\&      * here just one example
\&      */
\&     switch (OSSL_STORE_INFO_get_type(info)) {
\&     case OSSL_STORE_INFO_CERT:
\&         /* Print the X.509 certificate text */
\&         X509_print_fp(stdout, OSSL_STORE_INFO_get0_CERT(info));
\&         /* Print the X.509 certificate PEM output */
\&         PEM_write_X509(stdout, OSSL_STORE_INFO_get0_CERT(info));
\&         break;
\&     }
\& }
\&
\& OSSL_STORE_close(ctx);
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBOSSL_STORE_INFO\fR\|(3), \fBOSSL_STORE_LOADER\fR\|(3),
\&\fBOSSL_STORE_open\fR\|(3), \fBOSSL_STORE_expect\fR\|(3),
\&\fBOSSL_STORE_SEARCH\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
