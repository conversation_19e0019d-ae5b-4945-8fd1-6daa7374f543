.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_SIGNATURE-ED25519 7ossl"
.TH EVP_SIGNATURE-ED25519 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_SIGNATURE\-ED25519,
EVP_SIGNATURE\-ED448,
Ed25519,
Ed448
\&\- EVP_PKEY Ed25519 and Ed448 support
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBEd25519\fR and \fBEd448\fR EVP_PKEY implementation supports key generation,
one-shot digest sign and digest verify using PureEdDSA and \fBEd25519\fR or \fBEd448\fR
(see RFC8032). It has associated private and public key formats compatible with
RFC 8410.
.SS "ED25519 and ED448 Signature Parameters"
.IX Subsection "ED25519 and ED448 Signature Parameters"
No additional parameters can be set during one-shot signing or verification.
In particular, because PureEdDSA is used, a digest must \fBNOT\fR be specified when
signing or verifying.
See \fBEVP_PKEY\-X25519\fR\|(7) for information related to \fBX25519\fR and \fBX448\fR keys.
.PP
The following signature parameters can be retrieved using
\&\fBEVP_PKEY_CTX_get_params()\fR.
.IP """algorithm-id"" (\fBOSSL_SIGNATURE_PARAM_ALGORITHM_ID\fR) <octet string>" 4
.IX Item """algorithm-id"" (OSSL_SIGNATURE_PARAM_ALGORITHM_ID) <octet string>"
The parameters are described in \fBprovider\-signature\fR\|(7).
.SH NOTES
.IX Header "NOTES"
The PureEdDSA algorithm does not support the streaming mechanism
of other signature algorithms using, for example, \fBEVP_DigestUpdate()\fR.
The message to sign or verify must be passed using the one-shot
\&\fBEVP_DigestSign()\fR and \fBEVP_DigestVerify()\fR functions.
.PP
When calling \fBEVP_DigestSignInit()\fR or \fBEVP_DigestVerifyInit()\fR, the
digest \fItype\fR parameter \fBMUST\fR be set to NULL.
.PP
Applications wishing to sign certificates (or other structures such as
CRLs or certificate requests) using Ed25519 or Ed448 can either use \fBX509_sign()\fR
or \fBX509_sign_ctx()\fR in the usual way.
.PP
Ed25519 or Ed448 private keys can be set directly using
\&\fBEVP_PKEY_new_raw_private_key\fR\|(3) or loaded from a PKCS#8 private key file
using \fBPEM_read_bio_PrivateKey\fR\|(3) (or similar function). Completely new keys
can also be generated (see the example below). Setting a private key also sets
the associated public key.
.PP
Ed25519 or Ed448 public keys can be set directly using
\&\fBEVP_PKEY_new_raw_public_key\fR\|(3) or loaded from a SubjectPublicKeyInfo
structure in a PEM file using \fBPEM_read_bio_PUBKEY\fR\|(3) (or similar function).
.PP
Ed25519 and Ed448 can be tested with the \fBopenssl\-speed\fR\|(1) application
since version 1.1.1.
Valid algorithm names are \fBed25519\fR, \fBed448\fR and \fBeddsa\fR. If \fBeddsa\fR is
specified, then both Ed25519 and Ed448 are benchmarked.
.SH EXAMPLES
.IX Header "EXAMPLES"
To sign a message using a ED25519 or ED448 key:
.PP
.Vb 5
\&    void do_sign(EVP_PKEY *ed_key, unsigned char *msg, size_t msg_len)
\&    {
\&        size_t sig_len;
\&        unsigned char *sig = NULL;
\&        EVP_MD_CTX *md_ctx = EVP_MD_CTX_new();
\&
\&        EVP_DigestSignInit(md_ctx, NULL, NULL, NULL, ed_key);
\&        /* Calculate the requires size for the signature by passing a NULL buffer */
\&        EVP_DigestSign(md_ctx, NULL, &sig_len, msg, msg_len);
\&        sig = OPENSSL_zalloc(sig_len);
\&
\&        EVP_DigestSign(md_ctx, sig, &sig_len, msg, msg_len);
\&        ...
\&        OPENSSL_free(sig);
\&        EVP_MD_CTX_free(md_ctx);
\&    }
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY\-X25519\fR\|(7)
\&\fBprovider\-signature\fR\|(7),
\&\fBEVP_DigestSignInit\fR\|(3),
\&\fBEVP_DigestVerifyInit\fR\|(3),
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2017\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
