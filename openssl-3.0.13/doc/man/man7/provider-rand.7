.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PROVIDER-RAND 7ossl"
.TH PROVIDER-RAND 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
provider\-rand \- The random number generation library <\-> provider
functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 2
\& #include <openssl/core_dispatch.h>
\& #include <openssl/core_names.h>
\&
\& /*
\&  * None of these are actual functions, but are displayed like this for
\&  * the function signatures for functions that are offered as function
\&  * pointers in OSSL_DISPATCH arrays.
\&  */
\&
\& /* Context management */
\& void *OSSL_FUNC_rand_newctx(void *provctx, void *parent,
\&                             const OSSL_DISPATCH *parent_calls);
\& void OSSL_FUNC_rand_freectx(void *ctx);
\&
\& /* Random number generator functions: NIST */
\& int OSSL_FUNC_rand_instantiate(void *ctx, unsigned int strength,
\&                                int prediction_resistance,
\&                                const unsigned char *pstr, size_t pstr_len,
\&                                const OSSL_PARAM params[]);
\& int OSSL_FUNC_rand_uninstantiate(void *ctx);
\& int OSSL_FUNC_rand_generate(void *ctx, unsigned char *out, size_t outlen,
\&                             unsigned int strength, int prediction_resistance,
\&                             const unsigned char *addin, size_t addin_len);
\& int OSSL_FUNC_rand_reseed(void *ctx, int prediction_resistance,
\&                           const unsigned char *ent, size_t ent_len,
\&                           const unsigned char *addin, size_t addin_len);
\&
\& /* Random number generator functions: additional */
\& size_t OSSL_FUNC_rand_nonce(void *ctx, unsigned char *out, size_t outlen,
\&                             int strength, size_t min_noncelen,
\&                             size_t max_noncelen);
\& size_t OSSL_FUNC_rand_get_seed(void *ctx, unsigned char **buffer,
\&                                int entropy, size_t min_len, size_t max_len,
\&                                int prediction_resistance,
\&                                const unsigned char *adin, size_t adin_len);
\& void OSSL_FUNC_rand_clear_seed(void *ctx, unsigned char *buffer, size_t b_len);
\& int OSSL_FUNC_rand_verify_zeroization(void *ctx);
\&
\& /* Context Locking */
\& int OSSL_FUNC_rand_enable_locking(void *ctx);
\& int OSSL_FUNC_rand_lock(void *ctx);
\& void OSSL_FUNC_rand_unlock(void *ctx);
\&
\& /* RAND parameter descriptors */
\& const OSSL_PARAM *OSSL_FUNC_rand_gettable_params(void *provctx);
\& const OSSL_PARAM *OSSL_FUNC_rand_gettable_ctx_params(void *ctx, void *provctx);
\& const OSSL_PARAM *OSSL_FUNC_rand_settable_ctx_params(void *ctx, void *provctx);
\&
\& /* RAND parameters */
\& int OSSL_FUNC_rand_get_params(OSSL_PARAM params[]);
\& int OSSL_FUNC_rand_get_ctx_params(void *ctx, OSSL_PARAM params[]);
\& int OSSL_FUNC_rand_set_ctx_params(void *ctx, const OSSL_PARAM params[]);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This documentation is primarily aimed at provider authors. See \fBprovider\fR\|(7)
for further information.
.PP
The RAND operation enables providers to implement random number generation
algorithms and random number sources and make
them available to applications via the API function \fBEVP_RAND\fR\|(3).
.SS "Context Management Functions"
.IX Subsection "Context Management Functions"
\&\fBOSSL_FUNC_rand_newctx()\fR should create and return a pointer to a provider side
structure for holding context information during a rand operation.
A pointer to this context will be passed back in a number of the other rand
operation function calls.
The parameter \fIprovctx\fR is the provider context generated during provider
initialisation (see \fBprovider\fR\|(7)).
The parameter \fIparent\fR specifies another rand instance to be used for
seeding purposes.  If NULL and the specific instance supports it, the
operating system will be used for seeding.
The parameter \fIparent_calls\fR points to the dispatch table for \fIparent\fR.
Thus, the parent need not be from the same provider as the new instance.
.PP
\&\fBOSSL_FUNC_rand_freectx()\fR is passed a pointer to the provider side rand context in
the \fImctx\fR parameter.
If it receives NULL as \fIctx\fR value, it should not do anything other than
return.
This function should free any resources associated with that context.
.SS "Random Number Generator Functions: NIST"
.IX Subsection "Random Number Generator Functions: NIST"
These functions correspond to those defined in NIST SP 800\-90A and SP 800\-90C.
.PP
\&\fBOSSL_FUNC_rand_instantiate()\fR is used to instantiate the DRBG \fIctx\fR at a requested
security \fIstrength\fR.  In addition, \fIprediction_resistance\fR can be requested.
Additional input \fIaddin\fR of length \fIaddin_len\fR bytes can optionally
be provided.  The parameters specified in \fIparams\fR configure the DRBG and these
should be processed before instantiation.
.PP
\&\fBOSSL_FUNC_rand_uninstantiate()\fR is used to uninstantiate the DRBG \fIctx\fR.  After being
uninstantiated, a DRBG is unable to produce output until it is instantiated
anew.
.PP
\&\fBOSSL_FUNC_rand_generate()\fR is used to generate random bytes from the DRBG \fIctx\fR.
It will generate \fIoutlen\fR bytes placing them into the buffer pointed to by
\&\fIout\fR.  The generated bytes will meet the specified security \fIstrength\fR and,
if \fIprediction_resistance\fR is true, the bytes will be produced after reseeding
from a live entropy source.  Additional input \fIaddin\fR of length \fIaddin_len\fR
bytes can optionally be provided.
.SS "Random Number Generator Functions: Additional"
.IX Subsection "Random Number Generator Functions: Additional"
\&\fBOSSL_FUNC_rand_nonce()\fR is used to generate a nonce of the given \fIstrength\fR with a
length from \fImin_noncelen\fR to \fImax_noncelen\fR. If the output buffer \fIout\fR is
NULL, the length of the nonce should be returned.
.PP
\&\fBOSSL_FUNC_rand_get_seed()\fR is used by deterministic generators to obtain their
seeding material from their parent.  The seed bytes will meet the specified
security level of \fIentropy\fR bits and there will be between \fImin_len\fR
and \fImax_len\fR inclusive bytes in total.  If \fIprediction_resistance\fR is
true, the bytes will be produced from a live entropy source.  Additional
input \fIaddin\fR of length \fIaddin_len\fR bytes can optionally be provided.
A pointer to the seed material is returned in \fI*buffer\fR and this must be
freed by a later call to \fBOSSL_FUNC_rand_clear_seed()\fR.
.PP
\&\fBOSSL_FUNC_rand_clear_seed()\fR frees a seed \fIbuffer\fR of length \fIb_len\fR bytes
which was previously allocated by \fBOSSL_FUNC_rand_get_seed()\fR.
.PP
\&\fBOSSL_FUNC_rand_verify_zeroization()\fR is used to determine if the internal state of the
DRBG is zero.  This capability is mandated by NIST as part of the self
tests, it is unlikely to be useful in other circumstances.
.SS "Context Locking"
.IX Subsection "Context Locking"
When DRBGs are used by multiple threads, there must be locking employed to
ensure their proper operation.  Because locking introduces an overhead, it
is disabled by default.
.PP
\&\fBOSSL_FUNC_rand_enable_locking()\fR allows locking to be turned on for a DRBG and all of
its parent DRBGs.  From this call onwards, the DRBG can be used in a thread
safe manner.
.PP
\&\fBOSSL_FUNC_rand_lock()\fR is used to lock a DRBG.  Once locked, exclusive access
is guaranteed.
.PP
\&\fBOSSL_FUNC_rand_unlock()\fR is used to unlock a DRBG.
.SS "Rand Parameters"
.IX Subsection "Rand Parameters"
See \fBOSSL_PARAM\fR\|(3) for further details on the parameters structure used by
these functions.
.PP
\&\fBOSSL_FUNC_rand_get_params()\fR gets details of parameter values associated with the
provider algorithm and stores them in \fIparams\fR.
.PP
\&\fBOSSL_FUNC_rand_set_ctx_params()\fR sets rand parameters associated with the given
provider side rand context \fIctx\fR to \fIparams\fR.
Any parameter settings are additional to any that were previously set.
Passing NULL for \fIparams\fR should return true.
.PP
\&\fBOSSL_FUNC_rand_get_ctx_params()\fR gets details of currently set parameter values
associated with the given provider side rand context \fIctx\fR and stores them
in \fIparams\fR.
Passing NULL for \fIparams\fR should return true.
.PP
\&\fBOSSL_FUNC_rand_gettable_params()\fR, \fBOSSL_FUNC_rand_gettable_ctx_params()\fR,
and \fBOSSL_FUNC_rand_settable_ctx_params()\fR all return constant \fBOSSL_PARAM\fR\|(3)
arrays as descriptors of the parameters that \fBOSSL_FUNC_rand_get_params()\fR,
\&\fBOSSL_FUNC_rand_get_ctx_params()\fR, and \fBOSSL_FUNC_rand_set_ctx_params()\fR
can handle, respectively.  \fBOSSL_FUNC_rand_gettable_ctx_params()\fR
and \fBOSSL_FUNC_rand_settable_ctx_params()\fR will return the parameters
associated with the provider side context \fIctx\fR in its current state
if it is not NULL.  Otherwise, they return the parameters associated
with the provider side algorithm \fIprovctx\fR.
.PP
Parameters currently recognised by built-in rands are as follows. Not all
parameters are relevant to, or are understood by all rands:
.IP """state"" (\fBOSSL_RAND_PARAM_STATE\fR) <integer>" 4
.IX Item """state"" (OSSL_RAND_PARAM_STATE) <integer>"
Returns the state of the random number generator.
.IP """strength"" (\fBOSSL_RAND_PARAM_STRENGTH\fR) <unsigned integer>" 4
.IX Item """strength"" (OSSL_RAND_PARAM_STRENGTH) <unsigned integer>"
Returns the bit strength of the random number generator.
.PP
For rands that are also deterministic random bit generators (DRBGs), these
additional parameters are recognised. Not all
parameters are relevant to, or are understood by all DRBG rands:
.IP """reseed_requests"" (\fBOSSL_DRBG_PARAM_RESEED_REQUESTS\fR) <unsigned integer>" 4
.IX Item """reseed_requests"" (OSSL_DRBG_PARAM_RESEED_REQUESTS) <unsigned integer>"
Reads or set the number of generate requests before reseeding the
associated RAND ctx.
.IP """reseed_time_interval"" (\fBOSSL_DRBG_PARAM_RESEED_TIME_INTERVAL\fR) <integer>" 4
.IX Item """reseed_time_interval"" (OSSL_DRBG_PARAM_RESEED_TIME_INTERVAL) <integer>"
Reads or set the number of elapsed seconds before reseeding the
associated RAND ctx.
.IP """max_request"" (\fBOSSL_DRBG_PARAM_RESEED_REQUESTS\fR) <unsigned integer>" 4
.IX Item """max_request"" (OSSL_DRBG_PARAM_RESEED_REQUESTS) <unsigned integer>"
Specifies the maximum number of bytes that can be generated in a single
call to OSSL_FUNC_rand_generate.
.IP """min_entropylen"" (\fBOSSL_DRBG_PARAM_MIN_ENTROPYLEN\fR) <unsigned integer>" 4
.IX Item """min_entropylen"" (OSSL_DRBG_PARAM_MIN_ENTROPYLEN) <unsigned integer>"
.PD 0
.IP """max_entropylen"" (\fBOSSL_DRBG_PARAM_MAX_ENTROPYLEN\fR) <unsigned integer>" 4
.IX Item """max_entropylen"" (OSSL_DRBG_PARAM_MAX_ENTROPYLEN) <unsigned integer>"
.PD
Specify the minimum and maximum number of bytes of random material that
can be used to seed the DRBG.
.IP """min_noncelen"" (\fBOSSL_DRBG_PARAM_MIN_NONCELEN\fR) <unsigned integer>" 4
.IX Item """min_noncelen"" (OSSL_DRBG_PARAM_MIN_NONCELEN) <unsigned integer>"
.PD 0
.IP """max_noncelen"" (\fBOSSL_DRBG_PARAM_MAX_NONCELEN\fR) <unsigned integer>" 4
.IX Item """max_noncelen"" (OSSL_DRBG_PARAM_MAX_NONCELEN) <unsigned integer>"
.PD
Specify the minimum and maximum number of bytes of nonce that can be used to
instantiate the DRBG.
.IP """max_perslen"" (\fBOSSL_DRBG_PARAM_MAX_PERSLEN\fR) <unsigned integer>" 4
.IX Item """max_perslen"" (OSSL_DRBG_PARAM_MAX_PERSLEN) <unsigned integer>"
.PD 0
.IP """max_adinlen"" (\fBOSSL_DRBG_PARAM_MAX_ADINLEN\fR) <unsigned integer>" 4
.IX Item """max_adinlen"" (OSSL_DRBG_PARAM_MAX_ADINLEN) <unsigned integer>"
.PD
Specify the minimum and maximum number of bytes of personalisation string
that can be used with the DRBG.
.IP """reseed_counter"" (\fBOSSL_DRBG_PARAM_RESEED_COUNTER\fR) <unsigned integer>" 4
.IX Item """reseed_counter"" (OSSL_DRBG_PARAM_RESEED_COUNTER) <unsigned integer>"
Specifies the number of times the DRBG has been seeded or reseeded.
.IP """digest"" (\fBOSSL_DRBG_PARAM_DIGEST\fR) <UTF8 string>" 4
.IX Item """digest"" (OSSL_DRBG_PARAM_DIGEST) <UTF8 string>"
.PD 0
.IP """cipher"" (\fBOSSL_DRBG_PARAM_CIPHER\fR) <UTF8 string>" 4
.IX Item """cipher"" (OSSL_DRBG_PARAM_CIPHER) <UTF8 string>"
.IP """mac"" (\fBOSSL_DRBG_PARAM_MAC\fR) <UTF8 string>" 4
.IX Item """mac"" (OSSL_DRBG_PARAM_MAC) <UTF8 string>"
.PD
Sets the name of the underlying cipher, digest or MAC to be used.
It must name a suitable algorithm for the DRBG that's being used.
.IP """properties"" (\fBOSSL_DRBG_PARAM_PROPERTIES\fR) <UTF8 string>" 4
.IX Item """properties"" (OSSL_DRBG_PARAM_PROPERTIES) <UTF8 string>"
Sets the properties to be queried when trying to fetch an underlying algorithm.
This must be given together with the algorithm naming parameter to be
considered valid.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_FUNC_rand_newctx()\fR should return the newly created
provider side rand context, or NULL on failure.
.PP
\&\fBOSSL_FUNC_rand_gettable_params()\fR, \fBOSSL_FUNC_rand_gettable_ctx_params()\fR and
\&\fBOSSL_FUNC_rand_settable_ctx_params()\fR should return a constant \fBOSSL_PARAM\fR\|(3)
array, or NULL if none is offered.
.PP
\&\fBOSSL_FUNC_rand_nonce()\fR returns the size of the generated nonce, or 0 on error.
.PP
\&\fBOSSL_FUNC_rand_get_seed()\fR returns the size of the generated seed, or 0 on
error.
.PP
All of the remaining functions should return 1 for success or 0 on error.
.SH NOTES
.IX Header "NOTES"
The RAND life-cycle is described in \fBlife_cycle\-rand\fR\|(7).  Providers should
ensure that the various transitions listed there are supported.  At some point
the EVP layer will begin enforcing the listed transitions.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\fR\|(7),
\&\fBRAND\fR\|(7),
\&\fBEVP_RAND\fR\|(7),
\&\fBlife_cycle\-rand\fR\|(7),
\&\fBEVP_RAND\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The provider RAND interface was introduced in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
