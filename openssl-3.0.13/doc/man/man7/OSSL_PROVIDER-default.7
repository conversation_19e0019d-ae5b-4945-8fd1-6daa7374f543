.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_PROVIDER-DEFAULT 7ossl"
.TH OSSL_PROVIDER-DEFAULT 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_PROVIDER\-default \- OpenSSL default provider
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The OpenSSL default provider supplies the majority of OpenSSL's diverse
algorithm implementations. If an application doesn't specify anything else
explicitly (e.g. in the application or via config), then this is the
provider that will be used as fallback: It is loaded automatically the
first time that an algorithm is fetched from a provider or a function
acting on providers is called and no other provider has been loaded yet.
.PP
If an attempt to load a provider has already been made (whether successful
or not) then the default provider won't be loaded automatically. Therefore
if the default provider is to be used in conjunction with other providers
then it must be loaded explicitly. Automatic loading of the default
provider only occurs a maximum of once; if the default provider is
explicitly unloaded then the default provider will not be automatically
loaded again.
.SS Properties
.IX Subsection "Properties"
The implementations in this provider specifically have this property
defined:
.IP """provider=default""" 4
.IX Item """provider=default"""
.PP
It may be used in a property query string with fetching functions such as
\&\fBEVP_MD_fetch\fR\|(3) or \fBEVP_CIPHER_fetch\fR\|(3), as well as with other
functions that take a property query string, such as
\&\fBEVP_PKEY_CTX_new_from_name\fR\|(3).
.PP
It isn't mandatory to query for this property, except to make sure to get
implementations of this provider and none other.
.PP
Some implementations may define additional properties.  Exact information is
listed below
.SH "OPERATIONS AND ALGORITHMS"
.IX Header "OPERATIONS AND ALGORITHMS"
The OpenSSL default provider supports these operations and algorithms:
.SS "Hashing Algorithms / Message Digests"
.IX Subsection "Hashing Algorithms / Message Digests"
.IP "SHA1, see \fBEVP_MD\-SHA1\fR\|(7)" 4
.IX Item "SHA1, see EVP_MD-SHA1"
.PD 0
.IP "SHA2, see \fBEVP_MD\-SHA2\fR\|(7)" 4
.IX Item "SHA2, see EVP_MD-SHA2"
.IP "SHA3, see \fBEVP_MD\-SHA3\fR\|(7)" 4
.IX Item "SHA3, see EVP_MD-SHA3"
.IP "KECCAK-KMAC, see \fBEVP_MD\-KECCAK\-KMAC\fR\|(7)" 4
.IX Item "KECCAK-KMAC, see EVP_MD-KECCAK-KMAC"
.IP "SHAKE, see \fBEVP_MD\-SHAKE\fR\|(7)" 4
.IX Item "SHAKE, see EVP_MD-SHAKE"
.IP "BLAKE2, see \fBEVP_MD\-BLAKE2\fR\|(7)" 4
.IX Item "BLAKE2, see EVP_MD-BLAKE2"
.IP "SM3, see \fBEVP_MD\-SM3\fR\|(7)" 4
.IX Item "SM3, see EVP_MD-SM3"
.IP "MD5, see \fBEVP_MD\-MD5\fR\|(7)" 4
.IX Item "MD5, see EVP_MD-MD5"
.IP "MD5\-SHA1, see \fBEVP_MD\-MD5\-SHA1\fR\|(7)" 4
.IX Item "MD5-SHA1, see EVP_MD-MD5-SHA1"
.IP "RIPEMD160, see \fBEVP_MD\-RIPEMD160\fR\|(7)" 4
.IX Item "RIPEMD160, see EVP_MD-RIPEMD160"
.IP "NULL, see \fBEVP_MD\-NULL\fR\|(7)" 4
.IX Item "NULL, see EVP_MD-NULL"
.PD
.SS "Symmetric Ciphers"
.IX Subsection "Symmetric Ciphers"
.IP "AES, see \fBEVP_CIPHER\-AES\fR\|(7)" 4
.IX Item "AES, see EVP_CIPHER-AES"
.PD 0
.IP "ARIA, see \fBEVP_CIPHER\-ARIA\fR\|(7)" 4
.IX Item "ARIA, see EVP_CIPHER-ARIA"
.IP "CAMELLIA, see \fBEVP_CIPHER\-CAMELLIA\fR\|(7)" 4
.IX Item "CAMELLIA, see EVP_CIPHER-CAMELLIA"
.IP "3DES, see \fBEVP_CIPHER\-DES\fR\|(7)" 4
.IX Item "3DES, see EVP_CIPHER-DES"
.IP "SEED, see \fBEVP_CIPHER\-SEED\fR\|(7)" 4
.IX Item "SEED, see EVP_CIPHER-SEED"
.IP "SM4, see \fBEVP_CIPHER\-SM4\fR\|(7)" 4
.IX Item "SM4, see EVP_CIPHER-SM4"
.IP "ChaCha20, see \fBEVP_CIPHER\-CHACHA\fR\|(7)" 4
.IX Item "ChaCha20, see EVP_CIPHER-CHACHA"
.IP "ChaCha20\-Poly1305, see \fBEVP_CIPHER\-CHACHA\fR\|(7)" 4
.IX Item "ChaCha20-Poly1305, see EVP_CIPHER-CHACHA"
.IP "NULL, see \fBEVP_CIPHER\-NULL\fR\|(7)" 4
.IX Item "NULL, see EVP_CIPHER-NULL"
.PD
.SS "Message Authentication Code (MAC)"
.IX Subsection "Message Authentication Code (MAC)"
.IP "BLAKE2, see \fBEVP_MAC\-BLAKE2\fR\|(7)" 4
.IX Item "BLAKE2, see EVP_MAC-BLAKE2"
.PD 0
.IP "CMAC, see \fBEVP_MAC\-CMAC\fR\|(7)" 4
.IX Item "CMAC, see EVP_MAC-CMAC"
.IP "GMAC, see \fBEVP_MAC\-GMAC\fR\|(7)" 4
.IX Item "GMAC, see EVP_MAC-GMAC"
.IP "HMAC, see \fBEVP_MAC\-HMAC\fR\|(7)" 4
.IX Item "HMAC, see EVP_MAC-HMAC"
.IP "KMAC, see \fBEVP_MAC\-KMAC\fR\|(7)" 4
.IX Item "KMAC, see EVP_MAC-KMAC"
.IP "SIPHASH, see \fBEVP_MAC\-Siphash\fR\|(7)" 4
.IX Item "SIPHASH, see EVP_MAC-Siphash"
.IP "POLY1305, see \fBEVP_MAC\-Poly1305\fR\|(7)" 4
.IX Item "POLY1305, see EVP_MAC-Poly1305"
.PD
.SS "Key Derivation Function (KDF)"
.IX Subsection "Key Derivation Function (KDF)"
.IP "HKDF, see \fBEVP_KDF\-HKDF\fR\|(7)" 4
.IX Item "HKDF, see EVP_KDF-HKDF"
.PD 0
.IP "SSKDF, see \fBEVP_KDF\-SS\fR\|(7)" 4
.IX Item "SSKDF, see EVP_KDF-SS"
.IP "PBKDF2, see \fBEVP_KDF\-PBKDF2\fR\|(7)" 4
.IX Item "PBKDF2, see EVP_KDF-PBKDF2"
.IP "PKCS12KDF, see \fBEVP_KDF\-PKCS12KDF\fR\|(7)" 4
.IX Item "PKCS12KDF, see EVP_KDF-PKCS12KDF"
.IP "SSHKDF, see \fBEVP_KDF\-SSHKDF\fR\|(7)" 4
.IX Item "SSHKDF, see EVP_KDF-SSHKDF"
.IP "TLS1\-PRF, see \fBEVP_KDF\-TLS1_PRF\fR\|(7)" 4
.IX Item "TLS1-PRF, see EVP_KDF-TLS1_PRF"
.IP "KBKDF, see \fBEVP_KDF\-KB\fR\|(7)" 4
.IX Item "KBKDF, see EVP_KDF-KB"
.IP "X942KDF\-ASN1, see \fBEVP_KDF\-X942\-ASN1\fR\|(7)" 4
.IX Item "X942KDF-ASN1, see EVP_KDF-X942-ASN1"
.IP "X942KDF\-CONCAT, see \fBEVP_KDF\-X942\-CONCAT\fR\|(7)" 4
.IX Item "X942KDF-CONCAT, see EVP_KDF-X942-CONCAT"
.IP "X963KDF, see \fBEVP_KDF\-X963\fR\|(7)" 4
.IX Item "X963KDF, see EVP_KDF-X963"
.IP "SCRYPT, see \fBEVP_KDF\-SCRYPT\fR\|(7)" 4
.IX Item "SCRYPT, see EVP_KDF-SCRYPT"
.IP "KRB5KDF, see \fBEVP_KDF\-KRB5KDF\fR\|(7)" 4
.IX Item "KRB5KDF, see EVP_KDF-KRB5KDF"
.PD
.SS "Key Exchange"
.IX Subsection "Key Exchange"
.IP "DH, see \fBEVP_KEYEXCH\-DH\fR\|(7)" 4
.IX Item "DH, see EVP_KEYEXCH-DH"
.PD 0
.IP "ECDH, see \fBEVP_KEYEXCH\-ECDH\fR\|(7)" 4
.IX Item "ECDH, see EVP_KEYEXCH-ECDH"
.IP "X25519, see \fBEVP_KEYEXCH\-X25519\fR\|(7)" 4
.IX Item "X25519, see EVP_KEYEXCH-X25519"
.IP "X448, see \fBEVP_KEYEXCH\-X448\fR\|(7)" 4
.IX Item "X448, see EVP_KEYEXCH-X448"
.PD
.SS "Asymmetric Signature"
.IX Subsection "Asymmetric Signature"
.IP "DSA, see \fBEVP_SIGNATURE\-DSA\fR\|(7)" 4
.IX Item "DSA, see EVP_SIGNATURE-DSA"
.PD 0
.IP "RSA, see \fBEVP_SIGNATURE\-RSA\fR\|(7)" 4
.IX Item "RSA, see EVP_SIGNATURE-RSA"
.IP "HMAC, see \fBEVP_SIGNATURE\-HMAC\fR\|(7)" 4
.IX Item "HMAC, see EVP_SIGNATURE-HMAC"
.IP "SIPHASH, see \fBEVP_SIGNATURE\-Siphash\fR\|(7)" 4
.IX Item "SIPHASH, see EVP_SIGNATURE-Siphash"
.IP "POLY1305, see \fBEVP_SIGNATURE\-Poly1305\fR\|(7)" 4
.IX Item "POLY1305, see EVP_SIGNATURE-Poly1305"
.IP "CMAC, see \fBEVP_SIGNATURE\-CMAC\fR\|(7)" 4
.IX Item "CMAC, see EVP_SIGNATURE-CMAC"
.PD
.SS "Asymmetric Cipher"
.IX Subsection "Asymmetric Cipher"
.IP "RSA, see \fBEVP_ASYM_CIPHER\-RSA\fR\|(7)" 4
.IX Item "RSA, see EVP_ASYM_CIPHER-RSA"
.PD 0
.IP "SM2, see \fBEVP_ASYM_CIPHER\-SM2\fR\|(7)" 4
.IX Item "SM2, see EVP_ASYM_CIPHER-SM2"
.PD
.SS "Asymmetric Key Encapsulation"
.IX Subsection "Asymmetric Key Encapsulation"
.IP "RSA, see \fBEVP_KEM\-RSA\fR\|(7)" 4
.IX Item "RSA, see EVP_KEM-RSA"
.SS "Asymmetric Key Management"
.IX Subsection "Asymmetric Key Management"
.PD 0
.IP "DH, see \fBEVP_KEYMGMT\-DH\fR\|(7)" 4
.IX Item "DH, see EVP_KEYMGMT-DH"
.IP "DHX, see \fBEVP_KEYMGMT\-DHX\fR\|(7)" 4
.IX Item "DHX, see EVP_KEYMGMT-DHX"
.IP "DSA, see \fBEVP_KEYMGMT\-DSA\fR\|(7)" 4
.IX Item "DSA, see EVP_KEYMGMT-DSA"
.IP "RSA, see \fBEVP_KEYMGMT\-RSA\fR\|(7)" 4
.IX Item "RSA, see EVP_KEYMGMT-RSA"
.IP "EC, see \fBEVP_KEYMGMT\-EC\fR\|(7)" 4
.IX Item "EC, see EVP_KEYMGMT-EC"
.IP "X25519, see \fBEVP_KEYMGMT\-X25519\fR\|(7)" 4
.IX Item "X25519, see EVP_KEYMGMT-X25519"
.IP "X448, see \fBEVP_KEYMGMT\-X448\fR\|(7)" 4
.IX Item "X448, see EVP_KEYMGMT-X448"
.PD
.SS "Random Number Generation"
.IX Subsection "Random Number Generation"
.IP "CTR-DRBG, see \fBEVP_RAND\-CTR\-DRBG\fR\|(7)" 4
.IX Item "CTR-DRBG, see EVP_RAND-CTR-DRBG"
.PD 0
.IP "HASH-DRBG, see \fBEVP_RAND\-HASH\-DRBG\fR\|(7)" 4
.IX Item "HASH-DRBG, see EVP_RAND-HASH-DRBG"
.IP "HMAC-DRBG, see \fBEVP_RAND\-HMAC\-DRBG\fR\|(7)" 4
.IX Item "HMAC-DRBG, see EVP_RAND-HMAC-DRBG"
.IP "SEED-SRC,  see \fBEVP_RAND\-SEED\-SRC\fR\|(7)" 4
.IX Item "SEED-SRC, see EVP_RAND-SEED-SRC"
.IP "TEST-RAND, see \fBEVP_RAND\-TEST\-RAND\fR\|(7)" 4
.IX Item "TEST-RAND, see EVP_RAND-TEST-RAND"
.PD
.SS "Asymmetric Key Encoder"
.IX Subsection "Asymmetric Key Encoder"
The default provider also includes all of the encoding algorithms
present in the base provider.  Some of these have the property "fips=yes",
to allow them to be used together with the FIPS provider.
.IP "RSA, see \fBOSSL_ENCODER\-RSA\fR\|(7)" 4
.IX Item "RSA, see OSSL_ENCODER-RSA"
.PD 0
.IP "DH, see \fBOSSL_ENCODER\-DH\fR\|(7)" 4
.IX Item "DH, see OSSL_ENCODER-DH"
.IP "DSA, see \fBOSSL_ENCODER\-DSA\fR\|(7)" 4
.IX Item "DSA, see OSSL_ENCODER-DSA"
.IP "EC, see \fBOSSL_ENCODER\-EC\fR\|(7)" 4
.IX Item "EC, see OSSL_ENCODER-EC"
.IP "X25519, see \fBOSSL_ENCODER\-X25519\fR\|(7)" 4
.IX Item "X25519, see OSSL_ENCODER-X25519"
.IP "X448, see \fBOSSL_ENCODER\-X448\fR\|(7)" 4
.IX Item "X448, see OSSL_ENCODER-X448"
.PD
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\-core.h\fR\|(7), \fBopenssl\-core_dispatch.h\fR\|(7), \fBprovider\fR\|(7),
\&\fBOSSL_PROVIDER\-base\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The RIPEMD160 digest was added to the default provider in OpenSSL 3.0.7.
.PP
All other functionality was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
