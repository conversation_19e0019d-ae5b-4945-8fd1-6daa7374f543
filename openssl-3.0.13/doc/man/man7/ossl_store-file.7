.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_STORE-FILE 7ossl"
.TH OSSL_STORE-FILE 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ossl_store\-file \- The store 'file' scheme loader
.SH SYNOPSIS
.IX Header "SYNOPSIS"
#include <openssl/store.h>
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Support for the 'file' scheme is built into \f(CW\*(C`libcrypto\*(C'\fR.
Since files come in all kinds of formats and content types, the 'file'
scheme has its own layer of functionality called "file handlers",
which are used to try to decode diverse types of file contents.
.PP
In case a file is formatted as PEM, each called file handler receives
the PEM name (everything following any '\f(CW\*(C`\-\-\-\-\-BEGIN \*(C'\fR') as well as
possible PEM headers, together with the decoded PEM body.  Since PEM
formatted files can contain more than one object, the file handlers
are called upon for each such object.
.PP
If the file isn't determined to be formatted as PEM, the content is
loaded in raw form in its entirety and passed to the available file
handlers as is, with no PEM name or headers.
.PP
Each file handler is expected to handle PEM and non-PEM content as
appropriate.  Some may refuse non-PEM content for the sake of
determinism (for example, there are keys out in the wild that are
represented as an ASN.1 OCTET STRING.  In raw form, it's not easily
possible to distinguish those from any other data coming as an ASN.1
OCTET STRING, so such keys would naturally be accepted as PEM files
only).
.SH NOTES
.IX Header "NOTES"
When needed, the 'file' scheme loader will require a pass phrase by
using the \fBUI_METHOD\fR that was passed via \fBOSSL_STORE_open()\fR.
This pass phrase is expected to be UTF\-8 encoded, anything else will
give an undefined result.
The files made accessible through this loader are expected to be
standard compliant with regards to pass phrase encoding.
Files that aren't should be re-generated with a correctly encoded pass
phrase.
See \fBpassphrase\-encoding\fR\|(7) for more information.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBossl_store\fR\|(7), \fBpassphrase\-encoding\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
