.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY-DSA 7ossl"
.TH EVP_PKEY-DSA 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY\-DSA, EVP_KEYMGMT\-DSA \- EVP_PKEY DSA keytype and algorithm support
.SH DESCRIPTION
.IX Header "DESCRIPTION"
For \fBDSA\fR the FIPS186\-4 standard specifies that the values used for FFC
parameter generation are also required for parameter validation.
This means that optional FFC domain parameter values for \fIseed\fR, \fIpcounter\fR
and \fIgindex\fR may need to be stored for validation purposes. For \fBDSA\fR these
fields are not stored in the ASN1 data so they need to be stored externally if
validation is required.
.SS "DSA parameters"
.IX Subsection "DSA parameters"
The \fBDSA\fR key type supports the FFC parameters (see
"FFC parameters" in \fBEVP_PKEY\-FFC\fR\|(7)).
.SS "DSA key generation parameters"
.IX Subsection "DSA key generation parameters"
The \fBDSA\fR key type supports the FFC key generation parameters (see
"FFC key generation parameters" in \fBEVP_PKEY\-FFC\fR\|(7)
.PP
The following restrictions apply to the "pbits" field:
.PP
For "fips186_4" this must be either 2048 or 3072.
For "fips186_2" this must be 1024.
For "group" this can be any one of 2048, 3072, 4096, 6144 or 8192.
.SS "DSA key validation"
.IX Subsection "DSA key validation"
For DSA keys, \fBEVP_PKEY_param_check\fR\|(3) behaves in the following way:
The OpenSSL FIPS provider conforms to the rules within the FIPS186\-4
standard for FFC parameter validation. For backwards compatibility the OpenSSL
default provider uses a much simpler check (see below) for parameter validation,
unless the seed parameter is set.
.PP
For DSA keys, \fBEVP_PKEY_param_check_quick\fR\|(3) behaves in the following way:
A simple check of L and N and partial g is performed. The default provider
also supports validation of legacy "fips186_2" keys.
.PP
For DSA keys, \fBEVP_PKEY_public_check\fR\|(3), \fBEVP_PKEY_private_check\fR\|(3) and
\&\fBEVP_PKEY_pairwise_check\fR\|(3) the OpenSSL default and FIPS providers conform to
the rules within SP800\-56Ar3 for public, private and pairwise tests respectively.
.SH EXAMPLES
.IX Header "EXAMPLES"
An \fBEVP_PKEY\fR context can be obtained by calling:
.PP
.Vb 1
\&    EVP_PKEY_CTX *pctx = EVP_PKEY_CTX_new_from_name(NULL, "DSA", NULL);
.Ve
.PP
The \fBDSA\fR domain parameters can be generated by calling:
.PP
.Vb 6
\&    unsigned int pbits = 2048;
\&    unsigned int qbits = 256;
\&    int gindex = 1;
\&    OSSL_PARAM params[5];
\&    EVP_PKEY *param_key = NULL;
\&    EVP_PKEY_CTX *pctx = NULL;
\&
\&    pctx = EVP_PKEY_CTX_new_from_name(NULL, "DSA", NULL);
\&    EVP_PKEY_paramgen_init(pctx);
\&
\&    params[0] = OSSL_PARAM_construct_uint("pbits", &pbits);
\&    params[1] = OSSL_PARAM_construct_uint("qbits", &qbits);
\&    params[2] = OSSL_PARAM_construct_int("gindex", &gindex);
\&    params[3] = OSSL_PARAM_construct_utf8_string("digest", "SHA384", 0);
\&    params[4] = OSSL_PARAM_construct_end();
\&    EVP_PKEY_CTX_set_params(pctx, params);
\&
\&    EVP_PKEY_generate(pctx, &param_key);
\&    EVP_PKEY_CTX_free(pctx);
\&
\&    EVP_PKEY_print_params(bio_out, param_key, 0, NULL);
.Ve
.PP
A \fBDSA\fR key can be generated using domain parameters by calling:
.PP
.Vb 2
\&    EVP_PKEY *key = NULL;
\&    EVP_PKEY_CTX *gctx = NULL;
\&
\&    gctx = EVP_PKEY_CTX_new_from_pkey(NULL, param_key, NULL);
\&    EVP_PKEY_keygen_init(gctx);
\&    EVP_PKEY_generate(gctx, &key);
\&    EVP_PKEY_CTX_free(gctx);
\&    EVP_PKEY_print_private(bio_out, key, 0, NULL);
.Ve
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
The following sections of FIPS186\-4:
.IP "A.1.1.2 Generation of Probable Primes p and q Using an Approved Hash Function." 4
.IX Item "A.1.1.2 Generation of Probable Primes p and q Using an Approved Hash Function."
.PD 0
.IP "A.2.3 Generation of canonical generator g." 4
.IX Item "A.2.3 Generation of canonical generator g."
.IP "A.2.1 Unverifiable Generation of the Generator g." 4
.IX Item "A.2.1 Unverifiable Generation of the Generator g."
.PD
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY\-FFC\fR\|(7),
\&\fBEVP_SIGNATURE\-DSA\fR\|(7)
\&\fBEVP_PKEY\fR\|(3),
\&\fBprovider\-keymgmt\fR\|(7),
\&\fBEVP_KEYMGMT\fR\|(3),
\&\fBOSSL_PROVIDER\-default\fR\|(7),
\&\fBOSSL_PROVIDER\-FIPS\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
