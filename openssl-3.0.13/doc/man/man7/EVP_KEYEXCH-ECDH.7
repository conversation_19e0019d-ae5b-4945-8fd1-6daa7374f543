.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_KEYEXCH-ECDH 7ossl"
.TH EVP_KEYEXCH-ECDH 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_KEYEXCH\-ECDH \- ECDH Key Exchange algorithm support
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Key exchange support for the \fBECDH\fR key type.
.SS "ECDH Key Exchange parameters"
.IX Subsection "ECDH Key Exchange parameters"
.IP """ecdh-cofactor-mode"" (\fBOSSL_EXCHANGE_PARAM_EC_ECDH_COFACTOR_MODE\fR) <integer>" 4
.IX Item """ecdh-cofactor-mode"" (OSSL_EXCHANGE_PARAM_EC_ECDH_COFACTOR_MODE) <integer>"
Sets or gets the ECDH mode of operation for the associated key exchange ctx.
.Sp
In the context of an Elliptic Curve Diffie-Hellman key exchange, this parameter
can be used to select between the plain Diffie-Hellman (DH) or Cofactor
Diffie-Hellman (CDH) variants of the key exchange algorithm.
.Sp
When setting, the value should be 1, 0 or \-1, respectively forcing cofactor mode
on, off, or resetting it to the default for the private key associated with the
given key exchange ctx.
.Sp
When getting, the value should be either 1 or 0, respectively signaling if the
cofactor mode is on or off.
.Sp
See also \fBprovider\-keymgmt\fR\|(7) for the related
\&\fBOSSL_PKEY_PARAM_USE_COFACTOR_ECDH\fR parameter that can be set on a
per-key basis.
.IP """kdf-type"" (\fBOSSL_EXCHANGE_PARAM_KDF_TYPE\fR) <UTF8 string>" 4
.IX Item """kdf-type"" (OSSL_EXCHANGE_PARAM_KDF_TYPE) <UTF8 string>"
See "Common Key Exchange parameters" in \fBprovider\-keyexch\fR\|(7).
.IP """kdf-digest"" (\fBOSSL_EXCHANGE_PARAM_KDF_DIGEST\fR) <UTF8 string>" 4
.IX Item """kdf-digest"" (OSSL_EXCHANGE_PARAM_KDF_DIGEST) <UTF8 string>"
See "Common Key Exchange parameters" in \fBprovider\-keyexch\fR\|(7).
.IP """kdf-digest-props"" (\fBOSSL_EXCHANGE_PARAM_KDF_DIGEST_PROPS\fR) <UTF8 string>" 4
.IX Item """kdf-digest-props"" (OSSL_EXCHANGE_PARAM_KDF_DIGEST_PROPS) <UTF8 string>"
See "Common Key Exchange parameters" in \fBprovider\-keyexch\fR\|(7).
.IP """kdf-outlen"" (\fBOSSL_EXCHANGE_PARAM_KDF_OUTLEN\fR) <unsigned integer>" 4
.IX Item """kdf-outlen"" (OSSL_EXCHANGE_PARAM_KDF_OUTLEN) <unsigned integer>"
See "Common Key Exchange parameters" in \fBprovider\-keyexch\fR\|(7).
.IP """kdf-ukm"" (\fBOSSL_EXCHANGE_PARAM_KDF_UKM\fR) <octet string>" 4
.IX Item """kdf-ukm"" (OSSL_EXCHANGE_PARAM_KDF_UKM) <octet string>"
See "Common Key Exchange parameters" in \fBprovider\-keyexch\fR\|(7).
.SH EXAMPLES
.IX Header "EXAMPLES"
Keys for the host and peer must be generated as shown in
"Examples" in \fBEVP_PKEY\-EC\fR\|(7) using the same curve name.
.PP
The code to generate a shared secret for the normal case is identical to
"Examples" in \fBEVP_KEYEXCH\-DH\fR\|(7).
.PP
To derive a shared secret on the host using the host's key and the peer's public
key but also using X963KDF with a user key material:
.PP
.Vb 10
\&    /* It is assumed that the host_key, peer_pub_key and ukm are set up */
\&    void derive_secret(EVP_PKEY *host_key, EVP_PKEY *peer_key,
\&                       unsigned char *ukm, size_t ukm_len)
\&    {
\&        unsigned char secret[64];
\&        size_t out_len = sizeof(secret);
\&        size_t secret_len = out_len;
\&        unsigned int pad = 1;
\&        OSSL_PARAM params[6];
\&        EVP_PKEY_CTX *dctx = EVP_PKEY_CTX_new_from_pkey(NULL, host_key, NULL);
\&
\&        EVP_PKEY_derive_init(dctx);
\&
\&        params[0] = OSSL_PARAM_construct_uint(OSSL_EXCHANGE_PARAM_PAD, &pad);
\&        params[1] = OSSL_PARAM_construct_utf8_string(OSSL_EXCHANGE_PARAM_KDF_TYPE,
\&                                                     "X963KDF", 0);
\&        params[2] = OSSL_PARAM_construct_utf8_string(OSSL_EXCHANGE_PARAM_KDF_DIGEST,
\&                                                     "SHA1", 0);
\&        params[3] = OSSL_PARAM_construct_size_t(OSSL_EXCHANGE_PARAM_KDF_OUTLEN,
\&                                                &out_len);
\&        params[4] = OSSL_PARAM_construct_octet_string(OSSL_EXCHANGE_PARAM_KDF_UKM,
\&                                                      ukm, ukm_len);
\&        params[5] = OSSL_PARAM_construct_end();
\&        EVP_PKEY_CTX_set_params(dctx, params);
\&
\&        EVP_PKEY_derive_set_peer(dctx, peer_pub_key);
\&        EVP_PKEY_derive(dctx, secret, &secret_len);
\&        ...
\&        OPENSSL_clear_free(secret, secret_len);
\&        EVP_PKEY_CTX_free(dctx);
\&    }
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY\-EC\fR\|(7)
\&\fBEVP_PKEY\fR\|(3),
\&\fBprovider\-keyexch\fR\|(7),
\&\fBprovider\-keymgmt\fR\|(7),
\&\fBOSSL_PROVIDER\-default\fR\|(7),
\&\fBOSSL_PROVIDER\-FIPS\fR\|(7),
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
