.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PROVIDER 7ossl"
.TH PROVIDER 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
provider \- OpenSSL operation implementation providers
.SH SYNOPSIS
.IX Header "SYNOPSIS"
#include <openssl/provider.h>
.SH DESCRIPTION
.IX Header "DESCRIPTION"
.SS General
.IX Subsection "General"
This page contains information useful to provider authors.
.PP
A \fIprovider\fR, in OpenSSL terms, is a unit of code that provides one
or more implementations for various operations for diverse algorithms
that one might want to perform.
.PP
An \fIoperation\fR is something one wants to do, such as encryption and
decryption, key derivation, MAC calculation, signing and verification,
etc.
.PP
An \fIalgorithm\fR is a named method to perform an operation.
Very often, the algorithms revolve around cryptographic operations,
but may also revolve around other types of operation, such as managing
certain types of objects.
.PP
See \fBcrypto\fR\|(7) for further details.
.SS Provider
.IX Subsection "Provider"
A \fIprovider\fR offers an initialization function, as a set of base
functions in the form of an \fBOSSL_DISPATCH\fR\|(3) array, and by extension,
a set of \fBOSSL_ALGORITHM\fR\|(3)s (see \fBopenssl\-core.h\fR\|(7)).
It may be a dynamically loadable module, or may be built-in, in
OpenSSL libraries or in the application.
If it's a dynamically loadable module, the initialization function
must be named \f(CW\*(C`OSSL_provider_init\*(C'\fR and must be exported.
If it's built-in, the initialization function may have any name.
.PP
The initialization function must have the following signature:
.PP
.Vb 3
\& int NAME(const OSSL_CORE_HANDLE *handle,
\&          const OSSL_DISPATCH *in, const OSSL_DISPATCH **out,
\&          void **provctx);
.Ve
.PP
\&\fIhandle\fR is the OpenSSL library object for the provider, and works
as a handle for everything the OpenSSL libraries need to know about
the provider.
For the provider itself, it is passed to some of the functions given in the
dispatch array \fIin\fR.
.PP
\&\fIin\fR is a dispatch array of base functions offered by the OpenSSL
libraries, and the available functions are further described in
\&\fBprovider\-base\fR\|(7).
.PP
\&\fI*out\fR must be assigned a dispatch array of base functions that the
provider offers to the OpenSSL libraries.
The functions that may be offered are further described in
\&\fBprovider\-base\fR\|(7), and they are the central means of communication
between the OpenSSL libraries and the provider.
.PP
\&\fI*provctx\fR should be assigned a provider specific context to allow
the provider multiple simultaneous uses.
This pointer will be passed to various operation functions offered by
the provider.
.PP
Note that the provider will not be made available for applications to use until
the initialization function has completed and returned successfully.
.PP
One of the functions the provider offers to the OpenSSL libraries is
the central mechanism for the OpenSSL libraries to get access to
operation implementations for diverse algorithms.
Its referred to with the number \fBOSSL_FUNC_PROVIDER_QUERY_OPERATION\fR
and has the following signature:
.PP
.Vb 3
\& const OSSL_ALGORITHM *provider_query_operation(void *provctx,
\&                                                int operation_id,
\&                                                const int *no_store);
.Ve
.PP
\&\fIprovctx\fR is the provider specific context that was passed back by
the initialization function.
.PP
\&\fIoperation_id\fR is an operation identity (see "Operations" below).
.PP
\&\fIno_store\fR is a flag back to the OpenSSL libraries which, when
nonzero, signifies that the OpenSSL libraries will not store a
reference to the returned data in their internal store of
implementations.
.PP
The returned \fBOSSL_ALGORITHM\fR\|(3) is the foundation of any OpenSSL
library API that uses providers for their implementation, most
commonly in the \fIfetching\fR type of functions
(see "ALGORITHM FETCHING" in \fBcrypto\fR\|(7)).
.SS Operations
.IX Subsection "Operations"
Operations are referred to with numbers, via macros with names
starting with \f(CW\*(C`OSSL_OP_\*(C'\fR.
.PP
With each operation comes a set of defined function types that a
provider may or may not offer, depending on its needs.
.PP
Currently available operations are:
.IP Digests 4
.IX Item "Digests"
In the OpenSSL libraries, the corresponding method object is
\&\fBEVP_MD\fR.
The number for this operation is \fBOSSL_OP_DIGEST\fR.
The functions the provider can offer are described in
\&\fBprovider\-digest\fR\|(7).
.IP "Symmetric ciphers" 4
.IX Item "Symmetric ciphers"
In the OpenSSL libraries, the corresponding method object is
\&\fBEVP_CIPHER\fR.
The number for this operation is \fBOSSL_OP_CIPHER\fR.
The functions the provider can offer are described in
\&\fBprovider\-cipher\fR\|(7).
.IP "Message Authentication Code (MAC)" 4
.IX Item "Message Authentication Code (MAC)"
In the OpenSSL libraries, the corresponding method object is
\&\fBEVP_MAC\fR.
The number for this operation is \fBOSSL_OP_MAC\fR.
The functions the provider can offer are described in
\&\fBprovider\-mac\fR\|(7).
.IP "Key Derivation Function (KDF)" 4
.IX Item "Key Derivation Function (KDF)"
In the OpenSSL libraries, the corresponding method object is
\&\fBEVP_KDF\fR.
The number for this operation is \fBOSSL_OP_KDF\fR.
The functions the provider can offer are described in
\&\fBprovider\-kdf\fR\|(7).
.IP "Key Exchange" 4
.IX Item "Key Exchange"
In the OpenSSL libraries, the corresponding method object is
\&\fBEVP_KEYEXCH\fR.
The number for this operation is \fBOSSL_OP_KEYEXCH\fR.
The functions the provider can offer are described in
\&\fBprovider\-keyexch\fR\|(7).
.IP "Asymmetric Ciphers" 4
.IX Item "Asymmetric Ciphers"
In the OpenSSL libraries, the corresponding method object is
\&\fBEVP_ASYM_CIPHER\fR.
The number for this operation is \fBOSSL_OP_ASYM_CIPHER\fR.
The functions the provider can offer are described in
\&\fBprovider\-asym_cipher\fR\|(7).
.IP "Asymmetric Key Encapsulation" 4
.IX Item "Asymmetric Key Encapsulation"
In the OpenSSL libraries, the corresponding method object is \fBEVP_KEM\fR.
The number for this operation is \fBOSSL_OP_KEM\fR.
The functions the provider can offer are described in \fBprovider\-kem\fR\|(7).
.IP Encoding 4
.IX Item "Encoding"
In the OpenSSL libraries, the corresponding method object is
\&\fBOSSL_ENCODER\fR.
The number for this operation is \fBOSSL_OP_ENCODER\fR.
The functions the provider can offer are described in
\&\fBprovider\-encoder\fR\|(7).
.IP Decoding 4
.IX Item "Decoding"
In the OpenSSL libraries, the corresponding method object is
\&\fBOSSL_DECODER\fR.
The number for this operation is \fBOSSL_OP_DECODER\fR.
The functions the provider can offer are described in
\&\fBprovider\-decoder\fR\|(7).
.IP "Random Number Generation" 4
.IX Item "Random Number Generation"
The number for this operation is \fBOSSL_OP_RAND\fR.
The functions the provider can offer for random number generation are described
in \fBprovider\-rand\fR\|(7).
.IP "Key Management" 4
.IX Item "Key Management"
The number for this operation is \fBOSSL_OP_KEYMGMT\fR.
The functions the provider can offer for key management are described in
\&\fBprovider\-keymgmt\fR\|(7).
.IP "Signing and Signature Verification" 4
.IX Item "Signing and Signature Verification"
The number for this operation is \fBOSSL_OP_SIGNATURE\fR.
The functions the provider can offer for digital signatures are described in
\&\fBprovider\-signature\fR\|(7).
.IP "Store Management" 4
.IX Item "Store Management"
The number for this operation is \fBOSSL_OP_STORE\fR.
The functions the provider can offer for store management are described in
\&\fBprovider\-storemgmt\fR\|(7).
.PP
\fIAlgorithm naming\fR
.IX Subsection "Algorithm naming"
.PP
Algorithm names are case insensitive. Any particular algorithm can have multiple
aliases associated with it. The canonical OpenSSL naming scheme follows this
format:
.PP
ALGNAME[VERSION?][\-SUBNAME[VERSION?]?][\-SIZE?][\-MODE?]
.PP
VERSION is only present if there are multiple versions of an algorithm (e.g.
MD2, MD4, MD5).  It may be omitted if there is only one version.
.PP
SUBNAME may be present where multiple algorithms are combined together,
e.g. MD5\-SHA1.
.PP
SIZE is only present if multiple versions of an algorithm exist with different
sizes (e.g. AES\-128\-CBC, AES\-256\-CBC)
.PP
MODE is only present where applicable.
.PP
Other aliases may exist for example where standards bodies or common practice
use alternative names or names that OpenSSL has used historically.
.SH "OPENSSL PROVIDERS"
.IX Header "OPENSSL PROVIDERS"
OpenSSL provides a number of its own providers. These are the default, base,
fips, legacy and null providers. See \fBcrypto\fR\|(7) for an overview of these
providers.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_DigestInit_ex\fR\|(3), \fBEVP_EncryptInit_ex\fR\|(3),
\&\fBOSSL_LIB_CTX\fR\|(3),
\&\fBEVP_set_default_properties\fR\|(3),
\&\fBEVP_MD_fetch\fR\|(3),
\&\fBEVP_CIPHER_fetch\fR\|(3),
\&\fBEVP_KEYMGMT_fetch\fR\|(3),
\&\fBopenssl\-core.h\fR\|(7),
\&\fBprovider\-base\fR\|(7),
\&\fBprovider\-digest\fR\|(7),
\&\fBprovider\-cipher\fR\|(7),
\&\fBprovider\-keyexch\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The concept of providers and everything surrounding them was
introduced in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
