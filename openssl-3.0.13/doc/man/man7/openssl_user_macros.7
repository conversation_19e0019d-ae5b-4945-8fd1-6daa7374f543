.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL_USER_MACROS 7ossl"
.TH OPENSSL_USER_MACROS 7ossl 2025-08-08 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl_user_macros, OPENSSL_API_COMPAT, OPENSSL_NO_DEPRECATED
\&\- User defined macros
.SH DESCRIPTION
.IX Header "DESCRIPTION"
User defined macros allow the programmer to control certain aspects of
what is exposed by the OpenSSL headers.
.PP
\&\fBNOTE:\fR to be effective, a user defined macro \fImust be defined
before including any header file that depends on it\fR, either in the
compilation command (\f(CW\*(C`cc \-DMACRO=value\*(C'\fR) or by defining the macro in
source before including any headers.
.PP
Other manual pages may refer to this page when declarations depend on
user defined macros.
.SS "The macros"
.IX Subsection "The macros"
.IP \fBOPENSSL_API_COMPAT\fR 4
.IX Item "OPENSSL_API_COMPAT"
The value is a version number, given in one of the following two forms:
.RS 4
.ie n .IP """0xMNNFF000L""" 4
.el .IP \f(CW0xMNNFF000L\fR 4
.IX Item "0xMNNFF000L"
This is the form supported for all versions up to 1.1.x, where \f(CW\*(C`M\*(C'\fR
represents the major number, \f(CW\*(C`NN\*(C'\fR represents the minor number, and
\&\f(CW\*(C`FF\*(C'\fR represents the fix number, as a hexadecimal number.  For version
1.1.0, that's \f(CW\*(C`0x10100000L\*(C'\fR.
.Sp
Any version number may be given, but these numbers are
the current known major deprecation points, making them the most
meaningful:
.RS 4
.ie n .IP """0x00908000L"" (version 0.9.8)" 4
.el .IP "\f(CW0x00908000L\fR (version 0.9.8)" 4
.IX Item "0x00908000L (version 0.9.8)"
.PD 0
.ie n .IP """0x10000000L"" (version 1.0.0)" 4
.el .IP "\f(CW0x10000000L\fR (version 1.0.0)" 4
.IX Item "0x10000000L (version 1.0.0)"
.ie n .IP """0x10100000L"" (version 1.1.0)" 4
.el .IP "\f(CW0x10100000L\fR (version 1.1.0)" 4
.IX Item "0x10100000L (version 1.1.0)"
.RE
.RS 4
.PD
.Sp
For convenience, higher numbers are accepted as well, as long as
feasible.  For example, \f(CW\*(C`0x60000000L\*(C'\fR will work as expected.
However, it is recommended to start using the second form instead:
.RE
.ie n .IP """mmnnpp""" 4
.el .IP \f(CWmmnnpp\fR 4
.IX Item "mmnnpp"
This form is a simple decimal number calculated with this formula:
.Sp
\&\fImajor\fR * 10000 + \fIminor\fR * 100 + \fIpatch\fR
.Sp
where \fImajor\fR, \fIminor\fR and \fIpatch\fR are the desired major,
minor and patch components of the version number.  For example:
.RS 4
.IP "30000 corresponds to version 3.0.0" 4
.IX Item "30000 corresponds to version 3.0.0"
.PD 0
.IP "10002 corresponds to version 1.0.2" 4
.IX Item "10002 corresponds to version 1.0.2"
.IP "420101 corresponds to version 42.1.1" 4
.IX Item "420101 corresponds to version 42.1.1"
.RE
.RS 4
.RE
.RE
.RS 4
.PD
.Sp
If \fBOPENSSL_API_COMPAT\fR is undefined, this default value is used in its
place:
\&\f(CW30000\fR
.RE
.IP \fBOPENSSL_NO_DEPRECATED\fR 4
.IX Item "OPENSSL_NO_DEPRECATED"
If this macro is defined, all deprecated public symbols in all OpenSSL
versions up to and including the version given by \fBOPENSSL_API_COMPAT\fR
(or the default value given above, when \fBOPENSSL_API_COMPAT\fR isn't defined)
will be hidden.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
