.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "SSL 7ossl"
.TH SSL 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ssl \- OpenSSL SSL/TLS library
.SH SYNOPSIS
.IX Header "SYNOPSIS"
See the individual manual pages for details.
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The OpenSSL \fBssl\fR library implements several versions of the
Secure Sockets Layer, Transport Layer Security, and Datagram Transport Layer
Security protocols.
This page gives a brief overview of the extensive API and data types
provided by the library.
.PP
An \fBSSL_CTX\fR object is created as a framework to establish
TLS/SSL enabled connections (see \fBSSL_CTX_new\fR\|(3)).
Various options regarding certificates, algorithms etc. can be set
in this object.
.PP
When a network connection has been created, it can be assigned to an
\&\fBSSL\fR object. After the \fBSSL\fR object has been created using
\&\fBSSL_new\fR\|(3), \fBSSL_set_fd\fR\|(3) or
\&\fBSSL_set_bio\fR\|(3) can be used to associate the network
connection with the object.
.PP
When the TLS/SSL handshake is performed using
\&\fBSSL_accept\fR\|(3) or \fBSSL_connect\fR\|(3)
respectively.
\&\fBSSL_read_ex\fR\|(3), \fBSSL_read\fR\|(3), \fBSSL_write_ex\fR\|(3) and \fBSSL_write\fR\|(3) are
used to read and write data on the TLS/SSL connection.
\&\fBSSL_shutdown\fR\|(3) can be used to shut down the
TLS/SSL connection.
.SH "DATA STRUCTURES"
.IX Header "DATA STRUCTURES"
Here are some of the main data structures in the library.
.IP "\fBSSL_METHOD\fR (SSL Method)" 4
.IX Item "SSL_METHOD (SSL Method)"
This is a dispatch structure describing the internal \fBssl\fR library
methods/functions which implement the various protocol versions (SSLv3
TLSv1, ...). It's needed to create an \fBSSL_CTX\fR.
.IP "\fBSSL_CIPHER\fR (SSL Cipher)" 4
.IX Item "SSL_CIPHER (SSL Cipher)"
This structure holds the algorithm information for a particular cipher which
are a core part of the SSL/TLS protocol. The available ciphers are configured
on a \fBSSL_CTX\fR basis and the actual ones used are then part of the
\&\fBSSL_SESSION\fR.
.IP "\fBSSL_CTX\fR (SSL Context)" 4
.IX Item "SSL_CTX (SSL Context)"
This is the global context structure which is created by a server or client
once per program life-time and which holds mainly default values for the
\&\fBSSL\fR structures which are later created for the connections.
.IP "\fBSSL_SESSION\fR (SSL Session)" 4
.IX Item "SSL_SESSION (SSL Session)"
This is a structure containing the current TLS/SSL session details for a
connection: \fBSSL_CIPHER\fRs, client and server certificates, keys, etc.
.IP "\fBSSL\fR (SSL Connection)" 4
.IX Item "SSL (SSL Connection)"
This is the main SSL/TLS structure which is created by a server or client per
established connection. This actually is the core structure in the SSL API.
At run-time the application usually deals with this structure which has
links to mostly all other structures.
.SH "HEADER FILES"
.IX Header "HEADER FILES"
Currently the OpenSSL \fBssl\fR library provides the following C header files
containing the prototypes for the data structures and functions:
.IP \fI<openssl/ssl.h>\fR 4
.IX Item "<openssl/ssl.h>"
This is the common header file for the SSL/TLS API.  Include it into your
program to make the API of the \fBssl\fR library available. It internally
includes both more private SSL headers and headers from the \fBcrypto\fR library.
Whenever you need hard-core details on the internals of the SSL API, look
inside this header file.
This file also includes the others listed below.
.IP \fI<openssl/ssl2.h>\fR 4
.IX Item "<openssl/ssl2.h>"
Unused. Present for backwards compatibility only.
.IP \fI<openssl/ssl3.h>\fR 4
.IX Item "<openssl/ssl3.h>"
This is the sub header file dealing with the SSLv3 protocol only.
.IP \fI<openssl/tls1.h>\fR 4
.IX Item "<openssl/tls1.h>"
This is the sub header file dealing with the TLSv1 protocol only.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2018 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
