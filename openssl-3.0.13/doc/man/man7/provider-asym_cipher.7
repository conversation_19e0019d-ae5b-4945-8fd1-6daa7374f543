.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PROVIDER-ASYM_CIPHER 7ossl"
.TH PROVIDER-ASYM_CIPHER 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
provider\-asym_cipher \- The asym_cipher library <\-> provider functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 2
\& #include <openssl/core_dispatch.h>
\& #include <openssl/core_names.h>
\&
\& /*
\&  * None of these are actual functions, but are displayed like this for
\&  * the function signatures for functions that are offered as function
\&  * pointers in OSSL_DISPATCH arrays.
\&  */
\&
\& /* Context management */
\& void *OSSL_FUNC_asym_cipher_newctx(void *provctx);
\& void OSSL_FUNC_asym_cipher_freectx(void *ctx);
\& void *OSSL_FUNC_asym_cipher_dupctx(void *ctx);
\&
\& /* Encryption */
\& int OSSL_FUNC_asym_cipher_encrypt_init(void *ctx, void *provkey,
\&                                        const OSSL_PARAM params[]);
\& int OSSL_FUNC_asym_cipher_encrypt(void *ctx, unsigned char *out, size_t *outlen,
\&                                   size_t outsize, const unsigned char *in,
\&                                   size_t inlen);
\&
\& /* Decryption */
\& int OSSL_FUNC_asym_cipher_decrypt_init(void *ctx, void *provkey,
\&                                        const OSSL_PARAM params[]);
\& int OSSL_FUNC_asym_cipher_decrypt(void *ctx, unsigned char *out, size_t *outlen,
\&                                   size_t outsize, const unsigned char *in,
\&                                   size_t inlen);
\&
\& /* Asymmetric Cipher parameters */
\& int OSSL_FUNC_asym_cipher_get_ctx_params(void *ctx, OSSL_PARAM params[]);
\& const OSSL_PARAM *OSSL_FUNC_asym_cipher_gettable_ctx_params(void *provctx);
\& int OSSL_FUNC_asym_cipher_set_ctx_params(void *ctx, const OSSL_PARAM params[]);
\& const OSSL_PARAM *OSSL_FUNC_asym_cipher_settable_ctx_params(void *provctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This documentation is primarily aimed at provider authors. See \fBprovider\fR\|(7)
for further information.
.PP
The asymmetric cipher (OSSL_OP_ASYM_CIPHER) operation enables providers to
implement asymmetric cipher algorithms and make them available to applications
via the API functions \fBEVP_PKEY_encrypt\fR\|(3),
\&\fBEVP_PKEY_decrypt\fR\|(3) and
other related functions).
.PP
All "functions" mentioned here are passed as function pointers between
\&\fIlibcrypto\fR and the provider in \fBOSSL_DISPATCH\fR\|(3) arrays via
\&\fBOSSL_ALGORITHM\fR\|(3) arrays that are returned by the provider's
\&\fBprovider_query_operation()\fR function
(see "Provider Functions" in \fBprovider\-base\fR\|(7)).
.PP
All these "functions" have a corresponding function type definition
named \fBOSSL_FUNC_{name}_fn\fR, and a helper function to retrieve the
function pointer from an \fBOSSL_DISPATCH\fR\|(3) element named
\&\fBOSSL_FUNC_{name}\fR.
For example, the "function" \fBOSSL_FUNC_asym_cipher_newctx()\fR has these:
.PP
.Vb 3
\& typedef void *(OSSL_FUNC_asym_cipher_newctx_fn)(void *provctx);
\& static ossl_inline OSSL_FUNC_asym_cipher_newctx_fn
\&     OSSL_FUNC_asym_cipher_newctx(const OSSL_DISPATCH *opf);
.Ve
.PP
\&\fBOSSL_DISPATCH\fR\|(3) arrays are indexed by numbers that are provided as
macros in \fBopenssl\-core_dispatch.h\fR\|(7), as follows:
.PP
.Vb 3
\& OSSL_FUNC_asym_cipher_newctx               OSSL_FUNC_ASYM_CIPHER_NEWCTX
\& OSSL_FUNC_asym_cipher_freectx              OSSL_FUNC_ASYM_CIPHER_FREECTX
\& OSSL_FUNC_asym_cipher_dupctx               OSSL_FUNC_ASYM_CIPHER_DUPCTX
\&
\& OSSL_FUNC_asym_cipher_encrypt_init         OSSL_FUNC_ASYM_CIPHER_ENCRYPT_INIT
\& OSSL_FUNC_asym_cipher_encrypt              OSSL_FUNC_ASYM_CIPHER_ENCRYPT
\&
\& OSSL_FUNC_asym_cipher_decrypt_init         OSSL_FUNC_ASYM_CIPHER_DECRYPT_INIT
\& OSSL_FUNC_asym_cipher_decrypt              OSSL_FUNC_ASYM_CIPHER_DECRYPT
\&
\& OSSL_FUNC_asym_cipher_get_ctx_params       OSSL_FUNC_ASYM_CIPHER_GET_CTX_PARAMS
\& OSSL_FUNC_asym_cipher_gettable_ctx_params  OSSL_FUNC_ASYM_CIPHER_GETTABLE_CTX_PARAMS
\& OSSL_FUNC_asym_cipher_set_ctx_params       OSSL_FUNC_ASYM_CIPHER_SET_CTX_PARAMS
\& OSSL_FUNC_asym_cipher_settable_ctx_params  OSSL_FUNC_ASYM_CIPHER_SETTABLE_CTX_PARAMS
.Ve
.PP
An asymmetric cipher algorithm implementation may not implement all of these
functions.
In order to be a consistent set of functions a provider must implement
OSSL_FUNC_asym_cipher_newctx and OSSL_FUNC_asym_cipher_freectx.
It must also implement both of OSSL_FUNC_asym_cipher_encrypt_init and
OSSL_FUNC_asym_cipher_encrypt, or both of OSSL_FUNC_asym_cipher_decrypt_init and
OSSL_FUNC_asym_cipher_decrypt.
OSSL_FUNC_asym_cipher_get_ctx_params is optional but if it is present then so must
OSSL_FUNC_asym_cipher_gettable_ctx_params.
Similarly, OSSL_FUNC_asym_cipher_set_ctx_params is optional but if it is present then
so must OSSL_FUNC_asym_cipher_settable_ctx_params.
.PP
An asymmetric cipher algorithm must also implement some mechanism for generating,
loading or importing keys via the key management (OSSL_OP_KEYMGMT) operation.
See \fBprovider\-keymgmt\fR\|(7) for further details.
.SS "Context Management Functions"
.IX Subsection "Context Management Functions"
\&\fBOSSL_FUNC_asym_cipher_newctx()\fR should create and return a pointer to a provider side
structure for holding context information during an asymmetric cipher operation.
A pointer to this context will be passed back in a number of the other
asymmetric cipher operation function calls.
The parameter \fIprovctx\fR is the provider context generated during provider
initialisation (see \fBprovider\fR\|(7)).
.PP
\&\fBOSSL_FUNC_asym_cipher_freectx()\fR is passed a pointer to the provider side asymmetric
cipher context in the \fIctx\fR parameter.
This function should free any resources associated with that context.
.PP
\&\fBOSSL_FUNC_asym_cipher_dupctx()\fR should duplicate the provider side asymmetric cipher
context in the \fIctx\fR parameter and return the duplicate copy.
.SS "Encryption Functions"
.IX Subsection "Encryption Functions"
\&\fBOSSL_FUNC_asym_cipher_encrypt_init()\fR initialises a context for an asymmetric encryption
given a provider side asymmetric cipher context in the \fIctx\fR parameter, and a
pointer to a provider key object in the \fIprovkey\fR parameter.
The \fIparams\fR, if not NULL, should be set on the context in a manner similar to
using \fBOSSL_FUNC_asym_cipher_set_ctx_params()\fR.
The key object should have been previously generated, loaded or imported into
the provider using the key management (OSSL_OP_KEYMGMT) operation (see \fBprovider\-keymgmt\fR\|(7)).
\&\fBOSSL_FUNC_asym_cipher_encrypt()\fR performs the actual encryption itself.
A previously initialised asymmetric cipher context is passed in the \fIctx\fR
parameter.
The data to be encrypted is pointed to by the \fIin\fR parameter which is \fIinlen\fR
bytes long.
Unless \fIout\fR is NULL, the encrypted data should be written to the location
pointed to by the \fIout\fR parameter and it should not exceed \fIoutsize\fR bytes in
length.
The length of the encrypted data should be written to \fI*outlen\fR.
If \fIout\fR is NULL then the maximum length of the encrypted data should be
written to \fI*outlen\fR.
.SS "Decryption Functions"
.IX Subsection "Decryption Functions"
\&\fBOSSL_FUNC_asym_cipher_decrypt_init()\fR initialises a context for an asymmetric decryption
given a provider side asymmetric cipher context in the \fIctx\fR parameter, and a
pointer to a provider key object in the \fIprovkey\fR parameter.
The \fIparams\fR, if not NULL, should be set on the context in a manner similar to
using \fBOSSL_FUNC_asym_cipher_set_ctx_params()\fR.
The key object should have been previously generated, loaded or imported into
the provider using the key management (OSSL_OP_KEYMGMT) operation (see
\&\fBprovider\-keymgmt\fR\|(7)).
.PP
\&\fBOSSL_FUNC_asym_cipher_decrypt()\fR performs the actual decryption itself.
A previously initialised asymmetric cipher context is passed in the \fIctx\fR
parameter.
The data to be decrypted is pointed to by the \fIin\fR parameter which is \fIinlen\fR
bytes long.
Unless \fIout\fR is NULL, the decrypted data should be written to the location
pointed to by the \fIout\fR parameter and it should not exceed \fIoutsize\fR bytes in
length.
The length of the decrypted data should be written to \fI*outlen\fR.
If \fIout\fR is NULL then the maximum length of the decrypted data should be
written to \fI*outlen\fR.
.SS "Asymmetric Cipher Parameters"
.IX Subsection "Asymmetric Cipher Parameters"
See \fBOSSL_PARAM\fR\|(3) for further details on the parameters structure used by
the \fBOSSL_FUNC_asym_cipher_get_ctx_params()\fR and \fBOSSL_FUNC_asym_cipher_set_ctx_params()\fR
functions.
.PP
\&\fBOSSL_FUNC_asym_cipher_get_ctx_params()\fR gets asymmetric cipher parameters associated
with the given provider side asymmetric cipher context \fIctx\fR and stores them in
\&\fIparams\fR.
Passing NULL for \fIparams\fR should return true.
.PP
\&\fBOSSL_FUNC_asym_cipher_set_ctx_params()\fR sets the asymmetric cipher parameters associated
with the given provider side asymmetric cipher context \fIctx\fR to \fIparams\fR.
Any parameter settings are additional to any that were previously set.
Passing NULL for \fIparams\fR should return true.
.PP
Parameters currently recognised by built-in asymmetric cipher algorithms are as
follows.
Not all parameters are relevant to, or are understood by all asymmetric cipher
algorithms:
.IP """pad-mode"" (\fBOSSL_ASYM_CIPHER_PARAM_PAD_MODE\fR) <UTF8 string> OR <integer>" 4
.IX Item """pad-mode"" (OSSL_ASYM_CIPHER_PARAM_PAD_MODE) <UTF8 string> OR <integer>"
The type of padding to be used. The interpretation of this value will depend
on the algorithm in use.
.IP """digest"" (\fBOSSL_ASYM_CIPHER_PARAM_OAEP_DIGEST\fR) <UTF8 string>" 4
.IX Item """digest"" (OSSL_ASYM_CIPHER_PARAM_OAEP_DIGEST) <UTF8 string>"
Gets or sets the name of the OAEP digest algorithm used when OAEP padding is in
use.
.IP """digest"" (\fBOSSL_ASYM_CIPHER_PARAM_DIGEST\fR) <UTF8 string>" 4
.IX Item """digest"" (OSSL_ASYM_CIPHER_PARAM_DIGEST) <UTF8 string>"
Gets or sets the name of the digest algorithm used by the algorithm (where
applicable).
.IP """digest-props"" (\fBOSSL_ASYM_CIPHER_PARAM_OAEP_DIGEST_PROPS\fR) <UTF8 string>" 4
.IX Item """digest-props"" (OSSL_ASYM_CIPHER_PARAM_OAEP_DIGEST_PROPS) <UTF8 string>"
Gets or sets the properties to use when fetching the OAEP digest algorithm.
.IP """digest-props"" (\fBOSSL_ASYM_CIPHER_PARAM_DIGEST_PROPS\fR) <UTF8 string>" 4
.IX Item """digest-props"" (OSSL_ASYM_CIPHER_PARAM_DIGEST_PROPS) <UTF8 string>"
Gets or sets the properties to use when fetching the cipher digest algorithm.
.IP """mgf1\-digest"" (\fBOSSL_ASYM_CIPHER_PARAM_MGF1_DIGEST\fR) <UTF8 string>" 4
.IX Item """mgf1-digest"" (OSSL_ASYM_CIPHER_PARAM_MGF1_DIGEST) <UTF8 string>"
Gets or sets the name of the MGF1 digest algorithm used when OAEP or PSS padding
is in use.
.IP """mgf1\-digest\-props"" (\fBOSSL_ASYM_CIPHER_PARAM_MGF1_DIGEST_PROPS\fR) <UTF8 string>" 4
.IX Item """mgf1-digest-props"" (OSSL_ASYM_CIPHER_PARAM_MGF1_DIGEST_PROPS) <UTF8 string>"
Gets or sets the properties to use when fetching the MGF1 digest algorithm.
.IP """oaep-label"" (\fBOSSL_ASYM_CIPHER_PARAM_OAEP_LABEL\fR) <octet string ptr>" 4
.IX Item """oaep-label"" (OSSL_ASYM_CIPHER_PARAM_OAEP_LABEL) <octet string ptr>"
Gets the OAEP label used when OAEP padding is in use.
.IP """oaep-label"" (\fBOSSL_ASYM_CIPHER_PARAM_OAEP_LABEL\fR) <octet string>" 4
.IX Item """oaep-label"" (OSSL_ASYM_CIPHER_PARAM_OAEP_LABEL) <octet string>"
Sets the OAEP label used when OAEP padding is in use.
.IP """tls-client-version"" (\fBOSSL_ASYM_CIPHER_PARAM_TLS_CLIENT_VERSION\fR) <unsigned integer>" 4
.IX Item """tls-client-version"" (OSSL_ASYM_CIPHER_PARAM_TLS_CLIENT_VERSION) <unsigned integer>"
The TLS protocol version first requested by the client.
.IP """tls-negotiated-version"" (\fBOSSL_ASYM_CIPHER_PARAM_TLS_CLIENT_VERSION\fR) <unsigned integer>" 4
.IX Item """tls-negotiated-version"" (OSSL_ASYM_CIPHER_PARAM_TLS_CLIENT_VERSION) <unsigned integer>"
The negotiated TLS protocol version.
.PP
\&\fBOSSL_FUNC_asym_cipher_gettable_ctx_params()\fR and \fBOSSL_FUNC_asym_cipher_settable_ctx_params()\fR
get a constant \fBOSSL_PARAM\fR\|(3) array that describes the gettable and settable
parameters, i.e. parameters that can be used with \fBOSSL_FUNC_asym_cipherget_ctx_params()\fR
and \fBOSSL_FUNC_asym_cipher_set_ctx_params()\fR respectively.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_FUNC_asym_cipher_newctx()\fR and \fBOSSL_FUNC_asym_cipher_dupctx()\fR should return the newly
created provider side asymmetric cipher context, or NULL on failure.
.PP
All other functions should return 1 for success or 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The provider ASYM_CIPHER interface was introduced in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
