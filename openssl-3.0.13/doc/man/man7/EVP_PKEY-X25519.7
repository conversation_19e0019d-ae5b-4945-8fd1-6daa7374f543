.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY-X25519 7ossl"
.TH EVP_PKEY-X25519 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY\-X25519, EVP_PKEY\-X448, EVP_PKEY\-ED25519, EVP_PKEY\-ED448,
EVP_KEYMGMT\-X25519, EVP_KEYMGMT\-X448, EVP_KEYMGMT\-ED25519, EVP_KEYMGMT\-ED448
\&\- EVP_PKEY X25519, X448, ED25519 and ED448 keytype and algorithm support
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBX25519\fR, \fBX448\fR, \fBED25519\fR and \fBED448\fR keytypes are
implemented in OpenSSL's default and FIPS providers.  These implementations
support the associated key, containing the public key \fIpub\fR and the
private key \fIpriv\fR.
.PP
No additional parameters can be set during key generation.
.SS "Common X25519, X448, ED25519 and ED448 parameters"
.IX Subsection "Common X25519, X448, ED25519 and ED448 parameters"
In addition to the common parameters that all keytypes should support (see
"Common parameters" in \fBprovider\-keymgmt\fR\|(7)), the implementation of these keytypes
support the following.
.IP """group"" (\fBOSSL_PKEY_PARAM_GROUP_NAME\fR) <UTF8 string>" 4
.IX Item """group"" (OSSL_PKEY_PARAM_GROUP_NAME) <UTF8 string>"
This is only supported by X25519 and X448. The group name must be "x25519" or
"x448" respectively for those algorithms. This is only present for consistency
with other key exchange algorithms and is typically not needed.
.IP """pub"" (\fBOSSL_PKEY_PARAM_PUB_KEY\fR) <octet string>" 4
.IX Item """pub"" (OSSL_PKEY_PARAM_PUB_KEY) <octet string>"
The public key value.
.IP """priv"" (\fBOSSL_PKEY_PARAM_PRIV_KEY\fR) <octet string>" 4
.IX Item """priv"" (OSSL_PKEY_PARAM_PRIV_KEY) <octet string>"
The private key value.
.IP """encoded-pub-key"" (\fBOSSL_PKEY_PARAM_ENCODED_PUBLIC_KEY\fR) <octet string>" 4
.IX Item """encoded-pub-key"" (OSSL_PKEY_PARAM_ENCODED_PUBLIC_KEY) <octet string>"
Used for getting and setting the encoding of a public key for the \fBX25519\fR and
\&\fBX448\fR key types. Public keys are expected be encoded in a format as defined by
RFC7748.
.SS "ED25519 and ED448 parameters"
.IX Subsection "ED25519 and ED448 parameters"
.IP """mandatory-digest"" (\fBOSSL_PKEY_PARAM_MANDATORY_DIGEST\fR) <UTF8 string>" 4
.IX Item """mandatory-digest"" (OSSL_PKEY_PARAM_MANDATORY_DIGEST) <UTF8 string>"
The empty string, signifying that no digest may be specified.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
.IP "RFC 8032" 4
.IX Item "RFC 8032"
.PD 0
.IP "RFC 8410" 4
.IX Item "RFC 8410"
.PD
.SH EXAMPLES
.IX Header "EXAMPLES"
An \fBEVP_PKEY\fR context can be obtained by calling:
.PP
.Vb 2
\&    EVP_PKEY_CTX *pctx =
\&        EVP_PKEY_CTX_new_from_name(NULL, "X25519", NULL);
\&
\&    EVP_PKEY_CTX *pctx =
\&        EVP_PKEY_CTX_new_from_name(NULL, "X448", NULL);
\&
\&    EVP_PKEY_CTX *pctx =
\&        EVP_PKEY_CTX_new_from_name(NULL, "ED25519", NULL);
\&
\&    EVP_PKEY_CTX *pctx =
\&        EVP_PKEY_CTX_new_from_name(NULL, "ED448", NULL);
.Ve
.PP
An \fBX25519\fR key can be generated like this:
.PP
.Vb 1
\&    pkey = EVP_PKEY_Q_keygen(NULL, NULL, "X25519");
.Ve
.PP
An \fBX448\fR, \fBED25519\fR, or \fBED448\fR key can be generated likewise.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_KEYMGMT\fR\|(3), \fBEVP_PKEY\fR\|(3), \fBprovider\-keymgmt\fR\|(7),
\&\fBEVP_KEYEXCH\-X25519\fR\|(7), \fBEVP_KEYEXCH\-X448\fR\|(7),
\&\fBEVP_SIGNATURE\-ED25519\fR\|(7), \fBEVP_SIGNATURE\-ED448\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
