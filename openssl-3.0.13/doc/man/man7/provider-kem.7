.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PROVIDER-KEM 7ossl"
.TH PROVIDER-KEM 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
provider\-kem \- The kem library <\-> provider functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 2
\& #include <openssl/core_dispatch.h>
\& #include <openssl/core_names.h>
\&
\& /*
\&  * None of these are actual functions, but are displayed like this for
\&  * the function signatures for functions that are offered as function
\&  * pointers in OSSL_DISPATCH arrays.
\&  */
\&
\& /* Context management */
\& void *OSSL_FUNC_kem_newctx(void *provctx);
\& void OSSL_FUNC_kem_freectx(void *ctx);
\& void *OSSL_FUNC_kem_dupctx(void *ctx);
\&
\& /* Encapsulation */
\& int OSSL_FUNC_kem_encapsulate_init(void *ctx, void *provkey, const char *name,
\&                                    const OSSL_PARAM params[]);
\& int OSSL_FUNC_kem_encapsulate(void *ctx, unsigned char *out, size_t *outlen,
\&                               unsigned char *secret, size_t *secretlen);
\&
\& /* Decapsulation */
\& int OSSL_FUNC_kem_decapsulate_init(void *ctx, void *provkey, const char *name);
\& int OSSL_FUNC_kem_decapsulate(void *ctx, unsigned char *out, size_t *outlen,
\&                               const unsigned char *in, size_t inlen);
\&
\& /* KEM parameters */
\& int OSSL_FUNC_kem_get_ctx_params(void *ctx, OSSL_PARAM params[]);
\& const OSSL_PARAM *OSSL_FUNC_kem_gettable_ctx_params(void *ctx, void *provctx);
\& int OSSL_FUNC_kem_set_ctx_params(void *ctx, const OSSL_PARAM params[]);
\& const OSSL_PARAM *OSSL_FUNC_kem_settable_ctx_params(void *ctx, void *provctx);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This documentation is primarily aimed at provider authors. See \fBprovider\fR\|(7)
for further information.
.PP
The asymmetric kem (OSSL_OP_KEM) operation enables providers to
implement asymmetric kem algorithms and make them available to applications
via the API functions \fBEVP_PKEY_encapsulate\fR\|(3),
\&\fBEVP_PKEY_decapsulate\fR\|(3) and other related functions.
.PP
All "functions" mentioned here are passed as function pointers between
\&\fIlibcrypto\fR and the provider in \fBOSSL_DISPATCH\fR\|(3) arrays via
\&\fBOSSL_ALGORITHM\fR\|(3) arrays that are returned by the provider's
\&\fBprovider_query_operation()\fR function
(see "Provider Functions" in \fBprovider\-base\fR\|(7)).
.PP
All these "functions" have a corresponding function type definition
named \fBOSSL_FUNC_{name}_fn\fR, and a helper function to retrieve the
function pointer from an \fBOSSL_DISPATCH\fR\|(3) element named
\&\fBOSSL_FUNC_{name}\fR.
For example, the "function" \fBOSSL_FUNC_kem_newctx()\fR has these:
.PP
.Vb 3
\& typedef void *(OSSL_FUNC_kem_newctx_fn)(void *provctx);
\& static ossl_inline OSSL_FUNC_kem_newctx_fn
\&     OSSL_FUNC_kem_newctx(const OSSL_DISPATCH *opf);
.Ve
.PP
\&\fBOSSL_DISPATCH\fR\|(3) arrays are indexed by numbers that are provided as
macros in \fBopenssl\-core_dispatch.h\fR\|(7), as follows:
.PP
.Vb 3
\& OSSL_FUNC_kem_newctx               OSSL_FUNC_KEM_NEWCTX
\& OSSL_FUNC_kem_freectx              OSSL_FUNC_KEM_FREECTX
\& OSSL_FUNC_kem_dupctx               OSSL_FUNC_KEM_DUPCTX
\&
\& OSSL_FUNC_kem_encapsulate_init     OSSL_FUNC_KEM_ENCAPSULATE_INIT
\& OSSL_FUNC_kem_encapsulate          OSSL_FUNC_KEM_ENCAPSULATE
\&
\& OSSL_FUNC_kem_decapsulate_init     OSSL_FUNC_KEM_DECAPSULATE_INIT
\& OSSL_FUNC_kem_decapsulate          OSSL_FUNC_KEM_DECAPSULATE
\&
\& OSSL_FUNC_kem_get_ctx_params       OSSL_FUNC_KEM_GET_CTX_PARAMS
\& OSSL_FUNC_kem_gettable_ctx_params  OSSL_FUNC_KEM_GETTABLE_CTX_PARAMS
\& OSSL_FUNC_kem_set_ctx_params       OSSL_FUNC_KEM_SET_CTX_PARAMS
\& OSSL_FUNC_kem_settable_ctx_params  OSSL_FUNC_KEM_SETTABLE_CTX_PARAMS
.Ve
.PP
An asymmetric kem algorithm implementation may not implement all of these
functions.
In order to be a consistent set of functions a provider must implement
OSSL_FUNC_kem_newctx and OSSL_FUNC_kem_freectx.
It must also implement both of OSSL_FUNC_kem_encapsulate_init and
OSSL_FUNC_kem_encapsulate, or both of OSSL_FUNC_kem_decapsulate_init and
OSSL_FUNC_kem_decapsulate.
OSSL_FUNC_kem_get_ctx_params is optional but if it is present then so must
OSSL_FUNC_kem_gettable_ctx_params.
Similarly, OSSL_FUNC_kem_set_ctx_params is optional but if it is present then
so must OSSL_FUNC_kem_settable_ctx_params.
.PP
An asymmetric kem algorithm must also implement some mechanism for generating,
loading or importing keys via the key management (OSSL_OP_KEYMGMT) operation.
See \fBprovider\-keymgmt\fR\|(7) for further details.
.SS "Context Management Functions"
.IX Subsection "Context Management Functions"
\&\fBOSSL_FUNC_kem_newctx()\fR should create and return a pointer to a provider side
structure for holding context information during an asymmetric kem operation.
A pointer to this context will be passed back in a number of the other
asymmetric kem operation function calls.
The parameter \fIprovctx\fR is the provider context generated during provider
initialisation (see \fBprovider\fR\|(7)).
.PP
\&\fBOSSL_FUNC_kem_freectx()\fR is passed a pointer to the provider side asymmetric
kem context in the \fIctx\fR parameter.
This function should free any resources associated with that context.
.PP
\&\fBOSSL_FUNC_kem_dupctx()\fR should duplicate the provider side asymmetric kem
context in the \fIctx\fR parameter and return the duplicate copy.
.SS "Asymmetric Key Encapsulation Functions"
.IX Subsection "Asymmetric Key Encapsulation Functions"
\&\fBOSSL_FUNC_kem_encapsulate_init()\fR initialises a context for an asymmetric
encapsulation given a provider side asymmetric kem context in the \fIctx\fR
parameter, a pointer to a provider key object in the \fIprovkey\fR parameter and
the \fIname\fR of the algorithm.
The \fIparams\fR, if not NULL, should be set on the context in a manner similar to
using \fBOSSL_FUNC_kem_set_ctx_params()\fR.
The key object should have been previously generated, loaded or imported into
the provider using the key management (OSSL_OP_KEYMGMT) operation (see
\&\fBprovider\-keymgmt\fR\|(7)>.
.PP
\&\fBOSSL_FUNC_kem_encapsulate()\fR performs the actual encapsulation itself.
A previously initialised asymmetric kem context is passed in the \fIctx\fR
parameter.
Unless \fIout\fR is NULL, the data to be encapsulated is internally generated,
and returned into the buffer pointed to by the \fIsecret\fR parameter and the
encapsulated data should also be written to the location pointed to by the
\&\fIout\fR parameter. The length of the encapsulated data should be written to
\&\fI*outlen\fR and the length of the generated secret should be written to
\&\fI*secretlen\fR.
.PP
If \fIout\fR is NULL then the maximum length of the encapsulated data should be
written to \fI*outlen\fR, and the maximum length of the generated secret should be
written to \fI*secretlen\fR.
.SS "Decapsulation Functions"
.IX Subsection "Decapsulation Functions"
\&\fBOSSL_FUNC_kem_decapsulate_init()\fR initialises a context for an asymmetric
decapsulation given a provider side asymmetric kem context in the \fIctx\fR
parameter, a pointer to a provider key object in the \fIprovkey\fR parameter, and
a \fIname\fR of the algorithm.
The key object should have been previously generated, loaded or imported into
the provider using the key management (OSSL_OP_KEYMGMT) operation (see
\&\fBprovider\-keymgmt\fR\|(7)>.
.PP
\&\fBOSSL_FUNC_kem_decapsulate()\fR performs the actual decapsulation itself.
A previously initialised asymmetric kem context is passed in the \fIctx\fR
parameter.
The data to be decapsulated is pointed to by the \fIin\fR parameter which is \fIinlen\fR
bytes long.
Unless \fIout\fR is NULL, the decapsulated data should be written to the location
pointed to by the \fIout\fR parameter.
The length of the decapsulated data should be written to \fI*outlen\fR.
If \fIout\fR is NULL then the maximum length of the decapsulated data should be
written to \fI*outlen\fR.
.SS "Asymmetric Key Encapsulation Parameters"
.IX Subsection "Asymmetric Key Encapsulation Parameters"
See \fBOSSL_PARAM\fR\|(3) for further details on the parameters structure used by
the \fBOSSL_FUNC_kem_get_ctx_params()\fR and \fBOSSL_FUNC_kem_set_ctx_params()\fR
functions.
.PP
\&\fBOSSL_FUNC_kem_get_ctx_params()\fR gets asymmetric kem parameters associated
with the given provider side asymmetric kem context \fIctx\fR and stores them in
\&\fIparams\fR.
Passing NULL for \fIparams\fR should return true.
.PP
\&\fBOSSL_FUNC_kem_set_ctx_params()\fR sets the asymmetric kem parameters associated
with the given provider side asymmetric kem context \fIctx\fR to \fIparams\fR.
Any parameter settings are additional to any that were previously set.
Passing NULL for \fIparams\fR should return true.
.PP
No parameters are currently recognised by built-in asymmetric kem algorithms.
.PP
\&\fBOSSL_FUNC_kem_gettable_ctx_params()\fR and \fBOSSL_FUNC_kem_settable_ctx_params()\fR
get a constant \fBOSSL_PARAM\fR\|(3) array that describes the gettable and settable
parameters, i.e. parameters that can be used with \fBOSSL_FUNC_kem_get_ctx_params()\fR
and \fBOSSL_FUNC_kem_set_ctx_params()\fR respectively.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_FUNC_kem_newctx()\fR and \fBOSSL_FUNC_kem_dupctx()\fR should return the newly
created provider side asymmetric kem context, or NULL on failure.
.PP
All other functions should return 1 for success or 0 on error.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The provider KEM interface was introduced in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
