.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_KEYEXCH-DH 7ossl"
.TH EVP_KEYEXCH-DH 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_KEYEXCH\-DH
\&\- DH Key Exchange algorithm support
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Key exchange support for the \fBDH\fR key type.
.SS "DH key exchange parameters"
.IX Subsection "DH key exchange parameters"
.IP """pad"" (\fBOSSL_EXCHANGE_PARAM_PAD\fR) <unsigned integer>" 4
.IX Item """pad"" (OSSL_EXCHANGE_PARAM_PAD) <unsigned integer>"
Sets the padding mode for the associated key exchange ctx.
Setting a value of 1 will turn padding on.
Setting a value of 0 will turn padding off.
If padding is off then the derived shared secret may be smaller than the
largest possible secret size.
If padding is on then the derived shared secret will have its first bytes
filled with zeros where necessary to make the shared secret the same size as
the largest possible secret size.
The padding mode parameter is ignored (and padding implicitly enabled) when
the KDF type is set to "X942KDF\-ASN1" (\fBOSSL_KDF_NAME_X942KDF_ASN1\fR).
.IP """kdf-type"" (\fBOSSL_EXCHANGE_PARAM_KDF_TYPE\fR) <UTF8 string>" 4
.IX Item """kdf-type"" (OSSL_EXCHANGE_PARAM_KDF_TYPE) <UTF8 string>"
See "Common Key Exchange parameters" in \fBprovider\-keyexch\fR\|(7).
.IP """kdf-digest"" (\fBOSSL_EXCHANGE_PARAM_KDF_DIGEST\fR) <UTF8 string>" 4
.IX Item """kdf-digest"" (OSSL_EXCHANGE_PARAM_KDF_DIGEST) <UTF8 string>"
See "Common Key Exchange parameters" in \fBprovider\-keyexch\fR\|(7).
.IP """kdf-digest-props"" (\fBOSSL_EXCHANGE_PARAM_KDF_DIGEST_PROPS\fR) <UTF8 string>" 4
.IX Item """kdf-digest-props"" (OSSL_EXCHANGE_PARAM_KDF_DIGEST_PROPS) <UTF8 string>"
See "Common Key Exchange parameters" in \fBprovider\-keyexch\fR\|(7).
.IP """kdf-outlen"" (\fBOSSL_EXCHANGE_PARAM_KDF_OUTLEN\fR) <unsigned integer>" 4
.IX Item """kdf-outlen"" (OSSL_EXCHANGE_PARAM_KDF_OUTLEN) <unsigned integer>"
See "Common Key Exchange parameters" in \fBprovider\-keyexch\fR\|(7).
.IP """kdf-ukm"" (\fBOSSL_EXCHANGE_PARAM_KDF_UKM\fR) <octet string>" 4
.IX Item """kdf-ukm"" (OSSL_EXCHANGE_PARAM_KDF_UKM) <octet string>"
See "Common Key Exchange parameters" in \fBprovider\-keyexch\fR\|(7).
.IP """cekalg"" (\fBOSSL_KDF_PARAM_CEK_ALG\fR) <octet string ptr>" 4
.IX Item """cekalg"" (OSSL_KDF_PARAM_CEK_ALG) <octet string ptr>"
See "KDF Parameters" in \fBprovider\-kdf\fR\|(7).
.SH EXAMPLES
.IX Header "EXAMPLES"
The examples assume a host and peer both generate keys using the same
named group (or domain parameters). See "Examples" in \fBEVP_PKEY\-DH\fR\|(7).
Both the host and peer transfer their public key to each other.
.PP
To convert the peer's generated key pair to a public key in DER format in order
to transfer to the host:
.PP
.Vb 3
\&    EVP_PKEY *peer_key; /* It is assumed this contains the peers generated key */
\&    unsigned char *peer_pub_der = NULL;
\&    int peer_pub_der_len;
\&
\&    peer_pub_der_len = i2d_PUBKEY(peer_key, &peer_pub_der);
\&    ...
\&    OPENSSL_free(peer_pub_der);
.Ve
.PP
To convert the received peer's public key from DER format on the host:
.PP
.Vb 4
\&    const unsigned char *pd = peer_pub_der;
\&    EVP_PKEY *peer_pub_key = d2i_PUBKEY(NULL, &pd, peer_pub_der_len);
\&    ...
\&    EVP_PKEY_free(peer_pub_key);
.Ve
.PP
To derive a shared secret on the host using the host's key and the peer's public
key:
.PP
.Vb 8
\&    /* It is assumed that the host_key and peer_pub_key are set up */
\&    void derive_secret(EVP_KEY *host_key, EVP_PKEY *peer_pub_key)
\&    {
\&        unsigned int pad = 1;
\&        OSSL_PARAM params[2];
\&        unsigned char *secret = NULL;
\&        size_t secret_len = 0;
\&        EVP_PKEY_CTX *dctx = EVP_PKEY_CTX_new_from_pkey(NULL, host_key, NULL);
\&
\&        EVP_PKEY_derive_init(dctx);
\&
\&        /* Optionally set the padding */
\&        params[0] = OSSL_PARAM_construct_uint(OSSL_EXCHANGE_PARAM_PAD, &pad);
\&        params[1] = OSSL_PARAM_construct_end();
\&        EVP_PKEY_CTX_set_params(dctx, params);
\&
\&        EVP_PKEY_derive_set_peer(dctx, peer_pub_key);
\&
\&        /* Get the size by passing NULL as the buffer */
\&        EVP_PKEY_derive(dctx, NULL, &secret_len);
\&        secret = OPENSSL_zalloc(secret_len);
\&
\&        EVP_PKEY_derive(dctx, secret, &secret_len);
\&        ...
\&        OPENSSL_clear_free(secret, secret_len);
\&        EVP_PKEY_CTX_free(dctx);
\&    }
.Ve
.PP
Very similar code can be used by the peer to derive the same shared secret
using the host's public key and the peer's generated key pair.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY\-DH\fR\|(7),
\&\fBEVP_PKEY\-FFC\fR\|(7),
\&\fBEVP_PKEY\fR\|(3),
\&\fBprovider\-keyexch\fR\|(7),
\&\fBprovider\-keymgmt\fR\|(7),
\&\fBOSSL_PROVIDER\-default\fR\|(7),
\&\fBOSSL_PROVIDER\-FIPS\fR\|(7),
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
