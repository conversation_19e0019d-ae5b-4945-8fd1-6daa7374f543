.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_CIPHER-NULL 7ossl"
.TH EVP_CIPHER-NULL 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_CIPHER\-NULL \- The NULL EVP_CIPHER implementation
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Support for a NULL symmetric encryption using the \fBEVP_CIPHER\fR API.
This is used when the TLS cipher suite is TLS_NULL_WITH_NULL_NULL.
This does no encryption (just copies the data) and has a mac size of zero.
.SS "Algorithm Name"
.IX Subsection "Algorithm Name"
The following algorithm is available in the default provider:
.IP """NULL""" 4
.IX Item """NULL"""
.SS Parameters
.IX Subsection "Parameters"
This implementation supports the following parameters:
.PP
\fIGettable EVP_CIPHER parameters\fR
.IX Subsection "Gettable EVP_CIPHER parameters"
.PP
See "Gettable EVP_CIPHER parameters" in \fBEVP_EncryptInit\fR\|(3)
.PP
\fIGettable EVP_CIPHER_CTX parameters\fR
.IX Subsection "Gettable EVP_CIPHER_CTX parameters"
.IP """keylen"" (\fBOSSL_CIPHER_PARAM_KEYLEN\fR) <unsigned integer>" 4
.IX Item """keylen"" (OSSL_CIPHER_PARAM_KEYLEN) <unsigned integer>"
.PD 0
.IP """ivlen"" (\fBOSSL_CIPHER_PARAM_IVLEN\fR and <\fBOSSL_CIPHER_PARAM_AEAD_IVLEN\fR) <unsigned integer>" 4
.IX Item """ivlen"" (OSSL_CIPHER_PARAM_IVLEN and <OSSL_CIPHER_PARAM_AEAD_IVLEN) <unsigned integer>"
.IP """tls-mac"" (\fBOSSL_CIPHER_PARAM_TLS_MAC\fR) <octet ptr>" 4
.IX Item """tls-mac"" (OSSL_CIPHER_PARAM_TLS_MAC) <octet ptr>"
.PD
.PP
See "PARAMETERS" in \fBEVP_EncryptInit\fR\|(3) for further information.
.PP
\fISettable EVP_CIPHER_CTX parameters\fR
.IX Subsection "Settable EVP_CIPHER_CTX parameters"
.IP """tls-mac-size"" (\fBOSSL_CIPHER_PARAM_TLS_MAC_SIZE\fR) <unsigned integer>" 4
.IX Item """tls-mac-size"" (OSSL_CIPHER_PARAM_TLS_MAC_SIZE) <unsigned integer>"
.PP
See "PARAMETERS" in \fBEVP_EncryptInit\fR\|(3) for further information.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
RFC 5246 section\-*******
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\-cipher\fR\|(7), \fBOSSL_PROVIDER\-default\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
