.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_RAND-HMAC-DRBG 7ossl"
.TH EVP_RAND-HMAC-DRBG 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_RAND\-HMAC\-DRBG \- The HMAC DRBG EVP_RAND implementation
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Support for the HMAC deterministic random bit generator through the
\&\fBEVP_RAND\fR API.
.SS Identity
.IX Subsection "Identity"
"HMAC-DRBG" is the name for this implementation; it can be used with the
\&\fBEVP_RAND_fetch()\fR function.
.SS "Supported parameters"
.IX Subsection "Supported parameters"
The supported parameters are:
.IP """state"" (\fBOSSL_RAND_PARAM_STATE\fR) <integer>" 4
.IX Item """state"" (OSSL_RAND_PARAM_STATE) <integer>"
.PD 0
.IP """strength"" (\fBOSSL_RAND_PARAM_STRENGTH\fR) <unsigned integer>" 4
.IX Item """strength"" (OSSL_RAND_PARAM_STRENGTH) <unsigned integer>"
.IP """max_request"" (\fBOSSL_RAND_PARAM_MAX_REQUEST\fR) <unsigned integer>" 4
.IX Item """max_request"" (OSSL_RAND_PARAM_MAX_REQUEST) <unsigned integer>"
.IP """reseed_requests"" (\fBOSSL_DRBG_PARAM_RESEED_REQUESTS\fR) <unsigned integer>" 4
.IX Item """reseed_requests"" (OSSL_DRBG_PARAM_RESEED_REQUESTS) <unsigned integer>"
.IP """reseed_time_interval"" (\fBOSSL_DRBG_PARAM_RESEED_TIME_INTERVAL\fR) <integer>" 4
.IX Item """reseed_time_interval"" (OSSL_DRBG_PARAM_RESEED_TIME_INTERVAL) <integer>"
.IP """min_entropylen"" (\fBOSSL_DRBG_PARAM_MIN_ENTROPYLEN\fR) <unsigned integer>" 4
.IX Item """min_entropylen"" (OSSL_DRBG_PARAM_MIN_ENTROPYLEN) <unsigned integer>"
.IP """max_entropylen"" (\fBOSSL_DRBG_PARAM_MAX_ENTROPYLEN\fR) <unsigned integer>" 4
.IX Item """max_entropylen"" (OSSL_DRBG_PARAM_MAX_ENTROPYLEN) <unsigned integer>"
.IP """min_noncelen"" (\fBOSSL_DRBG_PARAM_MIN_NONCELEN\fR) <unsigned integer>" 4
.IX Item """min_noncelen"" (OSSL_DRBG_PARAM_MIN_NONCELEN) <unsigned integer>"
.IP """max_noncelen"" (\fBOSSL_DRBG_PARAM_MAX_NONCELEN\fR) <unsigned integer>" 4
.IX Item """max_noncelen"" (OSSL_DRBG_PARAM_MAX_NONCELEN) <unsigned integer>"
.IP """max_perslen"" (\fBOSSL_DRBG_PARAM_MAX_PERSLEN\fR) <unsigned integer>" 4
.IX Item """max_perslen"" (OSSL_DRBG_PARAM_MAX_PERSLEN) <unsigned integer>"
.IP """max_adinlen"" (\fBOSSL_DRBG_PARAM_MAX_ADINLEN\fR) <unsigned integer>" 4
.IX Item """max_adinlen"" (OSSL_DRBG_PARAM_MAX_ADINLEN) <unsigned integer>"
.IP """reseed_counter"" (\fBOSSL_DRBG_PARAM_RESEED_COUNTER\fR) <unsigned integer>" 4
.IX Item """reseed_counter"" (OSSL_DRBG_PARAM_RESEED_COUNTER) <unsigned integer>"
.IP """properties"" (\fBOSSL_DRBG_PARAM_PROPERTIES\fR) <UTF8 string>" 4
.IX Item """properties"" (OSSL_DRBG_PARAM_PROPERTIES) <UTF8 string>"
.IP """mac"" (\fBOSSL_DRBG_PARAM_MAC\fR) <UTF8 string>" 4
.IX Item """mac"" (OSSL_DRBG_PARAM_MAC) <UTF8 string>"
.IP """digest"" (\fBOSSL_DRBG_PARAM_DIGEST\fR) <UTF8 string>" 4
.IX Item """digest"" (OSSL_DRBG_PARAM_DIGEST) <UTF8 string>"
.PD
These parameters work as described in "PARAMETERS" in \fBEVP_RAND\fR\|(3).
.SH NOTES
.IX Header "NOTES"
A context for HMAC DRBG can be obtained by calling:
.PP
.Vb 2
\& EVP_RAND *rand = EVP_RAND_fetch(NULL, "HMAC\-DRBG", NULL);
\& EVP_RAND_CTX *rctx = EVP_RAND_CTX_new(rand);
.Ve
.SH EXAMPLES
.IX Header "EXAMPLES"
.Vb 5
\& EVP_RAND *rand;
\& EVP_RAND_CTX *rctx;
\& unsigned char bytes[100];
\& OSSL_PARAM params[3], *p = params;
\& unsigned int strength = 128;
\&
\& rand = EVP_RAND_fetch(NULL, "HMAC\-DRBG", NULL);
\& rctx = EVP_RAND_CTX_new(rand, NULL);
\& EVP_RAND_free(rand);
\&
\& *p++ = OSSL_PARAM_construct_utf8_string(OSSL_DRBG_PARAM_MAC, SN_hmac, 0);
\& *p++ = OSSL_PARAM_construct_utf8_string(OSSL_DRBG_PARAM_DIGEST, SN_sha256, 0);
\& *p = OSSL_PARAM_construct_end();
\& EVP_RAND_instantiate(rctx, strength, 0, NULL, 0, params);
\&
\& EVP_RAND_generate(rctx, bytes, sizeof(bytes), strength, 0, NULL, 0);
\&
\& EVP_RAND_CTX_free(rctx);
.Ve
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
NIST SP 800\-90A and SP 800\-90B
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_RAND\fR\|(3),
"PARAMETERS" in \fBEVP_RAND\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
