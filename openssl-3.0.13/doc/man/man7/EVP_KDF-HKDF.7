.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_KDF-HKDF 7ossl"
.TH EVP_KDF-HKDF 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_KDF\-HKDF \- The HKDF EVP_KDF implementation
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Support for computing the \fBHKDF\fR KDF through the \fBEVP_KDF\fR API.
.PP
The EVP_KDF\-HKDF algorithm implements the HKDF key derivation function.
HKDF follows the "extract-then-expand" paradigm, where the KDF logically
consists of two modules. The first stage takes the input keying material
and "extracts" from it a fixed-length pseudorandom key K. The second stage
"expands" the key K into several additional pseudorandom keys (the output
of the KDF).
.SS Identity
.IX Subsection "Identity"
"HKDF" is the name for this implementation; it
can be used with the \fBEVP_KDF_fetch()\fR function.
.SS "Supported parameters"
.IX Subsection "Supported parameters"
The supported parameters are:
.IP """properties"" (\fBOSSL_KDF_PARAM_PROPERTIES\fR) <UTF8 string>" 4
.IX Item """properties"" (OSSL_KDF_PARAM_PROPERTIES) <UTF8 string>"
.PD 0
.IP """digest"" (\fBOSSL_KDF_PARAM_DIGEST\fR) <UTF8 string>" 4
.IX Item """digest"" (OSSL_KDF_PARAM_DIGEST) <UTF8 string>"
.IP """key"" (\fBOSSL_KDF_PARAM_KEY\fR) <octet string>" 4
.IX Item """key"" (OSSL_KDF_PARAM_KEY) <octet string>"
.IP """salt"" (\fBOSSL_KDF_PARAM_SALT\fR) <octet string>" 4
.IX Item """salt"" (OSSL_KDF_PARAM_SALT) <octet string>"
.PD
These parameters work as described in "PARAMETERS" in \fBEVP_KDF\fR\|(3).
.IP """info"" (\fBOSSL_KDF_PARAM_INFO\fR) <octet string>" 4
.IX Item """info"" (OSSL_KDF_PARAM_INFO) <octet string>"
This parameter sets the info value.
The length of the context info buffer cannot exceed 1024 bytes;
this should be more than enough for any normal use of HKDF.
.IP """mode"" (\fBOSSL_KDF_PARAM_MODE\fR) <UTF8 string> or <integer>" 4
.IX Item """mode"" (OSSL_KDF_PARAM_MODE) <UTF8 string> or <integer>"
This parameter sets the mode for the HKDF operation.
There are three modes that are currently defined:
.RS 4
.IP """EXTRACT_AND_EXPAND"" or \fBEVP_KDF_HKDF_MODE_EXTRACT_AND_EXPAND\fR" 4
.IX Item """EXTRACT_AND_EXPAND"" or EVP_KDF_HKDF_MODE_EXTRACT_AND_EXPAND"
This is the default mode.  Calling \fBEVP_KDF_derive\fR\|(3) on an EVP_KDF_CTX set
up for HKDF will perform an extract followed by an expand operation in one go.
The derived key returned will be the result after the expand operation. The
intermediate fixed-length pseudorandom key K is not returned.
.Sp
In this mode the digest, key, salt and info values must be set before a key is
derived otherwise an error will occur.
.IP """EXTRACT_ONLY"" or \fBEVP_KDF_HKDF_MODE_EXTRACT_ONLY\fR" 4
.IX Item """EXTRACT_ONLY"" or EVP_KDF_HKDF_MODE_EXTRACT_ONLY"
In this mode calling \fBEVP_KDF_derive\fR\|(3) will just perform the extract
operation. The value returned will be the intermediate fixed-length pseudorandom
key K.  The \fIkeylen\fR parameter must match the size of K, which can be looked
up by calling \fBEVP_KDF_CTX_get_kdf_size()\fR after setting the mode and digest.
.Sp
The digest, key and salt values must be set before a key is derived otherwise
an error will occur.
.IP """EXPAND_ONLY"" or \fBEVP_KDF_HKDF_MODE_EXPAND_ONLY\fR" 4
.IX Item """EXPAND_ONLY"" or EVP_KDF_HKDF_MODE_EXPAND_ONLY"
In this mode calling \fBEVP_KDF_derive\fR\|(3) will just perform the expand
operation. The input key should be set to the intermediate fixed-length
pseudorandom key K returned from a previous extract operation.
.Sp
The digest, key and info values must be set before a key is derived otherwise
an error will occur.
.RE
.RS 4
.RE
.SH NOTES
.IX Header "NOTES"
A context for HKDF can be obtained by calling:
.PP
.Vb 2
\& EVP_KDF *kdf = EVP_KDF_fetch(NULL, "HKDF", NULL);
\& EVP_KDF_CTX *kctx = EVP_KDF_CTX_new(kdf);
.Ve
.PP
The output length of an HKDF expand operation is specified via the \fIkeylen\fR
parameter to the \fBEVP_KDF_derive\fR\|(3) function.  When using
EVP_KDF_HKDF_MODE_EXTRACT_ONLY the \fIkeylen\fR parameter must equal the size of
the intermediate fixed-length pseudorandom key otherwise an error will occur.
For that mode, the fixed output size can be looked up by calling \fBEVP_KDF_CTX_get_kdf_size()\fR
after setting the mode and digest on the \fBEVP_KDF_CTX\fR.
.SH EXAMPLES
.IX Header "EXAMPLES"
This example derives 10 bytes using SHA\-256 with the secret key "secret",
salt value "salt" and info value "label":
.PP
.Vb 4
\& EVP_KDF *kdf;
\& EVP_KDF_CTX *kctx;
\& unsigned char out[10];
\& OSSL_PARAM params[5], *p = params;
\&
\& kdf = EVP_KDF_fetch(NULL, "HKDF", NULL);
\& kctx = EVP_KDF_CTX_new(kdf);
\& EVP_KDF_free(kdf);
\&
\& *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_DIGEST,
\&                                         SN_sha256, strlen(SN_sha256));
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_KEY,
\&                                          "secret", (size_t)6);
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_INFO,
\&                                          "label", (size_t)5);
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SALT,
\&                                          "salt", (size_t)4);
\& *p = OSSL_PARAM_construct_end();
\& if (EVP_KDF_derive(kctx, out, sizeof(out), params) <= 0) {
\&     error("EVP_KDF_derive");
\& }
\&
\& EVP_KDF_CTX_free(kctx);
.Ve
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
RFC 5869
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_KDF\fR\|(3),
\&\fBEVP_KDF_CTX_new\fR\|(3),
\&\fBEVP_KDF_CTX_free\fR\|(3),
\&\fBEVP_KDF_CTX_get_kdf_size\fR\|(3),
\&\fBEVP_KDF_CTX_set_params\fR\|(3),
\&\fBEVP_KDF_derive\fR\|(3),
"PARAMETERS" in \fBEVP_KDF\fR\|(3),
\&\fBEVP_KDF\-TLS13_KDF\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
This functionality was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2016\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
