.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_SIGNATURE-RSA 7ossl"
.TH EVP_SIGNATURE-RSA 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_SIGNATURE\-RSA
\&\- The EVP_PKEY RSA signature implementation
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Support for computing RSA signatures.
See \fBEVP_PKEY\-RSA\fR\|(7) for information related to RSA keys.
.SS "Signature Parameters"
.IX Subsection "Signature Parameters"
The following signature parameters can be set using \fBEVP_PKEY_CTX_set_params()\fR.
This may be called after \fBEVP_PKEY_sign_init()\fR or \fBEVP_PKEY_verify_init()\fR,
and before calling \fBEVP_PKEY_sign()\fR or \fBEVP_PKEY_verify()\fR.
.IP """digest"" (\fBOSSL_SIGNATURE_PARAM_DIGEST\fR) <UTF8 string>" 4
.IX Item """digest"" (OSSL_SIGNATURE_PARAM_DIGEST) <UTF8 string>"
.PD 0
.IP """properties"" (\fBOSSL_SIGNATURE_PARAM_PROPERTIES\fR) <UTF8 string>" 4
.IX Item """properties"" (OSSL_SIGNATURE_PARAM_PROPERTIES) <UTF8 string>"
.PD
These common parameters are described in \fBprovider\-signature\fR\|(7).
.IP """pad-mode"" (\fBOSSL_SIGNATURE_PARAM_PAD_MODE\fR) <UTF8 string>" 4
.IX Item """pad-mode"" (OSSL_SIGNATURE_PARAM_PAD_MODE) <UTF8 string>"
The type of padding to be used. Its value can be one of the following:
.RS 4
.IP """none"" (\fBOSSL_PKEY_RSA_PAD_MODE_NONE\fR)" 4
.IX Item """none"" (OSSL_PKEY_RSA_PAD_MODE_NONE)"
.PD 0
.IP """pkcs1"" (\fBOSSL_PKEY_RSA_PAD_MODE_PKCSV15\fR)" 4
.IX Item """pkcs1"" (OSSL_PKEY_RSA_PAD_MODE_PKCSV15)"
.IP """x931"" (\fBOSSL_PKEY_RSA_PAD_MODE_X931\fR)" 4
.IX Item """x931"" (OSSL_PKEY_RSA_PAD_MODE_X931)"
.IP """pss"" (\fBOSSL_PKEY_RSA_PAD_MODE_PSS\fR)" 4
.IX Item """pss"" (OSSL_PKEY_RSA_PAD_MODE_PSS)"
.RE
.RS 4
.RE
.IP """mgf1\-digest"" (\fBOSSL_SIGNATURE_PARAM_MGF1_DIGEST\fR) <UTF8 string>" 4
.IX Item """mgf1-digest"" (OSSL_SIGNATURE_PARAM_MGF1_DIGEST) <UTF8 string>"
.PD
The digest algorithm name to use for the maskGenAlgorithm used by "pss" mode.
.IP """mgf1\-properties"" (\fBOSSL_SIGNATURE_PARAM_MGF1_PROPERTIES\fR) <UTF8 string>" 4
.IX Item """mgf1-properties"" (OSSL_SIGNATURE_PARAM_MGF1_PROPERTIES) <UTF8 string>"
Sets the name of the property query associated with the "mgf1\-digest" algorithm.
NULL is used if this optional value is not set.
.IP """saltlen"" (\fBOSSL_SIGNATURE_PARAM_PSS_SALTLEN\fR) <integer> or <UTF8 string>" 4
.IX Item """saltlen"" (OSSL_SIGNATURE_PARAM_PSS_SALTLEN) <integer> or <UTF8 string>"
The "pss" mode minimum salt length. The value can either be an integer,
a string value representing a number or one of the following string values:
.RS 4
.IP """digest"" (\fBOSSL_PKEY_RSA_PSS_SALT_LEN_DIGEST\fR)" 4
.IX Item """digest"" (OSSL_PKEY_RSA_PSS_SALT_LEN_DIGEST)"
Use the same length as the digest size.
.IP """max"" (\fBOSSL_PKEY_RSA_PSS_SALT_LEN_MAX\fR)" 4
.IX Item """max"" (OSSL_PKEY_RSA_PSS_SALT_LEN_MAX)"
Use the maximum salt length.
.IP """auto"" (\fBOSSL_PKEY_RSA_PSS_SALT_LEN_AUTO\fR)" 4
.IX Item """auto"" (OSSL_PKEY_RSA_PSS_SALT_LEN_AUTO)"
Auto detect the salt length.
.RE
.RS 4
.RE
.PP
The following signature parameters can be retrieved using
\&\fBEVP_PKEY_CTX_get_params()\fR.
.IP """algorithm-id"" (\fBOSSL_SIGNATURE_PARAM_ALGORITHM_ID\fR) <octet string>" 4
.IX Item """algorithm-id"" (OSSL_SIGNATURE_PARAM_ALGORITHM_ID) <octet string>"
This common parameter is described in \fBprovider\-signature\fR\|(7).
.IP """digest"" (\fBOSSL_SIGNATURE_PARAM_DIGEST\fR) <UTF8 string>" 4
.IX Item """digest"" (OSSL_SIGNATURE_PARAM_DIGEST) <UTF8 string>"
.PD 0
.IP """pad-mode"" (\fBOSSL_SIGNATURE_PARAM_PAD_MODE\fR) <UTF8 string>" 4
.IX Item """pad-mode"" (OSSL_SIGNATURE_PARAM_PAD_MODE) <UTF8 string>"
.IP """mgf1\-digest"" (\fBOSSL_SIGNATURE_PARAM_MGF1_DIGEST\fR) <UTF8 string>" 4
.IX Item """mgf1-digest"" (OSSL_SIGNATURE_PARAM_MGF1_DIGEST) <UTF8 string>"
.IP """saltlen"" (\fBOSSL_SIGNATURE_PARAM_PSS_SALTLEN\fR) <integer> or <UTF8 string>" 4
.IX Item """saltlen"" (OSSL_SIGNATURE_PARAM_PSS_SALTLEN) <integer> or <UTF8 string>"
.PD
These parameters are as described above.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY_CTX_set_params\fR\|(3),
\&\fBEVP_PKEY_sign\fR\|(3),
\&\fBEVP_PKEY_verify\fR\|(3),
\&\fBprovider\-signature\fR\|(7),
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
