.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-ENV 7ossl"
.TH OPENSSL-ENV 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-env \- OpenSSL environment variables
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The OpenSSL libraries use environment variables to override the
compiled-in default paths for various data.
To avoid security risks, the environment is usually not consulted when
the executable is set-user-ID or set-group-ID.
.IP \fBCTLOG_FILE\fR 4
.IX Item "CTLOG_FILE"
Specifies the path to a certificate transparency log list.
See \fBCTLOG_STORE_new\fR\|(3).
.IP \fBOPENSSL\fR 4
.IX Item "OPENSSL"
Specifies the path to the \fBopenssl\fR executable. Used by
the \fBrehash\fR script (see "Script Configuration" in \fBopenssl\-rehash\fR\|(1))
and by the \fBCA.pl\fR script (see "NOTES" in \fBCA.pl\fR\|(1)
.IP "\fBOPENSSL_CONF\fR, \fBOPENSSL_CONF_INCLUDE\fR" 4
.IX Item "OPENSSL_CONF, OPENSSL_CONF_INCLUDE"
Specifies the path to a configuration file and the directory for
included files.
See \fBconfig\fR\|(5).
.IP \fBOPENSSL_CONFIG\fR 4
.IX Item "OPENSSL_CONFIG"
Specifies a configuration option and filename for the \fBreq\fR and \fBca\fR
commands invoked by the \fBCA.pl\fR script.
See \fBCA.pl\fR\|(1).
.IP \fBOPENSSL_ENGINES\fR 4
.IX Item "OPENSSL_ENGINES"
Specifies the directory from which dynamic engines are loaded.
See \fBopenssl\-engine\fR\|(1).
.IP "\fBOPENSSL_MALLOC_FD\fR, \fBOPENSSL_MALLOC_FAILURES\fR" 4
.IX Item "OPENSSL_MALLOC_FD, OPENSSL_MALLOC_FAILURES"
If built with debugging, this allows memory allocation to fail.
See \fBOPENSSL_malloc\fR\|(3).
.IP \fBOPENSSL_MODULES\fR 4
.IX Item "OPENSSL_MODULES"
Specifies the directory from which cryptographic providers are loaded.
Equivalently, the generic \fB\-provider\-path\fR command-line option may be used.
.IP \fBOPENSSL_WIN32_UTF8\fR 4
.IX Item "OPENSSL_WIN32_UTF8"
If set, then \fBUI_OpenSSL\fR\|(3) returns UTF\-8 encoded strings, rather than
ones encoded in the current code page, and
the \fBopenssl\fR\|(1) program also transcodes the command-line parameters
from the current code page to UTF\-8.
This environment variable is only checked on Microsoft Windows platforms.
.IP \fBRANDFILE\fR 4
.IX Item "RANDFILE"
The state file for the random number generator.
This should not be needed in normal use.
See \fBRAND_load_file\fR\|(3).
.IP "\fBSSL_CERT_DIR\fR, \fBSSL_CERT_FILE\fR" 4
.IX Item "SSL_CERT_DIR, SSL_CERT_FILE"
Specify the default directory or file containing CA certificates.
See \fBSSL_CTX_load_verify_locations\fR\|(3).
.IP \fBTSGET\fR 4
.IX Item "TSGET"
Additional arguments for the \fBtsget\fR\|(1) command.
.IP "\fBOPENSSL_ia32cap\fR, \fBOPENSSL_sparcv9cap\fR, \fBOPENSSL_ppccap\fR, \fBOPENSSL_armcap\fR, \fBOPENSSL_s390xcap\fR" 4
.IX Item "OPENSSL_ia32cap, OPENSSL_sparcv9cap, OPENSSL_ppccap, OPENSSL_armcap, OPENSSL_s390xcap"
OpenSSL supports a number of different algorithm implementations for
various machines and, by default, it determines which to use based on the
processor capabilities and run time feature enquiry.  These environment
variables can be used to exert more control over this selection process.
See \fBOPENSSL_ia32cap\fR\|(3), \fBOPENSSL_s390xcap\fR\|(3).
.IP "\fBNO_PROXY\fR, \fBHTTPS_PROXY\fR, \fBHTTP_PROXY\fR" 4
.IX Item "NO_PROXY, HTTPS_PROXY, HTTP_PROXY"
Specify a proxy hostname.
See \fBOSSL_HTTP_parse_url\fR\|(3).
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
