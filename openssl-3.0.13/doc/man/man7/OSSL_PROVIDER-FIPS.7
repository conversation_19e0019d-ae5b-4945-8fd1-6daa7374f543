.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OSSL_PROVIDER-FIPS 7ossl"
.TH OSSL_PROVIDER-FIPS 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
OSSL_PROVIDER\-FIPS \- OpenSSL FIPS provider
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The OpenSSL FIPS provider is a special provider that conforms to the Federal
Information Processing Standards (FIPS) specified in FIPS 140\-2. This 'module'
contains an approved set of cryptographic algorithms that is validated by an
accredited testing laboratory.
.SS Properties
.IX Subsection "Properties"
The implementations in this provider specifically have these properties
defined:
.IP """provider=fips""" 4
.IX Item """provider=fips"""
.PD 0
.IP """fips=yes""" 4
.IX Item """fips=yes"""
.PD
.PP
It may be used in a property query string with fetching functions such as
\&\fBEVP_MD_fetch\fR\|(3) or \fBEVP_CIPHER_fetch\fR\|(3), as well as with other
functions that take a property query string, such as
\&\fBEVP_PKEY_CTX_new_from_name\fR\|(3).
.PP
It isn't mandatory to query for any of these properties, except to
make sure to get implementations of this provider and none other.
.PP
The "fips=yes" property can be use to make sure only FIPS approved
implementations are used for crypto operations.  This may also include
other non-crypto support operations that are not in the FIPS provider,
such as asymmetric key encoders,
see "Asymmetric Key Management" in \fBOSSL_PROVIDER\-default\fR\|(7).
.SH "OPERATIONS AND ALGORITHMS"
.IX Header "OPERATIONS AND ALGORITHMS"
The OpenSSL FIPS provider supports these operations and algorithms:
.SS "Hashing Algorithms / Message Digests"
.IX Subsection "Hashing Algorithms / Message Digests"
.IP "SHA1, see \fBEVP_MD\-SHA1\fR\|(7)" 4
.IX Item "SHA1, see EVP_MD-SHA1"
.PD 0
.IP "SHA2, see \fBEVP_MD\-SHA2\fR\|(7)" 4
.IX Item "SHA2, see EVP_MD-SHA2"
.IP "SHA3, see \fBEVP_MD\-SHA3\fR\|(7)" 4
.IX Item "SHA3, see EVP_MD-SHA3"
.IP "KECCAK-KMAC, see \fBEVP_MD\-KECCAK\-KMAC\fR\|(7)" 4
.IX Item "KECCAK-KMAC, see EVP_MD-KECCAK-KMAC"
.PD
.SS "Symmetric Ciphers"
.IX Subsection "Symmetric Ciphers"
.IP "AES, see \fBEVP_CIPHER\-AES\fR\|(7)" 4
.IX Item "AES, see EVP_CIPHER-AES"
.PD 0
.IP "DES\-EDE3 (TripleDES), see \fBEVP_CIPHER\-DES\fR\|(7)" 4
.IX Item "DES-EDE3 (TripleDES), see EVP_CIPHER-DES"
.PD
.SS "Message Authentication Code (MAC)"
.IX Subsection "Message Authentication Code (MAC)"
.IP "CMAC, see \fBEVP_MAC\-CMAC\fR\|(7)" 4
.IX Item "CMAC, see EVP_MAC-CMAC"
.PD 0
.IP "GMAC, see \fBEVP_MAC\-GMAC\fR\|(7)" 4
.IX Item "GMAC, see EVP_MAC-GMAC"
.IP "HMAC, see \fBEVP_MAC\-HMAC\fR\|(7)" 4
.IX Item "HMAC, see EVP_MAC-HMAC"
.IP "KMAC, see \fBEVP_MAC\-KMAC\fR\|(7)" 4
.IX Item "KMAC, see EVP_MAC-KMAC"
.PD
.SS "Key Derivation Function (KDF)"
.IX Subsection "Key Derivation Function (KDF)"
.IP "HKDF, see \fBEVP_KDF\-HKDF\fR\|(7)" 4
.IX Item "HKDF, see EVP_KDF-HKDF"
.PD 0
.IP "TLS13\-KDF, see \fBEVP_KDF\-TLS13_KDF\fR\|(7)" 4
.IX Item "TLS13-KDF, see EVP_KDF-TLS13_KDF"
.IP "SSKDF, see \fBEVP_KDF\-SS\fR\|(7)" 4
.IX Item "SSKDF, see EVP_KDF-SS"
.IP "PBKDF2, see \fBEVP_KDF\-PBKDF2\fR\|(7)" 4
.IX Item "PBKDF2, see EVP_KDF-PBKDF2"
.IP "SSHKDF, see \fBEVP_KDF\-SSHKDF\fR\|(7)" 4
.IX Item "SSHKDF, see EVP_KDF-SSHKDF"
.IP "TLS1\-PRF, see \fBEVP_KDF\-TLS1_PRF\fR\|(7)" 4
.IX Item "TLS1-PRF, see EVP_KDF-TLS1_PRF"
.IP "KBKDF, see \fBEVP_KDF\-KB\fR\|(7)" 4
.IX Item "KBKDF, see EVP_KDF-KB"
.IP "X942KDF\-ASN1, see \fBEVP_KDF\-X942\-ASN1\fR\|(7)" 4
.IX Item "X942KDF-ASN1, see EVP_KDF-X942-ASN1"
.IP "X942KDF\-CONCAT, see \fBEVP_KDF\-X942\-CONCAT\fR\|(7)" 4
.IX Item "X942KDF-CONCAT, see EVP_KDF-X942-CONCAT"
.IP "X963KDF, see \fBEVP_KDF\-X963\fR\|(7)" 4
.IX Item "X963KDF, see EVP_KDF-X963"
.PD
.SS "Key Exchange"
.IX Subsection "Key Exchange"
.IP "DH, see \fBEVP_KEYEXCH\-DH\fR\|(7)" 4
.IX Item "DH, see EVP_KEYEXCH-DH"
.PD 0
.IP "ECDH, see \fBEVP_KEYEXCH\-ECDH\fR\|(7)" 4
.IX Item "ECDH, see EVP_KEYEXCH-ECDH"
.IP "X25519, see \fBEVP_KEYEXCH\-X25519\fR\|(7)" 4
.IX Item "X25519, see EVP_KEYEXCH-X25519"
.IP "X448, see \fBEVP_KEYEXCH\-X448\fR\|(7)" 4
.IX Item "X448, see EVP_KEYEXCH-X448"
.PD
.SS "Asymmetric Signature"
.IX Subsection "Asymmetric Signature"
.IP "RSA, see \fBEVP_SIGNATURE\-RSA\fR\|(7)" 4
.IX Item "RSA, see EVP_SIGNATURE-RSA"
.PD 0
.IP "X25519, see \fBEVP_SIGNATURE\-ED25519\fR\|(7)" 4
.IX Item "X25519, see EVP_SIGNATURE-ED25519"
.IP "X448, see \fBEVP_SIGNATURE\-ED448\fR\|(7)" 4
.IX Item "X448, see EVP_SIGNATURE-ED448"
.IP "HMAC, see \fBEVP_SIGNATURE\-HMAC\fR\|(7)" 4
.IX Item "HMAC, see EVP_SIGNATURE-HMAC"
.IP "CMAC, see \fBEVP_SIGNATURE\-CMAC\fR\|(7)" 4
.IX Item "CMAC, see EVP_SIGNATURE-CMAC"
.PD
.SS "Asymmetric Cipher"
.IX Subsection "Asymmetric Cipher"
.IP "RSA, see \fBEVP_ASYM_CIPHER\-RSA\fR\|(7)" 4
.IX Item "RSA, see EVP_ASYM_CIPHER-RSA"
.SS "Asymmetric Key Encapsulation"
.IX Subsection "Asymmetric Key Encapsulation"
.PD 0
.IP "RSA, see \fBEVP_KEM\-RSA\fR\|(7)" 4
.IX Item "RSA, see EVP_KEM-RSA"
.PD
.SS "Asymmetric Key Management"
.IX Subsection "Asymmetric Key Management"
.IP "DH, see \fBEVP_KEYMGMT\-DH\fR\|(7)" 4
.IX Item "DH, see EVP_KEYMGMT-DH"
.PD 0
.IP "DHX, see \fBEVP_KEYMGMT\-DHX\fR\|(7)" 4
.IX Item "DHX, see EVP_KEYMGMT-DHX"
.IP "DSA, see \fBEVP_KEYMGMT\-DSA\fR\|(7)" 4
.IX Item "DSA, see EVP_KEYMGMT-DSA"
.IP "RSA, see \fBEVP_KEYMGMT\-RSA\fR\|(7)" 4
.IX Item "RSA, see EVP_KEYMGMT-RSA"
.IP "EC, see \fBEVP_KEYMGMT\-EC\fR\|(7)" 4
.IX Item "EC, see EVP_KEYMGMT-EC"
.IP "X25519, see \fBEVP_KEYMGMT\-X25519\fR\|(7)" 4
.IX Item "X25519, see EVP_KEYMGMT-X25519"
.IP "X448, see \fBEVP_KEYMGMT\-X448\fR\|(7)" 4
.IX Item "X448, see EVP_KEYMGMT-X448"
.PD
.SS "Random Number Generation"
.IX Subsection "Random Number Generation"
.IP "CTR-DRBG, see \fBEVP_RAND\-CTR\-DRBG\fR\|(7)" 4
.IX Item "CTR-DRBG, see EVP_RAND-CTR-DRBG"
.PD 0
.IP "HASH-DRBG, see \fBEVP_RAND\-HASH\-DRBG\fR\|(7)" 4
.IX Item "HASH-DRBG, see EVP_RAND-HASH-DRBG"
.IP "HMAC-DRBG, see \fBEVP_RAND\-HMAC\-DRBG\fR\|(7)" 4
.IX Item "HMAC-DRBG, see EVP_RAND-HMAC-DRBG"
.IP "TEST-RAND, see \fBEVP_RAND\-TEST\-RAND\fR\|(7)" 4
.IX Item "TEST-RAND, see EVP_RAND-TEST-RAND"
.PD
TEST-RAND is an unapproved algorithm.
.SH "SELF TESTING"
.IX Header "SELF TESTING"
One of the requirements for the FIPS module is self testing. An optional callback
mechanism is available to return information to the user using
\&\fBOSSL_SELF_TEST_set_callback\fR\|(3).
.PP
The parameters passed to the callback are described in \fBOSSL_SELF_TEST_new\fR\|(3)
.PP
The OpenSSL FIPS module uses the following mechanism to provide information
about the self tests as they run.
This is useful for debugging if a self test is failing.
The callback also allows forcing any self test to fail, in order to check that
it operates correctly on failure.
Note that all self tests run even if a self test failure occurs.
.PP
The FIPS module passes the following type(s) to \fBOSSL_SELF_TEST_onbegin()\fR.
.IP """Module_Integrity"" (\fBOSSL_SELF_TEST_TYPE_MODULE_INTEGRITY\fR)" 4
.IX Item """Module_Integrity"" (OSSL_SELF_TEST_TYPE_MODULE_INTEGRITY)"
Uses HMAC SHA256 on the module file to validate that the module has not been
modified. The integrity value is compared to a value written to a configuration
file during installation.
.IP """Install_Integrity"" (\fBOSSL_SELF_TEST_TYPE_INSTALL_INTEGRITY\fR)" 4
.IX Item """Install_Integrity"" (OSSL_SELF_TEST_TYPE_INSTALL_INTEGRITY)"
Uses HMAC SHA256 on a fixed string to validate that the installation process
has already been performed and the self test KATS have already been tested,
The integrity value is compared to a value written to a configuration
file after successfully running the self tests during installation.
.IP """KAT_Cipher"" (\fBOSSL_SELF_TEST_TYPE_KAT_CIPHER\fR)" 4
.IX Item """KAT_Cipher"" (OSSL_SELF_TEST_TYPE_KAT_CIPHER)"
Known answer test for a symmetric cipher.
.IP """KAT_AsymmetricCipher"" (\fBOSSL_SELF_TEST_TYPE_KAT_ASYM_CIPHER\fR)" 4
.IX Item """KAT_AsymmetricCipher"" (OSSL_SELF_TEST_TYPE_KAT_ASYM_CIPHER)"
Known answer test for a asymmetric cipher.
.IP """KAT_Digest"" (\fBOSSL_SELF_TEST_TYPE_KAT_DIGEST\fR)" 4
.IX Item """KAT_Digest"" (OSSL_SELF_TEST_TYPE_KAT_DIGEST)"
Known answer test for a digest.
.IP """KAT_Signature"" (\fBOSSL_SELF_TEST_TYPE_KAT_SIGNATURE\fR)" 4
.IX Item """KAT_Signature"" (OSSL_SELF_TEST_TYPE_KAT_SIGNATURE)"
Known answer test for a signature.
.IP """PCT_Signature"" (\fBOSSL_SELF_TEST_TYPE_PCT_SIGNATURE\fR)" 4
.IX Item """PCT_Signature"" (OSSL_SELF_TEST_TYPE_PCT_SIGNATURE)"
Pairwise Consistency check for a signature.
.IP """KAT_KDF"" (\fBOSSL_SELF_TEST_TYPE_KAT_KDF\fR)" 4
.IX Item """KAT_KDF"" (OSSL_SELF_TEST_TYPE_KAT_KDF)"
Known answer test for a key derivation function.
.IP """KAT_KA"" (\fBOSSL_SELF_TEST_TYPE_KAT_KA\fR)" 4
.IX Item """KAT_KA"" (OSSL_SELF_TEST_TYPE_KAT_KA)"
Known answer test for key agreement.
.IP """DRBG"" (\fBOSSL_SELF_TEST_TYPE_DRBG\fR)" 4
.IX Item """DRBG"" (OSSL_SELF_TEST_TYPE_DRBG)"
Known answer test for a Deterministic Random Bit Generator.
.IP """Conditional_PCT"" (\fBOSSL_SELF_TEST_TYPE_PCT\fR)" 4
.IX Item """Conditional_PCT"" (OSSL_SELF_TEST_TYPE_PCT)"
Conditional test that is run during the generation of key pairs.
.IP """Continuous_RNG_Test"" (\fBOSSL_SELF_TEST_TYPE_CRNG\fR)" 4
.IX Item """Continuous_RNG_Test"" (OSSL_SELF_TEST_TYPE_CRNG)"
Continuous random number generator test.
.PP
The "Module_Integrity" self test is always run at startup.
The "Install_Integrity" self test is used to check if the self tests have
already been run at installation time. If they have already run then the
self tests are not run on subsequent startups.
All other self test categories are run once at installation time, except for the
"Pairwise_Consistency_Test".
.PP
There is only one instance of the "Module_Integrity" and "Install_Integrity"
self tests. All other self tests may have multiple instances.
.PP
The FIPS module passes the following descriptions(s) to \fBOSSL_SELF_TEST_onbegin()\fR.
.IP """HMAC"" (\fBOSSL_SELF_TEST_DESC_INTEGRITY_HMAC\fR)" 4
.IX Item """HMAC"" (OSSL_SELF_TEST_DESC_INTEGRITY_HMAC)"
"Module_Integrity" and "Install_Integrity" use this.
.IP """RSA"" (\fBOSSL_SELF_TEST_DESC_PCT_RSA_PKCS1\fR)" 4
.IX Item """RSA"" (OSSL_SELF_TEST_DESC_PCT_RSA_PKCS1)"
.PD 0
.IP """ECDSA"" (\fBOSSL_SELF_TEST_DESC_PCT_ECDSA\fR)" 4
.IX Item """ECDSA"" (OSSL_SELF_TEST_DESC_PCT_ECDSA)"
.IP """DSA"" (\fBOSSL_SELF_TEST_DESC_PCT_DSA\fR)" 4
.IX Item """DSA"" (OSSL_SELF_TEST_DESC_PCT_DSA)"
.PD
Key generation tests used with the "Pairwise_Consistency_Test" type.
.IP """RSA_Encrypt"" (\fBOSSL_SELF_TEST_DESC_ASYM_RSA_ENC\fR)" 4
.IX Item """RSA_Encrypt"" (OSSL_SELF_TEST_DESC_ASYM_RSA_ENC)"
.PD 0
.IP """RSA_Decrypt"" (\fBOSSL_SELF_TEST_DESC_ASYM_RSA_DEC\fR)" 4
.IX Item """RSA_Decrypt"" (OSSL_SELF_TEST_DESC_ASYM_RSA_DEC)"
.PD
"KAT_AsymmetricCipher" uses this to indicate an encrypt or decrypt KAT.
.IP """AES_GCM"" (\fBOSSL_SELF_TEST_DESC_CIPHER_AES_GCM\fR)" 4
.IX Item """AES_GCM"" (OSSL_SELF_TEST_DESC_CIPHER_AES_GCM)"
.PD 0
.IP """AES_ECB_Decrypt"" (\fBOSSL_SELF_TEST_DESC_CIPHER_AES_ECB\fR)" 4
.IX Item """AES_ECB_Decrypt"" (OSSL_SELF_TEST_DESC_CIPHER_AES_ECB)"
.IP """TDES"" (\fBOSSL_SELF_TEST_DESC_CIPHER_TDES\fR)" 4
.IX Item """TDES"" (OSSL_SELF_TEST_DESC_CIPHER_TDES)"
.PD
Symmetric cipher tests used with the "KAT_Cipher" type.
.IP """SHA1"" (\fBOSSL_SELF_TEST_DESC_MD_SHA1\fR)" 4
.IX Item """SHA1"" (OSSL_SELF_TEST_DESC_MD_SHA1)"
.PD 0
.IP """SHA2"" (\fBOSSL_SELF_TEST_DESC_MD_SHA2\fR)" 4
.IX Item """SHA2"" (OSSL_SELF_TEST_DESC_MD_SHA2)"
.IP """SHA3"" (\fBOSSL_SELF_TEST_DESC_MD_SHA3\fR)" 4
.IX Item """SHA3"" (OSSL_SELF_TEST_DESC_MD_SHA3)"
.PD
Digest tests used with the "KAT_Digest" type.
.IP """DSA"" (\fBOSSL_SELF_TEST_DESC_SIGN_DSA\fR)" 4
.IX Item """DSA"" (OSSL_SELF_TEST_DESC_SIGN_DSA)"
.PD 0
.IP """RSA"" (\fBOSSL_SELF_TEST_DESC_SIGN_RSA\fR)" 4
.IX Item """RSA"" (OSSL_SELF_TEST_DESC_SIGN_RSA)"
.IP """ECDSA"" (\fBOSSL_SELF_TEST_DESC_SIGN_ECDSA\fR)" 4
.IX Item """ECDSA"" (OSSL_SELF_TEST_DESC_SIGN_ECDSA)"
.PD
Signature tests used with the "KAT_Signature" type.
.IP """ECDH"" (\fBOSSL_SELF_TEST_DESC_KA_ECDH\fR)" 4
.IX Item """ECDH"" (OSSL_SELF_TEST_DESC_KA_ECDH)"
.PD 0
.IP """DH"" (\fBOSSL_SELF_TEST_DESC_KA_DH\fR)" 4
.IX Item """DH"" (OSSL_SELF_TEST_DESC_KA_DH)"
.PD
Key agreement tests used with the "KAT_KA" type.
.IP """HKDF"" (\fBOSSL_SELF_TEST_DESC_KDF_HKDF\fR)" 4
.IX Item """HKDF"" (OSSL_SELF_TEST_DESC_KDF_HKDF)"
.PD 0
.IP """TLS13_KDF_EXTRACT"" (\fBOSSL_SELF_TEST_DESC_KDF_TLS13_EXTRACT\fR)" 4
.IX Item """TLS13_KDF_EXTRACT"" (OSSL_SELF_TEST_DESC_KDF_TLS13_EXTRACT)"
.IP """TLS13_KDF_EXPAND"" (\fBOSSL_SELF_TEST_DESC_KDF_TLS13_EXPAND\fR)" 4
.IX Item """TLS13_KDF_EXPAND"" (OSSL_SELF_TEST_DESC_KDF_TLS13_EXPAND)"
.IP """SSKDF"" (\fBOSSL_SELF_TEST_DESC_KDF_SSKDF\fR)" 4
.IX Item """SSKDF"" (OSSL_SELF_TEST_DESC_KDF_SSKDF)"
.IP """X963KDF"" (\fBOSSL_SELF_TEST_DESC_KDF_X963KDF\fR)" 4
.IX Item """X963KDF"" (OSSL_SELF_TEST_DESC_KDF_X963KDF)"
.IP """X942KDF"" (\fBOSSL_SELF_TEST_DESC_KDF_X942KDF\fR)" 4
.IX Item """X942KDF"" (OSSL_SELF_TEST_DESC_KDF_X942KDF)"
.IP """PBKDF2"" (\fBOSSL_SELF_TEST_DESC_KDF_PBKDF2\fR)" 4
.IX Item """PBKDF2"" (OSSL_SELF_TEST_DESC_KDF_PBKDF2)"
.IP """SSHKDF"" (\fBOSSL_SELF_TEST_DESC_KDF_SSHKDF\fR)" 4
.IX Item """SSHKDF"" (OSSL_SELF_TEST_DESC_KDF_SSHKDF)"
.IP """TLS12_PRF"" (\fBOSSL_SELF_TEST_DESC_KDF_TLS12_PRF\fR)" 4
.IX Item """TLS12_PRF"" (OSSL_SELF_TEST_DESC_KDF_TLS12_PRF)"
.IP """KBKDF"" (\fBOSSL_SELF_TEST_DESC_KDF_KBKDF\fR)" 4
.IX Item """KBKDF"" (OSSL_SELF_TEST_DESC_KDF_KBKDF)"
.PD
Key Derivation Function tests used with the "KAT_KDF" type.
.IP """CTR"" (\fBOSSL_SELF_TEST_DESC_DRBG_CTR\fR)" 4
.IX Item """CTR"" (OSSL_SELF_TEST_DESC_DRBG_CTR)"
.PD 0
.IP """HASH"" (\fBOSSL_SELF_TEST_DESC_DRBG_HASH\fR)" 4
.IX Item """HASH"" (OSSL_SELF_TEST_DESC_DRBG_HASH)"
.IP """HMAC"" (\fBOSSL_SELF_TEST_DESC_DRBG_HMAC\fR)" 4
.IX Item """HMAC"" (OSSL_SELF_TEST_DESC_DRBG_HMAC)"
.PD
DRBG tests used with the "DRBG" type.
.Sp
= item "RNG" (\fBOSSL_SELF_TEST_DESC_RNG\fR)
.Sp
"Continuous_RNG_Test" uses this.
.SH EXAMPLES
.IX Header "EXAMPLES"
A simple self test callback is shown below for illustrative purposes.
.PP
.Vb 1
\&  #include <openssl/self_test.h>
\&
\&  static OSSL_CALLBACK self_test_cb;
\&
\&  static int self_test_cb(const OSSL_PARAM params[], void *arg)
\&  {
\&    int ret = 0;
\&    const OSSL_PARAM *p = NULL;
\&    const char *phase = NULL, *type = NULL, *desc = NULL;
\&
\&    p = OSSL_PARAM_locate_const(params, OSSL_PROV_PARAM_SELF_TEST_PHASE);
\&    if (p == NULL || p\->data_type != OSSL_PARAM_UTF8_STRING)
\&        goto err;
\&    phase = (const char *)p\->data;
\&
\&    p = OSSL_PARAM_locate_const(params, OSSL_PROV_PARAM_SELF_TEST_DESC);
\&    if (p == NULL || p\->data_type != OSSL_PARAM_UTF8_STRING)
\&        goto err;
\&    desc = (const char *)p\->data;
\&
\&    p = OSSL_PARAM_locate_const(params, OSSL_PROV_PARAM_SELF_TEST_TYPE);
\&    if (p == NULL || p\->data_type != OSSL_PARAM_UTF8_STRING)
\&        goto err;
\&    type = (const char *)p\->data;
\&
\&    /* Do some logging */
\&    if (strcmp(phase, OSSL_SELF_TEST_PHASE_START) == 0)
\&        BIO_printf(bio_out, "%s : (%s) : ", desc, type);
\&    if (strcmp(phase, OSSL_SELF_TEST_PHASE_PASS) == 0
\&            || strcmp(phase, OSSL_SELF_TEST_PHASE_FAIL) == 0)
\&        BIO_printf(bio_out, "%s\en", phase);
\&
\&    /* Corrupt the SHA1 self test during the \*(Aqcorrupt\*(Aq phase by returning 0 */
\&    if (strcmp(phase, OSSL_SELF_TEST_PHASE_CORRUPT) == 0
\&            && strcmp(desc, OSSL_SELF_TEST_DESC_MD_SHA1) == 0) {
\&        BIO_printf(bio_out, "%s %s", phase, desc);
\&        return 0;
\&    }
\&    ret = 1;
\&  err:
\&    return ret;
\&  }
.Ve
.SH NOTES
.IX Header "NOTES"
Some released versions of OpenSSL do not include a validated
FIPS provider.  To determine which versions have undergone
the validation process, please refer to the
OpenSSL Downloads page <https://www.openssl.org/source/>.  If you
require FIPS-approved functionality, it is essential to build your FIPS
provider using one of the validated versions listed there.  Normally,
it is possible to utilize a FIPS provider constructed from one of the
validated versions alongside \fIlibcrypto\fR and \fIlibssl\fR compiled from any
release within the same major release series.  This flexibility enables
you to address bug fixes and CVEs that fall outside the FIPS boundary.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\-fipsinstall\fR\|(1),
\&\fBfips_config\fR\|(5),
\&\fBOSSL_SELF_TEST_set_callback\fR\|(3),
\&\fBOSSL_SELF_TEST_new\fR\|(3),
\&\fBOSSL_PARAM\fR\|(3),
\&\fBopenssl\-core.h\fR\|(7),
\&\fBopenssl\-core_dispatch.h\fR\|(7),
\&\fBprovider\fR\|(7),
<https://www.openssl.org/source/>
.SH HISTORY
.IX Header "HISTORY"
This functionality was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
