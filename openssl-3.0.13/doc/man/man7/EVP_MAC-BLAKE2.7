.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_MAC-BLAKE2 7ossl"
.TH EVP_MAC-BLAKE2 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_MAC\-BLAKE2, EVP_MAC\-BLAKE2BMAC, EVP_MAC\-BLAKE2SMAC
\&\- The BLAKE2 EVP_MAC implementations
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Support for computing BLAKE2 MACs through the \fBEVP_MAC\fR API.
.SS Identity
.IX Subsection "Identity"
These implementations are identified with one of these names and
properties, to be used with \fBEVP_MAC_fetch()\fR:
.IP """BLAKE2BMAC"", ""provider=default""" 4
.IX Item """BLAKE2BMAC"", ""provider=default"""
.PD 0
.IP """BLAKE2SMAC"", ""provider=default""" 4
.IX Item """BLAKE2SMAC"", ""provider=default"""
.PD
.SS "Supported parameters"
.IX Subsection "Supported parameters"
The general description of these parameters can be found in
"PARAMETERS" in \fBEVP_MAC\fR\|(3).
.PP
All these parameters (except for "block-size") can be set with
\&\fBEVP_MAC_CTX_set_params()\fR.
Furthermore, the "size" parameter can be retrieved with
\&\fBEVP_MAC_CTX_get_params()\fR, or with \fBEVP_MAC_CTX_get_mac_size()\fR.
The length of the "size" parameter should not exceed that of a \fBsize_t\fR.
Likewise, the "block-size" parameter can be retrieved with
\&\fBEVP_MAC_CTX_get_params()\fR, or with \fBEVP_MAC_CTX_get_block_size()\fR.
.IP """key"" (\fBOSSL_MAC_PARAM_KEY\fR) <octet string>" 4
.IX Item """key"" (OSSL_MAC_PARAM_KEY) <octet string>"
Sets the MAC key.
It may be at most 64 bytes for BLAKE2BMAC or 32 for BLAKE2SMAC and at
least 1 byte in both cases.
Setting this parameter is identical to passing a \fIkey\fR to \fBEVP_MAC_init\fR\|(3).
.IP """custom"" (\fBOSSL_MAC_PARAM_CUSTOM\fR) <octet string>" 4
.IX Item """custom"" (OSSL_MAC_PARAM_CUSTOM) <octet string>"
Sets the customization/personalization string.
It is an optional value of at most 16 bytes for BLAKE2BMAC or 8 for
BLAKE2SMAC, and is empty by default.
.IP """salt"" (\fBOSSL_MAC_PARAM_SALT\fR) <octet string>" 4
.IX Item """salt"" (OSSL_MAC_PARAM_SALT) <octet string>"
Sets the salt.
It is an optional value of at most 16 bytes for BLAKE2BMAC or 8 for
BLAKE2SMAC, and is empty by default.
.IP """size"" (\fBOSSL_MAC_PARAM_SIZE\fR) <unsigned integer>" 4
.IX Item """size"" (OSSL_MAC_PARAM_SIZE) <unsigned integer>"
Sets the MAC size.
It can be any number between 1 and 32 for EVP_MAC_BLAKE2S or between 1
and 64 for EVP_MAC_BLAKE2B.
It is 32 and 64 respectively by default.
.IP """block-size"" (\fBOSSL_MAC_PARAM_BLOCK_SIZE\fR) <unsigned integer>" 4
.IX Item """block-size"" (OSSL_MAC_PARAM_BLOCK_SIZE) <unsigned integer>"
Gets the MAC block size.
It is 64 for EVP_MAC_BLAKE2S and 128 for EVP_MAC_BLAKE2B.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_MAC_CTX_get_params\fR\|(3), \fBEVP_MAC_CTX_set_params\fR\|(3),
"PARAMETERS" in \fBEVP_MAC\fR\|(3), \fBOSSL_PARAM\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The macros and functions described here were added to OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
