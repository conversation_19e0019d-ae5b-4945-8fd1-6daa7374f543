.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_SIGNATURE-HMAC 7ossl"
.TH EVP_SIGNATURE-HMAC 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_SIGNATURE\-HMAC, EVP_SIGNATURE\-Siphash, EVP_SIGNATURE\-Poly1305,
EVP_SIGNATURE\-CMAC
\&\- The legacy EVP_PKEY MAC signature implementations
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The algorithms described here have legacy support for creating MACs using
\&\fBEVP_DigestSignInit\fR\|(3) and related functions. This is not the preferred way of
creating MACs. Instead you should use the newer \fBEVP_MAC_init\fR\|(3) functions.
This mechanism is provided for backwards compatibility with older versions of
OpenSSL.
.PP
The same signature parameters can be set using \fBEVP_PKEY_CTX_set_params()\fR as can
be set via \fBEVP_MAC_CTX_set_params()\fR for the underlying EVP_MAC. See
\&\fBEVP_MAC\-HMAC\fR\|(7), \fBEVP_MAC\-Siphash\fR\|(7), \fBEVP_MAC\-Poly1305\fR\|(7) and
\&\fBEVP_MAC\-CMAC\fR\|(7) for details.
.PP
.Vb 3
\& See L<EVP_PKEY\-HMAC(7)>, L<EVP_PKEY\-Siphash(7)>, L<EVP_PKEY\-Poly1305(7)> or
\& L<EVP_PKEY\-CMAC(7)> for details about parameters that are supported during the
\& creation of an EVP_PKEY.
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_MAC_init\fR\|(3),
\&\fBEVP_DigestSignInit\fR\|(3),
\&\fBEVP_PKEY\-HMAC\fR\|(7),
\&\fBEVP_PKEY\-Siphash\fR\|(7),
\&\fBEVP_PKEY\-Poly1305\fR\|(7),
\&\fBEVP_PKEY\-CMAC\fR\|(7),
\&\fBEVP_MAC\-HMAC\fR\|(7),
\&\fBEVP_MAC\-Siphash\fR\|(7),
\&\fBEVP_MAC\-Poly1305\fR\|(7),
\&\fBEVP_MAC\-CMAC\fR\|(7),
\&\fBprovider\-signature\fR\|(7),
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
