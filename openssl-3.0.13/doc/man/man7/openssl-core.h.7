.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-CORE.H 7ossl"
.TH OPENSSL-CORE.H 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl/core.h \- OpenSSL Core types
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/core.h>
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fI<openssl/core.h>\fR header defines a number of public types that
are used to communicate between the OpenSSL libraries and
implementation providers.
These types are designed to minimise the need for intimate knowledge
of internal structures between the OpenSSL libraries and the providers.
.PP
The types are:
.IP \fBOSSL_DISPATCH\fR\|(3) 4
.IX Item "OSSL_DISPATCH"
.PD 0
.IP \fBOSSL_ITEM\fR\|(3) 4
.IX Item "OSSL_ITEM"
.IP \fBOSSL_ALGORITHM\fR\|(3) 4
.IX Item "OSSL_ALGORITHM"
.IP \fBOSSL_PARAM\fR\|(3) 4
.IX Item "OSSL_PARAM"
.IP \fBOSSL_CALLBACK\fR\|(3) 4
.IX Item "OSSL_CALLBACK"
.IP \fBOSSL_PASSPHRASE_CALLBACK\fR\|(3) 4
.IX Item "OSSL_PASSPHRASE_CALLBACK"
.PD
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\-core_dispatch.h\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The types described here were added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
