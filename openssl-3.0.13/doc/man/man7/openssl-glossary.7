.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "OPENSSL-GLOSSARY 7ossl"
.TH OPENSSL-GLOSSARY 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
openssl\-glossary \- An OpenSSL Glossary
.SH DESCRIPTION
.IX Header "DESCRIPTION"
.IP Algorithm 4
.IX Item "Algorithm"
Cryptographic primitives such as the SHA256 digest, or AES encryption are
referred to in OpenSSL as "algorithms". There can be more than one
implementation for any given algorithm available for use.
.Sp
\&\fBcrypto\fR\|(7)
.IP "ASN.1, ASN1" 4
.IX Item "ASN.1, ASN1"
ASN.1 ("Abstract Syntax Notation One") is a notation for describing abstract
types and values.  It is defined in the ITU-T documents X.680 to X.683:
.Sp
<https://www.itu.int/rec/T\-REC\-X.680>,
<https://www.itu.int/rec/T\-REC\-X.681>,
<https://www.itu.int/rec/T\-REC\-X.682>,
<https://www.itu.int/rec/T\-REC\-X.683>
.IP "Base Provider" 4
.IX Item "Base Provider"
An OpenSSL Provider that contains encoders and decoders for OpenSSL keys.  All
the algorithm implementations in the Base Provider are also available in the
Default Provider.
.Sp
\&\fBOSSL_PROVIDER\-base\fR\|(7)
.IP Decoder 4
.IX Item "Decoder"
A decoder is a type of algorithm used for decoding keys and parameters from some
external format such as PEM or DER.
.Sp
\&\fBOSSL_DECODER_CTX_new_for_pkey\fR\|(3)
.IP "Default Provider" 4
.IX Item "Default Provider"
An OpenSSL Provider that contains the most common OpenSSL algorithm
implementations. It is loaded by default if no other provider is available. All
the algorithm implementations in the Base Provider are also available in the
Default Provider.
.Sp
\&\fBOSSL_PROVIDER\-default\fR\|(7)
.IP "DER (""Distinguished Encoding Rules"")" 4
.IX Item "DER (""Distinguished Encoding Rules"")"
DER is a binary encoding of data, structured according to an ASN.1
specification.  This is a common encoding used for cryptographic objects
such as private and public keys, certificates, CRLs, ...
.Sp
It is defined in ITU-T document X.690:
.Sp
<https://www.itu.int/rec/T\-REC\-X.690>
.IP Encoder 4
.IX Item "Encoder"
An encoder is a type of algorithm used for encoding keys and parameters to some
external format such as PEM or DER.
.Sp
\&\fBOSSL_ENCODER_CTX_new_for_pkey\fR\|(3)
.IP "Explicit Fetching" 4
.IX Item "Explicit Fetching"
Explicit Fetching is a type of Fetching (see Fetching). Explicit Fetching is
where a function call is made to obtain an algorithm object representing an
implementation such as \fBEVP_MD_fetch\fR\|(3) or \fBEVP_CIPHER_fetch\fR\|(3)
.IP Fetching 4
.IX Item "Fetching"
Fetching is the process of looking through the available algorithm
implementations, applying selection criteria (via a property query string), and
finally choosing the implementation that will be used.
.Sp
Also see Explicit Fetching and Implicit Fetching.
.Sp
\&\fBcrypto\fR\|(7)
.IP "FIPS Provider" 4
.IX Item "FIPS Provider"
An OpenSSL Provider that contains OpenSSL algorithm implementations that have
been validated according to the FIPS 140\-2 standard.
.Sp
\&\fBOSSL_PROVIDER\-FIPS\fR\|(7)
.IP "Implicit Fetching" 4
.IX Item "Implicit Fetching"
Implicit Fetching is a type of Fetching (see Fetching). Implicit Fetching is
where an algorithm object with no associated implementation is used such as the
return value from \fBEVP_sha256\fR\|(3) or \fBEVP_aes_128_cbc\fR\|(3). With implicit
fetching an implementation is fetched automatically using default selection
criteria the first time the algorithm is used.
.IP "Legacy Provider" 4
.IX Item "Legacy Provider"
An OpenSSL Provider that contains algorithm implementations that are considered
insecure or are no longer in common use.
.Sp
\&\fBOSSL_PROVIDER\-legacy\fR\|(7)
.IP "Library Context" 4
.IX Item "Library Context"
A Library Context in OpenSSL is represented by the type \fBOSSL_LIB_CTX\fR. It can
be thought of as a scope within which configuration options apply. If an
application does not explicitly create a library context then the "default"
one is used. Many OpenSSL functions can take a library context as an argument.
A NULL value can always be passed to indicate the default library context.
.Sp
\&\fBOSSL_LIB_CTX\fR\|(3)
.IP MSBLOB 4
.IX Item "MSBLOB"
MSBLOB is a Microsoft specific binary format for RSA and DSA keys, both
private and public.  This form is never passphrase protected.
.IP "Null Provider" 4
.IX Item "Null Provider"
An OpenSSL Provider that contains no algorithm implementations. This can be
useful to prevent the default provider from being automatically loaded in a
library context.
.Sp
\&\fBOSSL_PROVIDER\-null\fR\|(7)
.IP Operation 4
.IX Item "Operation"
An operation is a group of OpenSSL functions with a common purpose such as
encryption, or digesting.
.Sp
\&\fBcrypto\fR\|(7)
.IP "PEM (""Privacy Enhanced Message"")" 4
.IX Item "PEM (""Privacy Enhanced Message"")"
PEM is a format used for encoding of binary content into a mail and ASCII
friendly form.  The content is a series of base64\-encoded lines, surrounded
by begin/end markers each on their own line.  For example:
.Sp
.Vb 4
\& \-\-\-\-\-BEGIN PRIVATE KEY\-\-\-\-\-
\& MIICdg....
\& ... bhTQ==
\& \-\-\-\-\-END PRIVATE KEY\-\-\-\-\-
.Ve
.Sp
Optional header line(s) may appear after the begin line, and their existence
depends on the type of object being written or read.
.Sp
For all OpenSSL uses, the binary content is expected to be a DER encoded
structure.
.Sp
This is defined in IETF RFC 1421:
.Sp
<https://tools.ietf.org/html/rfc1421>
.IP PKCS#8 4
.IX Item "PKCS#8"
PKCS#8 is a specification of ASN.1 structures that OpenSSL uses for storing
or transmitting any private key in a key type agnostic manner.
There are two structures worth noting for OpenSSL use, one that contains the
key data in unencrypted form (known as "PrivateKeyInfo") and an encrypted
wrapper structure (known as "EncryptedPrivateKeyInfo").
.Sp
This is specified in RFC 5208:
.Sp
<https://tools.ietf.org/html/rfc5208>
.IP Property 4
.IX Item "Property"
A property is a way of classifying and selecting algorithm implementations.
A property is a key/value pair expressed as a string. For example all algorithm
implementations in the default provider have the property "provider=default".
An algorithm implementation can have multiple properties defined against it.
.Sp
Also see Property Query String.
.Sp
\&\fBproperty\fR\|(7)
.IP "Property Query String" 4
.IX Item "Property Query String"
A property query string is a string containing a sequence of properties that
can be used to select an algorithm implementation. For example the query string
"provider=example,foo=bar" will select algorithms from the "example" provider
that have a "foo" property defined for them with a value of "bar".
.Sp
Property Query Strings are used during fetching. See Fetching.
.Sp
\&\fBproperty\fR\|(7)
.IP Provider 4
.IX Item "Provider"
A provider in OpenSSL is a component that groups together algorithm
implementations. Providers can come from OpenSSL itself or from third parties.
.Sp
\&\fBprovider\fR\|(7)
.IP PVK 4
.IX Item "PVK"
PVK is a Microsoft specific binary format for RSA and DSA private keys.
This form may be passphrase protected.
.IP SubjectPublicKeyInfo 4
.IX Item "SubjectPublicKeyInfo"
SubjectPublicKeyInfo is an ASN.1 structure that OpenSSL uses for storing and
transmitting any public key in a key type agnostic manner.
.Sp
This is specified as part of the specification for certificates, RFC 5280:
.Sp
<https://tools.ietf.org/html/rfc5280>
.SH HISTORY
.IX Header "HISTORY"
This glossary was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
