.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PROVIDER-MAC 7ossl"
.TH PROVIDER-MAC 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
provider\-mac \- The mac library <\-> provider functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 2
\& #include <openssl/core_dispatch.h>
\& #include <openssl/core_names.h>
\&
\& /*
\&  * None of these are actual functions, but are displayed like this for
\&  * the function signatures for functions that are offered as function
\&  * pointers in OSSL_DISPATCH arrays.
\&  */
\&
\& /* Context management */
\& void *OSSL_FUNC_mac_newctx(void *provctx);
\& void OSSL_FUNC_mac_freectx(void *mctx);
\& void *OSSL_FUNC_mac_dupctx(void *src);
\&
\& /* Encryption/decryption */
\& int OSSL_FUNC_mac_init(void *mctx, unsigned char *key, size_t keylen,
\&                        const OSSL_PARAM params[]);
\& int OSSL_FUNC_mac_update(void *mctx, const unsigned char *in, size_t inl);
\& int OSSL_FUNC_mac_final(void *mctx, unsigned char *out, size_t *outl, size_t outsize);
\&
\& /* MAC parameter descriptors */
\& const OSSL_PARAM *OSSL_FUNC_mac_gettable_params(void *provctx);
\& const OSSL_PARAM *OSSL_FUNC_mac_gettable_ctx_params(void *mctx, void *provctx);
\& const OSSL_PARAM *OSSL_FUNC_mac_settable_ctx_params(void *mctx, void *provctx);
\&
\& /* MAC parameters */
\& int OSSL_FUNC_mac_get_params(OSSL_PARAM params[]);
\& int OSSL_FUNC_mac_get_ctx_params(void *mctx, OSSL_PARAM params[]);
\& int OSSL_FUNC_mac_set_ctx_params(void *mctx, const OSSL_PARAM params[]);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This documentation is primarily aimed at provider authors. See \fBprovider\fR\|(7)
for further information.
.PP
The MAC operation enables providers to implement mac algorithms and make
them available to applications via the API functions \fBEVP_MAC_init\fR\|(3),
\&\fBEVP_MAC_update\fR\|(3) and \fBEVP_MAC_final\fR\|(3).
.PP
All "functions" mentioned here are passed as function pointers between
\&\fIlibcrypto\fR and the provider in \fBOSSL_DISPATCH\fR\|(3) arrays via
\&\fBOSSL_ALGORITHM\fR\|(3) arrays that are returned by the provider's
\&\fBprovider_query_operation()\fR function
(see "Provider Functions" in \fBprovider\-base\fR\|(7)).
.PP
All these "functions" have a corresponding function type definition
named \fBOSSL_FUNC_{name}_fn\fR, and a helper function to retrieve the
function pointer from an \fBOSSL_DISPATCH\fR\|(3) element named
\&\fBOSSL_FUNC_{name}\fR.
For example, the "function" \fBOSSL_FUNC_mac_newctx()\fR has these:
.PP
.Vb 3
\& typedef void *(OSSL_FUNC_mac_newctx_fn)(void *provctx);
\& static ossl_inline OSSL_FUNC_mac_newctx_fn
\&     OSSL_FUNC_mac_newctx(const OSSL_DISPATCH *opf);
.Ve
.PP
\&\fBOSSL_DISPATCH\fR\|(3) arrays are indexed by numbers that are provided as
macros in \fBopenssl\-core_dispatch.h\fR\|(7), as follows:
.PP
.Vb 3
\& OSSL_FUNC_mac_newctx               OSSL_FUNC_MAC_NEWCTX
\& OSSL_FUNC_mac_freectx              OSSL_FUNC_MAC_FREECTX
\& OSSL_FUNC_mac_dupctx               OSSL_FUNC_MAC_DUPCTX
\&
\& OSSL_FUNC_mac_init                 OSSL_FUNC_MAC_INIT
\& OSSL_FUNC_mac_update               OSSL_FUNC_MAC_UPDATE
\& OSSL_FUNC_mac_final                OSSL_FUNC_MAC_FINAL
\&
\& OSSL_FUNC_mac_get_params           OSSL_FUNC_MAC_GET_PARAMS
\& OSSL_FUNC_mac_get_ctx_params       OSSL_FUNC_MAC_GET_CTX_PARAMS
\& OSSL_FUNC_mac_set_ctx_params       OSSL_FUNC_MAC_SET_CTX_PARAMS
\&
\& OSSL_FUNC_mac_gettable_params      OSSL_FUNC_MAC_GETTABLE_PARAMS
\& OSSL_FUNC_mac_gettable_ctx_params  OSSL_FUNC_MAC_GETTABLE_CTX_PARAMS
\& OSSL_FUNC_mac_settable_ctx_params  OSSL_FUNC_MAC_SETTABLE_CTX_PARAMS
.Ve
.PP
A mac algorithm implementation may not implement all of these functions.
In order to be a consistent set of functions, at least the following functions
must be implemented: \fBOSSL_FUNC_mac_newctx()\fR, \fBOSSL_FUNC_mac_freectx()\fR, \fBOSSL_FUNC_mac_init()\fR,
\&\fBOSSL_FUNC_mac_update()\fR, \fBOSSL_FUNC_mac_final()\fR.
All other functions are optional.
.SS "Context Management Functions"
.IX Subsection "Context Management Functions"
\&\fBOSSL_FUNC_mac_newctx()\fR should create and return a pointer to a provider side
structure for holding context information during a mac operation.
A pointer to this context will be passed back in a number of the other mac
operation function calls.
The parameter \fIprovctx\fR is the provider context generated during provider
initialisation (see \fBprovider\fR\|(7)).
.PP
\&\fBOSSL_FUNC_mac_freectx()\fR is passed a pointer to the provider side mac context in
the \fImctx\fR parameter.
If it receives NULL as \fImctx\fR value, it should not do anything other than
return.
This function should free any resources associated with that context.
.PP
\&\fBOSSL_FUNC_mac_dupctx()\fR should duplicate the provider side mac context in the
\&\fImctx\fR parameter and return the duplicate copy.
.SS "Encryption/Decryption Functions"
.IX Subsection "Encryption/Decryption Functions"
\&\fBOSSL_FUNC_mac_init()\fR initialises a mac operation given a newly created provider
side mac context in the \fImctx\fR parameter.  The \fIparams\fR are set before setting
the MAC \fIkey\fR of \fIkeylen\fR bytes.
.PP
\&\fBOSSL_FUNC_mac_update()\fR is called to supply data for MAC computation of a previously
initialised mac operation.
The \fImctx\fR parameter contains a pointer to a previously initialised provider
side context.
\&\fBOSSL_FUNC_mac_update()\fR may be called multiple times for a single mac operation.
.PP
\&\fBOSSL_FUNC_mac_final()\fR completes the MAC computation started through previous
\&\fBOSSL_FUNC_mac_init()\fR and \fBOSSL_FUNC_mac_update()\fR calls.
The \fImctx\fR parameter contains a pointer to the provider side context.
The resulting MAC should be written to \fIout\fR and the amount of data written
to \fI*outl\fR, which should not exceed \fIoutsize\fR bytes.
The same expectations apply to \fIoutsize\fR as documented for
\&\fBEVP_MAC_final\fR\|(3).
.SS "Mac Parameters"
.IX Subsection "Mac Parameters"
See \fBOSSL_PARAM\fR\|(3) for further details on the parameters structure used by
these functions.
.PP
\&\fBOSSL_FUNC_mac_get_params()\fR gets details of parameter values associated with the
provider algorithm and stores them in \fIparams\fR.
.PP
\&\fBOSSL_FUNC_mac_set_ctx_params()\fR sets mac parameters associated with the given
provider side mac context \fImctx\fR to \fIparams\fR.
Any parameter settings are additional to any that were previously set.
Passing NULL for \fIparams\fR should return true.
.PP
\&\fBOSSL_FUNC_mac_get_ctx_params()\fR gets details of currently set parameter values
associated with the given provider side mac context \fImctx\fR and stores them
in \fIparams\fR.
Passing NULL for \fIparams\fR should return true.
.PP
\&\fBOSSL_FUNC_mac_gettable_params()\fR, \fBOSSL_FUNC_mac_gettable_ctx_params()\fR,
and \fBOSSL_FUNC_mac_settable_ctx_params()\fR all return constant \fBOSSL_PARAM\fR\|(3)
arrays as descriptors of the parameters that \fBOSSL_FUNC_mac_get_params()\fR,
\&\fBOSSL_FUNC_mac_get_ctx_params()\fR, and \fBOSSL_FUNC_mac_set_ctx_params()\fR
can handle, respectively.  \fBOSSL_FUNC_mac_gettable_ctx_params()\fR and
\&\fBOSSL_FUNC_mac_settable_ctx_params()\fR will return the parameters associated
with the provider side context \fImctx\fR in its current state if it is
not NULL.  Otherwise, they return the parameters associated with the
provider side algorithm \fIprovctx\fR.
.PP
All MAC implementations are expected to handle the following parameters:
.IP "with \fBOSSL_FUNC_set_ctx_params()\fR:" 4
.IX Item "with OSSL_FUNC_set_ctx_params():"
.RS 4
.PD 0
.IP """key"" (\fBOSSL_MAC_PARAM_KEY\fR) <octet string>" 4
.IX Item """key"" (OSSL_MAC_PARAM_KEY) <octet string>"
.PD
Sets the key in the associated MAC ctx.  This is identical to passing a \fIkey\fR
argument to the \fBOSSL_FUNC_mac_init()\fR function.
.RE
.RS 4
.RE
.IP "with \fBOSSL_FUNC_get_params()\fR:" 4
.IX Item "with OSSL_FUNC_get_params():"
.RS 4
.PD 0
.IP """size"" (\fBOSSL_MAC_PARAM_SIZE\fR) <integer>" 4
.IX Item """size"" (OSSL_MAC_PARAM_SIZE) <integer>"
.PD
Can be used to get the default MAC size (which might be the only allowable
MAC size for the implementation).
.Sp
Note that some implementations allow setting the size that the resulting MAC
should have as well, see the documentation of the implementation.
.RE
.RS 4
.IP """size"" (\fBOSSL_MAC_PARAM_BLOCK_SIZE\fR) <integer>" 4
.IX Item """size"" (OSSL_MAC_PARAM_BLOCK_SIZE) <integer>"
Can be used to get the MAC block size (if supported by the algorithm).
.RE
.RS 4
.RE
.SH NOTES
.IX Header "NOTES"
The MAC life-cycle is described in \fBlife_cycle\-rand\fR\|(7).  Providers should
ensure that the various transitions listed there are supported.  At some point
the EVP layer will begin enforcing the listed transitions.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_FUNC_mac_newctx()\fR and \fBOSSL_FUNC_mac_dupctx()\fR should return the newly created
provider side mac context, or NULL on failure.
.PP
\&\fBOSSL_FUNC_mac_init()\fR, \fBOSSL_FUNC_mac_update()\fR, \fBOSSL_FUNC_mac_final()\fR, \fBOSSL_FUNC_mac_get_params()\fR,
\&\fBOSSL_FUNC_mac_get_ctx_params()\fR and \fBOSSL_FUNC_mac_set_ctx_params()\fR should return 1 for
success or 0 on error.
.PP
\&\fBOSSL_FUNC_mac_gettable_params()\fR, \fBOSSL_FUNC_mac_gettable_ctx_params()\fR and
\&\fBOSSL_FUNC_mac_settable_ctx_params()\fR should return a constant \fBOSSL_PARAM\fR\|(3)
array, or NULL if none is offered.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\fR\|(7),
\&\fBEVP_MAC\-BLAKE2\fR\|(7), \fBEVP_MAC\-CMAC\fR\|(7), \fBEVP_MAC\-GMAC\fR\|(7),
\&\fBEVP_MAC\-HMAC\fR\|(7), \fBEVP_MAC\-KMAC\fR\|(7), \fBEVP_MAC\-Poly1305\fR\|(7),
\&\fBEVP_MAC\-Siphash\fR\|(7),
\&\fBlife_cycle\-mac\fR\|(7), \fBEVP_MAC\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The provider MAC interface was introduced in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
