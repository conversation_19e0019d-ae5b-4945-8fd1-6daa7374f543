.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PROPERTY 7ossl"
.TH PROPERTY 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
property \- Properties, a selection mechanism for algorithm implementations
.SH DESCRIPTION
.IX Header "DESCRIPTION"
As of OpenSSL 3.0, a new method has been introduced to decide which of
multiple implementations of an algorithm will be used.
The method is centered around the concept of properties.
Each implementation defines a number of properties and when an algorithm
is being selected, filters based on these properties can be used to
choose the most appropriate implementation of the algorithm.
.PP
Properties are like variables, they are referenced by name and have a value
assigned.
.SS "Property Names"
.IX Subsection "Property Names"
Property names fall into two categories: those reserved by the OpenSSL
project and user defined names.
A \fIreserved\fR property name consists of a single C\-style identifier
(except for leading underscores not being permitted), which begins
with a letter and can be followed by any number of letters, numbers
and underscores.
Property names are case-insensitive, but OpenSSL will only use lowercase
letters.
.PP
A \fIuser defined\fR property name is similar, but it \fBmust\fR consist of
two or more C\-style identifiers, separated by periods.
The last identifier in the name can be considered the 'true' property
name, which is prefixed by some sort of 'namespace'.
Providers for example could include their name in the prefix and use
property names like
.PP
.Vb 2
\&  <provider_name>.<property_name>
\&  <provider_name>.<algorithm_name>.<property_name>
.Ve
.SS Properties
.IX Subsection "Properties"
A \fIproperty\fR is a \fIname=value\fR pair.
A \fIproperty definition\fR is a sequence of comma separated properties.
There can be any number of properties in a definition, however each name must
be unique.
For example: "" defines an empty property definition (i.e., no restriction);
"my.foo=bar" defines a property named \fImy.foo\fR which has a string value \fIbar\fR
and "iteration.count=3" defines a property named \fIiteration.count\fR which
has a numeric value of \fI3\fR.
The full syntax for property definitions appears below.
.SS Implementations
.IX Subsection "Implementations"
Each implementation of an algorithm can define any number of
properties.
For example, the default provider defines the property \fIprovider=default\fR
for all of its algorithms.
Likewise, OpenSSL's FIPS provider defines \fIprovider=fips\fR and the legacy
provider defines \fIprovider=legacy\fR for all of their algorithms.
.SS Queries
.IX Subsection "Queries"
A \fIproperty query clause\fR is a single conditional test.
For example, "fips=yes", "provider!=default" or "?iteration.count=3".
The first two represent mandatory clauses, such clauses \fBmust\fR match
for any algorithm to even be under consideration.
The third clause represents an optional clause.
Matching such clauses is not a requirement, but any additional optional
match counts in favor of the algorithm.
More details about that in the \fBLookups\fR section.
A \fIproperty query\fR is a sequence of comma separated property query clauses.
It is an error if a property name appears in more than one query clause.
The full syntax for property queries appears below, but the available syntactic
features are:
.IP \(bu 4
\&\fB=\fR is an infix operator providing an equality test.
.IP \(bu 4
\&\fB!=\fR is an infix operator providing an inequality test.
.IP \(bu 4
\&\fB?\fR is a prefix operator that means that the following clause is optional
but preferred.
.IP \(bu 4
\&\fB\-\fR is a prefix operator that means any global query clause involving the
following property name should be ignored.
.IP \(bu 4
\&\fB"..."\fR is a quoted string.
The quotes are not included in the body of the string.
.IP \(bu 4
\&\fB'...'\fR is a quoted string.
The quotes are not included in the body of the string.
.SS Lookups
.IX Subsection "Lookups"
When an algorithm is looked up, a property query is used to determine
the best matching algorithm.
All mandatory query clauses \fBmust\fR be present and the implementation
that additionally has the largest number of matching optional query
clauses will be used.
If there is more than one such optimal candidate, the result will be
chosen from amongst those in an indeterminate way.
Ordering of optional clauses is not significant.
.SS Shortcut
.IX Subsection "Shortcut"
In order to permit a more concise expression of boolean properties, there
is one short cut: a property name alone (e.g. "my.property") is
exactly equivalent to "my.property=yes" in both definitions and queries.
.SS "Global and Local"
.IX Subsection "Global and Local"
Two levels of property query are supported.
A context based property query that applies to all fetch operations and a local
property query.
Where both the context and local queries include a clause with the same name,
the local clause overrides the context clause.
.PP
It is possible for a local property query to remove a clause in the context
property query by preceding the property name with a '\-'.
For example, a context property query that contains "fips=yes" would normally
result in implementations that have "fips=yes".
.PP
However, if the setting of the "fips" property is irrelevant to the
operations being performed, the local property query can include the
clause "\-fips".
Note that the local property query could not use "fips=no" because that would
disallow any implementations with "fips=yes" rather than not caring about the
setting.
.SH SYNTAX
.IX Header "SYNTAX"
The lexical syntax in EBNF is given by:
.PP
.Vb 11
\& Definition     ::= PropertyName ( \*(Aq=\*(Aq Value )?
\&                        ( \*(Aq,\*(Aq PropertyName ( \*(Aq=\*(Aq Value )? )*
\& Query          ::= PropertyQuery ( \*(Aq,\*(Aq PropertyQuery )*
\& PropertyQuery  ::= \*(Aq\-\*(Aq PropertyName
\&                  | \*(Aq?\*(Aq? ( PropertyName (( \*(Aq=\*(Aq | \*(Aq!=\*(Aq ) Value)?)
\& Value          ::= NumberLiteral | StringLiteral
\& StringLiteral  ::= QuotedString | UnquotedString
\& QuotedString   ::= \*(Aq"\*(Aq [^"]* \*(Aq"\*(Aq | "\*(Aq" [^\*(Aq]* "\*(Aq"
\& UnquotedString ::= [A\-Za\-z] [^{space},]+
\& NumberLiteral  ::= \*(Aq0\*(Aq ( [0\-7]* | \*(Aqx\*(Aq [0\-9A\-Fa\-f]+ ) | \*(Aq\-\*(Aq? [1\-9] [0\-9]+
\& PropertyName   ::= [A\-Za\-z] [A\-Za\-z0\-9_]* ( \*(Aq.\*(Aq [A\-Za\-z] [A\-Za\-z0\-9_]* )*
.Ve
.PP
The flavour of EBNF being used is defined by:
<https://www.w3.org/TR/2010/REC\-xquery\-20101214/#EBNFNotation>.
.SH HISTORY
.IX Header "HISTORY"
Properties were added in OpenSSL 3.0
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
