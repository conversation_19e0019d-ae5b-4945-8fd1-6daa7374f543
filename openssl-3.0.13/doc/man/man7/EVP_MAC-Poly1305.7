.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_MAC-POLY1305 7ossl"
.TH EVP_MAC-POLY1305 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_MAC\-Poly1305 \- The Poly1305 EVP_MAC implementation
.SH DESCRIPTION
.IX Header "DESCRIPTION"
Support for computing Poly1305 MACs through the \fBEVP_MAC\fR API.
.SS Identity
.IX Subsection "Identity"
This implementation is identified with this name and properties, to be
used with \fBEVP_MAC_fetch()\fR:
.IP """POLY1305"", ""provider=default""" 4
.IX Item """POLY1305"", ""provider=default"""
.SS "Supported parameters"
.IX Subsection "Supported parameters"
The general description of these parameters can be found in
"PARAMETERS" in \fBEVP_MAC\fR\|(3).
.PP
The following parameter can be set with \fBEVP_MAC_CTX_set_params()\fR:
.IP """key"" (\fBOSSL_MAC_PARAM_KEY\fR) <octet string>" 4
.IX Item """key"" (OSSL_MAC_PARAM_KEY) <octet string>"
Sets the MAC key.
Setting this parameter is identical to passing a \fIkey\fR to \fBEVP_MAC_init\fR\|(3).
.PP
The following parameters can be retrieved with
\&\fBEVP_MAC_CTX_get_params()\fR:
.IP """size"" (\fBOSSL_MAC_PARAM_SIZE\fR) <unsigned integer>" 4
.IX Item """size"" (OSSL_MAC_PARAM_SIZE) <unsigned integer>"
Gets the MAC size.
.PP
The "size" parameter can also be retrieved with with \fBEVP_MAC_CTX_get_mac_size()\fR.
The length of the "size" parameter should not exceed that of an \fBunsigned int\fR.
.SH NOTES
.IX Header "NOTES"
The OpenSSL implementation of the Poly 1305 MAC corresponds to RFC 7539.
.PP
It is critical to never reuse the key.  The security implication noted in
RFC 8439 applies equally to the OpenSSL implementation.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_MAC_CTX_get_params\fR\|(3), \fBEVP_MAC_CTX_set_params\fR\|(3),
"PARAMETERS" in \fBEVP_MAC\fR\|(3), \fBOSSL_PARAM\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2018\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
