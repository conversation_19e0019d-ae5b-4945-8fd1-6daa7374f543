.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "LIFE_CYCLE-CIPHER 7ossl"
.TH LIFE_CYCLE-CIPHER 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
life_cycle\-cipher \- The cipher algorithm life\-cycle
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All symmetric ciphers (CIPHERs) go through a number of stages in their
life-cycle:
.IP start 4
.IX Item "start"
This state represents the CIPHER before it has been allocated.  It is the
starting state for any life-cycle transitions.
.IP newed 4
.IX Item "newed"
This state represents the CIPHER after it has been allocated.
.IP initialised 4
.IX Item "initialised"
These states represent the CIPHER when it is set up and capable of processing
input.  There are three possible initialised states:
.RS 4
.IP "initialised using EVP_CipherInit" 4
.IX Item "initialised using EVP_CipherInit"
.PD 0
.IP "initialised for decryption using EVP_DecryptInit" 4
.IX Item "initialised for decryption using EVP_DecryptInit"
.IP "initialised for encryption using EVP_EncryptInit" 4
.IX Item "initialised for encryption using EVP_EncryptInit"
.RE
.RS 4
.RE
.IP updated 4
.IX Item "updated"
.PD
These states represent the CIPHER when it is set up and capable of processing
additional input or generating output.  The three possible states directly
correspond to those for initialised above.  The three different streams should
not be mixed.
.IP finaled 4
.IX Item "finaled"
This state represents the CIPHER when it has generated output.
.IP freed 4
.IX Item "freed"
This state is entered when the CIPHER is freed.  It is the terminal state
for all life-cycle transitions.
.SS "State Transition Diagram"
.IX Subsection "State Transition Diagram"
The usual life-cycle of a CIPHER is illustrated:
                                 +---------------------------+
                                 |                           |
                                 |           start           |
                                 |                           |
                                 +---------------------------+   + - - - - - - - - - - - - - +
                                       |                         '  any of the initialised   '
                                       | EVP_CIPHER_CTX_new      ' updated or finaled states '
                                       v                         '                           '
                                 +---------------------------+   + - - - - - - - - - - - - - +
                                 |                           |      |
                                 |           newed           |      | EVP_CIPHER_CTX_reset
                                 |                           | <----+
                                 +---------------------------+
                                    |   |                 |
                          +---------+   |                 +---------+
          EVP_DecryptInit |             | EVP_CipherInit            | EVP_EncryptInit
                          v             v                           v
 +---------------------------+   +---------------------------+   +---------------------------+
 |                           |   |                           |   |                           |
 |        initialised        |   |        initialised        |   |        initialised        |
 |       for decryption      |   |                           |   |       for encryption      |
 +---------------------------+   +---------------------------+   +---------------------------+
   |                                   |                                                   |
   | EVP_DecryptUpdate                 | EVP_CipherUpdate                EVP_EncryptUpdate |
   |                                   v                                                   |
   |                             +---------------------------+                             |
   |                             |                           |--------------------+        |
   |                             |          updated          |   EVP_CipherUpdate |        |
   |                             |                           | <------------------+        |
   v                             +---------------------------+                             v
 +---------------------------+                         |         +---------------------------+
 |                           |---------------------+   |         |                           |
 |          updated          |   EVP_DecryptUpdate |   |         |          updated          |------+
 |       for decryption      | <-------------------+   |         |       for encryption      |      |
 +---------------------------+                         |         +---------------------------+      |
                          |            EVP_CipherFinal |            |           ^                   |
                          +-------+                    |   +--------+           |                   |
                 EVP_DecryptFinal |                    |   | EVP_EncryptFinal   +-------------------+
                                  v                    v   v                      EVP_EncryptUpdate
                                 +---------------------------+
                                 |                           |-----------------------------+
                                 |          finaled          |                             |
                                 |                           | <---------------------------+
                                 +---------------------------+   EVP_CIPHER_CTX_get_params
                                       |                             (AEAD encryption)
                                       | EVP_CIPHER_CTX_free
                                       v
                                 +---------------------------+
                                 |                           |
                                 |           freed           |
                                 |                           |
                                 +---------------------------+
.SS "Formal State Transitions"
.IX Subsection "Formal State Transitions"
This section defines all of the legal state transitions.
This is the canonical list.
 Function Call                ---------------------------------------------- Current State -----------------------------------------------
                              start   newed    initialised   updated     finaled   initialised   updated    initialised   updated    freed
                                                                                    decryption  decryption   encryption  encryption
 EVP_CIPHER_CTX_new           newed
 EVP_CipherInit                    initialised initialised initialised initialised initialised initialised  initialised initialised
 EVP_DecryptInit                   initialised initialised initialised initialised initialised initialised  initialised initialised
                                    decryption  decryption  decryption  decryption  decryption  decryption  decryption  decryption
 EVP_EncryptInit                   initialised initialised initialised initialised initialised initialised  initialised initialised
                                    encryption  encryption  encryption  encryption  encryption  encryption  encryption  encryption
 EVP_CipherUpdate                                updated     updated
 EVP_DecryptUpdate                                                                   updated     updated
                                                                                    decryption  decryption
 EVP_EncryptUpdate                                                                                            updated     updated
                                                                                                             encryption  encryption
 EVP_CipherFinal                                             finaled
 EVP_DecryptFinal                                                                                finaled
 EVP_EncryptFinal                                                                                                         finaled
 EVP_CIPHER_CTX_free          freed   freed       freed       freed       freed       freed       freed        freed       freed
 EVP_CIPHER_CTX_reset                 newed       newed       newed       newed       newed       newed        newed       newed
 EVP_CIPHER_CTX_get_params            newed    initialised   updated               initialised   updated    initialised   updated
                                                                                    decryption  decryption   encryption  encryption
 EVP_CIPHER_CTX_set_params            newed    initialised   updated               initialised   updated    initialised   updated
                                                                                    decryption  decryption   encryption  encryption
 EVP_CIPHER_CTX_gettable_params       newed    initialised   updated               initialised   updated    initialised   updated
                                                                                    decryption  decryption   encryption  encryption
 EVP_CIPHER_CTX_settable_params       newed    initialised   updated               initialised   updated    initialised   updated
                                                                                    decryption  decryption   encryption  encryption
.SH NOTES
.IX Header "NOTES"
At some point the EVP layer will begin enforcing the transitions described
herein.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\-cipher\fR\|(7), \fBEVP_EncryptInit\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
