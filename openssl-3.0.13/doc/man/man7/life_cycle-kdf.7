.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "LIFE_CYCLE-KDF 7ossl"
.TH LIFE_CYCLE-KDF 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
life_cycle\-kdf \- The KDF algorithm life\-cycle
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All key derivation functions (KDFs) and pseudo random functions (PRFs)
go through a number of stages in their life-cycle:
.IP start 4
.IX Item "start"
This state represents the KDF/PRF before it has been allocated.  It is the
starting state for any life-cycle transitions.
.IP newed 4
.IX Item "newed"
This state represents the KDF/PRF after it has been allocated.
.IP deriving 4
.IX Item "deriving"
This state represents the KDF/PRF when it is set up and capable of generating
output.
.IP freed 4
.IX Item "freed"
This state is entered when the KDF/PRF is freed.  It is the terminal state
for all life-cycle transitions.
.SS "State Transition Diagram"
.IX Subsection "State Transition Diagram"
The usual life-cycle of a KDF/PRF is illustrated:
                     +-------------------+
                     |       start       |
                     +-------------------+
                       |
                       | EVP_KDF_CTX_new
                       v
                     +-------------------+
                     |       newed       | <+
                     +-------------------+  |
                       |                    |
                       | EVP_KDF_derive     |
                       v                    | EVP_KDF_CTX_reset
    EVP_KDF_derive   +-------------------+  |
  + - - - - - - - -  |                   |  |
  '                  |     deriving      |  |
  + - - - - - - - -> |                   | -+
                     +-------------------+
                       |
                       | EVP_KDF_CTX_free
                       v
                     +-------------------+
                     |       freed       |
                     +-------------------+
.SS "Formal State Transitions"
.IX Subsection "Formal State Transitions"
This section defines all of the legal state transitions.
This is the canonical list.
 Function Call                   ------------- Current State -------------
                                 start       newed       deriving    freed
 EVP_KDF_CTX_new                 newed
 EVP_KDF_derive                             deriving     deriving
 EVP_KDF_CTX_free                freed       freed        freed
 EVP_KDF_CTX_reset                           newed        newed
 EVP_KDF_CTX_get_params                      newed       deriving
 EVP_KDF_CTX_set_params                      newed       deriving
 EVP_KDF_CTX_gettable_params                 newed       deriving
 EVP_KDF_CTX_settable_params                 newed       deriving
.SH NOTES
.IX Header "NOTES"
At some point the EVP layer will begin enforcing the transitions described
herein.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\-kdf\fR\|(7), \fBEVP_KDF\fR\|(3).
.SH HISTORY
.IX Header "HISTORY"
The provider KDF interface was introduced in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
