.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP 7ossl"
.TH EVP 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
evp \- high\-level cryptographic functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/evp.h>
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The EVP library provides a high-level interface to cryptographic
functions.
.PP
The \fBEVP_Seal\fR\fIXXX\fR and \fBEVP_Open\fR\fIXXX\fR
functions provide public key encryption and decryption to implement digital "envelopes".
.PP
The \fBEVP_DigestSign\fR\fIXXX\fR and
\&\fBEVP_DigestVerify\fR\fIXXX\fR functions implement
digital signatures and Message Authentication Codes (MACs). Also see the older
\&\fBEVP_Sign\fR\fIXXX\fR and \fBEVP_Verify\fR\fIXXX\fR
functions.
.PP
Symmetric encryption is available with the \fBEVP_Encrypt\fR\fIXXX\fR
functions.  The \fBEVP_Digest\fR\fIXXX\fR functions provide message digests.
.PP
The \fBEVP_PKEY\fR\fIXXX\fR functions provide a high-level interface to
asymmetric algorithms. To create a new EVP_PKEY see
\&\fBEVP_PKEY_new\fR\|(3). EVP_PKEYs can be associated
with a private key of a particular algorithm by using the functions
described on the \fBEVP_PKEY_fromdata\fR\|(3) page, or
new keys can be generated using \fBEVP_PKEY_keygen\fR\|(3).
EVP_PKEYs can be compared using \fBEVP_PKEY_eq\fR\|(3), or printed using
\&\fBEVP_PKEY_print_private\fR\|(3). \fBEVP_PKEY_todata\fR\|(3) can be used to convert a
key back into an \fBOSSL_PARAM\fR\|(3) array.
.PP
The EVP_PKEY functions support the full range of asymmetric algorithm operations:
.IP "For key agreement see \fBEVP_PKEY_derive\fR\|(3)" 4
.IX Item "For key agreement see EVP_PKEY_derive"
.PD 0
.IP "For signing and verifying see \fBEVP_PKEY_sign\fR\|(3), \fBEVP_PKEY_verify\fR\|(3) and \fBEVP_PKEY_verify_recover\fR\|(3). However, note that these functions do not perform a digest of the data to be signed. Therefore, normally you would use the \fBEVP_DigestSignInit\fR\|(3) functions for this purpose." 4
.IX Item "For signing and verifying see EVP_PKEY_sign, EVP_PKEY_verify and EVP_PKEY_verify_recover. However, note that these functions do not perform a digest of the data to be signed. Therefore, normally you would use the EVP_DigestSignInit functions for this purpose."
.IP "For encryption and decryption see \fBEVP_PKEY_encrypt\fR\|(3) and \fBEVP_PKEY_decrypt\fR\|(3) respectively. However, note that these functions perform encryption and decryption only. As public key encryption is an expensive operation, normally you would wrap an encrypted message in a ""digital envelope"" using the \fBEVP_SealInit\fR\|(3) and \fBEVP_OpenInit\fR\|(3) functions." 4
.IX Item "For encryption and decryption see EVP_PKEY_encrypt and EVP_PKEY_decrypt respectively. However, note that these functions perform encryption and decryption only. As public key encryption is an expensive operation, normally you would wrap an encrypted message in a ""digital envelope"" using the EVP_SealInit and EVP_OpenInit functions."
.PD
.PP
The \fBEVP_BytesToKey\fR\|(3) function provides some limited support for password
based encryption. Careful selection of the parameters will provide a PKCS#5 PBKDF1 compatible
implementation. However, new applications should not typically use this (preferring, for example,
PBKDF2 from PCKS#5).
.PP
The \fBEVP_Encode\fR\fIXXX\fR and
\&\fBEVP_Decode\fR\fIXXX\fR functions implement base 64 encoding
and decoding.
.PP
All the symmetric algorithms (ciphers), digests and asymmetric algorithms
(public key algorithms) can be replaced by ENGINE modules providing alternative
implementations. If ENGINE implementations of ciphers or digests are registered
as defaults, then the various EVP functions will automatically use those
implementations automatically in preference to built in software
implementations. For more information, consult the \fBengine\fR\|(3) man page.
.PP
Although low-level algorithm specific functions exist for many algorithms
their use is discouraged. They cannot be used with an ENGINE and ENGINE
versions of new algorithms cannot be accessed using the low-level functions.
Also makes code harder to adapt to new algorithms and some options are not
cleanly supported at the low-level and some operations are more efficient
using the high-level interface.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_DigestInit\fR\|(3),
\&\fBEVP_EncryptInit\fR\|(3),
\&\fBEVP_OpenInit\fR\|(3),
\&\fBEVP_SealInit\fR\|(3),
\&\fBEVP_DigestSignInit\fR\|(3),
\&\fBEVP_SignInit\fR\|(3),
\&\fBEVP_VerifyInit\fR\|(3),
\&\fBEVP_EncodeInit\fR\|(3),
\&\fBEVP_PKEY_new\fR\|(3),
\&\fBEVP_PKEY_fromdata\fR\|(3),
\&\fBEVP_PKEY_todata\fR\|(3),
\&\fBEVP_PKEY_keygen\fR\|(3),
\&\fBEVP_PKEY_print_private\fR\|(3),
\&\fBEVP_PKEY_decrypt\fR\|(3),
\&\fBEVP_PKEY_encrypt\fR\|(3),
\&\fBEVP_PKEY_sign\fR\|(3),
\&\fBEVP_PKEY_verify\fR\|(3),
\&\fBEVP_PKEY_verify_recover\fR\|(3),
\&\fBEVP_PKEY_derive\fR\|(3),
\&\fBEVP_BytesToKey\fR\|(3),
\&\fBENGINE_by_id\fR\|(3)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
