.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PROVIDER-OBJECT 7ossl"
.TH PROVIDER-OBJECT 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
provider\-object \- A specification for a provider\-native object abstraction
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 2
\& #include <openssl/core_object.h>
\& #include <openssl/core_names.h>
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The provider-native object abstraction is a set of \fBOSSL_PARAM\fR\|(3) keys and
values that can be used to pass provider-native objects to OpenSSL library
code or between different provider operation implementations with the help
of OpenSSL library code.
.PP
The intention is that certain provider-native operations can pass any sort
of object that belong with other operations, or with OpenSSL library code.
.PP
An object may be passed in the following manners:
.IP 1. 4
\&\fIBy value\fR
.Sp
This means that the \fIobject data\fR is passed as an octet string or an UTF8
string, which can be handled in diverse ways by other provided implementations.
The encoding of the object depends on the context it's used in; for example,
\&\fBOSSL_DECODER\fR\|(3) allows multiple encodings, depending on existing decoders.
If central OpenSSL library functionality is to handle the data directly, it
\&\fBmust\fR be encoded in DER for all object types except for \fBOSSL_OBJECT_NAME\fR
(see "Parameter reference" below), where it's assumed to a plain UTF8 string.
.IP 2. 4
\&\fIBy reference\fR
.Sp
This means that the \fIobject data\fR isn't passed directly, an \fIobject
reference\fR is passed instead.  It's an octet string that only the correct
provider understands correctly.
.PP
Objects \fIby value\fR can be used by anything that handles DER encoded
objects.
.PP
Objects \fIby reference\fR need a higher level of cooperation from the
implementation where the object originated (let's call it X) and its target
implementation (let's call it Y):
.IP 1. 4
\&\fIAn object loading function in the target implementation\fR
.Sp
The target implementation (Y) may have a function that can take an \fIobject
reference\fR.  This can only be used if the target implementation is from the
same provider as the one originating the object abstraction in question (X).
.Sp
The exact target implementation to use is determined from the \fIobject type\fR
and possibly the \fIobject data type\fR.
For example, when the OpenSSL library receives an object abstraction with the
\&\fIobject type\fR \fBOSSL_OBJECT_PKEY\fR, it will fetch a \fBprovider\-keymgmt\fR\|(7)
using the \fIobject data type\fR as its key type (the second argument in
\&\fBEVP_KEYMGMT_fetch\fR\|(3)).
.IP 2. 4
\&\fIAn object exporter in the originating implementation\fR
.Sp
The originating implementation (X) may have an exporter function.  This
exporter function can be used to export the object in \fBOSSL_PARAM\fR\|(3) form,
that can then be imported by the target implementation's imported function.
.Sp
This can be used when it's not possible to fetch the target implementation
(Y) from the same provider.
.SS "Parameter reference"
.IX Subsection "Parameter reference"
A provider-native object abstraction is an \fBOSSL_PARAM\fR\|(3) with a selection
of the following parameters:
.IP """data"" (\fBOSSL_OBJECT_PARAM_DATA\fR) <octet string> or <UTF8 string>" 4
.IX Item """data"" (OSSL_OBJECT_PARAM_DATA) <octet string> or <UTF8 string>"
The object data \fIpassed by value\fR.
.IP """reference"" (\fBOSSL_OBJECT_PARAM_REFERENCE\fR) <octet string>" 4
.IX Item """reference"" (OSSL_OBJECT_PARAM_REFERENCE) <octet string>"
The object data \fIpassed by reference\fR.
.IP """type"" (\fBOSSL_OBJECT_PARAM_TYPE\fR) <integer>" 4
.IX Item """type"" (OSSL_OBJECT_PARAM_TYPE) <integer>"
The \fIobject type\fR, a number that may have any of the following values (all
defined in \fI<openssl/core_object.h>\fR):
.RS 4
.IP \fBOSSL_OBJECT_NAME\fR 4
.IX Item "OSSL_OBJECT_NAME"
The object data may only be \fIpassed by value\fR, and should be a UTF8
string.
.Sp
This is useful for \fBprovider\-storemgmt\fR\|(7) when a URI load results in new
URIs.
.IP \fBOSSL_OBJECT_PKEY\fR 4
.IX Item "OSSL_OBJECT_PKEY"
The object data is suitable as provider-native \fBEVP_PKEY\fR key data.  The
object data may be \fIpassed by value\fR or \fIpassed by reference\fR.
.IP \fBOSSL_OBJECT_CERT\fR 4
.IX Item "OSSL_OBJECT_CERT"
The object data is suitable as \fBX509\fR data.  The object data for this
object type can only be \fIpassed by value\fR, and should be an octet string.
.Sp
Since there's no provider-native X.509 object, OpenSSL libraries that
receive this object abstraction are expected to convert the data to a
\&\fBX509\fR object with \fBd2i_X509()\fR.
.IP \fBOSSL_OBJECT_CRL\fR 4
.IX Item "OSSL_OBJECT_CRL"
The object data is suitable as \fBX509_CRL\fR data.  The object data can
only be \fIpassed by value\fR, and should be an octet string.
.Sp
Since there's no provider-native X.509 CRL object, OpenSSL libraries that
receive this object abstraction are expected to convert the data to a
\&\fBX509_CRL\fR object with \fBd2i_X509_CRL()\fR.
.RE
.RS 4
.RE
.IP """data-type"" (\fBOSSL_OBJECT_PARAM_DATA_TYPE\fR) <UTF8 string>" 4
.IX Item """data-type"" (OSSL_OBJECT_PARAM_DATA_TYPE) <UTF8 string>"
The specific type of the object content.  Legitimate values depend on the
object type; if it is \fBOSSL_OBJECT_PKEY\fR, the data type is expected to be a
key type suitable for fetching a \fBprovider\-keymgmt\fR\|(7) that can handle the
data.
.IP """data-structure"" (\fBOSSL_OBJECT_PARAM_DATA_STRUCTURE\fR) <UTF8 string>" 4
.IX Item """data-structure"" (OSSL_OBJECT_PARAM_DATA_STRUCTURE) <UTF8 string>"
The outermost structure of the object content.  Legitimate values depend on
the object type.
.IP """desc"" (\fBOSSL_OBJECT_PARAM_DESC\fR) <UTF8 string>" 4
.IX Item """desc"" (OSSL_OBJECT_PARAM_DESC) <UTF8 string>"
A human readable text that describes extra details on the object.
.PP
When a provider-native object abstraction is used, it \fImust\fR contain object
data in at least one form (object data \fIpassed by value\fR, i.e. the "data"
item, or object data \fIpassed by reference\fR, i.e. the "reference" item).
Both may be present at once, in which case the OpenSSL library code that
receives this will use the most optimal variant.
.PP
For objects with the object type \fBOSSL_OBJECT_NAME\fR, that object type
\&\fImust\fR be given.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\fR\|(7), \fBOSSL_DECODER\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
The concept of providers and everything surrounding them was
introduced in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
