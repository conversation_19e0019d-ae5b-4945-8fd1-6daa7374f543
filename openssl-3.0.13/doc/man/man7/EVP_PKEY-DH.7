.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY-DH 7ossl"
.TH EVP_PKEY-DH 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY\-DH, EVP_PKEY\-DHX, EVP_KEYMGMT\-DH, EVP_KEYMGMT\-DHX
\&\- EVP_PKEY DH and DHX keytype and algorithm support
.SH DESCRIPTION
.IX Header "DESCRIPTION"
For \fBDH\fR FFC key agreement, two classes of domain parameters can be used:
"safe" domain parameters that are associated with approved named safe-prime
groups, and a class of "FIPS186\-type" domain parameters. FIPS186\-type domain
parameters should only be used for backward compatibility with existing
applications that cannot be upgraded to use the approved safe-prime groups.
.PP
See \fBEVP_PKEY\-FFC\fR\|(7) for more information about FFC keys.
.PP
The \fBDH\fR key type uses PKCS#3 format which saves \fIp\fR and \fIg\fR, but not the
\&\fIq\fR value.
The \fBDHX\fR key type uses X9.42 format which saves the value of \fIq\fR and this
must be used for FIPS186\-4. If key validation is required, users should be aware
of the nuances associated with FIPS186\-4 style parameters as discussed in
"DH key validation".
.SS "DH and DHX domain parameters"
.IX Subsection "DH and DHX domain parameters"
In addition to the common FCC parameters that all FFC keytypes should support
(see "FFC parameters" in \fBEVP_PKEY\-FFC\fR\|(7)) the \fBDHX\fR and \fBDH\fR keytype
implementations support the following:
.IP """group"" (\fBOSSL_PKEY_PARAM_GROUP_NAME\fR) <UTF8 string>" 4
.IX Item """group"" (OSSL_PKEY_PARAM_GROUP_NAME) <UTF8 string>"
Sets or gets a string that associates a \fBDH\fR or \fBDHX\fR named safe prime group
with known values for \fIp\fR, \fIq\fR and \fIg\fR.
.Sp
The following values can be used by the OpenSSL's default and FIPS providers:
"ffdhe2048", "ffdhe3072", "ffdhe4096", "ffdhe6144", "ffdhe8192",
"modp_2048", "modp_3072", "modp_4096", "modp_6144", "modp_8192".
.Sp
The following additional values can also be used by OpenSSL's default provider:
"modp_1536", "dh_1024_160", "dh_2048_224", "dh_2048_256".
.Sp
DH/DHX named groups can be easily validated since the parameters are well known.
For protocols that only transfer \fIp\fR and \fIg\fR the value of \fIq\fR can also be
retrieved.
.SS "DH and DHX additional parameters"
.IX Subsection "DH and DHX additional parameters"
.IP """encoded-pub-key"" (\fBOSSL_PKEY_PARAM_ENCODED_PUBLIC_KEY\fR) <octet string>" 4
.IX Item """encoded-pub-key"" (OSSL_PKEY_PARAM_ENCODED_PUBLIC_KEY) <octet string>"
Used for getting and setting the encoding of the DH public key used in a key
exchange message for the TLS protocol.
See \fBEVP_PKEY_set1_encoded_public_key()\fR and \fBEVP_PKEY_get1_encoded_public_key()\fR.
.SS "DH additional domain parameters"
.IX Subsection "DH additional domain parameters"
.IP """safeprime-generator"" (\fBOSSL_PKEY_PARAM_DH_GENERATOR\fR) <integer>" 4
.IX Item """safeprime-generator"" (OSSL_PKEY_PARAM_DH_GENERATOR) <integer>"
Used for DH generation of safe primes using the old safe prime generator code.
The default value is 2.
It is recommended to use a named safe prime group instead, if domain parameter
validation is required.
.Sp
Randomly generated safe primes are not allowed by FIPS, so setting this value
for the OpenSSL FIPS provider will instead choose a named safe prime group
based on the size of \fIp\fR.
.SS "DH and DHX domain parameter / key generation parameters"
.IX Subsection "DH and DHX domain parameter / key generation parameters"
In addition to the common FFC key generation parameters that all FFC key types
should support (see "FFC key generation parameters" in \fBEVP_PKEY\-FFC\fR\|(7)) the
\&\fBDH\fR and \fBDHX\fR keytype implementation supports the following:
.IP """type"" (\fBOSSL_PKEY_PARAM_FFC_TYPE\fR) <UTF8 string>" 4
.IX Item """type"" (OSSL_PKEY_PARAM_FFC_TYPE) <UTF8 string>"
Sets the type of parameter generation. For \fBDH\fR valid values are:
.RS 4
.IP """fips186_4""" 4
.IX Item """fips186_4"""
.PD 0
.IP """default""" 4
.IX Item """default"""
.IP """fips186_2""" 4
.IX Item """fips186_2"""
.PD
These are described in "FFC key generation parameters" in \fBEVP_PKEY\-FFC\fR\|(7)
.IP """group""" 4
.IX Item """group"""
This specifies that a named safe prime name will be chosen using the "pbits"
type.
.IP """generator""" 4
.IX Item """generator"""
A safe prime generator. See the "safeprime-generator" type above.
This is only valid for \fBDH\fR keys.
.RE
.RS 4
.RE
.IP """pbits"" (\fBOSSL_PKEY_PARAM_FFC_PBITS\fR) <unsigned integer>" 4
.IX Item """pbits"" (OSSL_PKEY_PARAM_FFC_PBITS) <unsigned integer>"
Sets the size (in bits) of the prime 'p'.
.Sp
For "fips186_4" this must be 2048.
For "fips186_2" this must be 1024.
For "group" this can be any one of 2048, 3072, 4096, 6144 or 8192.
.IP """priv_len"" (\fBOSSL_PKEY_PARAM_DH_PRIV_LEN\fR) <integer>" 4
.IX Item """priv_len"" (OSSL_PKEY_PARAM_DH_PRIV_LEN) <integer>"
An optional value to set the maximum length of the generated private key.
The default value used if this is not set is the maximum value of
BN_num_bits(\fIq\fR)). The minimum value that this can be set to is 2 * s.
Where s is the security strength of the key which has values of
112, 128, 152, 176 and 200 for key sizes of 2048, 3072, 4096, 6144 and 8192.
.SS "DH key validation"
.IX Subsection "DH key validation"
For \fBDHX\fR that is not a named group the FIPS186\-4 standard specifies that the
values used for FFC parameter generation are also required for parameter
validation. This means that optional FFC domain parameter values for
\&\fIseed\fR, \fIpcounter\fR and \fIgindex\fR or \fIhindex\fR may need to be stored for
validation purposes.
For \fBDHX\fR the \fIseed\fR and \fIpcounter\fR can be stored in ASN1 data
(but the \fIgindex\fR or \fIhindex\fR cannot be stored). It is recommended to use a
named safe prime group instead.
.PP
For DH keys, \fBEVP_PKEY_param_check\fR\|(3) behaves in the following way:
The OpenSSL FIPS provider tests if the parameters are either an approved safe
prime group OR that the FFC parameters conform to FIPS186\-4 as defined in
SP800\-56Ar3 \fIAssurances of Domain-Parameter Validity\fR.
The OpenSSL default provider uses simpler checks that allows there to be no \fIq\fR
value for backwards compatibility.
.PP
For DH keys, \fBEVP_PKEY_param_check_quick\fR\|(3) is equivalent to
\&\fBEVP_PKEY_param_check\fR\|(3).
.PP
For DH keys, \fBEVP_PKEY_public_check\fR\|(3) conforms to
SP800\-56Ar3 \fIFFC Full Public-Key Validation\fR.
.PP
For DH keys, \fBEVP_PKEY_public_check_quick\fR\|(3) conforms to
SP800\-56Ar3 \fIFFC Partial Public-Key Validation\fR when the
DH key is an approved named safe prime group, otherwise it is the same as
\&\fBEVP_PKEY_public_check\fR\|(3).
.PP
For DH Keys, \fBEVP_PKEY_private_check\fR\|(3) tests that the private key is in the
correct range according to SP800\-56Ar3. The OpenSSL FIPS provider requires the
value of \fIq\fR to be set (note that this is set for named safe prime groups).
For backwards compatibility the OpenSSL default provider only requires \fIp\fR to
be set.
.PP
For DH keys, \fBEVP_PKEY_pairwise_check\fR\|(3) conforms to
SP800\-56Ar3 \fIOwner Assurance of Pair-wise Consistency\fR.
.SH EXAMPLES
.IX Header "EXAMPLES"
An \fBEVP_PKEY\fR context can be obtained by calling:
.PP
.Vb 1
\&    EVP_PKEY_CTX *pctx = EVP_PKEY_CTX_new_from_name(NULL, "DH", NULL);
.Ve
.PP
A \fBDH\fR key can be generated with a named safe prime group by calling:
.PP
.Vb 4
\&    int priv_len = 2 * 112;
\&    OSSL_PARAM params[3];
\&    EVP_PKEY *pkey = NULL;
\&    EVP_PKEY_CTX *pctx = EVP_PKEY_CTX_new_from_name(NULL, "DH", NULL);
\&
\&    params[0] = OSSL_PARAM_construct_utf8_string("group", "ffdhe2048", 0);
\&    /* "priv_len" is optional */
\&    params[1] = OSSL_PARAM_construct_int("priv_len", &priv_len);
\&    params[2] = OSSL_PARAM_construct_end();
\&
\&    EVP_PKEY_keygen_init(pctx);
\&    EVP_PKEY_CTX_set_params(pctx, params);
\&    EVP_PKEY_generate(pctx, &pkey);
\&    ...
\&    EVP_PKEY_free(pkey);
\&    EVP_PKEY_CTX_free(pctx);
.Ve
.PP
\&\fBDHX\fR domain parameters can be generated according to \fBFIPS186\-4\fR by calling:
.PP
.Vb 6
\&    int gindex = 2;
\&    unsigned int pbits = 2048;
\&    unsigned int qbits = 256;
\&    OSSL_PARAM params[6];
\&    EVP_PKEY *param_key = NULL;
\&    EVP_PKEY_CTX *pctx = NULL;
\&
\&    pctx = EVP_PKEY_CTX_new_from_name(NULL, "DHX", NULL);
\&    EVP_PKEY_paramgen_init(pctx);
\&
\&    params[0] = OSSL_PARAM_construct_uint("pbits", &pbits);
\&    params[1] = OSSL_PARAM_construct_uint("qbits", &qbits);
\&    params[2] = OSSL_PARAM_construct_int("gindex", &gindex);
\&    params[3] = OSSL_PARAM_construct_utf8_string("type", "fips186_4", 0);
\&    params[4] = OSSL_PARAM_construct_utf8_string("digest", "SHA256", 0);
\&    params[5] = OSSL_PARAM_construct_end();
\&    EVP_PKEY_CTX_set_params(pctx, params);
\&
\&    EVP_PKEY_generate(pctx, &param_key);
\&
\&    EVP_PKEY_print_params(bio_out, param_key, 0, NULL);
\&    ...
\&    EVP_PKEY_free(param_key);
\&    EVP_PKEY_CTX_free(pctx);
.Ve
.PP
A \fBDH\fR key can be generated using domain parameters by calling:
.PP
.Vb 2
\&    EVP_PKEY *key = NULL;
\&    EVP_PKEY_CTX *gctx = EVP_PKEY_CTX_new_from_pkey(NULL, param_key, NULL);
\&
\&    EVP_PKEY_keygen_init(gctx);
\&    EVP_PKEY_generate(gctx, &key);
\&    EVP_PKEY_print_private(bio_out, key, 0, NULL);
\&    ...
\&    EVP_PKEY_free(key);
\&    EVP_PKEY_CTX_free(gctx);
.Ve
.PP
To validate \fBFIPS186\-4\fR \fBDHX\fR domain parameters decoded from \fBPEM\fR or
\&\fBDER\fR data, additional values used during generation may be required to
be set into the key.
.PP
\&\fBEVP_PKEY_todata()\fR, \fBOSSL_PARAM_merge()\fR, and \fBEVP_PKEY_fromdata()\fR are useful
to add these parameters to the original key or domain parameters before
the actual validation. In production code the return values should be checked.
.PP
.Vb 11
\&    EVP_PKEY *received_domp = ...; /* parameters received and decoded */
\&    unsigned char *seed = ...;     /* and additional parameters received */
\&    size_t seedlen = ...;          /* by other means, required */
\&    int gindex = ...;              /* for the validation */
\&    int pcounter = ...;
\&    int hindex = ...;
\&    OSSL_PARAM extra_params[4];
\&    OSSL_PARAM *domain_params = NULL;
\&    OSSL_PARAM *merged_params = NULL;
\&    EVP_PKEY_CTX *ctx = NULL, *validate_ctx = NULL;
\&    EVP_PKEY *complete_domp = NULL;
\&
\&    EVP_PKEY_todata(received_domp, OSSL_KEYMGMT_SELECT_DOMAIN_PARAMETERS,
\&                    &domain_params);
\&    extra_params[0] = OSSL_PARAM_construct_octet_string("seed", seed, seedlen);
\&    /*
\&     * NOTE: For unverifiable g use "hindex" instead of "gindex"
\&     * extra_params[1] = OSSL_PARAM_construct_int("hindex", &hindex);
\&     */
\&    extra_params[1] = OSSL_PARAM_construct_int("gindex", &gindex);
\&    extra_params[2] = OSSL_PARAM_construct_int("pcounter", &pcounter);
\&    extra_params[3] = OSSL_PARAM_construct_end();
\&    merged_params = OSSL_PARAM_merge(domain_params, extra_params);
\&
\&    ctx = EVP_PKEY_CTX_new_from_name(NULL, "DHX", NULL);
\&    EVP_PKEY_fromdata_init(ctx);
\&    EVP_PKEY_fromdata(ctx, &complete_domp, OSSL_KEYMGMT_SELECT_ALL,
\&                      merged_params);
\&
\&    validate_ctx = EVP_PKEY_CTX_new_from_pkey(NULL, complete_domp, NULL);
\&    if (EVP_PKEY_param_check(validate_ctx) > 0)
\&        /* validation_passed(); */
\&    else
\&        /* validation_failed(); */
\&
\&    OSSL_PARAM_free(domain_params);
\&    OSSL_PARAM_free(merged_params);
\&    EVP_PKEY_CTX_free(ctx);
\&    EVP_PKEY_CTX_free(validate_ctx);
\&    EVP_PKEY_free(complete_domp);
.Ve
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
.IP "RFC 7919 (TLS ffdhe named safe prime groups)" 4
.IX Item "RFC 7919 (TLS ffdhe named safe prime groups)"
.PD 0
.IP "RFC 3526 (IKE modp named safe prime groups)" 4
.IX Item "RFC 3526 (IKE modp named safe prime groups)"
.IP "RFC 5114 (Additional DH named groups for dh_1024_160"", ""dh_2048_224"" and ""dh_2048_256"")." 4
.IX Item "RFC 5114 (Additional DH named groups for dh_1024_160"", ""dh_2048_224"" and ""dh_2048_256"")."
.PD
.PP
The following sections of SP800\-56Ar3:
.IP "******* FFC Domain Parameter Selection/Generation" 4
.IX Item "******* FFC Domain Parameter Selection/Generation"
.PD 0
.IP "Appendix D: FFC Safe-prime Groups" 4
.IX Item "Appendix D: FFC Safe-prime Groups"
.PD
.PP
The following sections of FIPS186\-4:
.IP "A.1.1.2 Generation of Probable Primes p and q Using an Approved Hash Function." 4
.IX Item "A.1.1.2 Generation of Probable Primes p and q Using an Approved Hash Function."
.PD 0
.IP "A.2.3 Generation of canonical generator g." 4
.IX Item "A.2.3 Generation of canonical generator g."
.IP "A.2.1 Unverifiable Generation of the Generator g." 4
.IX Item "A.2.1 Unverifiable Generation of the Generator g."
.PD
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_PKEY\-FFC\fR\|(7),
\&\fBEVP_KEYEXCH\-DH\fR\|(7)
\&\fBEVP_PKEY\fR\|(3),
\&\fBprovider\-keymgmt\fR\|(7),
\&\fBEVP_KEYMGMT\fR\|(3),
\&\fBOSSL_PROVIDER\-default\fR\|(7),
\&\fBOSSL_PROVIDER\-FIPS\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
