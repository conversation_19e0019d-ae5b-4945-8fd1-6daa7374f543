.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PROVIDER-KEYMGMT 7ossl"
.TH PROVIDER-KEYMGMT 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
provider\-keymgmt \- The KEYMGMT library <\-> provider functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/core_dispatch.h>
\&
\& /*
\&  * None of these are actual functions, but are displayed like this for
\&  * the function signatures for functions that are offered as function
\&  * pointers in OSSL_DISPATCH arrays.
\&  */
\&
\& /* Key object (keydata) creation and destruction */
\& void *OSSL_FUNC_keymgmt_new(void *provctx);
\& void OSSL_FUNC_keymgmt_free(void *keydata);
\&
\& /* Generation, a more complex constructor */
\& void *OSSL_FUNC_keymgmt_gen_init(void *provctx, int selection,
\&                                  const OSSL_PARAM params[]);
\& int OSSL_FUNC_keymgmt_gen_set_template(void *genctx, void *template);
\& int OSSL_FUNC_keymgmt_gen_set_params(void *genctx, const OSSL_PARAM params[]);
\& const OSSL_PARAM *OSSL_FUNC_keymgmt_gen_settable_params(void *genctx,
\&                                                         void *provctx);
\& void *OSSL_FUNC_keymgmt_gen(void *genctx, OSSL_CALLBACK *cb, void *cbarg);
\& void OSSL_FUNC_keymgmt_gen_cleanup(void *genctx);
\&
\& /* Key loading by object reference, also a constructor */
\& void *OSSL_FUNC_keymgmt_load(const void *reference, size_t *reference_sz);
\&
\& /* Key object information */
\& int OSSL_FUNC_keymgmt_get_params(void *keydata, OSSL_PARAM params[]);
\& const OSSL_PARAM *OSSL_FUNC_keymgmt_gettable_params(void *provctx);
\& int OSSL_FUNC_keymgmt_set_params(void *keydata, const OSSL_PARAM params[]);
\& const OSSL_PARAM *OSSL_FUNC_keymgmt_settable_params(void *provctx);
\&
\& /* Key object content checks */
\& int OSSL_FUNC_keymgmt_has(const void *keydata, int selection);
\& int OSSL_FUNC_keymgmt_match(const void *keydata1, const void *keydata2,
\&                             int selection);
\&
\& /* Discovery of supported operations */
\& const char *OSSL_FUNC_keymgmt_query_operation_name(int operation_id);
\&
\& /* Key object import and export functions */
\& int OSSL_FUNC_keymgmt_import(void *keydata, int selection, const OSSL_PARAM params[]);
\& const OSSL_PARAM *OSSL_FUNC_keymgmt_import_types(int selection);
\& int OSSL_FUNC_keymgmt_export(void *keydata, int selection,
\&                              OSSL_CALLBACK *param_cb, void *cbarg);
\& const OSSL_PARAM *OSSL_FUNC_keymgmt_export_types(int selection);
\&
\& /* Key object duplication, a constructor */
\& void *OSSL_FUNC_keymgmt_dup(const void *keydata_from, int selection);
\&
\& /* Key object validation */
\& int OSSL_FUNC_keymgmt_validate(const void *keydata, int selection, int checktype);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The KEYMGMT operation doesn't have much public visibility in OpenSSL
libraries, it's rather an internal operation that's designed to work
in tandem with operations that use private/public key pairs.
.PP
Because the KEYMGMT operation shares knowledge with the operations it
works with in tandem, they must belong to the same provider.
The OpenSSL libraries will ensure that they do.
.PP
The primary responsibility of the KEYMGMT operation is to hold the
provider side key data for the OpenSSL library EVP_PKEY structure.
.PP
All "functions" mentioned here are passed as function pointers between
\&\fIlibcrypto\fR and the provider in \fBOSSL_DISPATCH\fR\|(3) arrays via
\&\fBOSSL_ALGORITHM\fR\|(3) arrays that are returned by the provider's
\&\fBprovider_query_operation()\fR function
(see "Provider Functions" in \fBprovider\-base\fR\|(7)).
.PP
All these "functions" have a corresponding function type definition
named \fBOSSL_FUNC_{name}_fn\fR, and a helper function to retrieve the
function pointer from a \fBOSSL_DISPATCH\fR\|(3) element named
\&\fBOSSL_FUNC_{name}\fR.
For example, the "function" \fBOSSL_FUNC_keymgmt_new()\fR has these:
.PP
.Vb 3
\& typedef void *(OSSL_FUNC_keymgmt_new_fn)(void *provctx);
\& static ossl_inline OSSL_FUNC_keymgmt_new_fn
\&     OSSL_FUNC_keymgmt_new(const OSSL_DISPATCH *opf);
.Ve
.PP
\&\fBOSSL_DISPATCH\fR\|(3) arrays are indexed by numbers that are provided as
macros in \fBopenssl\-core_dispatch.h\fR\|(7), as follows:
.PP
.Vb 2
\& OSSL_FUNC_keymgmt_new                  OSSL_FUNC_KEYMGMT_NEW
\& OSSL_FUNC_keymgmt_free                 OSSL_FUNC_KEYMGMT_FREE
\&
\& OSSL_FUNC_keymgmt_gen_init             OSSL_FUNC_KEYMGMT_GEN_INIT
\& OSSL_FUNC_keymgmt_gen_set_template     OSSL_FUNC_KEYMGMT_GEN_SET_TEMPLATE
\& OSSL_FUNC_keymgmt_gen_set_params       OSSL_FUNC_KEYMGMT_GEN_SET_PARAMS
\& OSSL_FUNC_keymgmt_gen_settable_params  OSSL_FUNC_KEYMGMT_GEN_SETTABLE_PARAMS
\& OSSL_FUNC_keymgmt_gen                  OSSL_FUNC_KEYMGMT_GEN
\& OSSL_FUNC_keymgmt_gen_cleanup          OSSL_FUNC_KEYMGMT_GEN_CLEANUP
\&
\& OSSL_FUNC_keymgmt_load                 OSSL_FUNC_KEYMGMT_LOAD
\&
\& OSSL_FUNC_keymgmt_get_params           OSSL_FUNC_KEYMGMT_GET_PARAMS
\& OSSL_FUNC_keymgmt_gettable_params      OSSL_FUNC_KEYMGMT_GETTABLE_PARAMS
\& OSSL_FUNC_keymgmt_set_params           OSSL_FUNC_KEYMGMT_SET_PARAMS
\& OSSL_FUNC_keymgmt_settable_params      OSSL_FUNC_KEYMGMT_SETTABLE_PARAMS
\&
\& OSSL_FUNC_keymgmt_query_operation_name OSSL_FUNC_KEYMGMT_QUERY_OPERATION_NAME
\&
\& OSSL_FUNC_keymgmt_has                  OSSL_FUNC_KEYMGMT_HAS
\& OSSL_FUNC_keymgmt_validate             OSSL_FUNC_KEYMGMT_VALIDATE
\& OSSL_FUNC_keymgmt_match                OSSL_FUNC_KEYMGMT_MATCH
\&
\& OSSL_FUNC_keymgmt_import               OSSL_FUNC_KEYMGMT_IMPORT
\& OSSL_FUNC_keymgmt_import_types         OSSL_FUNC_KEYMGMT_IMPORT_TYPES
\& OSSL_FUNC_keymgmt_export               OSSL_FUNC_KEYMGMT_EXPORT
\& OSSL_FUNC_keymgmt_export_types         OSSL_FUNC_KEYMGMT_EXPORT_TYPES
\&
\& OSSL_FUNC_keymgmt_dup                  OSSL_FUNC_KEYMGMT_DUP
.Ve
.SS "Key Objects"
.IX Subsection "Key Objects"
A key object is a collection of data for an asymmetric key, and is
represented as \fIkeydata\fR in this manual.
.PP
The exact contents of a key object are defined by the provider, and it
is assumed that different operations in one and the same provider use
the exact same structure to represent this collection of data, so that
for example, a key object that has been created using the KEYMGMT
interface that we document here can be passed as is to other provider
operations, such as \fBOP_signature_sign_init()\fR (see
\&\fBprovider\-signature\fR\|(7)).
.PP
With some of the KEYMGMT functions, it's possible to select a specific
subset of data to handle, governed by the bits in a \fIselection\fR
indicator.  The bits are:
.IP \fBOSSL_KEYMGMT_SELECT_PRIVATE_KEY\fR 4
.IX Item "OSSL_KEYMGMT_SELECT_PRIVATE_KEY"
Indicating that the private key data in a key object should be
considered.
.IP \fBOSSL_KEYMGMT_SELECT_PUBLIC_KEY\fR 4
.IX Item "OSSL_KEYMGMT_SELECT_PUBLIC_KEY"
Indicating that the public key data in a key object should be
considered.
.IP \fBOSSL_KEYMGMT_SELECT_DOMAIN_PARAMETERS\fR 4
.IX Item "OSSL_KEYMGMT_SELECT_DOMAIN_PARAMETERS"
Indicating that the domain parameters in a key object should be
considered.
.IP \fBOSSL_KEYMGMT_SELECT_OTHER_PARAMETERS\fR 4
.IX Item "OSSL_KEYMGMT_SELECT_OTHER_PARAMETERS"
Indicating that other parameters in a key object should be
considered.
.Sp
Other parameters are key parameters that don't fit any other
classification.  In other words, this particular selector bit works as
a last resort bit bucket selector.
.PP
Some selector bits have also been combined for easier use:
.IP \fBOSSL_KEYMGMT_SELECT_ALL_PARAMETERS\fR 4
.IX Item "OSSL_KEYMGMT_SELECT_ALL_PARAMETERS"
Indicating that all key object parameters should be considered,
regardless of their more granular classification.
.Sp
This is a combination of \fBOSSL_KEYMGMT_SELECT_DOMAIN_PARAMETERS\fR and
\&\fBOSSL_KEYMGMT_SELECT_OTHER_PARAMETERS\fR.
.IP \fBOSSL_KEYMGMT_SELECT_KEYPAIR\fR 4
.IX Item "OSSL_KEYMGMT_SELECT_KEYPAIR"
Indicating that both the whole key pair in a key object should be
considered, i.e. the combination of public and private key.
.Sp
This is a combination of \fBOSSL_KEYMGMT_SELECT_PRIVATE_KEY\fR and
\&\fBOSSL_KEYMGMT_SELECT_PUBLIC_KEY\fR.
.IP \fBOSSL_KEYMGMT_SELECT_ALL\fR 4
.IX Item "OSSL_KEYMGMT_SELECT_ALL"
Indicating that everything in a key object should be considered.
.PP
The exact interpretation of those bits or how they combine is left to
each function where you can specify a selector.
.PP
It's left to the provider implementation to decide what is reasonable
to do with regards to received selector bits and how to do it.
Among others, an implementation of \fBOSSL_FUNC_keymgmt_match()\fR might opt
to not compare the private half if it has compared the public half,
since a match of one half implies a match of the other half.
.SS "Constructing and Destructing Functions"
.IX Subsection "Constructing and Destructing Functions"
\&\fBOSSL_FUNC_keymgmt_new()\fR should create a provider side key object.  The
provider context \fIprovctx\fR is passed and may be incorporated in the
key object, but that is not mandatory.
.PP
\&\fBOSSL_FUNC_keymgmt_free()\fR should free the passed \fIkeydata\fR.
.PP
\&\fBOSSL_FUNC_keymgmt_gen_init()\fR, \fBOSSL_FUNC_keymgmt_gen_set_template()\fR,
\&\fBOSSL_FUNC_keymgmt_gen_set_params()\fR, \fBOSSL_FUNC_keymgmt_gen_settable_params()\fR,
\&\fBOSSL_FUNC_keymgmt_gen()\fR and \fBOSSL_FUNC_keymgmt_gen_cleanup()\fR work together as a
more elaborate context based key object constructor.
.PP
\&\fBOSSL_FUNC_keymgmt_gen_init()\fR should create the key object generation context
and initialize it with \fIselections\fR, which will determine what kind
of contents the key object to be generated should get.
The \fIparams\fR, if not NULL, should be set on the context in a manner similar to
using \fBOSSL_FUNC_keymgmt_set_params()\fR.
.PP
\&\fBOSSL_FUNC_keymgmt_gen_set_template()\fR should add \fItemplate\fR to the context
\&\fIgenctx\fR.  The \fItemplate\fR is assumed to be a key object constructed
with the same KEYMGMT, and from which content that the implementation
chooses can be used as a template for the key object to be generated.
Typically, the generation of a DSA or DH key would get the domain
parameters from this \fItemplate\fR.
.PP
\&\fBOSSL_FUNC_keymgmt_gen_set_params()\fR should set additional parameters from
\&\fIparams\fR in the key object generation context \fIgenctx\fR.
.PP
\&\fBOSSL_FUNC_keymgmt_gen_settable_params()\fR should return a constant array of
descriptor \fBOSSL_PARAM\fR\|(3), for parameters that \fBOSSL_FUNC_keymgmt_gen_set_params()\fR
can handle.
.PP
\&\fBOSSL_FUNC_keymgmt_gen()\fR should perform the key object generation itself, and
return the result.  The callback \fIcb\fR should be called at regular
intervals with indications on how the key object generation
progresses.
.PP
\&\fBOSSL_FUNC_keymgmt_gen_cleanup()\fR should clean up and free the key object
generation context \fIgenctx\fR
.PP
\&\fBOSSL_FUNC_keymgmt_load()\fR creates a provider side key object based on a
\&\fIreference\fR object with a size of \fIreference_sz\fR bytes, that only the
provider knows how to interpret, but that may come from other operations.
Outside the provider, this reference is simply an array of bytes.
.PP
At least one of \fBOSSL_FUNC_keymgmt_new()\fR, \fBOSSL_FUNC_keymgmt_gen()\fR and
\&\fBOSSL_FUNC_keymgmt_load()\fR are mandatory, as well as \fBOSSL_FUNC_keymgmt_free()\fR and
\&\fBOSSL_FUNC_keymgmt_has()\fR. Additionally, if \fBOSSL_FUNC_keymgmt_gen()\fR is present,
\&\fBOSSL_FUNC_keymgmt_gen_init()\fR and \fBOSSL_FUNC_keymgmt_gen_cleanup()\fR must be
present as well.
.SS "Key Object Information Functions"
.IX Subsection "Key Object Information Functions"
\&\fBOSSL_FUNC_keymgmt_get_params()\fR should extract information data associated
with the given \fIkeydata\fR, see "Common Information Parameters".
.PP
\&\fBOSSL_FUNC_keymgmt_gettable_params()\fR should return a constant array of
descriptor \fBOSSL_PARAM\fR\|(3), for parameters that \fBOSSL_FUNC_keymgmt_get_params()\fR
can handle.
.PP
If \fBOSSL_FUNC_keymgmt_gettable_params()\fR is present, \fBOSSL_FUNC_keymgmt_get_params()\fR
must also be present, and vice versa.
.PP
\&\fBOSSL_FUNC_keymgmt_set_params()\fR should update information data associated
with the given \fIkeydata\fR, see "Common Information Parameters".
.PP
\&\fBOSSL_FUNC_keymgmt_settable_params()\fR should return a constant array of
descriptor \fBOSSL_PARAM\fR\|(3), for parameters that \fBOSSL_FUNC_keymgmt_set_params()\fR
can handle.
.PP
If \fBOSSL_FUNC_keymgmt_settable_params()\fR is present, \fBOSSL_FUNC_keymgmt_set_params()\fR
must also be present, and vice versa.
.SS "Key Object Checking Functions"
.IX Subsection "Key Object Checking Functions"
\&\fBOSSL_FUNC_keymgmt_query_operation_name()\fR should return the name of the
supported algorithm for the operation \fIoperation_id\fR.  This is
similar to \fBprovider_query_operation()\fR (see \fBprovider\-base\fR\|(7)),
but only works as an advisory.  If this function is not present, or
returns NULL, the caller is free to assume that there's an algorithm
from the same provider, of the same name as the one used to fetch the
keymgmt and try to use that.
.PP
\&\fBOSSL_FUNC_keymgmt_has()\fR should check whether the given \fIkeydata\fR contains the subsets
of data indicated by the \fIselector\fR.  A combination of several
selector bits must consider all those subsets, not just one.  An
implementation is, however, free to consider an empty subset of data
to still be a valid subset. For algorithms where some selection is
not meaningful such as \fBOSSL_KEYMGMT_SELECT_DOMAIN_PARAMETERS\fR for
RSA keys the function should just return 1 as the selected subset
is not really missing in the key.
.PP
\&\fBOSSL_FUNC_keymgmt_validate()\fR should check if the \fIkeydata\fR contains valid
data subsets indicated by \fIselection\fR.  Some combined selections of
data subsets may cause validation of the combined data.
For example, the combination of \fBOSSL_KEYMGMT_SELECT_PRIVATE_KEY\fR and
\&\fBOSSL_KEYMGMT_SELECT_PUBLIC_KEY\fR (or \fBOSSL_KEYMGMT_SELECT_KEYPAIR\fR
for short) is expected to check that the pairwise consistency of
\&\fIkeydata\fR is valid. The \fIchecktype\fR parameter controls what type of check is
performed on the subset of data. Two types of check are defined:
\&\fBOSSL_KEYMGMT_VALIDATE_FULL_CHECK\fR and \fBOSSL_KEYMGMT_VALIDATE_QUICK_CHECK\fR.
The interpretation of how much checking is performed in a full check versus a
quick check is key type specific. Some providers may have no distinction
between a full check and a quick check. For algorithms where some selection is
not meaningful such as \fBOSSL_KEYMGMT_SELECT_DOMAIN_PARAMETERS\fR for
RSA keys the function should just return 1 as there is nothing to validate for
that selection.
.PP
\&\fBOSSL_FUNC_keymgmt_match()\fR should check if the data subset indicated by
\&\fIselection\fR in \fIkeydata1\fR and \fIkeydata2\fR match.  It is assumed that
the caller has ensured that \fIkeydata1\fR and \fIkeydata2\fR are both owned
by the implementation of this function.
.SS "Key Object Import, Export and Duplication Functions"
.IX Subsection "Key Object Import, Export and Duplication Functions"
\&\fBOSSL_FUNC_keymgmt_import()\fR should import data indicated by \fIselection\fR into
\&\fIkeydata\fR with values taken from the \fBOSSL_PARAM\fR\|(3) array \fIparams\fR.
.PP
\&\fBOSSL_FUNC_keymgmt_export()\fR should extract values indicated by \fIselection\fR
from \fIkeydata\fR, create an \fBOSSL_PARAM\fR\|(3) array with them and call
\&\fIparam_cb\fR with that array as well as the given \fIcbarg\fR.
.PP
\&\fBOSSL_FUNC_keymgmt_import_types()\fR should return a constant array of descriptor
\&\fBOSSL_PARAM\fR\|(3) for data indicated by \fIselection\fR, for parameters that
\&\fBOSSL_FUNC_keymgmt_import()\fR can handle.
.PP
\&\fBOSSL_FUNC_keymgmt_export_types()\fR should return a constant array of descriptor
\&\fBOSSL_PARAM\fR\|(3) for data indicated by \fIselection\fR, that the
\&\fBOSSL_FUNC_keymgmt_export()\fR callback can expect to receive.
.PP
\&\fBOSSL_FUNC_keymgmt_dup()\fR should duplicate data subsets indicated by
\&\fIselection\fR or the whole key data \fIkeydata_from\fR and create a new
provider side key object with the data.
.SS "Common Information Parameters"
.IX Subsection "Common Information Parameters"
See \fBOSSL_PARAM\fR\|(3) for further details on the parameters structure.
.PP
Common information parameters currently recognised by all built-in
keymgmt algorithms are as follows:
.IP """bits"" (\fBOSSL_PKEY_PARAM_BITS\fR) <integer>" 4
.IX Item """bits"" (OSSL_PKEY_PARAM_BITS) <integer>"
The value should be the cryptographic length of the cryptosystem to
which the key belongs, in bits.  The definition of cryptographic
length is specific to the key cryptosystem.
.IP """max-size"" (\fBOSSL_PKEY_PARAM_MAX_SIZE\fR) <integer>" 4
.IX Item """max-size"" (OSSL_PKEY_PARAM_MAX_SIZE) <integer>"
The value should be the maximum size that a caller should allocate to
safely store a signature (called \fIsig\fR in \fBprovider\-signature\fR\|(7)),
the result of asymmetric encryption / decryption (\fIout\fR in
\&\fBprovider\-asym_cipher\fR\|(7), a derived secret (\fIsecret\fR in
\&\fBprovider\-keyexch\fR\|(7), and similar data).
.Sp
Because an EVP_KEYMGMT method is always tightly bound to another method
(signature, asymmetric cipher, key exchange, ...) and must be of the
same provider, this number only needs to be synchronised with the
dimensions handled in the rest of the same provider.
.IP """security-bits"" (\fBOSSL_PKEY_PARAM_SECURITY_BITS\fR) <integer>" 4
.IX Item """security-bits"" (OSSL_PKEY_PARAM_SECURITY_BITS) <integer>"
The value should be the number of security bits of the given key.
Bits of security is defined in SP800\-57.
.IP """mandatory-digest"" (\fBOSSL_PKEY_PARAM_MANDATORY_DIGEST\fR) <UTF8 string>" 4
.IX Item """mandatory-digest"" (OSSL_PKEY_PARAM_MANDATORY_DIGEST) <UTF8 string>"
If there is a mandatory digest for performing a signature operation with
keys from this keymgmt, this parameter should get its name as value.
.Sp
When \fBEVP_PKEY_get_default_digest_name()\fR queries this parameter and it's
filled in by the implementation, its return value will be 2.
.Sp
If the keymgmt implementation fills in the value \f(CW""\fR or \f(CW"UNDEF"\fR,
\&\fBEVP_PKEY_get_default_digest_name\fR\|(3) will place the string \f(CW"UNDEF"\fR into
its argument \fImdname\fR.  This signifies that no digest should be specified
with the corresponding signature operation.
.IP """default-digest"" (\fBOSSL_PKEY_PARAM_DEFAULT_DIGEST\fR) <UTF8 string>" 4
.IX Item """default-digest"" (OSSL_PKEY_PARAM_DEFAULT_DIGEST) <UTF8 string>"
If there is a default digest for performing a signature operation with
keys from this keymgmt, this parameter should get its name as value.
.Sp
When \fBEVP_PKEY_get_default_digest_name\fR\|(3) queries this parameter and it's
filled in by the implementation, its return value will be 1.  Note that if
\&\fBOSSL_PKEY_PARAM_MANDATORY_DIGEST\fR is responded to as well,
\&\fBEVP_PKEY_get_default_digest_name\fR\|(3) ignores the response to this
parameter.
.Sp
If the keymgmt implementation fills in the value \f(CW""\fR or \f(CW"UNDEF"\fR,
\&\fBEVP_PKEY_get_default_digest_name\fR\|(3) will place the string \f(CW"UNDEF"\fR into
its argument \fImdname\fR.  This signifies that no digest has to be specified
with the corresponding signature operation, but may be specified as an
option.
.SH "RETURN VALUES"
.IX Header "RETURN VALUES"
\&\fBOSSL_FUNC_keymgmt_new()\fR and \fBOSSL_FUNC_keymgmt_dup()\fR should return a valid
reference to the newly created provider side key object, or NULL on failure.
.PP
\&\fBOSSL_FUNC_keymgmt_import()\fR, \fBOSSL_FUNC_keymgmt_export()\fR, \fBOSSL_FUNC_keymgmt_get_params()\fR and
\&\fBOSSL_FUNC_keymgmt_set_params()\fR should return 1 for success or 0 on error.
.PP
\&\fBOSSL_FUNC_keymgmt_validate()\fR should return 1 on successful validation, or 0 on
failure.
.PP
\&\fBOSSL_FUNC_keymgmt_has()\fR should return 1 if all the selected data subsets are contained
in the given \fIkeydata\fR or 0 otherwise.
.PP
\&\fBOSSL_FUNC_keymgmt_query_operation_name()\fR should return a pointer to a string matching
the requested operation, or NULL if the same name used to fetch the keymgmt
applies.
.PP
\&\fBOSSL_FUNC_keymgmt_gettable_params()\fR and \fBOSSL_FUNC_keymgmt_settable_params()\fR
\&\fBOSSL_FUNC_keymgmt_import_types()\fR, \fBOSSL_FUNC_keymgmt_export_types()\fR
should
always return a constant \fBOSSL_PARAM\fR\|(3) array.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\fR\|(7),
\&\fBEVP_PKEY\-X25519\fR\|(7), \fBEVP_PKEY\-X448\fR\|(7), \fBEVP_PKEY\-ED25519\fR\|(7),
\&\fBEVP_PKEY\-ED448\fR\|(7), \fBEVP_PKEY\-EC\fR\|(7), \fBEVP_PKEY\-RSA\fR\|(7),
\&\fBEVP_PKEY\-DSA\fR\|(7), \fBEVP_PKEY\-DH\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The KEYMGMT interface was introduced in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2024 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
