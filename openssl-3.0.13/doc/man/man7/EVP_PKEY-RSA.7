.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_PKEY-RSA 7ossl"
.TH EVP_PKEY-RSA 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_PKEY\-RSA, EVP_KEYMGMT\-RSA, RSA
\&\- EVP_PKEY RSA keytype and algorithm support
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The \fBRSA\fR keytype is implemented in OpenSSL's default and FIPS providers.
That implementation supports the basic RSA keys, containing the modulus \fIn\fR,
the public exponent \fIe\fR, the private exponent \fId\fR, and a collection of prime
factors, exponents and coefficient for CRT calculations, of which the first
few are known as \fIp\fR and \fIq\fR, \fIdP\fR and \fIdQ\fR, and \fIqInv\fR.
.SS "Common RSA parameters"
.IX Subsection "Common RSA parameters"
In addition to the common parameters that all keytypes should support (see
"Common parameters" in \fBprovider\-keymgmt\fR\|(7)), the \fBRSA\fR keytype implementation
supports the following.
.IP """n"" (\fBOSSL_PKEY_PARAM_RSA_N\fR) <unsigned integer>" 4
.IX Item """n"" (OSSL_PKEY_PARAM_RSA_N) <unsigned integer>"
The RSA modulus "n" value.
.IP """e"" (\fBOSSL_PKEY_PARAM_RSA_E\fR) <unsigned integer>" 4
.IX Item """e"" (OSSL_PKEY_PARAM_RSA_E) <unsigned integer>"
The RSA public exponent "e" value.
This value must always be set when creating a raw key using \fBEVP_PKEY_fromdata\fR\|(3).
Note that when a decryption operation is performed, that this value is used for
blinding purposes to prevent timing attacks.
.IP """d"" (\fBOSSL_PKEY_PARAM_RSA_D\fR) <unsigned integer>" 4
.IX Item """d"" (OSSL_PKEY_PARAM_RSA_D) <unsigned integer>"
The RSA private exponent "d" value.
.IP """rsa\-factor1"" (\fBOSSL_PKEY_PARAM_RSA_FACTOR1\fR) <unsigned integer>" 4
.IX Item """rsa-factor1"" (OSSL_PKEY_PARAM_RSA_FACTOR1) <unsigned integer>"
.PD 0
.IP """rsa\-factor2"" (\fBOSSL_PKEY_PARAM_RSA_FACTOR2\fR) <unsigned integer>" 4
.IX Item """rsa-factor2"" (OSSL_PKEY_PARAM_RSA_FACTOR2) <unsigned integer>"
.IP """rsa\-factor3"" (\fBOSSL_PKEY_PARAM_RSA_FACTOR3\fR) <unsigned integer>" 4
.IX Item """rsa-factor3"" (OSSL_PKEY_PARAM_RSA_FACTOR3) <unsigned integer>"
.IP """rsa\-factor4"" (\fBOSSL_PKEY_PARAM_RSA_FACTOR4\fR) <unsigned integer>" 4
.IX Item """rsa-factor4"" (OSSL_PKEY_PARAM_RSA_FACTOR4) <unsigned integer>"
.IP """rsa\-factor5"" (\fBOSSL_PKEY_PARAM_RSA_FACTOR5\fR) <unsigned integer>" 4
.IX Item """rsa-factor5"" (OSSL_PKEY_PARAM_RSA_FACTOR5) <unsigned integer>"
.IP """rsa\-factor6"" (\fBOSSL_PKEY_PARAM_RSA_FACTOR6\fR) <unsigned integer>" 4
.IX Item """rsa-factor6"" (OSSL_PKEY_PARAM_RSA_FACTOR6) <unsigned integer>"
.IP """rsa\-factor7"" (\fBOSSL_PKEY_PARAM_RSA_FACTOR7\fR) <unsigned integer>" 4
.IX Item """rsa-factor7"" (OSSL_PKEY_PARAM_RSA_FACTOR7) <unsigned integer>"
.IP """rsa\-factor8"" (\fBOSSL_PKEY_PARAM_RSA_FACTOR8\fR) <unsigned integer>" 4
.IX Item """rsa-factor8"" (OSSL_PKEY_PARAM_RSA_FACTOR8) <unsigned integer>"
.IP """rsa\-factor9"" (\fBOSSL_PKEY_PARAM_RSA_FACTOR9\fR) <unsigned integer>" 4
.IX Item """rsa-factor9"" (OSSL_PKEY_PARAM_RSA_FACTOR9) <unsigned integer>"
.IP """rsa\-factor10"" (\fBOSSL_PKEY_PARAM_RSA_FACTOR10\fR) <unsigned integer>" 4
.IX Item """rsa-factor10"" (OSSL_PKEY_PARAM_RSA_FACTOR10) <unsigned integer>"
.PD
RSA prime factors. The factors are known as "p", "q" and "r_i" in RFC8017.
Up to eight additional "r_i" prime factors are supported.
.IP """rsa\-exponent1"" (\fBOSSL_PKEY_PARAM_RSA_EXPONENT1\fR) <unsigned integer>" 4
.IX Item """rsa-exponent1"" (OSSL_PKEY_PARAM_RSA_EXPONENT1) <unsigned integer>"
.PD 0
.IP """rsa\-exponent2"" (\fBOSSL_PKEY_PARAM_RSA_EXPONENT2\fR) <unsigned integer>" 4
.IX Item """rsa-exponent2"" (OSSL_PKEY_PARAM_RSA_EXPONENT2) <unsigned integer>"
.IP """rsa\-exponent3"" (\fBOSSL_PKEY_PARAM_RSA_EXPONENT3\fR) <unsigned integer>" 4
.IX Item """rsa-exponent3"" (OSSL_PKEY_PARAM_RSA_EXPONENT3) <unsigned integer>"
.IP """rsa\-exponent4"" (\fBOSSL_PKEY_PARAM_RSA_EXPONENT4\fR) <unsigned integer>" 4
.IX Item """rsa-exponent4"" (OSSL_PKEY_PARAM_RSA_EXPONENT4) <unsigned integer>"
.IP """rsa\-exponent5"" (\fBOSSL_PKEY_PARAM_RSA_EXPONENT5\fR) <unsigned integer>" 4
.IX Item """rsa-exponent5"" (OSSL_PKEY_PARAM_RSA_EXPONENT5) <unsigned integer>"
.IP """rsa\-exponent6"" (\fBOSSL_PKEY_PARAM_RSA_EXPONENT6\fR) <unsigned integer>" 4
.IX Item """rsa-exponent6"" (OSSL_PKEY_PARAM_RSA_EXPONENT6) <unsigned integer>"
.IP """rsa\-exponent7"" (\fBOSSL_PKEY_PARAM_RSA_EXPONENT7\fR) <unsigned integer>" 4
.IX Item """rsa-exponent7"" (OSSL_PKEY_PARAM_RSA_EXPONENT7) <unsigned integer>"
.IP """rsa\-exponent8"" (\fBOSSL_PKEY_PARAM_RSA_EXPONENT8\fR) <unsigned integer>" 4
.IX Item """rsa-exponent8"" (OSSL_PKEY_PARAM_RSA_EXPONENT8) <unsigned integer>"
.IP """rsa\-exponent9"" (\fBOSSL_PKEY_PARAM_RSA_EXPONENT9\fR) <unsigned integer>" 4
.IX Item """rsa-exponent9"" (OSSL_PKEY_PARAM_RSA_EXPONENT9) <unsigned integer>"
.IP """rsa\-exponent10"" (\fBOSSL_PKEY_PARAM_RSA_EXPONENT10\fR) <unsigned integer>" 4
.IX Item """rsa-exponent10"" (OSSL_PKEY_PARAM_RSA_EXPONENT10) <unsigned integer>"
.PD
RSA CRT (Chinese Remainder Theorem) exponents. The exponents are known
as "dP", "dQ" and "d_i" in RFC8017.
Up to eight additional "d_i" exponents are supported.
.IP """rsa\-coefficient1"" (\fBOSSL_PKEY_PARAM_RSA_COEFFICIENT1\fR) <unsigned integer>" 4
.IX Item """rsa-coefficient1"" (OSSL_PKEY_PARAM_RSA_COEFFICIENT1) <unsigned integer>"
.PD 0
.IP """rsa\-coefficient2"" (\fBOSSL_PKEY_PARAM_RSA_COEFFICIENT2\fR) <unsigned integer>" 4
.IX Item """rsa-coefficient2"" (OSSL_PKEY_PARAM_RSA_COEFFICIENT2) <unsigned integer>"
.IP """rsa\-coefficient3"" (\fBOSSL_PKEY_PARAM_RSA_COEFFICIENT3\fR) <unsigned integer>" 4
.IX Item """rsa-coefficient3"" (OSSL_PKEY_PARAM_RSA_COEFFICIENT3) <unsigned integer>"
.IP """rsa\-coefficient4"" (\fBOSSL_PKEY_PARAM_RSA_COEFFICIENT4\fR) <unsigned integer>" 4
.IX Item """rsa-coefficient4"" (OSSL_PKEY_PARAM_RSA_COEFFICIENT4) <unsigned integer>"
.IP """rsa\-coefficient5"" (\fBOSSL_PKEY_PARAM_RSA_COEFFICIENT5\fR) <unsigned integer>" 4
.IX Item """rsa-coefficient5"" (OSSL_PKEY_PARAM_RSA_COEFFICIENT5) <unsigned integer>"
.IP """rsa\-coefficient6"" (\fBOSSL_PKEY_PARAM_RSA_COEFFICIENT6\fR) <unsigned integer>" 4
.IX Item """rsa-coefficient6"" (OSSL_PKEY_PARAM_RSA_COEFFICIENT6) <unsigned integer>"
.IP """rsa\-coefficient7"" (\fBOSSL_PKEY_PARAM_RSA_COEFFICIENT7\fR) <unsigned integer>" 4
.IX Item """rsa-coefficient7"" (OSSL_PKEY_PARAM_RSA_COEFFICIENT7) <unsigned integer>"
.IP """rsa\-coefficient8"" (\fBOSSL_PKEY_PARAM_RSA_COEFFICIENT8\fR) <unsigned integer>" 4
.IX Item """rsa-coefficient8"" (OSSL_PKEY_PARAM_RSA_COEFFICIENT8) <unsigned integer>"
.IP """rsa\-coefficient9"" (\fBOSSL_PKEY_PARAM_RSA_COEFFICIENT9\fR) <unsigned integer>" 4
.IX Item """rsa-coefficient9"" (OSSL_PKEY_PARAM_RSA_COEFFICIENT9) <unsigned integer>"
.PD
RSA CRT (Chinese Remainder Theorem) coefficients. The coefficients are known as
"qInv" and "t_i".
Up to eight additional "t_i" exponents are supported.
.SS "RSA key generation parameters"
.IX Subsection "RSA key generation parameters"
When generating RSA keys, the following key generation parameters may be used.
.IP """bits"" (\fBOSSL_PKEY_PARAM_RSA_BITS\fR) <unsigned integer>" 4
.IX Item """bits"" (OSSL_PKEY_PARAM_RSA_BITS) <unsigned integer>"
The value should be the cryptographic length for the \fBRSA\fR cryptosystem, in
bits.
.IP """primes"" (\fBOSSL_PKEY_PARAM_RSA_PRIMES\fR) <unsigned integer>" 4
.IX Item """primes"" (OSSL_PKEY_PARAM_RSA_PRIMES) <unsigned integer>"
The value should be the number of primes for the generated \fBRSA\fR key.  The
default is 2.  It isn't permitted to specify a larger number of primes than
10.  Additionally, the number of primes is limited by the length of the key
being generated so the maximum number could be less.
Some providers may only support a value of 2.
.IP """e"" (\fBOSSL_PKEY_PARAM_RSA_E\fR) <unsigned integer>" 4
.IX Item """e"" (OSSL_PKEY_PARAM_RSA_E) <unsigned integer>"
The RSA "e" value. The value may be any odd number greater than or equal to
65537. The default value is 65537.
For legacy reasons a value of 3 is currently accepted but is deprecated.
.SS "RSA key generation parameters for FIPS module testing"
.IX Subsection "RSA key generation parameters for FIPS module testing"
When generating RSA keys, the following additional key generation parameters may
be used for algorithm testing purposes only. Do not use these to generate
RSA keys for a production environment.
.IP """xp"" (\fBOSSL_PKEY_PARAM_RSA_TEST_XP\fR) <unsigned integer>" 4
.IX Item """xp"" (OSSL_PKEY_PARAM_RSA_TEST_XP) <unsigned integer>"
.PD 0
.IP """xq"" (\fBOSSL_PKEY_PARAM_RSA_TEST_XQ\fR) <unsigned integer>" 4
.IX Item """xq"" (OSSL_PKEY_PARAM_RSA_TEST_XQ) <unsigned integer>"
.PD
These 2 fields are normally randomly generated and are used to generate "p" and
"q".
.IP """xp1"" (\fBOSSL_PKEY_PARAM_RSA_TEST_XP1\fR) <unsigned integer>" 4
.IX Item """xp1"" (OSSL_PKEY_PARAM_RSA_TEST_XP1) <unsigned integer>"
.PD 0
.IP """xp2"" (\fBOSSL_PKEY_PARAM_RSA_TEST_XP2\fR) <unsigned integer>" 4
.IX Item """xp2"" (OSSL_PKEY_PARAM_RSA_TEST_XP2) <unsigned integer>"
.IP """xq1"" (\fBOSSL_PKEY_PARAM_RSA_TEST_XQ1\fR) <unsigned integer>" 4
.IX Item """xq1"" (OSSL_PKEY_PARAM_RSA_TEST_XQ1) <unsigned integer>"
.IP """xq2"" (\fBOSSL_PKEY_PARAM_RSA_TEST_XQ2\fR) <unsigned integer>" 4
.IX Item """xq2"" (OSSL_PKEY_PARAM_RSA_TEST_XQ2) <unsigned integer>"
.PD
These 4 fields are normally randomly generated. The prime factors "p1", "p2",
"q1" and "q2" are determined from these values.
.SS "RSA key parameters for FIPS module testing"
.IX Subsection "RSA key parameters for FIPS module testing"
The following intermediate values can be retrieved only if the values
specified in "RSA key generation parameters for FIPS module testing" are set.
These should not be accessed in a production environment.
.IP """p1"" (\fBOSSL_PKEY_PARAM_RSA_TEST_P1\fR) <unsigned integer>" 4
.IX Item """p1"" (OSSL_PKEY_PARAM_RSA_TEST_P1) <unsigned integer>"
.PD 0
.IP """p2"" (\fBOSSL_PKEY_PARAM_RSA_TEST_P2\fR) <unsigned integer>" 4
.IX Item """p2"" (OSSL_PKEY_PARAM_RSA_TEST_P2) <unsigned integer>"
.IP """q1"" (\fBOSSL_PKEY_PARAM_RSA_TEST_Q1\fR) <unsigned integer>" 4
.IX Item """q1"" (OSSL_PKEY_PARAM_RSA_TEST_Q1) <unsigned integer>"
.IP """q2"" (\fBOSSL_PKEY_PARAM_RSA_TEST_Q2\fR) <unsigned integer>" 4
.IX Item """q2"" (OSSL_PKEY_PARAM_RSA_TEST_Q2) <unsigned integer>"
.PD
The auxiliary probable primes.
.SS "RSA key validation"
.IX Subsection "RSA key validation"
For RSA keys, \fBEVP_PKEY_param_check\fR\|(3) and \fBEVP_PKEY_param_check_quick\fR\|(3)
both return 1 unconditionally.
.PP
For RSA keys, \fBEVP_PKEY_public_check\fR\|(3) conforms to the SP800\-56Br1 \fIpublic key
check\fR when the OpenSSL FIPS provider is used. The OpenSSL default provider
performs similar tests but relaxes the keysize restrictions for backwards
compatibility.
.PP
For RSA keys, \fBEVP_PKEY_public_check_quick\fR\|(3) is the same as
\&\fBEVP_PKEY_public_check\fR\|(3).
.PP
For RSA keys, \fBEVP_PKEY_private_check\fR\|(3) conforms to the SP800\-56Br1
\&\fIprivate key test\fR.
.PP
For RSA keys, \fBEVP_PKEY_pairwise_check\fR\|(3) conforms to the
SP800\-56Br1 \fIKeyPair Validation check\fR for the OpenSSL FIPS provider. The
OpenSSL default provider allows testing of the validity of multi-primes.
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
.IP FIPS186\-4 4
.IX Item "FIPS186-4"
Section B.3.6  Generation of Probable Primes with Conditions Based on
Auxiliary Probable Primes
.IP "RFC 8017, excluding RSA-PSS and RSA-OAEP" 4
.IX Item "RFC 8017, excluding RSA-PSS and RSA-OAEP"
.SH EXAMPLES
.IX Header "EXAMPLES"
An \fBEVP_PKEY\fR context can be obtained by calling:
.PP
.Vb 2
\&    EVP_PKEY_CTX *pctx =
\&        EVP_PKEY_CTX_new_from_name(NULL, "RSA", NULL);
.Ve
.PP
An \fBRSA\fR key can be generated simply like this:
.PP
.Vb 1
\&    pkey = EVP_RSA_gen(4096);
.Ve
.PP
or like this:
.PP
.Vb 3
\&    EVP_PKEY *pkey = NULL;
\&    EVP_PKEY_CTX *pctx =
\&        EVP_PKEY_CTX_new_from_name(NULL, "RSA", NULL);
\&
\&    EVP_PKEY_keygen_init(pctx);
\&    EVP_PKEY_generate(pctx, &pkey);
\&    EVP_PKEY_CTX_free(pctx);
.Ve
.PP
An \fBRSA\fR key can be generated with key generation parameters:
.PP
.Vb 5
\&    unsigned int primes = 3;
\&    unsigned int bits = 4096;
\&    OSSL_PARAM params[3];
\&    EVP_PKEY *pkey = NULL;
\&    EVP_PKEY_CTX *pctx = EVP_PKEY_CTX_new_from_name(NULL, "RSA", NULL);
\&
\&    EVP_PKEY_keygen_init(pctx);
\&
\&    params[0] = OSSL_PARAM_construct_uint("bits", &bits);
\&    params[1] = OSSL_PARAM_construct_uint("primes", &primes);
\&    params[2] = OSSL_PARAM_construct_end();
\&    EVP_PKEY_CTX_set_params(pctx, params);
\&
\&    EVP_PKEY_generate(pctx, &pkey);
\&    EVP_PKEY_print_private(bio_out, pkey, 0, NULL);
\&    EVP_PKEY_CTX_free(pctx);
.Ve
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_RSA_gen\fR\|(3), \fBEVP_KEYMGMT\fR\|(3), \fBEVP_PKEY\fR\|(3), \fBprovider\-keymgmt\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
