.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_KDF-SS 7ossl"
.TH EVP_KDF-SS 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_KDF\-SS \- The Single Step / One Step EVP_KDF implementation
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The EVP_KDF\-SS algorithm implements the Single Step key derivation function (SSKDF).
SSKDF derives a key using input such as a shared secret key (that was generated
during the execution of a key establishment scheme) and fixedinfo.
SSKDF is also informally referred to as 'Concat KDF'.
.SS "Auxiliary function"
.IX Subsection "Auxiliary function"
The implementation uses a selectable auxiliary function H, which can be one of:
.IP "\fBH(x) = hash(x, digest=md)\fR" 4
.IX Item "H(x) = hash(x, digest=md)"
.PD 0
.IP "\fBH(x) = HMAC_hash(x, key=salt, digest=md)\fR" 4
.IX Item "H(x) = HMAC_hash(x, key=salt, digest=md)"
.IP "\fBH(x) = KMACxxx(x, key=salt, custom=""KDF"", outlen=mac_size)\fR" 4
.IX Item "H(x) = KMACxxx(x, key=salt, custom=""KDF"", outlen=mac_size)"
.PD
.PP
Both the HMAC and KMAC implementations set the key using the 'salt' value.
The hash and HMAC also require the digest to be set.
.SS Identity
.IX Subsection "Identity"
"SSKDF" is the name for this implementation; it
can be used with the \fBEVP_KDF_fetch()\fR function.
.SS "Supported parameters"
.IX Subsection "Supported parameters"
The supported parameters are:
.IP """properties"" (\fBOSSL_KDF_PARAM_PROPERTIES\fR) <UTF8 string>" 4
.IX Item """properties"" (OSSL_KDF_PARAM_PROPERTIES) <UTF8 string>"
.PD 0
.IP """digest"" (\fBOSSL_KDF_PARAM_DIGEST\fR) <UTF8 string>" 4
.IX Item """digest"" (OSSL_KDF_PARAM_DIGEST) <UTF8 string>"
.PD
This parameter is ignored for KMAC.
.IP """mac"" (\fBOSSL_KDF_PARAM_MAC\fR) <UTF8 string>" 4
.IX Item """mac"" (OSSL_KDF_PARAM_MAC) <UTF8 string>"
.PD 0
.IP """maclen"" (\fBOSSL_KDF_PARAM_MAC_SIZE\fR) <unsigned integer>" 4
.IX Item """maclen"" (OSSL_KDF_PARAM_MAC_SIZE) <unsigned integer>"
.IP """salt"" (\fBOSSL_KDF_PARAM_SALT\fR) <octet string>" 4
.IX Item """salt"" (OSSL_KDF_PARAM_SALT) <octet string>"
.PD
These parameters work as described in "PARAMETERS" in \fBEVP_KDF\fR\|(3).
.IP """key"" (\fBOSSL_KDF_PARAM_SECRET\fR) <octet string>" 4
.IX Item """key"" (OSSL_KDF_PARAM_SECRET) <octet string>"
This parameter set the shared secret that is used for key derivation.
.IP """info"" (\fBOSSL_KDF_PARAM_INFO\fR) <octet string>" 4
.IX Item """info"" (OSSL_KDF_PARAM_INFO) <octet string>"
This parameter sets an optional value for fixedinfo, also known as otherinfo.
.SH NOTES
.IX Header "NOTES"
A context for SSKDF can be obtained by calling:
.PP
.Vb 2
\& EVP_KDF *kdf = EVP_KDF_fetch(NULL, "SSKDF", NULL);
\& EVP_KDF_CTX *kctx = EVP_KDF_CTX_new(kdf);
.Ve
.PP
The output length of an SSKDF is specified via the \fIkeylen\fR
parameter to the \fBEVP_KDF_derive\fR\|(3) function.
.SH EXAMPLES
.IX Header "EXAMPLES"
This example derives 10 bytes using H(x) = SHA\-256, with the secret key "secret"
and fixedinfo value "label":
.PP
.Vb 4
\& EVP_KDF *kdf;
\& EVP_KDF_CTX *kctx;
\& unsigned char out[10];
\& OSSL_PARAM params[4], *p = params;
\&
\& kdf = EVP_KDF_fetch(NULL, "SSKDF", NULL);
\& kctx = EVP_KDF_CTX_new(kdf);
\& EVP_KDF_free(kdf);
\&
\& *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_DIGEST,
\&                                         SN_sha256, strlen(SN_sha256));
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_KEY,
\&                                          "secret", (size_t)6);
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_INFO,
\&                                          "label", (size_t)5);
\& *p = OSSL_PARAM_construct_end();
\& if (EVP_KDF_derive(kctx, out, sizeof(out), params) <= 0) {
\&     error("EVP_KDF_derive");
\& }
\&
\& EVP_KDF_CTX_free(kctx);
.Ve
.PP
This example derives 10 bytes using H(x) = HMAC(SHA\-256), with the secret key "secret",
fixedinfo value "label" and salt "salt":
.PP
.Vb 4
\& EVP_KDF *kdf;
\& EVP_KDF_CTX *kctx;
\& unsigned char out[10];
\& OSSL_PARAM params[6], *p = params;
\&
\& kdf = EVP_KDF_fetch(NULL, "SSKDF", NULL);
\& kctx = EVP_KDF_CTX_new(kdf);
\& EVP_KDF_free(kdf);
\&
\& *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_MAC,
\&                                         SN_hmac, strlen(SN_hmac));
\& *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_DIGEST,
\&                                         SN_sha256, strlen(SN_sha256));
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SECRET,
\&                                          "secret", (size_t)6);
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_INFO,
\&                                          "label", (size_t)5);
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SALT,
\&                                          "salt", (size_t)4);
\& *p = OSSL_PARAM_construct_end();
\& if (EVP_KDF_derive(kctx, out, sizeof(out), params) <= 0) {
\&     error("EVP_KDF_derive");
\& }
\&
\& EVP_KDF_CTX_free(kctx);
.Ve
.PP
This example derives 10 bytes using H(x) = KMAC128(x,salt,outlen), with the secret key "secret"
fixedinfo value "label", salt of "salt" and KMAC outlen of 20:
.PP
.Vb 4
\& EVP_KDF *kdf;
\& EVP_KDF_CTX *kctx;
\& unsigned char out[10];
\& OSSL_PARAM params[6], *p = params;
\&
\& kdf = EVP_KDF_fetch(NULL, "SSKDF", NULL);
\& kctx = EVP_KDF_CTX_new(kdf);
\& EVP_KDF_free(kdf);
\&
\& *p++ = OSSL_PARAM_construct_utf8_string(OSSL_KDF_PARAM_MAC,
\&                                         SN_kmac128, strlen(SN_kmac128));
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SECRET,
\&                                          "secret", (size_t)6);
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_INFO,
\&                                          "label", (size_t)5);
\& *p++ = OSSL_PARAM_construct_octet_string(OSSL_KDF_PARAM_SALT,
\&                                          "salt", (size_t)4);
\& *p++ = OSSL_PARAM_construct_size_t(OSSL_KDF_PARAM_MAC_SIZE, (size_t)20);
\& *p = OSSL_PARAM_construct_end();
\& if (EVP_KDF_derive(kctx, out, sizeof(out), params) <= 0) {
\&     error("EVP_KDF_derive");
\& }
\&
\& EVP_KDF_CTX_free(kctx);
.Ve
.SH "CONFORMING TO"
.IX Header "CONFORMING TO"
NIST SP800\-56Cr1.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_KDF\fR\|(3),
\&\fBEVP_KDF_CTX_new\fR\|(3),
\&\fBEVP_KDF_CTX_free\fR\|(3),
\&\fBEVP_KDF_CTX_set_params\fR\|(3),
\&\fBEVP_KDF_CTX_get_kdf_size\fR\|(3),
\&\fBEVP_KDF_derive\fR\|(3),
"PARAMETERS" in \fBEVP_KDF\fR\|(3)
.SH HISTORY
.IX Header "HISTORY"
This functionality was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2023 The OpenSSL Project Authors. All Rights Reserved.  Copyright
(c) 2019, Oracle and/or its affiliates.  All rights reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
