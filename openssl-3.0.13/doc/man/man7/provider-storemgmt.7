.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "PROVIDER-STOREMGMT 7ossl"
.TH PROVIDER-STOREMGMT 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
provider\-storemgmt \- The OSSL_STORE library <\-> provider functions
.SH SYNOPSIS
.IX Header "SYNOPSIS"
.Vb 1
\& #include <openssl/core_dispatch.h>
\&
\& /*
\&  * None of these are actual functions, but are displayed like this for
\&  * the function signatures for functions that are offered as function
\&  * pointers in OSSL_DISPATCH arrays.
\&  */
\&
\& void *OSSL_FUNC_store_open(void *provctx, const char *uri);
\& void *OSSL_FUNC_store_attach(void *provctx, OSSL_CORE_BIO *bio);
\& const OSSL_PARAM *store_settable_ctx_params(void *provctx);
\& int OSSL_FUNC_store_set_ctx_params(void *loaderctx, const OSSL_PARAM[]);
\& int OSSL_FUNC_store_load(void *loaderctx,
\&                          OSSL_CALLBACK *object_cb, void *object_cbarg,
\&                          OSSL_PASSPHRASE_CALLBACK *pw_cb, void *pw_cbarg);
\& int OSSL_FUNC_store_eof(void *loaderctx);
\& int OSSL_FUNC_store_close(void *loaderctx);
\&
\& int OSSL_FUNC_store_export_object
\&     (void *loaderctx, const void *objref, size_t objref_sz,
\&      OSSL_CALLBACK *export_cb, void *export_cbarg);
.Ve
.SH DESCRIPTION
.IX Header "DESCRIPTION"
The STORE operation is the provider side of the \fBossl_store\fR\|(7) API.
.PP
The primary responsibility of the STORE operation is to load all sorts
of objects from a container indicated by URI.  These objects are given
to the OpenSSL library in provider-native object abstraction form (see
\&\fBprovider\-object\fR\|(7)).  The OpenSSL library is then responsible for
passing on that abstraction to suitable provided functions.
.PP
Examples of functions that the OpenSSL library can pass the abstraction to
include \fBOSSL_FUNC_keymgmt_load()\fR (\fBprovider\-keymgmt\fR\|(7)),
\&\fBOSSL_FUNC_store_export_object()\fR (which exports the object in parameterized
form).
.PP
All "functions" mentioned here are passed as function pointers between
\&\fIlibcrypto\fR and the provider in \fBOSSL_DISPATCH\fR\|(3) arrays via
\&\fBOSSL_ALGORITHM\fR\|(3) arrays that are returned by the provider's
\&\fBprovider_query_operation()\fR function
(see "Provider Functions" in \fBprovider\-base\fR\|(7)).
.PP
All these "functions" have a corresponding function type definition named
\&\fBOSSL_FUNC_{name}_fn\fR, and a helper function to retrieve the function pointer
from a \fBOSSL_DISPATCH\fR\|(3) element named \fBOSSL_get_{name}\fR.
For example, the "function" \fBOSSL_FUNC_store_attach()\fR has these:
.PP
.Vb 4
\& typedef void *(OSSL_FUNC_store_attach_fn)(void *provctx,
\&                                           OSSL_CORE_BIO * bio);
\& static ossl_inline OSSL_FUNC_store_attach_fn
\&     OSSL_FUNC_store_attach(const OSSL_DISPATCH *opf);
.Ve
.PP
\&\fBOSSL_DISPATCH\fR\|(3) arrays are indexed by numbers that are provided as macros
in \fBopenssl\-core_dispatch.h\fR\|(7), as follows:
.PP
.Vb 8
\& OSSL_FUNC_store_open                 OSSL_FUNC_STORE_OPEN
\& OSSL_FUNC_store_attach               OSSL_FUNC_STORE_ATTACH
\& OSSL_FUNC_store_settable_ctx_params  OSSL_FUNC_STORE_SETTABLE_CTX_PARAMS
\& OSSL_FUNC_store_set_ctx_params       OSSL_FUNC_STORE_SET_CTX_PARAMS
\& OSSL_FUNC_store_load                 OSSL_FUNC_STORE_LOAD
\& OSSL_FUNC_store_eof                  OSSL_FUNC_STORE_EOF
\& OSSL_FUNC_store_close                OSSL_FUNC_STORE_CLOSE
\& OSSL_FUNC_store_export_object        OSSL_FUNC_STORE_EXPORT_OBJECT
.Ve
.SS Functions
.IX Subsection "Functions"
\&\fBOSSL_FUNC_store_open()\fR should create a provider side context with data based
on the input \fIuri\fR.  The implementation is entirely responsible for the
interpretation of the URI.
.PP
\&\fBOSSL_FUNC_store_attach()\fR should create a provider side context with the core
\&\fBBIO\fR \fIbio\fR attached.  This is an alternative to using a URI to find storage,
supporting \fBOSSL_STORE_attach\fR\|(3).
.PP
\&\fBOSSL_FUNC_store_settable_ctx_params()\fR should return a constant array of
descriptor \fBOSSL_PARAM\fR\|(3), for parameters that \fBOSSL_FUNC_store_set_ctx_params()\fR
can handle.
.PP
\&\fBOSSL_FUNC_store_set_ctx_params()\fR should set additional parameters, such as what
kind of data to expect, search criteria, and so on.  More on those below, in
"Load Parameters".  Whether unrecognised parameters are an error or simply
ignored is at the implementation's discretion.
Passing NULL for \fIparams\fR should return true.
.PP
\&\fBOSSL_FUNC_store_load()\fR loads the next object from the URI opened by
\&\fBOSSL_FUNC_store_open()\fR, creates an object abstraction for it (see
\&\fBprovider\-object\fR\|(7)), and calls \fIobject_cb\fR with it as well as
\&\fIobject_cbarg\fR.  \fIobject_cb\fR will then interpret the object abstraction
and do what it can to wrap it or decode it into an OpenSSL structure.  In
case a passphrase needs to be prompted to unlock an object, \fIpw_cb\fR should
be called.
.PP
\&\fBOSSL_FUNC_store_eof()\fR indicates if the end of the set of objects from the
URI has been reached.  When that happens, there's no point trying to do any
further loading.
.PP
\&\fBOSSL_FUNC_store_close()\fR frees the provider side context \fIctx\fR.
.PP
When a provider-native object is created by a store manager it would be unsuitable
for direct use with a foreign provider. The export function allows for
exporting the object to that foreign provider if the foreign provider
supports the type of the object and provides an import function.
.PP
\&\fBOSSL_FUNC_store_export_object()\fR should export the object of size \fIobjref_sz\fR
referenced by \fIobjref\fR as an \fBOSSL_PARAM\fR\|(3) array and pass that to the
\&\fIexport_cb\fR as well as the given \fIexport_cbarg\fR.
.SS "Load Parameters"
.IX Subsection "Load Parameters"
.IP """expect"" (\fBOSSL_STORE_PARAM_EXPECT\fR) <integer>" 4
.IX Item """expect"" (OSSL_STORE_PARAM_EXPECT) <integer>"
Is a hint of what type of data the OpenSSL library expects to get.
This is only useful for optimization, as the library will check that the
object types match the expectation too.
.Sp
The number that can be given through this parameter is found in
\&\fI<openssl/store.h>\fR, with the macros having names starting with
\&\f(CW\*(C`OSSL_STORE_INFO_\*(C'\fR.  These are further described in
"SUPPORTED OBJECTS" in \fBOSSL_STORE_INFO\fR\|(3).
.IP """subject"" (\fBOSSL_STORE_PARAM_SUBJECT\fR) <octet string>" 4
.IX Item """subject"" (OSSL_STORE_PARAM_SUBJECT) <octet string>"
Indicates that the caller wants to search for an object with the given
subject associated.  This can be used to select specific certificates
by subject.
.Sp
The contents of the octet string is expected to be in DER form.
.IP """issuer"" (\fBOSSL_STORE_PARAM_ISSUER\fR) <octet string>" 4
.IX Item """issuer"" (OSSL_STORE_PARAM_ISSUER) <octet string>"
Indicates that the caller wants to search for an object with the given
issuer associated.  This can be used to select specific certificates
by issuer.
.Sp
The contents of the octet string is expected to be in DER form.
.IP """serial"" (\fBOSSL_STORE_PARAM_SERIAL\fR) <integer>" 4
.IX Item """serial"" (OSSL_STORE_PARAM_SERIAL) <integer>"
Indicates that the caller wants to search for an object with the given
serial number associated.
.IP """digest"" (\fBOSSL_STORE_PARAM_DIGEST\fR) <UTF8 string>" 4
.IX Item """digest"" (OSSL_STORE_PARAM_DIGEST) <UTF8 string>"
.PD 0
.IP """fingerprint"" (\fBOSSL_STORE_PARAM_FINGERPRINT\fR) <octet string>" 4
.IX Item """fingerprint"" (OSSL_STORE_PARAM_FINGERPRINT) <octet string>"
.PD
Indicates that the caller wants to search for an object with the given
fingerprint, computed with the given digest.
.IP """alias"" (\fBOSSL_STORE_PARAM_ALIAS\fR) <UTF8 string>" 4
.IX Item """alias"" (OSSL_STORE_PARAM_ALIAS) <UTF8 string>"
Indicates that the caller wants to search for an object with the given
alias (some call it a "friendly name").
.IP """properties"" (\fBOSSL_STORE_PARAM_PROPERTIES\fR) <utf8 string>" 4
.IX Item """properties"" (OSSL_STORE_PARAM_PROPERTIES) <utf8 string>"
Property string to use when querying for algorithms such as the \fBOSSL_DECODER\fR
decoder implementations.
.IP """input-type"" (\fBOSSL_STORE_PARAM_INPUT_TYPE\fR) <utf8 string>" 4
.IX Item """input-type"" (OSSL_STORE_PARAM_INPUT_TYPE) <utf8 string>"
Type of the input format as a hint to use when decoding the objects in the
store.
.PP
Several of these search criteria may be combined.  For example, to
search for a certificate by issuer+serial, both the "issuer" and the
"serial" parameters will be given.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBprovider\fR\|(7)
.SH HISTORY
.IX Header "HISTORY"
The STORE interface was introduced in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2022 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
