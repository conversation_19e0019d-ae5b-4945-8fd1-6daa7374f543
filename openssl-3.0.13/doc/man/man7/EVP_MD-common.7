.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "EVP_MD-COMMON 7ossl"
.TH EVP_MD-COMMON 7ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
EVP_MD\-common \- The OpenSSL EVP_MD implementations, common things
.SH DESCRIPTION
.IX Header "DESCRIPTION"
All the OpenSSL EVP_MD implementations understand the following
\&\fBOSSL_PARAM\fR\|(3) entries that are
gettable with \fBEVP_MD_get_params\fR\|(3), as well as these:
.IP """blocksize"" (\fBOSSL_DIGEST_PARAM_BLOCK_SIZE\fR) <unsigned integer>" 4
.IX Item """blocksize"" (OSSL_DIGEST_PARAM_BLOCK_SIZE) <unsigned integer>"
The digest block size.
The length of the "blocksize" parameter should not exceed that of a
\&\fBsize_t\fR.
.Sp
This value can also be retrieved with \fBEVP_MD_get_block_size\fR\|(3).
.IP """size"" (\fBOSSL_DIGEST_PARAM_SIZE\fR) <unsigned integer>" 4
.IX Item """size"" (OSSL_DIGEST_PARAM_SIZE) <unsigned integer>"
The digest output size.
The length of the "size" parameter should not exceed that of a \fBsize_t\fR.
.Sp
This value can also be retrieved with \fBEVP_MD_get_size\fR\|(3).
.IP """flags"" (\fBOSSL_DIGEST_PARAM_FLAGS\fR) <unsigned integer>" 4
.IX Item """flags"" (OSSL_DIGEST_PARAM_FLAGS) <unsigned integer>"
Diverse flags that describe exceptional behaviour for the digest.
These flags are described in "DESCRIPTION" in \fBEVP_MD_meth_set_flags\fR\|(3).
.Sp
The length of the "flags" parameter should equal that of an
\&\fBunsigned long int\fR.
.Sp
This value can also be retrieved with \fBEVP_MD_get_flags\fR\|(3).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBEVP_MD_get_params\fR\|(3), \fBprovider\-digest\fR\|(7)
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2020\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
