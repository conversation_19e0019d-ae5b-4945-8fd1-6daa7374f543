.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "FIPS_CONFIG 5ossl"
.TH FIPS_CONFIG 5ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
fips_config \- OpenSSL FIPS configuration
.SH DESCRIPTION
.IX Header "DESCRIPTION"
A separate configuration file, using the OpenSSL \fBconfig\fR\|(5) syntax,
is used to hold information about the FIPS module. This includes a digest
of the shared library file, and status about the self-testing.
This data is used automatically by the module itself for two
purposes:
.IP "\- Run the startup FIPS self-test known answer tests (KATS)." 4
.IX Item "- Run the startup FIPS self-test known answer tests (KATS)."
This is normally done once, at installation time, but may also be set up to
run each time the module is used.
.IP "\- Verify the module's checksum." 4
.IX Item "- Verify the module's checksum."
This is done each time the module is used.
.PP
This file is generated by the \fBopenssl\-fipsinstall\fR\|(1) program, and
used internally by the FIPS module during its initialization.
.PP
The following options are supported. They should all appear in a section
whose name is identified by the \fBfips\fR option in the \fBproviders\fR
section, as described in "Provider Configuration Module" in \fBconfig\fR\|(5).
.IP \fBactivate\fR 4
.IX Item "activate"
If present, the module is activated. The value assigned to this name is not
significant.
.IP \fBinstall-version\fR 4
.IX Item "install-version"
A version number for the fips install process. Should be 1.
.IP \fBconditional-errors\fR 4
.IX Item "conditional-errors"
The FIPS module normally enters an internal error mode if any self test fails.
Once this error mode is active, no services or cryptographic algorithms are
accessible from this point on.
Continuous tests are a subset of the self tests (e.g., a key pair test during key
generation, or the CRNG output test).
Setting this value to \f(CW0\fR allows the error mode to not be triggered if any
continuous test fails. The default value of \f(CW1\fR will trigger the error mode.
Regardless of the value, the operation (e.g., key generation) that called the
continuous test will return an error code if its continuous test fails. The
operation may then be retried if the error mode has not been triggered.
.IP \fBsecurity-checks\fR 4
.IX Item "security-checks"
This indicates if run-time checks related to enforcement of security parameters
such as minimum security strength of keys and approved curve names are used.
A value of '1' will perform the checks, otherwise if the value is '0' the checks
are not performed and FIPS compliance must be done by procedures documented in
the relevant Security Policy.
.IP \fBmodule-mac\fR 4
.IX Item "module-mac"
The calculated MAC of the FIPS provider file.
.IP \fBinstall-status\fR 4
.IX Item "install-status"
An indicator that the self-tests were successfully run.
This should only be written after the module has
successfully passed its self tests during installation.
If this field is not present, then the self tests will run when the module
loads.
.IP \fBinstall-mac\fR 4
.IX Item "install-mac"
A MAC of the value of the \fBinstall-status\fR option, to prevent accidental
changes to that value.
It is written-to at the same time as \fBinstall-status\fR is updated.
.PP
For example:
.PP
.Vb 8
\& [fips_sect]
\& activate = 1
\& install\-version = 1
\& conditional\-errors = 1
\& security\-checks = 1
\& module\-mac = 41:D0:FA:C2:5D:41:75:CD:7D:C3:90:55:6F:A4:DC
\& install\-mac = FE:10:13:5A:D3:B4:C7:82:1B:1E:17:4C:AC:84:0C
\& install\-status = INSTALL_SELF_TEST_KATS_RUN
.Ve
.SH NOTES
.IX Header "NOTES"
When using the FIPS provider, it is recommended that the
\&\fBconfig_diagnostics\fR option is enabled to prevent accidental use of
non-FIPS validated algorithms via broken or mistaken configuration.
See \fBconfig\fR\|(5).
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBconfig\fR\|(5)
\&\fBopenssl\-fipsinstall\fR\|(1)
.SH HISTORY
.IX Header "HISTORY"
This functionality was added in OpenSSL 3.0.
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2019\-2021 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
