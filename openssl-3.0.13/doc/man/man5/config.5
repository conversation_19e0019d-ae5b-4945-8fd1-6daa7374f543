.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "CONFIG 5ossl"
.TH CONFIG 5ossl 2024-01-30 3.0.13 OpenSSL
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
config \- OpenSSL CONF library configuration files
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This page documents the syntax of OpenSSL configuration files,
as parsed by \fBNCONF_load\fR\|(3) and related functions.
This format is used by many of the OpenSSL commands, and to
initialize the libraries when used by any application.
.PP
The first part describes the general syntax of the configuration
files, and subsequent sections describe the semantics of individual
modules. Other modules are described in \fBfips_config\fR\|(5) and
\&\fBx509v3_config\fR\|(5).
The syntax for defining ASN.1 values is described in
\&\fBASN1_generate_nconf\fR\|(3).
.SH SYNTAX
.IX Header "SYNTAX"
A configuration file is a series of lines.  Blank lines, and whitespace
between the elements of a line, have no significance. A comment starts
with a \fB#\fR character; the rest of the line is ignored. If the \fB#\fR
is the first non-space character in a line, the entire line is ignored.
.SS Directives
.IX Subsection "Directives"
Two directives can be used to control the parsing of configuration files:
\&\fB.include\fR and \fB.pragma\fR.
.PP
For compatibility with older versions of OpenSSL, an equal sign after the
directive will be ignored.  Older versions will treat it as an assignment,
so care should be taken if the difference in semantics is important.
.PP
A file can include other files using the include syntax:
.PP
.Vb 1
\&  .include [=] pathname
.Ve
.PP
If \fBpathname\fR is a simple filename, that file is included directly at
that point.  Included files can have \fB.include\fR statements that specify
other files.  If \fBpathname\fR is a directory, all files within that directory
that have a \f(CW\*(C`.cnf\*(C'\fR or \f(CW\*(C`.conf\*(C'\fR extension will be included.  (This is only
available on systems with POSIX IO support.)  Any sub-directories found
inside the \fBpathname\fR are \fBignored\fR.  Similarly, if a file is opened
while scanning a directory, and that file has an \fB.include\fR directive
that specifies a directory, that is also ignored.
.PP
As a general rule, the \fBpathname\fR should be an absolute path; this can
be enforced with the \fBabspath\fR and \fBincludedir\fR pragmas, described below.
The environment variable \fBOPENSSL_CONF_INCLUDE\fR, if it exists,
is prepended to all relative pathnames.
If the pathname is still relative, it is interpreted based on the
current working directory.
.PP
To require all file inclusions to name absolute paths, use the following
directive:
.PP
.Vb 1
\& .pragma [=] abspath:value
.Ve
.PP
The default behavior, where the \fBvalue\fR is \fBfalse\fR or \fBoff\fR, is to allow
relative paths. To require all \fB.include\fR pathnames to be absolute paths,
use a \fBvalue\fR of \fBtrue\fR or \fBon\fR.
.PP
In these files, the dollar sign, \fB$\fR, is used to reference a variable, as
described below.  On some platforms, however, it is common to treat \fB$\fR
as a regular character in symbol names.  Supporting this behavior can be
done with the following directive:
.PP
.Vb 1
\& .pragma [=] dollarid:value
.Ve
.PP
The default behavior, where the \fBvalue\fR is \fBfalse\fR or \fBoff\fR, is to treat
the dollarsign as indicating a variable name; \f(CW\*(C`foo$bar\*(C'\fR is interpreted as
\&\f(CW\*(C`foo\*(C'\fR followed by the expansion of the variable \f(CW\*(C`bar\*(C'\fR. If \fBvalue\fR is
\&\fBtrue\fR or \fBon\fR, then \f(CW\*(C`foo$bar\*(C'\fR is a single seven-character name and
variable expansions must be specified using braces or parentheses.
.PP
.Vb 1
\& .pragma [=] includedir:value
.Ve
.PP
If a relative pathname is specified in the \fB.include\fR directive, and
the \fBOPENSSL_CONF_INCLUDE\fR environment variable doesn't exist, then
the value of the \fBincludedir\fR pragma, if it exists, is prepended to the
pathname.
.SS Settings
.IX Subsection "Settings"
A configuration file is divided into a number of \fIsections\fR.  A section
begins with the section name in square brackets, and ends when a new
section starts, or at the end of the file.  The section name can consist
of alphanumeric characters and underscores.
Whitespace between the name and the brackets is removed.
.PP
The first section of a configuration file is special and is referred to
as the \fBdefault\fR section. This section is usually unnamed and spans from
the start of file until the first named section. When a name is being
looked up, it is first looked up in the current or named section,
and then the default section if necessary.
.PP
The environment is mapped onto a section called \fBENV\fR.
.PP
Within a section are a series of name/value assignments, described in more
detail below.  As a reminder, the square brackets shown in this example
are required, not optional:
.PP
.Vb 7
\& [ section ]
\& name1 = This is value1
\& name2 = Another value
\& ...
\& [ newsection ]
\& name1 = New value1
\& name3 = Value 3
.Ve
.PP
The \fBname\fR can contain any alphanumeric characters as well as a few
punctuation symbols such as \fB.\fR \fB,\fR \fB;\fR and \fB_\fR.
Whitespace after the name and before the equal sign is ignored.
.PP
If a name is repeated in the same section, then all but the last
value are ignored. In certain circumstances, such as with
Certificate DNs, the same field may occur multiple times.
In order to support this, commands like \fBopenssl\-req\fR\|(1) ignore any
leading text that is preceded with a period. For example:
.PP
.Vb 2
\& 1.OU = First OU
\& 2.OU = Second OU
.Ve
.PP
The \fBvalue\fR consists of the string following the \fB=\fR character until end
of line with any leading and trailing whitespace removed.
.PP
The value string undergoes variable expansion. The text \f(CW$var\fR or \f(CW\*(C`${var}\*(C'\fR
inserts the value of the named variable from the current section.
To use a value from another section use \f(CW$section::name\fR
or \f(CW\*(C`${section::name}\*(C'\fR.
By using \f(CW$ENV::name\fR, the value of the specified environment
variable will be substituted.
.PP
Variables must be defined before their value is referenced, otherwise
an error is flagged and the file will not load.
This can be worked around by specifying a default value in the \fBdefault\fR
section before the variable is used.
.PP
Any name/value settings in an \fBENV\fR section are available
to the configuration file, but are not propagated to the environment.
.PP
It is an error if the value ends up longer than 64k.
.PP
It is possible to escape certain characters by using a single \fB'\fR or
double \fB"\fR quote around the value, or using a backslash \fB\e\fR before the
character,
By making the last character of a line a \fB\e\fR
a \fBvalue\fR string can be spread across multiple lines. In addition
the sequences \fB\en\fR, \fB\er\fR, \fB\eb\fR and \fB\et\fR are recognized.
.PP
The expansion and escape rules as described above that apply to \fBvalue\fR
also apply to the pathname of the \fB.include\fR directive.
.SH "OPENSSL LIBRARY CONFIGURATION"
.IX Header "OPENSSL LIBRARY CONFIGURATION"
The sections below use the informal term \fImodule\fR to refer to a part
of the OpenSSL functionality. This is not the same as the formal term
\&\fIFIPS module\fR, for example.
.PP
The OpenSSL configuration looks up the value of \fBopenssl_conf\fR
in the default section and takes that as the name of a section that specifies
how to configure any modules in the library. It is not an error to leave
any module in its default configuration. An application can specify a
different name by calling \fBCONF_modules_load_file()\fR, for example, directly.
.PP
OpenSSL also looks up the value of \fBconfig_diagnostics\fR.
If this exists and has a nonzero numeric value, any error suppressing flags
passed to \fBCONF_modules_load()\fR will be ignored.
This is useful for diagnosing misconfigurations but its use in
production requires additional consideration.  With this option enabled,
a configuration error will completely prevent access to a service.
Without this option and in the presence of a configuration error, access
will be allowed but the desired configuration will \fBnot\fR be used.
.PP
.Vb 3
\& # These must be in the default section
\& config_diagnostics = 1
\& openssl_conf = openssl_init
\&
\& [openssl_init]
\& oid_section = oids
\& providers = providers
\& alg_section = evp_properties
\& ssl_conf = ssl_configuration
\& engines = engines
\& random = random
\&
\& [oids]
\& ... new oids here ...
\&
\& [providers]
\& ... provider stuff here ...
\&
\& [evp_properties]
\& ... EVP properties here ...
\&
\& [ssl_configuration]
\& ... SSL/TLS configuration properties here ...
\&
\& [engines]
\& ... engine properties here ...
\&
\& [random]
\& ... random properties here ...
.Ve
.PP
The semantics of each module are described below. The phrase "in the
initialization section" refers to the section identified by the
\&\fBopenssl_conf\fR or other name (given as \fBopenssl_init\fR in the
example above).  The examples below assume the configuration above
is used to specify the individual sections.
.SS "ASN.1 Object Identifier Configuration"
.IX Subsection "ASN.1 Object Identifier Configuration"
The name \fBoid_section\fR in the initialization section names the section
containing name/value pairs of OID's.
The name is the short name; the value is an optional long name followed
by a comma, and the numeric value.
While some OpenSSL commands have their own section for specifying OID's,
this section makes them available to all commands and applications.
.PP
.Vb 4
\& [oids]
\& shortName = a very long OID name, *******
\& newoid1 = *******.1
\& some_other_oid = 1.2.3.5
.Ve
.PP
If a full configuration with the above fragment is in the file
\&\fIexample.cnf\fR, then the following command line:
.PP
.Vb 1
\& OPENSSL_CONF=example.cnf openssl asn1parse \-genstr OID:*******.1
.Ve
.PP
will output:
.PP
.Vb 1
\& 0:d=0  hl=2 l=   4 prim: OBJECT            :newoid1
.Ve
.PP
showing that the OID "newoid1" has been added as "*******.1".
.SS "Provider Configuration"
.IX Subsection "Provider Configuration"
The name \fBproviders\fR in the initialization section names the section
containing cryptographic provider configuration. The name/value assignments
in this section each name a provider, and point to the configuration section
for that provider. The provider-specific section is used to specify how
to load the module, activate it, and set other parameters.
.PP
Within a provider section, the following names have meaning:
.IP \fBidentity\fR 4
.IX Item "identity"
This is used to specify an alternate name, overriding the default name
specified in the list of providers.  For example:
.Sp
.Vb 2
\& [providers]
\& foo = foo_provider
\&
\& [foo_provider]
\& identity = my_fips_module
.Ve
.IP \fBmodule\fR 4
.IX Item "module"
Specifies the pathname of the module (typically a shared library) to load.
.IP \fBactivate\fR 4
.IX Item "activate"
If present, the module is activated. The value assigned to this name is not
significant.
.PP
All parameters in the section as well as sub-sections are made
available to the provider.
.PP
\fIDefault provider and its activation\fR
.IX Subsection "Default provider and its activation"
.PP
If no providers are activated explicitly, the default one is activated implicitly.
See \fBOSSL_PROVIDER\-default\fR\|(7) for more details.
.PP
If you add a section explicitly activating any other provider(s),
you most probably need to explicitly activate the default provider,
otherwise it becomes unavailable in openssl. It may make the system remotely unavailable.
.SS "EVP Configuration"
.IX Subsection "EVP Configuration"
The name \fBalg_section\fR in the initialization section names the section
containing algorithmic properties when using the \fBEVP\fR API.
.PP
Within the algorithm properties section, the following names have meaning:
.IP \fBdefault_properties\fR 4
.IX Item "default_properties"
The value may be anything that is acceptable as a property query
string for \fBEVP_set_default_properties()\fR.
.IP "\fBfips_mode\fR (deprecated)" 4
.IX Item "fips_mode (deprecated)"
The value is a boolean that can be \fByes\fR or \fBno\fR.  If the value is
\&\fByes\fR, this is exactly equivalent to:
.Sp
.Vb 1
\& default_properties = fips=yes
.Ve
.Sp
If the value is \fBno\fR, nothing happens. Using this name is deprecated, and
if used, it must be the only name in the section.
.SS "SSL Configuration"
.IX Subsection "SSL Configuration"
The name \fBssl_conf\fR in the initialization section names the section
containing the list of SSL/TLS configurations.
As with the providers, each name in this section identifies a
section with the configuration for that name. For example:
.PP
.Vb 4
\& [ssl_configuration]
\& server = server_tls_config
\& client = client_tls_config
\& system_default = tls_system_default
\&
\& [server_tls_config]
\& ... configuration for SSL/TLS servers ...
\&
\& [client_tls_config]
\& ... configuration for SSL/TLS clients ...
.Ve
.PP
The configuration name \fBsystem_default\fR has a special meaning.  If it
exists, it is applied whenever an \fBSSL_CTX\fR object is created.  For example,
to impose system-wide minimum TLS and DTLS protocol versions:
.PP
.Vb 3
\& [tls_system_default]
\& MinProtocol = TLSv1.2
\& MinProtocol = DTLSv1.2
.Ve
.PP
The minimum TLS protocol is applied to \fBSSL_CTX\fR objects that are TLS-based,
and the minimum DTLS protocol to those are DTLS-based.
The same applies also to maximum versions set with \fBMaxProtocol\fR.
.PP
Each configuration section consists of name/value pairs that are parsed
by \fBSSL_CONF_cmd\|(3)\fR, which will be called by \fBSSL_CTX_config()\fR or
\&\fBSSL_config()\fR, appropriately.  Note that any characters before an initial
dot in the configuration section are ignored, so that the same command can
be used multiple times. This probably is most useful for loading different
key types, as shown here:
.PP
.Vb 3
\& [server_tls_config]
\& RSA.Certificate = server\-rsa.pem
\& ECDSA.Certificate = server\-ecdsa.pem
.Ve
.SS "Engine Configuration"
.IX Subsection "Engine Configuration"
The name \fBengines\fR in the initialization section names the section
containing the list of ENGINE configurations.
As with the providers, each name in this section identifies an engine
with the configuration for that engine.
The engine-specific section is used to specify how to load the engine,
activate it, and set other parameters.
.PP
Within an engine section, the following names have meaning:
.IP \fBengine_id\fR 4
.IX Item "engine_id"
This is used to specify an alternate name, overriding the default name
specified in the list of engines. If present, it must be first.
For example:
.Sp
.Vb 2
\& [engines]
\& foo = foo_engine
\&
\& [foo_engine]
\& engine_id = myfoo
.Ve
.IP \fBdynamic_path\fR 4
.IX Item "dynamic_path"
This loads and adds an ENGINE from the given path. It is equivalent to
sending the ctrls \fBSO_PATH\fR with the path argument followed by \fBLIST_ADD\fR
with value \fB2\fR and \fBLOAD\fR to the dynamic ENGINE.  If this is not the
required behaviour then alternative ctrls can be sent directly to the
dynamic ENGINE using ctrl commands.
.IP \fBinit\fR 4
.IX Item "init"
This specifies whether to initialize the ENGINE. If the value is \fB0\fR the
ENGINE will not be initialized, if the value is \fB1\fR an attempt is made
to initialize
the ENGINE immediately. If the \fBinit\fR command is not present then an
attempt will be made to initialize the ENGINE after all commands in its
section have been processed.
.IP \fBdefault_algorithms\fR 4
.IX Item "default_algorithms"
This sets the default algorithms an ENGINE will supply using the function
\&\fBENGINE_set_default_string()\fR.
.PP
All other names are taken to be the name of a ctrl command that is
sent to the ENGINE, and the value is the argument passed with the command.
The special value \fBEMPTY\fR means no value is sent with the command.
For example:
.PP
.Vb 2
\& [engines]
\& foo = foo_engine
\&
\& [foo_engine]
\& dynamic_path = /some/path/fooengine.so
\& some_ctrl = some_value
\& default_algorithms = ALL
\& other_ctrl = EMPTY
.Ve
.SS "Random Configuration"
.IX Subsection "Random Configuration"
The name \fBrandom\fR in the initialization section names the section
containing the random number generator settings.
.PP
Within the random section, the following names have meaning:
.IP \fBrandom\fR 4
.IX Item "random"
This is used to specify the random bit generator.
For example:
.Sp
.Vb 2
\& [random]
\& random = CTR\-DRBG
.Ve
.Sp
The available random bit generators are:
.RS 4
.IP \fBCTR-DRBG\fR 4
.IX Item "CTR-DRBG"
.PD 0
.IP \fBHASH-DRBG\fR 4
.IX Item "HASH-DRBG"
.IP \fBHMAC-DRBG\fR 4
.IX Item "HMAC-DRBG"
.RE
.RS 4
.RE
.IP \fBcipher\fR 4
.IX Item "cipher"
.PD
This specifies what cipher a \fBCTR-DRBG\fR random bit generator will use.
Other random bit generators ignore this name.
The default value is \fBAES\-256\-CTR\fR.
.IP \fBdigest\fR 4
.IX Item "digest"
This specifies what digest the \fBHASH-DRBG\fR or \fBHMAC-DRBG\fR random bit
generators will use.  Other random bit generators ignore this name.
.IP \fBproperties\fR 4
.IX Item "properties"
This sets the property query used when fetching the random bit generator and
any underlying algorithms.
.IP \fBseed\fR 4
.IX Item "seed"
This sets the randomness source that should be used.  By default \fBSEED-SRC\fR
will be used outside of the FIPS provider.  The FIPS provider uses call backs
to access the same randomness sources from outside the validated boundary.
.IP \fBseed_properties\fR 4
.IX Item "seed_properties"
This sets the property query used when fetching the randomness source.
.SH EXAMPLES
.IX Header "EXAMPLES"
This example shows how to use quoting and escaping.
.PP
.Vb 3
\& # This is the default section.
\& HOME = /temp
\& configdir = $ENV::HOME/config
\&
\& [ section_one ]
\& # Quotes permit leading and trailing whitespace
\& any = " any variable name "
\& other = A string that can \e
\& cover several lines \e
\& by including \e\e characters
\& message = Hello World\en
\&
\& [ section_two ]
\& greeting = $section_one::message
.Ve
.PP
This example shows how to expand environment variables safely.
In this example, the variable \fBtempfile\fR is intended to refer
to a temporary file, and the environment variable \fBTEMP\fR or
\&\fBTMP\fR, if present, specify the directory where the file
should be put.
Since the default section is checked if a variable does not
exist, it is possible to set \fBTMP\fR to default to \fI/tmp\fR, and
\&\fBTEMP\fR to default to \fBTMP\fR.
.PP
.Vb 3
\& # These two lines must be in the default section.
\& TMP = /tmp
\& TEMP = $ENV::TMP
\&
\& # This can be used anywhere
\& tmpfile = ${ENV::TEMP}/tmp.filename
.Ve
.PP
This example shows how to enforce FIPS mode for the application
\&\fIsample\fR.
.PP
.Vb 1
\& sample = fips_config
\&
\& [fips_config]
\& alg_section = evp_properties
\&
\& [evp_properties]
\& default_properties = "fips=yes"
.Ve
.SH ENVIRONMENT
.IX Header "ENVIRONMENT"
.IP \fBOPENSSL_CONF\fR 4
.IX Item "OPENSSL_CONF"
The path to the config file, or the empty string for none.
Ignored in set-user-ID and set-group-ID programs.
.IP \fBOPENSSL_ENGINES\fR 4
.IX Item "OPENSSL_ENGINES"
The path to the engines directory.
Ignored in set-user-ID and set-group-ID programs.
.IP \fBOPENSSL_MODULES\fR 4
.IX Item "OPENSSL_MODULES"
The path to the directory with OpenSSL modules, such as providers.
Ignored in set-user-ID and set-group-ID programs.
.IP \fBOPENSSL_CONF_INCLUDE\fR 4
.IX Item "OPENSSL_CONF_INCLUDE"
The optional path to prepend to all \fB.include\fR paths.
.SH BUGS
.IX Header "BUGS"
There is no way to include characters using the octal \fB\ennn\fR form. Strings
are all null terminated so nulls cannot form part of the value.
.PP
The escaping isn't quite right: if you want to use sequences like \fB\en\fR
you can't use any quote escaping on the same line.
.PP
The limit that only one directory can be opened and read at a time
can be considered a bug and should be fixed.
.SH HISTORY
.IX Header "HISTORY"
An undocumented API, \fBNCONF_WIN32()\fR, used a slightly different set
of parsing rules there were intended to be tailored to
the Microsoft Windows platform.
Specifically, the backslash character was not an escape character and
could be used in pathnames, only the double-quote character was recognized,
and comments began with a semi-colon.
This function was deprecated in OpenSSL 3.0; applications with
configuration files using that syntax will have to be modified.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBopenssl\-x509\fR\|(1), \fBopenssl\-req\fR\|(1), \fBopenssl\-ca\fR\|(1),
\&\fBopenssl\-fipsinstall\fR\|(1),
\&\fBASN1_generate_nconf\fR\|(3),
\&\fBEVP_set_default_properties\fR\|(3),
\&\fBCONF_modules_load\fR\|(3),
\&\fBCONF_modules_load_file\fR\|(3),
\&\fBfips_config\fR\|(5), and
\&\fBx509v3_config\fR\|(5).
.SH COPYRIGHT
.IX Header "COPYRIGHT"
Copyright 2000\-2023 The OpenSSL Project Authors. All Rights Reserved.
.PP
Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
<https://www.openssl.org/source/license.html>.
