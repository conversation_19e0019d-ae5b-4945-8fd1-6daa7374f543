=pod

=begin comment
{- join("\n", @autowarntext) -}

=end comment

=head1 NAME

openssl-dsa - DSA key processing

=head1 SYNOPSIS

B<openssl> B<dsa>
[B<-help>]
[B<-inform> B<DER>|B<PEM>]
[B<-outform> B<DER>|B<PEM>]
[B<-in> I<filename>]
[B<-passin> I<arg>]
[B<-out> I<filename>]
[B<-passout> I<arg>]
[B<-aes128>]
[B<-aes192>]
[B<-aes256>]
[B<-aria128>]
[B<-aria192>]
[B<-aria256>]
[B<-camellia128>]
[B<-camellia192>]
[B<-camellia256>]
[B<-des>]
[B<-des3>]
[B<-idea>]
[B<-text>]
[B<-noout>]
[B<-modulus>]
[B<-pubin>]
[B<-pubout>]
[B<-pvk-strong>]
[B<-pvk-weak>]
[B<-pvk-none>]
{- $OpenSSL::safe::opt_engine_synopsis -}{- $OpenSSL::safe::opt_provider_synopsis -}

=head1 DESCRIPTION

This command processes DSA keys. They can be converted between various
forms and their components printed out. B<Note> This command uses the
traditional SSLeay compatible format for private key encryption: newer
applications should use the more secure PKCS#8 format using the B<pkcs8>

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-inform> B<DER>|B<PEM>

The key input format; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-outform> B<DER>|B<PEM>

The key output format; the default is B<PEM>.
See L<openssl-format-options(1)> for details.

Private keys are a sequence of B<ASN.1 INTEGERS>: the version (zero), B<p>,
B<q>, B<g>, and the public and private key components.  Public keys
are a B<SubjectPublicKeyInfo> structure with the B<DSA> type.

The B<PEM> format also accepts PKCS#8 data.

=item B<-in> I<filename>

This specifies the input filename to read a key from or standard input if this
option is not specified. If the key is encrypted a pass phrase will be
prompted for.

=item B<-out> I<filename>

This specifies the output filename to write a key to or standard output by
is not specified. If any encryption options are set then a pass phrase will be
prompted for. The output filename should B<not> be the same as the input
filename.

=item B<-passin> I<arg>, B<-passout> I<arg>

The password source for the input and output file.
For more information about the format of B<arg>
see L<openssl-passphrase-options(1)>.

=item B<-aes128>, B<-aes192>, B<-aes256>, B<-aria128>, B<-aria192>, B<-aria256>, B<-camellia128>, B<-camellia192>, B<-camellia256>, B<-des>, B<-des3>, B<-idea>

These options encrypt the private key with the specified
cipher before outputting it. A pass phrase is prompted for.
If none of these options is specified the key is written in plain text. This
means that this command can be used to remove the pass phrase from a key
by not giving any encryption option is given, or to add or change the pass
phrase by setting them.
These options can only be used with PEM format output files.

=item B<-text>

Prints out the public, private key components and parameters.

=item B<-noout>

This option prevents output of the encoded version of the key.

=item B<-modulus>

This option prints out the value of the public key component of the key.

=item B<-pubin>

By default, a private key is read from the input file. With this option a
public key is read instead.

=item B<-pubout>

By default, a private key is output. With this option a public
key will be output instead. This option is automatically set if the input is
a public key.

=item B<-pvk-strong>

Enable 'Strong' PVK encoding level (default).

=item B<-pvk-weak>

Enable 'Weak' PVK encoding level.

=item B<-pvk-none>

Don't enforce PVK encoding.

{- $OpenSSL::safe::opt_engine_item -}

{- $OpenSSL::safe::opt_provider_item -}

=back

The L<openssl-pkey(1)> command is capable of performing all the operations
this command can, as well as supporting other public key types.

=head1 EXAMPLES

The documentation for the L<openssl-pkey(1)> command contains examples
equivalent to the ones listed here.

To remove the pass phrase on a DSA private key:

 openssl dsa -in key.pem -out keyout.pem

To encrypt a private key using triple DES:

 openssl dsa -in key.pem -des3 -out keyout.pem

To convert a private key from PEM to DER format:

 openssl dsa -in key.pem -outform DER -out keyout.der

To print out the components of a private key to standard output:

 openssl dsa -in key.pem -text -noout

To just output the public part of a private key:

 openssl dsa -in key.pem -pubout -out pubkey.pem

=head1 SEE ALSO

L<openssl(1)>,
L<openssl-pkey(1)>,
L<openssl-dsaparam(1)>,
L<openssl-gendsa(1)>,
L<openssl-rsa(1)>,
L<openssl-genrsa(1)>

=head1 HISTORY

The B<-engine> option was deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
