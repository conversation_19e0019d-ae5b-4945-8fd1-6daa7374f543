=pod

=begin comment

WARNING: do not edit!
Generated by <PERSON><PERSON><PERSON> from doc/man1/openssl-gendsa.pod.in

=end comment

=head1 NAME

openssl-gendsa - generate a DSA private key from a set of parameters

=head1 SYNOPSIS

B<openssl> B<gendsa>
[B<-help>]
[B<-out> I<filename>]
[B<-passout> I<arg>]
[B<-aes128>]
[B<-aes192>]
[B<-aes256>]
[B<-aria128>]
[B<-aria192>]
[B<-aria256>]
[B<-camellia128>]
[B<-camellia192>]
[B<-camellia256>]
[B<-des>]
[B<-des3>]
[B<-idea>]
[B<-verbose>]
[B<-rand> I<files>]
[B<-writerand> I<file>]
[B<-engine> I<id>]
[B<-provider> I<name>]
[B<-provider-path> I<path>]
[B<-propquery> I<propq>]
[I<paramfile>]

=head1 DESCRIPTION

This command generates a DSA private key from a DSA parameter file
(which will be typically generated by the L<openssl-dsaparam(1)> command).

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-out> I<filename>

Output the key to the specified file. If this argument is not specified then
standard output is used.

=item B<-passout> I<arg>

The passphrase used for the output file.
See L<openssl-passphrase-options(1)>.

=item B<-aes128>, B<-aes192>, B<-aes256>, B<-aria128>, B<-aria192>, B<-aria256>, B<-camellia128>, B<-camellia192>, B<-camellia256>, B<-des>, B<-des3>, B<-idea>

These options encrypt the private key with specified
cipher before outputting it. A pass phrase is prompted for.
If none of these options is specified no encryption is used.

Note that all options must be given before the I<paramfile> argument.
Otherwise they are ignored.

=item B<-verbose>

Print extra details about the operations being performed.

=item B<-rand> I<files>, B<-writerand> I<file>

See L<openssl(1)/Random State Options> for details.

=item B<-engine> I<id>

See L<openssl(1)/Engine Options>.
This option is deprecated.

=item I<paramfile>

The DSA parameter file to use. The parameters in this file determine
the size of the private key. DSA parameters can be generated and
examined using the L<openssl-dsaparam(1)> command.

=item B<-provider> I<name>

=item B<-provider-path> I<path>

=item B<-propquery> I<propq>

See L<openssl(1)/Provider Options>, L<provider(7)>, and L<property(7)>.

=back

=head1 NOTES

DSA key generation is little more than random number generation so it is
much quicker that RSA key generation for example.

=head1 SEE ALSO

L<openssl(1)>,
L<openssl-genpkey(1)>,
L<openssl-dsaparam(1)>,
L<openssl-dsa(1)>,
L<openssl-genrsa(1)>,
L<openssl-rsa(1)>

=head1 HISTORY

The B<-engine> option was deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
