=pod

=begin comment
WARNING: do not edit!
Generated by <PERSON><PERSON><PERSON> from doc/man1/openssl-ec.pod.in

=end comment

=head1 NAME

openssl-ec - EC key processing

=head1 SYNOPSIS

B<openssl> B<ec>
[B<-help>]
[B<-inform> B<DER>|B<PEM>|B<P12>|B<ENGINE>]
[B<-outform> B<DER>|B<PEM>]
[B<-in> I<filename>|I<uri>]
[B<-passin> I<arg>]
[B<-out> I<filename>]
[B<-passout> I<arg>]
[B<-des>]
[B<-des3>]
[B<-idea>]
[B<-text>]
[B<-noout>]
[B<-param_out>]
[B<-pubin>]
[B<-pubout>]
[B<-conv_form> I<arg>]
[B<-param_enc> I<arg>]
[B<-no_public>]
[B<-check>]
[B<-engine> I<id>]
[B<-provider> I<name>]
[B<-provider-path> I<path>]
[B<-propquery> I<propq>]

=head1 DESCRIPTION

The L<openssl-ec(1)> command processes EC keys. They can be converted between
various forms and their components printed out. B<Note> OpenSSL uses the
private key format specified in 'SEC 1: Elliptic Curve Cryptography'
(http://www.secg.org/). To convert an OpenSSL EC private key into the
PKCS#8 private key format use the L<openssl-pkcs8(1)> command.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-inform> B<DER>|B<PEM>|B<P12>|B<ENGINE>

The key input format; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-outform> B<DER>|B<PEM>

The key output format; the default is B<PEM>.
See L<openssl-format-options(1)> for details.

Private keys are an SEC1 private key or PKCS#8 format.
Public keys are a B<SubjectPublicKeyInfo> as specified in IETF RFC 3280.

=item B<-in> I<filename>|I<uri>

This specifies the input to read a key from or standard input if this
option is not specified. If the key is encrypted a pass phrase will be
prompted for.

=item B<-out> I<filename>

This specifies the output filename to write a key to or standard output by
is not specified. If any encryption options are set then a pass phrase will be
prompted for. The output filename should B<not> be the same as the input
filename.

=item B<-passin> I<arg>, B<-passout> I<arg>

The password source for the input and output file.
For more information about the format of B<arg>
see L<openssl-passphrase-options(1)>.

=item B<-des>|B<-des3>|B<-idea>

These options encrypt the private key with the DES, triple DES, IDEA or
any other cipher supported by OpenSSL before outputting it. A pass phrase is
prompted for.
If none of these options is specified the key is written in plain text. This
means that using this command to read in an encrypted key with no
encryption option can be used to remove the pass phrase from a key, or by
setting the encryption options it can be use to add or change the pass phrase.
These options can only be used with PEM format output files.

=item B<-text>

Prints out the public, private key components and parameters.

=item B<-noout>

This option prevents output of the encoded version of the key.

=item B<-param_out>

Print the elliptic curve parameters.

=item B<-pubin>

By default, a private key is read from the input file. With this option a
public key is read instead.

=item B<-pubout>

By default a private key is output. With this option a public
key will be output instead. This option is automatically set if the input is
a public key.

=item B<-conv_form> I<arg>

This specifies how the points on the elliptic curve are converted
into octet strings. Possible values are: B<compressed>, B<uncompressed> (the
default value) and B<hybrid>. For more information regarding
the point conversion forms please read the X9.62 standard.
B<Note> Due to patent issues the B<compressed> option is disabled
by default for binary curves and can be enabled by defining
the preprocessor macro B<OPENSSL_EC_BIN_PT_COMP> at compile time.

=item B<-param_enc> I<arg>

This specifies how the elliptic curve parameters are encoded.
Possible value are: B<named_curve>, i.e. the ec parameters are
specified by an OID, or B<explicit> where the ec parameters are
explicitly given (see RFC 3279 for the definition of the
EC parameters structures). The default value is B<named_curve>.
B<Note> the B<implicitlyCA> alternative, as specified in RFC 3279,
is currently not implemented in OpenSSL.

=item B<-no_public>

This option omits the public key components from the private key output.

=item B<-check>

This option checks the consistency of an EC private or public key.

=item B<-engine> I<id>

See L<openssl(1)/Engine Options>.
This option is deprecated.

=item B<-provider> I<name>

=item B<-provider-path> I<path>

=item B<-propquery> I<propq>

See L<openssl(1)/Provider Options>, L<provider(7)>, and L<property(7)>.

=back

The L<openssl-pkey(1)> command is capable of performing all the operations
this command can, as well as supporting other public key types.

=head1 EXAMPLES

The documentation for the L<openssl-pkey(1)> command contains examples
equivalent to the ones listed here.

To encrypt a private key using triple DES:

 openssl ec -in key.pem -des3 -out keyout.pem

To convert a private key from PEM to DER format:

 openssl ec -in key.pem -outform DER -out keyout.der

To print out the components of a private key to standard output:

 openssl ec -in key.pem -text -noout

To just output the public part of a private key:

 openssl ec -in key.pem -pubout -out pubkey.pem

To change the parameters encoding to B<explicit>:

 openssl ec -in key.pem -param_enc explicit -out keyout.pem

To change the point conversion form to B<compressed>:

 openssl ec -in key.pem -conv_form compressed -out keyout.pem

=head1 SEE ALSO

L<openssl(1)>,
L<openssl-pkey(1)>,
L<openssl-ecparam(1)>,
L<openssl-dsa(1)>,
L<openssl-rsa(1)>

=head1 HISTORY

The B<-engine> option was deprecated in OpenSSL 3.0.

The B<-conv_form> and B<-no_public> options are no longer supported
with keys loaded from an engine in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2003-2022 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
