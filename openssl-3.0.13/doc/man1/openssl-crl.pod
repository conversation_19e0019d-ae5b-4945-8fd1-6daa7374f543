=pod

=begin comment

WARNING: do not edit!
Generated by <PERSON><PERSON>le from doc/man1/openssl-crl.pod.in

=end comment

=head1 NAME

openssl-crl - CRL command

=head1 SYNOPSIS

B<openssl> B<crl>
[B<-help>]
[B<-inform> B<DER>|B<PEM>]
[B<-outform> B<DER>|B<PEM>]
[B<-key> I<filename>]
[B<-keyform> B<DER>|B<PEM>|B<P12>]
[B<-dateopt>]
[B<-text>]
[B<-in> I<filename>]
[B<-out> I<filename>]
[B<-gendelta> I<filename>]
[B<-badsig>]
[B<-verify>]
[B<-noout>]
[B<-hash>]
[B<-hash_old>]
[B<-fingerprint>]
[B<-crlnumber>]
[B<-issuer>]
[B<-lastupdate>]
[B<-nextupdate>]
[B<-nameopt> I<option>]
[B<-CAfile> I<file>]
[B<-no-CAfile>]
[B<-CApath> I<dir>]
[B<-no-CApath>]
[B<-CAstore> I<uri>]
[B<-no-CAstore>]
[B<-provider> I<name>]
[B<-provider-path> I<path>]
[B<-propquery> I<propq>]

=head1 DESCRIPTION

This command processes CRL files in DER or PEM format.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-inform> B<DER>|B<PEM>

The CRL input format; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-outform> B<DER>|B<PEM>

The CRL output format; the default is B<PEM>.
See L<openssl-format-options(1)> for details.

=item B<-key> I<filename>

The private key to be used to sign the CRL.

=item B<-keyform> B<DER>|B<PEM>|B<P12>

The format of the private key file; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-in> I<filename>

This specifies the input filename to read from or standard input if this
option is not specified.

=item B<-out> I<filename>

Specifies the output filename to write to or standard output by
default.

=item B<-gendelta> I<filename>

Output a comparison of the main CRL and the one specified here.

=item B<-badsig>

Corrupt the signature before writing it; this can be useful
for testing.

=item B<-dateopt>

Specify the date output format. Values are: rfc_822 and iso_8601.
Defaults to rfc_822.

=item B<-text>

Print out the CRL in text form.

=item B<-verify>

Verify the signature in the CRL.

=item B<-noout>

Don't output the encoded version of the CRL.

=item B<-fingerprint>

Output the fingerprint of the CRL.

=item B<-crlnumber>

Output the number of the CRL.

=item B<-hash>

Output a hash of the issuer name. This can be use to lookup CRLs in
a directory by issuer name.

=item B<-hash_old>

Outputs the "hash" of the CRL issuer name using the older algorithm
as used by OpenSSL before version 1.0.0.

=item B<-issuer>

Output the issuer name.

=item B<-lastupdate>

Output the lastUpdate field.

=item B<-nextupdate>

Output the nextUpdate field.

=item B<-nameopt> I<option>

This specifies how the subject or issuer names are displayed.
See L<openssl-namedisplay-options(1)> for details.

=item B<-CAfile> I<file>, B<-no-CAfile>, B<-CApath> I<dir>, B<-no-CApath>,
B<-CAstore> I<uri>, B<-no-CAstore>

See L<openssl-verification-options(1)/Trusted Certificate Options> for details.

=item B<-provider> I<name>

=item B<-provider-path> I<path>

=item B<-propquery> I<propq>

See L<openssl(1)/Provider Options>, L<provider(7)>, and L<property(7)>.

=back

=head1 EXAMPLES

Convert a CRL file from PEM to DER:

 openssl crl -in crl.pem -outform DER -out crl.der

Output the text form of a DER encoded certificate:

 openssl crl -in crl.der -text -noout

=head1 BUGS

Ideally it should be possible to create a CRL using appropriate options
and files too.

=head1 SEE ALSO

L<openssl(1)>,
L<openssl-crl2pkcs7(1)>,
L<openssl-ca(1)>,
L<openssl-x509(1)>,
L<ossl_store-file(7)>

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
