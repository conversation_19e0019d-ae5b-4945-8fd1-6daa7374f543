=pod
{- OpenSSL::safe::output_do_not_edit_headers(); -}

=head1 NAME

openssl-info - print OpenSSL built-in information

=head1 SYNOPSIS

B<openssl info>
[B<-help>]
[B<-configdir>]
[B<-enginesdir>]
[B<-modulesdir> ]
[B<-dsoext>]
[B<-dirnamesep>]
[B<-listsep>]
[B<-seeds>]
[B<-cpusettings>]

=head1 DESCRIPTION

This command is used to print out information about OpenSSL.
The information is written exactly as it is with no extra text, which
makes useful for scripts.

As a consequence, only one item may be chosen for each run of this
command.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-configdir>

Outputs the default directory for OpenSSL configuration files.

=item B<-enginesdir>

Outputs the default directory for OpenSSL engine modules.

=item B<-modulesdir>

Outputs the default directory for OpenSSL dynamically loadable modules
other than engine modules.

=item B<-dsoext>

Outputs the DSO extension OpenSSL uses.

=item B<-dirnamesep>

Outputs the separator character between a directory specification and
a filename.
Note that on some operating systems, this is not the same as the
separator between directory elements.

=item B<-listsep>

Outputs the OpenSSL list separator character.
This is typically used to construct C<$PATH> (C<%PATH%> on Windows)
style lists.

=item B<-seeds>

Outputs the randomness seed sources.

=item B<-cpusettings>

Outputs the OpenSSL CPU settings info.

=back

=head1 HISTORY

This command was added in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2019-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
