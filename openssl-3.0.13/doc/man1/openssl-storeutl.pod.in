=pod

=begin comment
{- join("\n", @autowarntext) -}

=end comment

=head1 NAME

openssl-storeutl - STORE command

=head1 SYNOPSIS

B<openssl> B<storeutl>
[B<-help>]
[B<-out> I<file>]
[B<-noout>]
[B<-passin> I<arg>]
[B<-text> I<arg>]
[B<-r>]
[B<-certs>]
[B<-keys>]
[B<-crls>]
[B<-subject> I<arg>]
[B<-issuer> I<arg>]
[B<-serial> I<arg>]
[B<-alias> I<arg>]
[B<-fingerprint> I<arg>]
[B<-I<digest>>]
{- $OpenSSL::safe::opt_engine_synopsis -}{- $OpenSSL::safe::opt_provider_synopsis -}
I<uri>

=head1 DESCRIPTION

This command can be used to display the contents (after
decryption as the case may be) fetched from the given URI.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-out> I<filename>

specifies the output filename to write to or standard output by
default.

=item B<-noout>

this option prevents output of the PEM data.

=item B<-passin> I<arg>

the key password source. For more information about the format of I<arg>
see L<openssl-passphrase-options(1)>.

=item B<-text>

Prints out the objects in text form, similarly to the B<-text> output from
L<openssl-x509(1)>, L<openssl-pkey(1)>, etc.

=item B<-r>

Fetch objects recursively when possible.

=item B<-certs>

=item B<-keys>

=item B<-crls>

Only select the certificates, keys or CRLs from the given URI.
However, if this URI would return a set of names (URIs), those are always
returned.

Note that all options must be given before the I<uri> argument.
Otherwise they are ignored.

=item B<-subject> I<arg>

Search for an object having the subject name I<arg>.

The arg must be formatted as C</type0=value0/type1=value1/type2=...>.
Special characters may be escaped by C<\> (backslash), whitespace is retained.
Empty values are permitted but are ignored for the search.  That is,
a search with an empty value will have the same effect as not specifying
the type at all.
Giving a single C</> will lead to an empty sequence of RDNs (a NULL-DN).
Multi-valued RDNs can be formed by placing a C<+> character instead of a C</>
between the AttributeValueAssertions (AVAs) that specify the members of the set.

Example:

C</DC=org/DC=OpenSSL/DC=users/UID=123456+CN=John Doe>

=item B<-issuer> I<arg>

=item B<-serial> I<arg>

Search for an object having the given issuer name and serial number.
These two options I<must> be used together.
The issuer arg must be formatted as C</type0=value0/type1=value1/type2=...>,
characters may be escaped by \ (backslash), no spaces are skipped.
The serial arg may be specified as a decimal value or a hex value if preceded
by C<0x>.

=item B<-alias> I<arg>

Search for an object having the given alias.

=item B<-fingerprint> I<arg>

Search for an object having the given fingerprint.

=item B<-I<digest>>

The digest that was used to compute the fingerprint given with B<-fingerprint>.

{- $OpenSSL::safe::opt_engine_item -}

{- $OpenSSL::safe::opt_provider_item -}

=back

=head1 SEE ALSO

L<openssl(1)>

=head1 HISTORY

This command was added in OpenSSL 1.1.1.

The B<-engine> option was deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2016-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
