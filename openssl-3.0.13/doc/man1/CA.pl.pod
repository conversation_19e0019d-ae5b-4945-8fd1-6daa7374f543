=pod

=head1 NAME

CA.pl - friendlier interface for OpenSSL certificate programs

=head1 SYNOPSIS

B<CA.pl>
B<-?> |
B<-h> |
B<-help>

B<CA.pl>
B<-newcert> |
B<-newreq> |
B<-newreq-nodes> |
B<-xsign> |
B<-sign> |
B<-signCA> |
B<-signcert> |
B<-crl> |
B<-newca>
[B<-extra-I<cmd>> I<parameter>]

B<CA.pl> B<-pkcs12> [I<certname>]

B<CA.pl> B<-verify> I<certfile> ...

B<CA.pl> B<-revoke> I<certfile> [I<reason>]

=head1 DESCRIPTION

The B<CA.pl> script is a perl script that supplies the relevant command line
arguments to the L<openssl(1)> command for some common certificate operations.
It is intended to simplify the process of certificate creation and management
by the use of some simple options.

The script is intended as a simple front end for the L<openssl(1)> program for
use by a beginner. Its behaviour isn't always what is wanted. For more control
over the behaviour of the certificate commands call the L<openssl(1)> command
directly.

Most of the filenames mentioned below can be modified by editing the
B<CA.pl> script.

Under some environments it may not be possible to run the B<CA.pl> script
directly (for example Win32) and the default configuration file location may
be wrong. In this case the command:

 perl -S CA.pl

can be used and the B<OPENSSL_CONF> environment variable can be set to point to
the correct path of the configuration file.

=head1 OPTIONS

=over 4

=item B<-?>, B<-h>, B<-help>

Prints a usage message.

=item B<-newcert>

Creates a new self signed certificate. The private key is written to the file
F<newkey.pem> and the request written to the file F<newreq.pem>.
Invokes L<openssl-req(1)>.

=item B<-newreq>

Creates a new certificate request. The private key is written to the file
F<newkey.pem> and the request written to the file F<newreq.pem>.
Executes L<openssl-req(1)> under the hood.

=item B<-newreq-nodes>

Is like B<-newreq> except that the private key will not be encrypted.
Uses L<openssl-req(1)>.

=item B<-newca>

Creates a new CA hierarchy for use with the B<ca> program (or the B<-signcert>
and B<-xsign> options). The user is prompted to enter the filename of the CA
certificates (which should also contain the private key) or by hitting ENTER
details of the CA will be prompted for. The relevant files and directories
are created in a directory called F<demoCA> in the current directory.
Uses L<openssl-req(1)> and L<openssl-ca(1)>.

If the F<demoCA> directory already exists then the B<-newca> command will not
overwrite it and will do nothing. This can happen if a previous call using
the B<-newca> option terminated abnormally. To get the correct behaviour
delete the directory if it already exists.

=item B<-pkcs12>

Create a PKCS#12 file containing the user certificate, private key and CA
certificate. It expects the user certificate and private key to be in the
file F<newcert.pem> and the CA certificate to be in the file F<demoCA/cacert.pem>,
it creates a file F<newcert.p12>. This command can thus be called after the
B<-sign> option. The PKCS#12 file can be imported directly into a browser.
If there is an additional argument on the command line it will be used as the
"friendly name" for the certificate (which is typically displayed in the browser
list box), otherwise the name "My Certificate" is used.
Delegates work to L<openssl-pkcs12(1)>.

=item B<-sign>, B<-signcert>, B<-xsign>

Calls the L<openssl-ca(1)> command to sign a certificate request. It expects the
request to be in the file F<newreq.pem>. The new certificate is written to the
file F<newcert.pem> except in the case of the B<-xsign> option when it is
written to standard output.

=item B<-signCA>

This option is the same as the B<-sign> option except it uses the
configuration file section B<v3_ca> and so makes the signed request a
valid CA certificate. This is useful when creating intermediate CA from
a root CA.  Extra params are passed to L<openssl-ca(1)>.

=item B<-signcert>

This option is the same as B<-sign> except it expects a self signed certificate
to be present in the file F<newreq.pem>.
Extra params are passed to L<openssl-x509(1)> and L<openssl-ca(1)>.

=item B<-crl>

Generate a CRL. Executes L<openssl-ca(1)>.

=item B<-revoke> I<certfile> [I<reason>]

Revoke the certificate contained in the specified B<certfile>. An optional
reason may be specified, and must be one of: B<unspecified>,
B<keyCompromise>, B<CACompromise>, B<affiliationChanged>, B<superseded>,
B<cessationOfOperation>, B<certificateHold>, or B<removeFromCRL>.
Leverages L<openssl-ca(1)>.

=item B<-verify>

Verifies certificates against the CA certificate for F<demoCA>. If no
certificates are specified on the command line it tries to verify the file
F<newcert.pem>.  Invokes L<openssl-verify(1)>.

=item B<-extra-I<cmd>> I<parameter>

For each option B<extra-I<cmd>>, pass I<parameter> to the L<openssl(1)>
sub-command with the same name as I<cmd>, if that sub-command is invoked.
For example, if L<openssl-req(1)> is invoked, the I<parameter> given with
B<-extra-req> will be passed to it.
For multi-word parameters, either repeat the option or quote the I<parameters>
so it looks like one word to your shell.
See the individual command documentation for more information.

=back

=head1 EXAMPLES

Create a CA hierarchy:

 CA.pl -newca

Complete certificate creation example: create a CA, create a request, sign
the request and finally create a PKCS#12 file containing it.

 CA.pl -newca
 CA.pl -newreq
 CA.pl -sign
 CA.pl -pkcs12 "My Test Certificate"

=head1 ENVIRONMENT

The environment variable B<OPENSSL> may be used to specify the name of
the OpenSSL program. It can be a full pathname, or a relative one.

The environment variable B<OPENSSL_CONFIG> may be used to specify a
configuration option and value to the B<req> and B<ca> commands invoked by
this script. It's value should be the option and pathname, as in
C<-config /path/to/conf-file>.

=head1 SEE ALSO

L<openssl(1)>,
L<openssl-x509(1)>,
L<openssl-ca(1)>,
L<openssl-req(1)>,
L<openssl-pkcs12(1)>,
L<config(5)>

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
