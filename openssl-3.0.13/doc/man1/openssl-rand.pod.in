=pod
{- OpenSSL::safe::output_do_not_edit_headers(); -}

=head1 NAME

openssl-rand - generate pseudo-random bytes

=head1 SYNOPSIS

B<openssl rand>
[B<-help>]
[B<-out> I<file>]
[B<-base64>]
[B<-hex>]
{- $OpenSSL::safe::opt_engine_synopsis -}{- $OpenSSL::safe::opt_r_synopsis -}
{- $OpenSSL::safe::opt_provider_synopsis -}
I<num>

=head1 DESCRIPTION

This command generates I<num> random bytes using a cryptographically
secure pseudo random number generator (CSPRNG).

The random bytes are generated using the L<RAND_bytes(3)> function,
which provides a security level of 256 bits, provided it managed to
seed itself successfully from a trusted operating system entropy source.
Otherwise, the command will fail with a nonzero error code.
For more details, see L<RAND_bytes(3)>, L<RAND(7)>, and L<EVP_RAND(7)>.

=head1 OPTIONS

=over 4

=item B<-help>

Print out a usage message.

=item B<-out> I<file>

Write to I<file> instead of standard output.

=item B<-base64>

Perform base64 encoding on the output.

=item B<-hex>

Show the output as a hex string.

{- $OpenSSL::safe::opt_engine_item -}

{- $OpenSSL::safe::opt_r_item -}

{- $OpenSSL::safe::opt_provider_item -}

=back

=head1 SEE ALSO

L<openssl(1)>,
L<RAND_bytes(3)>,
L<RAND(7)>,
L<EVP_RAND(7)>

=head1 HISTORY

The B<-engine> option was deprecated in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2021 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
