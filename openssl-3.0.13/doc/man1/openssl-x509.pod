=pod

=begin comment

WARNING: do not edit!
Generated by <PERSON><PERSON><PERSON> from doc/man1/openssl-x509.pod.in

=end comment

=head1 NAME

openssl-x509 - Certificate display and signing command

=head1 SYNOPSIS

B<openssl> B<x509>
[B<-help>]
[B<-in> I<filename>|I<uri>]
[B<-passin> I<arg>]
[B<-new>]
[B<-x509toreq>]
[B<-req>]
[B<-copy_extensions> I<arg>]
[B<-inform> B<DER>|B<PEM>]
[B<-vfyopt> I<nm>:I<v>]
[B<-key> I<filename>|I<uri>]
[B<-keyform> B<DER>|B<PEM>|B<P12>|B<ENGINE>]
[B<-signkey> I<filename>|I<uri>]
[B<-out> I<filename>]
[B<-outform> B<DER>|B<PEM>]
[B<-nocert>]
[B<-noout>]
[B<-dateopt>]
[B<-text>]
[B<-certopt> I<option>]
[B<-fingerprint>]
[B<-alias>]
[B<-serial>]
[B<-startdate>]
[B<-enddate>]
[B<-dates>]
[B<-subject>]
[B<-issuer>]
[B<-nameopt> I<option>]
[B<-email>]
[B<-hash>]
[B<-subject_hash>]
[B<-subject_hash_old>]
[B<-issuer_hash>]
[B<-issuer_hash_old>]
[B<-ext> I<extensions>]
[B<-ocspid>]
[B<-ocsp_uri>]
[B<-purpose>]
[B<-pubkey>]
[B<-modulus>]
[B<-checkend> I<num>]
[B<-checkhost> I<host>]
[B<-checkemail> I<host>]
[B<-checkip> I<ipaddr>]
[B<-set_serial> I<n>]
[B<-next_serial>]
[B<-days> I<arg>]
[B<-preserve_dates>]
[B<-subj> I<arg>]
[B<-force_pubkey> I<filename>]
[B<-clrext>]
[B<-extfile> I<filename>]
[B<-extensions> I<section>]
[B<-sigopt> I<nm>:I<v>]
[B<-badsig>]
[B<-I<digest>>]
[B<-CA> I<filename>|I<uri>]
[B<-CAform> B<DER>|B<PEM>|B<P12>]
[B<-CAkey> I<filename>|I<uri>]
[B<-CAkeyform> B<DER>|B<PEM>|B<P12>|B<ENGINE>]
[B<-CAserial> I<filename>]
[B<-CAcreateserial>]
[B<-trustout>]
[B<-setalias> I<arg>]
[B<-clrtrust>]
[B<-addtrust> I<arg>]
[B<-clrreject>]
[B<-addreject> I<arg>]
[B<-rand> I<files>]
[B<-writerand> I<file>]
[B<-engine> I<id>]
[B<-provider> I<name>]
[B<-provider-path> I<path>]
[B<-propquery> I<propq>]

=head1 DESCRIPTION

This command is a multi-purposes certificate handling command.
It can be used to print certificate information,
convert certificates to various forms, edit certificate trust settings,
generate certificates from scratch or from certificating requests
and then self-signing them or signing them like a "micro CA".

Since there are a large number of options they will split up into
various sections.

=head1 OPTIONS

=head2 Input, Output, and General Purpose Options

=over 4

=item B<-help>

Print out a usage message.

=item B<-in> I<filename>|I<uri>

This specifies the input to read a certificate from
or the input file for reading a certificate request if the B<-req> flag is used.
In both cases this defaults to standard input.

This option cannot be combined with the B<-new> flag.

=item B<-passin> I<arg>

The key and certificate file password source.
For more information about the format of I<arg>
see L<openssl-passphrase-options(1)>.

=item B<-new>

Generate a certificate from scratch, not using an input certificate
or certificate request. So the B<-in> option must not be used in this case.
Instead, the B<-subj> option needs to be given.
The public key to include can be given with the B<-force_pubkey> option
and defaults to the key given with the B<-key> (or B<-signkey>) option,
which implies self-signature.

=item B<-x509toreq>

Output a PKCS#10 certificate request (rather than a certificate).
The B<-key> (or B<-signkey>) option must be used to provide the private key for
self-signing; the corresponding public key is placed in the subjectPKInfo field.

X.509 extensions included in a certificate input are not copied by default.
X.509 extensions to be added can be specified using the B<-extfile> option.

=item B<-req>

By default a certificate is expected on input.
With this option a PKCS#10 certificate request is expected instead,
which must be correctly self-signed.

X.509 extensions included in the request are not copied by default.
X.509 extensions to be added can be specified using the B<-extfile> option.

=item B<-copy_extensions> I<arg>

Determines how to handle X.509 extensions
when converting from a certificate to a request using the B<-x509toreq> option
or converting from a request to a certificate using the B<-req> option.
If I<arg> is B<none> or this option is not present then extensions are ignored.
If I<arg> is B<copy> or B<copyall> then all extensions are copied,
except that subject identifier and authority key identifier extensions
are not taken over when producing a certificate request.

The B<-ext> option can be used to further restrict which extensions to copy.

=item B<-inform> B<DER>|B<PEM>

The input file format; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-vfyopt> I<nm>:I<v>

Pass options to the signature algorithm during verify operations.
Names and values of these options are algorithm-specific.

=item B<-key> I<filename>|I<uri>

This option provides the private key for signing a new certificate or
certificate request.
Unless B<-force_pubkey> is given, the corresponding public key is placed in
the new certificate or certificate request, resulting in a self-signature.

This option cannot be used in conjunction with the B<-CA> option.

It sets the issuer name to the subject name (i.e., makes it self-issued)
and changes the public key to the supplied value (unless overridden
by B<-force_pubkey>).
Unless the B<-preserve_dates> option is supplied,
it sets the validity start date to the current time
and the end date to a value determined by the B<-days> option.

=item B<-signkey> I<filename>|I<uri>

This option is an alias of B<-key>.

=item B<-keyform> B<DER>|B<PEM>|B<P12>|B<ENGINE>

The key input format; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-out> I<filename>

This specifies the output filename to write to or standard output by default.

=item B<-outform> B<DER>|B<PEM>

The output format; the default is B<PEM>.
See L<openssl-format-options(1)> for details.

=item B<-nocert>

Do not output a certificate (except for printing as requested by below options).

=item B<-noout>

This option prevents output except for printing as requested by below options.

=back

=head2 Certificate Printing Options

Note: the B<-alias> and B<-purpose> options are also printing options
but are described in the L</Trust Settings> section.

=over 4

=item B<-dateopt>

Specify the date output format. Values are: rfc_822 and iso_8601.
Defaults to rfc_822.

=item B<-text>

Prints out the certificate in text form. Full details are printed including the
public key, signature algorithms, issuer and subject names, serial number
any extensions present and any trust settings.

=item B<-certopt> I<option>

Customise the print format used with B<-text>. The I<option> argument
can be a single option or multiple options separated by commas.
The B<-certopt> switch may be also be used more than once to set multiple
options. See the L</Text Printing Flags> section for more information.

=item B<-fingerprint>

Calculates and prints the digest of the DER encoded version of the entire
certificate (see digest options).
This is commonly called a "fingerprint". Because of the nature of message
digests, the fingerprint of a certificate is unique to that certificate and
two certificates with the same fingerprint can be considered to be the same.

=item B<-alias>

Prints the certificate "alias" (nickname), if any.

=item B<-serial>

Prints the certificate serial number.

=item B<-startdate>

Prints out the start date of the certificate, that is the notBefore date.

=item B<-enddate>

Prints out the expiry date of the certificate, that is the notAfter date.

=item B<-dates>

Prints out the start and expiry dates of a certificate.

=item B<-subject>

Prints the subject name.

=item B<-issuer>

Prints the issuer name.

=item B<-nameopt> I<option>

This specifies how the subject or issuer names are displayed.
See L<openssl-namedisplay-options(1)> for details.

=item B<-email>

Prints the email address(es) if any.

=item B<-hash>

Synonym for "-subject_hash" for backward compatibility reasons.

=item B<-subject_hash>

Prints the "hash" of the certificate subject name. This is used in OpenSSL to
form an index to allow certificates in a directory to be looked up by subject
name.

=item B<-subject_hash_old>

Prints the "hash" of the certificate subject name using the older algorithm
as used by OpenSSL before version 1.0.0.

=item B<-issuer_hash>

Prints the "hash" of the certificate issuer name.

=item B<-issuer_hash_old>

Prints the "hash" of the certificate issuer name using the older algorithm
as used by OpenSSL before version 1.0.0.

=item B<-ext> I<extensions>

Prints out the certificate extensions in text form.
Can also be used to restrict which extensions to copy.
Extensions are specified
with a comma separated string, e.g., "subjectAltName,subjectKeyIdentifier".
See the L<x509v3_config(5)> manual page for the extension names.

=item B<-ocspid>

Prints the OCSP hash values for the subject name and public key.

=item B<-ocsp_uri>

Prints the OCSP responder address(es) if any.

=item B<-purpose>

This option performs tests on the certificate extensions and outputs
the results. For a more complete description see
L<openssl-verification-options(1)/Certificate Extensions>.

=item B<-pubkey>

Prints the certificate's SubjectPublicKeyInfo block in PEM format.

=item B<-modulus>

This option prints out the value of the modulus of the public key
contained in the certificate.

=back

=head2 Certificate Checking Options

=over 4

=item B<-checkend> I<arg>

Checks if the certificate expires within the next I<arg> seconds and exits
nonzero if yes it will expire or zero if not.

=item B<-checkhost> I<host>

Check that the certificate matches the specified host.

=item B<-checkemail> I<email>

Check that the certificate matches the specified email address.

=item B<-checkip> I<ipaddr>

Check that the certificate matches the specified IP address.

=back

=head2 Certificate Output Options

=over 4

=item B<-set_serial> I<n>

Specifies the serial number to use.
This option can be used with the B<-key>, B<-signkey>, or B<-CA> options.
If used in conjunction with the B<-CA> option
the serial number file (as specified by the B<-CAserial> option) is not used.

The serial number can be decimal or hex (if preceded by C<0x>).

=item B<-next_serial>

Set the serial to be one more than the number in the certificate.

=item B<-days> I<arg>

Specifies the number of days until a newly generated certificate expires.
The default is 30.
Cannot be used together with the B<-preserve_dates> option.

=item B<-preserve_dates>

When signing a certificate, preserve "notBefore" and "notAfter" dates of any
input certificate instead of adjusting them to current time and duration.
Cannot be used together with the B<-days> option.

=item B<-subj> I<arg>

When a certificate is created set its subject name to the given value.
When the certificate is self-signed the issuer name is set to the same value.

The arg must be formatted as C</type0=value0/type1=value1/type2=...>.
Special characters may be escaped by C<\> (backslash), whitespace is retained.
Empty values are permitted, but the corresponding type will not be included
in the certificate.
Giving a single C</> will lead to an empty sequence of RDNs (a NULL-DN).
Multi-valued RDNs can be formed by placing a C<+> character instead of a C</>
between the AttributeValueAssertions (AVAs) that specify the members of the set.
Example:

C</DC=org/DC=OpenSSL/DC=users/UID=123456+CN=John Doe>

This option can be used in conjunction with the B<-force_pubkey> option
to create a certificate even without providing an input certificate
or certificate request.

=item B<-force_pubkey> I<filename>

When a certificate is created set its public key to the key in I<filename>
instead of the key contained in the input
or given with the B<-key> (or B<-signkey>) option.

This option is useful for creating self-issued certificates that are not
self-signed, for instance when the key cannot be used for signing, such as DH.
It can also be used in conjunction with B<-new> and B<-subj> to directly
generate a certificate containing any desired public key.

=item B<-clrext>

When transforming a certificate to a new certificate
by default all certificate extensions are retained.

When transforming a certificate or certificate request,
the B<-clrext> option prevents taking over any extensions from the source.
In any case, when producing a certificate request,
neither subject identifier nor authority key identifier extensions are included.

=item B<-extfile> I<filename>

Configuration file containing certificate and request X.509 extensions to add.

=item B<-extensions> I<section>

The section in the extfile to add X.509 extensions from.
If this option is not
specified then the extensions should either be contained in the unnamed
(default) section or the default section should contain a variable called
"extensions" which contains the section to use.
See the L<x509v3_config(5)> manual page for details of the
extension section format.

=item B<-sigopt> I<nm>:I<v>

Pass options to the signature algorithm during sign operations.
This option may be given multiple times.
Names and values provided using this option are algorithm-specific.

=item B<-badsig>

Corrupt the signature before writing it; this can be useful
for testing.

=item B<-I<digest>>

The digest to use.
This affects any signing or printing option that uses a message
digest, such as the B<-fingerprint>, B<-key>, and B<-CA> options.
Any digest supported by the L<openssl-dgst(1)> command can be used.
If not specified then SHA1 is used with B<-fingerprint> or
the default digest for the signing algorithm is used, typically SHA256.

=back

=head2 Micro-CA Options

=over 4

=item B<-CA> I<filename>|I<uri>

Specifies the "CA" certificate to be used for signing.
When present, this behaves like a "micro CA" as follows:
The subject name of the "CA" certificate is placed as issuer name in the new
certificate, which is then signed using the "CA" key given as detailed below.

This option cannot be used in conjunction with B<-key> (or B<-signkey>).
This option is normally combined with the B<-req> option referencing a CSR.
Without the B<-req> option the input must be an existing certificate
unless the B<-new> option is given, which generates a certificate from scratch.

=item B<-CAform> B<DER>|B<PEM>|B<P12>,

The format for the CA certificate; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-CAkey> I<filename>|I<uri>

Sets the CA private key to sign a certificate with.
The private key must match the public key of the certificate given with B<-CA>.
If this option is not provided then the key must be present in the B<-CA> input.

=item B<-CAkeyform> B<DER>|B<PEM>|B<P12>|B<ENGINE>

The format for the CA key; unspecified by default.
See L<openssl-format-options(1)> for details.

=item B<-CAserial> I<filename>

Sets the CA serial number file to use.

When creating a certificate with this option and with the B<-CA> option,
the certificate serial number is stored in the given file.
This file consists of one line containing
an even number of hex digits with the serial number used last time.
After reading this number, it is incremented and used, and the file is updated.

The default filename consists of the CA certificate file base name with
F<.srl> appended. For example if the CA certificate file is called
F<mycacert.pem> it expects to find a serial number file called
F<mycacert.srl>.

If the B<-CA> option is specified and neither <-CAserial> or <-CAcreateserial>
is given and the default serial number file does not exist,
a random number is generated; this is the recommended practice.

=item B<-CAcreateserial>

With this option and the B<-CA> option
the CA serial number file is created if it does not exist.
A random number is generated, used for the certificate,
and saved into the serial number file determined as described above.

=back

=head2 Trust Settings

A B<trusted certificate> is an ordinary certificate which has several
additional pieces of information attached to it such as the permitted
and prohibited uses of the certificate and possibly an "alias" (nickname).

Normally when a certificate is being verified at least one certificate
must be "trusted". By default a trusted certificate must be stored
locally and must be a root CA: any certificate chain ending in this CA
is then usable for any purpose.

Trust settings currently are only used with a root CA.
They allow a finer control over the purposes the root CA can be used for.
For example, a CA may be trusted for SSL client but not SSL server use.

See L<openssl-verification-options(1)> for more information
on the meaning of trust settings.

Future versions of OpenSSL will recognize trust settings on any
certificate: not just root CAs.

=over 4

=item B<-trustout>

Mark any certificate PEM output as <trusted> certificate rather than ordinary.
An ordinary or trusted certificate can be input but by default an ordinary
certificate is output and any trust settings are discarded.
With the B<-trustout> option a trusted certificate is output. A trusted
certificate is automatically output if any trust settings are modified.

=item B<-setalias> I<arg>

Sets the "alias" of the certificate. This will allow the certificate
to be referred to using a nickname for example "Steve's Certificate".

=item B<-clrtrust>

Clears all the permitted or trusted uses of the certificate.

=item B<-addtrust> I<arg>

Adds a trusted certificate use.
Any object name can be used here but currently only B<clientAuth>,
B<serverAuth>, B<emailProtection>, and B<anyExtendedKeyUsage> are defined.
As of OpenSSL 1.1.0, the last of these blocks all purposes when rejected or
enables all purposes when trusted.
Other OpenSSL applications may define additional uses.

=item B<-clrreject>

Clears all the prohibited or rejected uses of the certificate.

=item B<-addreject> I<arg>

Adds a prohibited trust anchor purpose.
It accepts the same values as the B<-addtrust> option.

=back

=head2 Generic options

=over 4

=item B<-rand> I<files>, B<-writerand> I<file>

See L<openssl(1)/Random State Options> for details.

=item B<-engine> I<id>

See L<openssl(1)/Engine Options>.
This option is deprecated.

=item B<-provider> I<name>

=item B<-provider-path> I<path>

=item B<-propquery> I<propq>

See L<openssl(1)/Provider Options>, L<provider(7)>, and L<property(7)>.

=back

=head2 Text Printing Flags

As well as customising the name printing format, it is also possible to
customise the actual fields printed using the B<certopt> option when
the B<text> option is present. The default behaviour is to print all fields.

=over 4

=item B<compatible>

Use the old format. This is equivalent to specifying no printing options at all.

=item B<no_header>

Don't print header information: that is the lines saying "Certificate"
and "Data".

=item B<no_version>

Don't print out the version number.

=item B<no_serial>

Don't print out the serial number.

=item B<no_signame>

Don't print out the signature algorithm used.

=item B<no_validity>

Don't print the validity, that is the B<notBefore> and B<notAfter> fields.

=item B<no_subject>

Don't print out the subject name.

=item B<no_issuer>

Don't print out the issuer name.

=item B<no_pubkey>

Don't print out the public key.

=item B<no_sigdump>

Don't give a hexadecimal dump of the certificate signature.

=item B<no_aux>

Don't print out certificate trust information.

=item B<no_extensions>

Don't print out any X509V3 extensions.

=item B<ext_default>

Retain default extension behaviour: attempt to print out unsupported
certificate extensions.

=item B<ext_error>

Print an error message for unsupported certificate extensions.

=item B<ext_parse>

ASN1 parse unsupported extensions.

=item B<ext_dump>

Hex dump unsupported extensions.

=item B<ca_default>

The value used by L<openssl-ca(1)>, equivalent to B<no_issuer>, B<no_pubkey>,
B<no_header>, and B<no_version>.

=back

=head1 EXAMPLES

Note: in these examples the '\' means the example should be all on one
line.

Print the contents of a certificate:

 openssl x509 -in cert.pem -noout -text

Print the "Subject Alternative Name" extension of a certificate:

 openssl x509 -in cert.pem -noout -ext subjectAltName

Print more extensions of a certificate:

 openssl x509 -in cert.pem -noout -ext subjectAltName,nsCertType

Print the certificate serial number:

 openssl x509 -in cert.pem -noout -serial

Print the certificate subject name:

 openssl x509 -in cert.pem -noout -subject

Print the certificate subject name in RFC2253 form:

 openssl x509 -in cert.pem -noout -subject -nameopt RFC2253

Print the certificate subject name in oneline form on a terminal
supporting UTF8:

 openssl x509 -in cert.pem -noout -subject -nameopt oneline,-esc_msb

Print the certificate SHA1 fingerprint:

 openssl x509 -sha1 -in cert.pem -noout -fingerprint

Convert a certificate from PEM to DER format:

 openssl x509 -in cert.pem -inform PEM -out cert.der -outform DER

Convert a certificate to a certificate request:

 openssl x509 -x509toreq -in cert.pem -out req.pem -key key.pem

Convert a certificate request into a self-signed certificate using
extensions for a CA:

 openssl x509 -req -in careq.pem -extfile openssl.cnf -extensions v3_ca \
        -key key.pem -out cacert.pem

Sign a certificate request using the CA certificate above and add user
certificate extensions:

 openssl x509 -req -in req.pem -extfile openssl.cnf -extensions v3_usr \
        -CA cacert.pem -CAkey key.pem -CAcreateserial

Set a certificate to be trusted for SSL client use and change set its alias to
"Steve's Class 1 CA"

 openssl x509 -in cert.pem -addtrust clientAuth \
        -setalias "Steve's Class 1 CA" -out trust.pem

=head1 NOTES

The conversion to UTF8 format used with the name options assumes that
T61Strings use the ISO8859-1 character set. This is wrong but Netscape
and MSIE do this as do many certificates. So although this is incorrect
it is more likely to print the majority of certificates correctly.

The B<-email> option searches the subject name and the subject alternative
name extension. Only unique email addresses will be printed out: it will
not print the same address more than once.

=head1 BUGS

It is possible to produce invalid certificates or requests by specifying the
wrong private key, using unsuitable X.509 extensions,
or using inconsistent options in some cases: these should be checked.

There should be options to explicitly set such things as start and end
dates rather than an offset from the current time.

=head1 SEE ALSO

L<openssl(1)>,
L<openssl-req(1)>,
L<openssl-ca(1)>,
L<openssl-genrsa(1)>,
L<openssl-gendsa(1)>,
L<openssl-verify(1)>,
L<x509v3_config(5)>

=head1 HISTORY

The hash algorithm used in the B<-subject_hash> and B<-issuer_hash> options
before OpenSSL 1.0.0 was based on the deprecated MD5 algorithm and the encoding
of the distinguished name. In OpenSSL 1.0.0 and later it is based on a canonical
version of the DN using SHA1. This means that any directories using the old
form must have their links rebuilt using L<openssl-rehash(1)> or similar.

The B<-signkey> option has been renamed to B<-key> in OpenSSL 3.0,
keeping the old name as an alias.

The B<-engine> option was deprecated in OpenSSL 3.0.

The B<-C> option was removed in OpenSSL 3.0.

=head1 COPYRIGHT

Copyright 2000-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
