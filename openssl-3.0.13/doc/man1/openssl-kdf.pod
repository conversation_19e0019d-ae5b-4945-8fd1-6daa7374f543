=pod

=begin comment

WARNING: do not edit!
Generated by <PERSON><PERSON><PERSON> from doc/man1/openssl-kdf.pod.in

=end comment

=head1 NAME

openssl-kdf - perform Key Derivation Function operations

=head1 SYNOPSIS

B<openssl kdf>
[B<-help>]
[B<-cipher>]
[B<-digest>]
[B<-mac>]
[B<-kdfopt> I<nm>:I<v>]
[B<-keylen> I<num>]
[B<-out> I<filename>]
[B<-binary>]
[B<-provider> I<name>]
[B<-provider-path> I<path>]
[B<-propquery> I<propq>]
I<kdf_name>

=head1 DESCRIPTION

The key derivation functions generate a derived key from either a secret or
password.

=head1 OPTIONS

=over 4

=item B<-help>

Print a usage message.

=item B<-keylen> I<num>

The output size of the derived key. This field is required.

=item B<-out> I<filename>

Filename to output to, or standard output by default.

=item B<-binary>

Output the derived key in binary form. Uses hexadecimal text format if not specified.

=item B<-cipher> I<name>

Specify the cipher to be used by the KDF.
Not all KDFs require a cipher and it is an error to use this option in such
cases.

=item B<-digest> I<name>

Specify the digest to be used by the KDF.
Not all KDFs require a digest and it is an error to use this option in such
cases.
To see the list of supported digests, use C<openssl list -digest-commands>.

=item B<-mac> I<name>

Specify the MAC to be used by the KDF.
Not all KDFs require a MAC and it is an error to use this option in such
cases.

=item B<-kdfopt> I<nm>:I<v>

Passes options to the KDF algorithm.
A comprehensive list of parameters can be found in L<EVP_KDF(3)/PARAMETERS>.
Common parameter names used by EVP_KDF_CTX_set_params() are:

=over 4

=item B<key:>I<string>

Specifies the secret key as an alphanumeric string (use if the key contains
printable characters only).
The string length must conform to any restrictions of the KDF algorithm.
A key must be specified for most KDF algorithms.

=item B<hexkey:>I<string>

Alternative to the B<key:> option where
the secret key is specified in hexadecimal form (two hex digits per byte).

=item B<pass:>I<string>

Specifies the password as an alphanumeric string (use if the password contains
printable characters only).
The password must be specified for PBKDF2 and scrypt.

=item B<hexpass:>I<string>

Alternative to the B<pass:> option where
the password is specified in hexadecimal form (two hex digits per byte).

=item B<salt:>I<string>

Specifies a non-secret unique cryptographic salt as an alphanumeric string
(use if it contains printable characters only).
The length must conform to any restrictions of the KDF algorithm.
A salt parameter is required for several KDF algorithms,
such as L<EVP_KDF-PBKDF2(7)>.

=item B<hexsalt:>I<string>

Alternative to the B<salt:> option where
the salt is specified in hexadecimal form (two hex digits per byte).

=item B<info:>I<string>

Some KDF implementations, such as L<EVP_KDF-HKDF(7)>, take an 'info' parameter
for binding the derived key material
to application- and context-specific information.
Specifies the info, fixed info, other info or shared info argument
as an alphanumeric string (use if it contains printable characters only).
The length must conform to any restrictions of the KDF algorithm.

=item B<hexinfo:>I<string>

Alternative to the B<info:> option where
the info is specified in hexadecimal form (two hex digits per byte).

=item B<digest:>I<string>

This option is identical to the B<-digest> option.

=item B<cipher:>I<string>

This option is identical to the B<-cipher> option.

=item B<mac:>I<string>

This option is identical to the B<-mac> option.

=back

=item B<-provider> I<name>

=item B<-provider-path> I<path>

=item B<-propquery> I<propq>

See L<openssl(1)/Provider Options>, L<provider(7)>, and L<property(7)>.

=item I<kdf_name>

Specifies the name of a supported KDF algorithm which will be used.
The supported algorithms names include TLS1-PRF, HKDF, SSKDF, PBKDF2,
SSHKDF, X942KDF-ASN1, X942KDF-CONCAT, X963KDF and SCRYPT.

=back

=head1 EXAMPLES

Use TLS1-PRF to create a hex-encoded derived key from a secret key and seed:

    openssl kdf -keylen 16 -kdfopt digest:SHA2-256 -kdfopt key:secret \
                -kdfopt seed:seed TLS1-PRF

Use HKDF to create a hex-encoded derived key from a secret key, salt and info:

    openssl kdf -keylen 10 -kdfopt digest:SHA2-256 -kdfopt key:secret \
                -kdfopt salt:salt -kdfopt info:label HKDF

Use SSKDF with KMAC to create a hex-encoded derived key from a secret key, salt and info:

    openssl kdf -keylen 64 -kdfopt mac:KMAC-128 -kdfopt maclen:20 \
                -kdfopt hexkey:b74a149a161545 -kdfopt hexinfo:348a37a2 \
                -kdfopt hexsalt:3638271ccd68a2 SSKDF

Use SSKDF with HMAC to create a hex-encoded derived key from a secret key, salt and info:

    openssl kdf -keylen 16 -kdfopt mac:HMAC -kdfopt digest:SHA2-256 \
                -kdfopt hexkey:b74a149a -kdfopt hexinfo:348a37a2 \
                -kdfopt hexsalt:3638271c SSKDF

Use SSKDF with Hash to create a hex-encoded derived key from a secret key, salt and info:

    openssl kdf -keylen 14 -kdfopt digest:SHA2-256 \
                -kdfopt hexkey:6dbdc23f045488 \
                -kdfopt hexinfo:a1b2c3d4 SSKDF

Use SSHKDF to create a hex-encoded derived key from a secret key, hash and session_id:

    openssl kdf -keylen 16 -kdfopt digest:SHA2-256 \
                -kdfopt hexkey:0102030405 \
                -kdfopt hexxcghash:06090A \
                -kdfopt hexsession_id:01020304 \
                -kdfopt type:A SSHKDF

Use PBKDF2 to create a hex-encoded derived key from a password and salt:

    openssl kdf -keylen 32 -kdfopt digest:SHA256 -kdfopt pass:password \
                -kdfopt salt:salt -kdfopt iter:2 PBKDF2

Use scrypt to create a hex-encoded derived key from a password and salt:

    openssl kdf -keylen 64 -kdfopt pass:password -kdfopt salt:NaCl \
                -kdfopt n:1024 -kdfopt r:8 -kdfopt p:16 \
                -kdfopt maxmem_bytes:10485760 SCRYPT

=head1 NOTES

The KDF mechanisms that are available will depend on the options
used when building OpenSSL.

=head1 SEE ALSO

L<openssl(1)>,
L<openssl-pkeyutl(1)>,
L<EVP_KDF(3)>,
L<EVP_KDF-SCRYPT(7)>,
L<EVP_KDF-TLS1_PRF(7)>,
L<EVP_KDF-PBKDF2(7)>,
L<EVP_KDF-HKDF(7)>,
L<EVP_KDF-SS(7)>,
L<EVP_KDF-SSHKDF(7)>,
L<EVP_KDF-X942-ASN1(7)>,
L<EVP_KDF-X942-CONCAT(7)>,
L<EVP_KDF-X963(7)>

=head1 HISTORY

Added in OpenSSL 3.0

=head1 COPYRIGHT

Copyright 2019-2023 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
