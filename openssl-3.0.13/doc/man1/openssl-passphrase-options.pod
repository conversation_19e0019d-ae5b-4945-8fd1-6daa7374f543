=pod

=head1 NAME

openssl-passphrase-options - Pass phrase options

=head1 SYNOPSIS

B<openssl>
I<command>
[ I<options> ... ]
[ I<parameters> ... ]

=head1 DESCRIPTION

Several OpenSSL commands accept password arguments, typically using B<-passin>
and B<-passout> for input and output passwords respectively. These allow
the password to be obtained from a variety of sources. Both of these
options take a single argument whose format is described below. If no
password argument is given and a password is required then the user is
prompted to enter one: this will typically be read from the current
terminal with echoing turned off.

Note that character encoding may be relevant, please see
L<passphrase-encoding(7)>.

=head1 OPTIONS

=head2 Pass Phrase Option Arguments

Pass phrase arguments can be formatted as follows.

=over 4

=item B<pass:>I<password>

The actual password is I<password>. Since the password is visible
to utilities (like 'ps' under Unix) this form should only be used
where security is not important.

=item B<env:>I<var>

Obtain the password from the environment variable I<var>. Since
the environment of other processes is visible on certain platforms
(e.g. ps under certain Unix OSes) this option should be used with caution.

=item B<file:>I<pathname>

The first line of I<pathname> is the password. If the same I<pathname>
argument is supplied to B<-passin> and B<-passout> arguments then the first
line will be used for the input password and the next line for the output
password. I<pathname> need not refer to a regular file: it could for example
refer to a device or named pipe.

=item B<fd:>I<number>

Read the password from the file descriptor I<number>. This can be used to
send the data via a pipe for example.

=item B<stdin>

Read the password from standard input.

=back

=head1 COPYRIGHT

Copyright 2000-2020 The OpenSSL Project Authors. All Rights Reserved.

Licensed under the Apache License 2.0 (the "License").  You may not use
this file except in compliance with the License.  You can obtain a copy
in the file LICENSE in the source distribution or at
L<https://www.openssl.org/source/license.html>.

=cut
