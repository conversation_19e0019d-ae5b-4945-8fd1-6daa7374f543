#!/bin/bash

# 编译脚本
# 用于编译 himixInteract 和 himixPub 程序

# 设置编译参数
CC=gcc
CFLAGS="-Wall -g -I./source"
LDFLAGS="-lssl -lcrypto -lmosquitto -lpthread -lsqlite3 -ljpeg -lm"

# 源文件列表
SOURCES="himixInteract.c source/utils.c source/activation.c source/cJSON.c source/file_monitor.c source/tcp.c source/camera_ssh.c source/file_paths.c"
HIMIX_PUB_SOURCES="himixPub.c source/utils.c source/activation.c source/cJSON.c source/file_paths.c source/tcp.c source/camera_ssh.c"

echo "开始编译..."

# 编译 himixInteract
echo "编译 himixInteract..."
$CC $CFLAGS -o mqttDemo $SOURCES $LDFLAGS
if [ $? -eq 0 ]; then
    echo "himixInteract 编译成功"
else
    echo "himixInteract 编译失败"
    exit 1
fi

# 编译 himixPub
echo "编译 himixPub..."
$CC $CFLAGS -o himixPub $HIMIX_PUB_SOURCES $LDFLAGS
if [ $? -eq 0 ]; then
    echo "himixPub 编译成功"
else
    echo "himixPub 编译失败"
    exit 1
fi

echo "编译完成！"
echo "生成的可执行文件："
echo "  - mqttDemo (himixInteract)"
echo "  - himixPub"
