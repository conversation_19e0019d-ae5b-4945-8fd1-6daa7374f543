#include <stdio.h>
#include <time.h>
#include <stdarg.h>
#include <unistd.h>
#include "utils.h"
#include "file.h"
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <limits.h>

static FILE* log_fp = NULL;
static FILE* error_fp = NULL;

// 导出的全局变量，供其他模块使用
FILE* log_file = NULL;
FILE* error_file = NULL;

// 全局配置变量定义
DeviceConfig g_device_config = {0};

// 定义最大日志文件大小（例如 10MB）
#define MAX_LOG_SIZE (10 * 1024 * 1024)
// 定义保留的日志文件数量
#define MAX_LOG_FILES 5

// 检查文件大小
static long get_file_size(const char* filename) {
    struct stat st;
    if (stat(filename, &st) == 0) {
        return st.st_size;
    }
    return -1;
}

// 压缩旧的日志文件
static void compress_log_file(const char* filename) {
    char cmd[512];
    snprintf(cmd, sizeof(cmd), "gzip -f %s", filename);
    system(cmd);
}

// 日志文件轮转
static void rotate_log_files(const char* base_filename) {
    char old_name[256], new_name[256];
    
    // 删除最老的日志文件
    snprintf(old_name, sizeof(old_name), "%s.%d", base_filename, MAX_LOG_FILES);
    remove(old_name);
    
    // 重命名现有的日志文件
    for (int i = MAX_LOG_FILES - 1; i >= 1; i--) {
        snprintf(old_name, sizeof(old_name), "%s.%d", base_filename, i);
        snprintf(new_name, sizeof(new_name), "%s.%d", base_filename, i + 1);
        rename(old_name, new_name);
    }
    
    // 重命名当前日志文件
    snprintf(new_name, sizeof(new_name), "%s.1", base_filename);
    rename(base_filename, new_name);
    
    // 压缩旧的日志文件
    for (int i = 2; i <= MAX_LOG_FILES; i++) {
        snprintf(old_name, sizeof(old_name), "%s.%d", base_filename, i);
        if (access(old_name, F_OK) == 0) {
            compress_log_file(old_name);
        }
    }
}

void print_with_time(const char* format, ...)
{
    time_t now;
    struct tm *timeinfo;
    char time_buffer[32];
    
    // 获取当前时间
    time(&now);
    timeinfo = localtime(&now);
    
    // 格式化时间字符串 [YYYY-MM-DD HH:MM:SS]
    strftime(time_buffer, sizeof(time_buffer), "[%Y-%m-%d %H:%M:%S] ", timeinfo);
    
    // 打印时间戳
    printf("%s", time_buffer);
    
    // 处理可变参数
    va_list args;
    va_start(args, format);
    vprintf(format, args);
    va_end(args);
    
    // 刷新输出缓冲区
    fflush(stdout);
}

void print_error_with_time(const char* format, ...)
{
    time_t now;
    struct tm *timeinfo;
    char time_buffer[32];
    
    // 获取当前时间
    time(&now);
    timeinfo = localtime(&now);
    
    // 格式化时间字符串 [YYYY-MM-DD HH:MM:SS]
    strftime(time_buffer, sizeof(time_buffer), "[%Y-%m-%d %H:%M:%S] ", timeinfo);
    
    // 打印时间戳到stderr
    fprintf(stderr, "%s", time_buffer);
    
    // 处理可变参数
    va_list args;
    va_start(args, format);
    vfprintf(stderr, format, args);
    va_end(args);
    
    // 刷新错误输出缓冲区
    fflush(stderr);
}

int reopen_log_file(void)
{
    // 检查当前日志文件大小
    long size = get_file_size(LOG_FILE);
    if (size > MAX_LOG_SIZE) {
        // 关闭现有文件
        if (log_fp != NULL) {
            fclose(log_fp);
            log_fp = NULL;
        }
        if (error_fp != NULL) {
            fclose(error_fp);
            error_fp = NULL;
        }
        
        // 执行日志轮转
        rotate_log_files(LOG_FILE);
        rotate_log_files(ERROR_LOG_FILE);
    }

    // 关闭之前的文件（如果有）
    if (log_fp != NULL) {
        fclose(log_fp);
    }
    if (error_fp != NULL) {
        fclose(error_fp);
    }

    // 打开普通日志文件
    log_fp = fopen(LOG_FILE, "a");
    if (log_fp == NULL) {
        fprintf(stderr, "Failed to open log file: %s\n", LOG_FILE);
        return -1;
    }

    // 打开错误日志文件
    error_fp = fopen(ERROR_LOG_FILE, "a");
    if (error_fp == NULL) {
        fprintf(stderr, "Failed to open error log file: %s\n", ERROR_LOG_FILE);
        fclose(log_fp);
        return -1;
    }

    // 重定向标准输出到log.txt
    if (dup2(fileno(log_fp), STDOUT_FILENO) == -1) {
        print_error_with_time("Failed to redirect stdout");
        fclose(log_fp);
        fclose(error_fp);
        return -1;
    }

    // 重定向标准错误到error.txt
    if (dup2(fileno(error_fp), STDERR_FILENO) == -1) {
        print_error_with_time("Failed to redirect stderr");
        fclose(log_fp);
        fclose(error_fp);
        return -1;
    }

    // 设置缓冲区为行缓冲
    setvbuf(log_fp, NULL, _IOLBF, 0);
    setvbuf(error_fp, NULL, _IOLBF, 0);

    return 0;
}

void close_log_files(void)
{
    if (log_fp) {
        fclose(log_fp);
        log_fp = NULL;
    }
    if (error_fp) {
        fclose(error_fp);
        error_fp = NULL;
    }
}

// 改进的重启设备函数 - 先释放资源再重启
void reboot_device(void)
{
    // 先关闭日志文件
    print_with_time("正在准备重启设备...\n");
    
    // 关闭日志文件
    close_log_files();
    
    // 终止所有与mqtt相关的进程（包括当前进程）
    int ret = system("pkill -f mqtt");
    
    // 注意：下面的代码只有在pkill没有杀死当前进程时才会执行
    if(ret != 0)
    {
        print_error_with_time("终止MQTT进程失败，返回码: %d\n", ret);
    }
    
    // 执行重启脚本
    char cmd[PATH_MAX + 10];
    snprintf(cmd, sizeof(cmd), "bash %s", REBOOT_SCRIPT);
    ret = system(cmd);
    if(ret != 0)
    {
        print_error_with_time("重启设备失败，返回码: %d\n", ret);
    }
    else
    {
        print_with_time("重启设备成功\n");
    }
    
    // 强制退出当前进程
    exit(0);
}

// 解析配置文件
int parse_config_file(const char* config_file)
{
    FILE *fp;
    char line[256];
    char section[64] = "";
    
    fp = fopen(config_file, "r");
    if (fp == NULL) {
        print_error_with_time("无法打开配置文件: %s\n", config_file);
        return -1;
    }
    
    print_with_time("正在解析配置文件: %s\n", config_file);
    
    while (fgets(line, sizeof(line), fp)) {
        // 去除行尾的换行符
        size_t len = strlen(line);
        if (len > 0 && line[len-1] == '\n')
            line[len-1] = '\0';
        
        // 跳过空行
        if (strlen(line) == 0)
            continue;
            
        // 检查是否是节名
        if (line[0] == '[' && line[len-2] == ']') {
            strncpy(section, line + 1, len - 3);
            section[len - 3] = '\0';
            continue;
        }
        
        // 解析键值对
        char key[128], value[128];
        if (sscanf(line, "%[^=] = %[^\n]", key, value) == 2) {
            // 去除键和值两端的空格
            char *k = key;
            while (*k == ' ') k++;
            char *v = value;
            while (*v == ' ') v++;
            
            size_t k_len = strlen(k);
            while (k_len > 0 && (k[k_len-1] == ' ' || k[k_len-1] == '\t')) {
                k[k_len-1] = '\0';
                k_len--;
            }
            
            if (strcmp(section, "DeviceInfo") == 0) {
                if (strcmp(k, "SSID") == 0)
                    strncpy(g_device_config.ssid, v, MAX_CONFIG_LEN - 1);
                else if (strcmp(k, "Camera") == 0)
                    strncpy(g_device_config.camera, v, MAX_CONFIG_LEN - 1);
                else if (strcmp(k, "AppVersion") == 0)
                    strncpy(g_device_config.app_version, v, MAX_CONFIG_LEN - 1);
                else if (strcmp(k, "HardWareVersion") == 0)
                    strncpy(g_device_config.hardware_version, v, MAX_CONFIG_LEN - 1);
                else if (strcmp(k, "DeviceType") == 0)
                    g_device_config.device_type = atoi(v);
            }
            else if (strcmp(section, "CalculationControl") == 0) {
                if (strcmp(k, "CalculationFrequency") == 0)
                    strncpy(g_device_config.calculation_frequency, v, MAX_CONFIG_LEN - 1);
            }
            else if (strcmp(section, "TransmissionControl") == 0) {
                if (strcmp(k, "TransmissionFrequency") == 0)
                    strncpy(g_device_config.transmission_frequency, v, MAX_CONFIG_LEN - 1);
                else if (strcmp(k, "ImageProtocol") == 0)
                    g_device_config.image_protocol = atoi(v);
                else if (strcmp(k, "IP") == 0)
                    strncpy(g_device_config.server_ip, v, MAX_CONFIG_LEN - 1);
                else if (strcmp(k, "MqttPort") == 0)
                    g_device_config.mqtt_port = atoi(v);
                else if (strcmp(k, "TcpPort") == 0)
                    g_device_config.tcp_port = atoi(v);
                else if (strcmp(k, "BackupIP") == 0)
                    strncpy(g_device_config.backup_ip, v, MAX_CONFIG_LEN - 1);
                else if (strcmp(k, "BackupMqttPort") == 0)
                    g_device_config.backup_mqtt_port = atoi(v);
                else if (strcmp(k, "BackupTcpPort") == 0)
                    g_device_config.backup_tcp_port = atoi(v);
            }
            else if (strcmp(section, "MotorControl") == 0) {
                if (strcmp(k, "MaxSpeed") == 0)
                    g_device_config.max_speed = atoi(v);
            }
            else if (strcmp(section, "Activation") == 0) {
                if (strcmp(k, "RegCode") == 0)
                    strncpy(g_device_config.reg_code, v, MAX_CONFIG_LEN - 1);
                else if (strcmp(k, "ExpiryDate") == 0)
                    strncpy(g_device_config.expiry_date, v, MAX_CONFIG_LEN - 1);
            }
            else if (strcmp(section, "DeviceStatus") == 0) {
                if (strcmp(k, "ExVoltage") == 0)
                    strncpy(g_device_config.ex_voltage, v, MAX_CONFIG_LEN - 1);
                else if (strcmp(k, "ExStorage") == 0)
                    strncpy(g_device_config.ex_storage, v, MAX_CONFIG_LEN - 1);
                else if (strcmp(k, "APN") == 0)
                    strncpy(g_device_config.apn, v, MAX_CONFIG_LEN - 1);
                else if (strcmp(k, "NetworkSignal") == 0)
                    strncpy(g_device_config.network_signal, v, MAX_CONFIG_LEN - 1);
            }
        }
    }
    
    fclose(fp);
    print_with_time("配置文件解析完成\n");
    return 0;
}

// 打印设备配置信息
void print_device_config(void)
{
    print_with_time("设备配置信息:\n");
    print_with_time("设备ID: %s\n", g_device_config.ssid);
    print_with_time("相机类型: %s\n", g_device_config.camera);
    print_with_time("软件版本: %s\n", g_device_config.app_version);
    print_with_time("硬件版本: %s\n", g_device_config.hardware_version);
    print_with_time("设备类型: %d\n", g_device_config.device_type);
    print_with_time("计算频率: %s\n", g_device_config.calculation_frequency);
    print_with_time("传输频率: %s\n", g_device_config.transmission_frequency);
    print_with_time("服务器IP: %s\n", g_device_config.server_ip);
    print_with_time("MQTT端口: %d\n", g_device_config.mqtt_port);
    print_with_time("TCP端口: %d\n", g_device_config.tcp_port);
    print_with_time("注册码: %s\n", g_device_config.reg_code);
    print_with_time("过期日期: %s\n", g_device_config.expiry_date);
}

char* get_device_info(void)
{
    // 分配足够大的内存来存储所有配置信息
    char* device_info = (char*)malloc(4096);  // 1KB应该足够了，根据需要可以调整
    if (!device_info) {
        print_error_with_time("内存分配失败\n");
        return NULL;
    }

    // 使用 snprintf 格式化所有配置信息
    snprintf(device_info, 4096,
        "设备ID: %s\n"
        "相机类型: %s\n"
        "软件版本: %s\n"
        "硬件版本: %s\n"
        "设备类型: %d\n"
        "计算频率: %s\n"
        "传输频率: %s\n"
        "服务器IP: %s\n"
        "MQTT端口: %d\n"
        "TCP端口: %d\n"
        "备用IP: %s\n"
        "备用MQTT端口: %d\n"
        "备用TCP端口: %d\n"
        "最大速度: %d\n"
        "注册码: %s\n"
        "过期日期: %s\n"
        "外部电压: %s\n"
        "外部存储: %s\n"
        "APN: %s\n"
        "网络信号: %s\n",
        g_device_config.ssid,
        g_device_config.camera,
        g_device_config.app_version,
        g_device_config.hardware_version,
        g_device_config.device_type,
        g_device_config.calculation_frequency,
        g_device_config.transmission_frequency,
        g_device_config.server_ip,
        g_device_config.mqtt_port,
        g_device_config.tcp_port,
        g_device_config.backup_ip,
        g_device_config.backup_mqtt_port,
        g_device_config.backup_tcp_port,
        g_device_config.max_speed,
        g_device_config.reg_code,
        g_device_config.expiry_date,
        g_device_config.ex_voltage,
        g_device_config.ex_storage,
        g_device_config.apn,
        g_device_config.network_signal
    );

    return device_info;  // 注意：调用者负责释放返回的内存
}

// 添加日志清理函数
void cleanup_old_logs(void) {
    time_t now = time(NULL);
    struct stat st;
    char filename[256];
    
    // 检查并清理超过30天的日志文件
    for (int i = 1; i <= MAX_LOG_FILES; i++) {
        snprintf(filename, sizeof(filename), "%s.%d", LOG_FILE, i);
        if (stat(filename, &st) == 0) {
            // 如果文件超过30天
            if (difftime(now, st.st_mtime) > (30 * 24 * 60 * 60)) {
                remove(filename);
            }
        }
        
        snprintf(filename, sizeof(filename), "%s.%d", ERROR_LOG_FILE, i);
        if (stat(filename, &st) == 0) {
            if (difftime(now, st.st_mtime) > (30 * 24 * 60 * 60)) {
                remove(filename);
            }
        }
    }
}

